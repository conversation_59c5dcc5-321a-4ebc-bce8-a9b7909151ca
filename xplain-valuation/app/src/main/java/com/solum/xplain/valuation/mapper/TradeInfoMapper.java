package com.solum.xplain.valuation.mapper;

import static com.solum.xplain.extensions.utils.StandardIdUtils.tradeStandardId;
import static com.solum.xplain.valuation.calculation.TradeUtils.CLIENT_PV;
import static java.util.Optional.ofNullable;

import com.opengamma.strata.basics.StandardId;
import com.opengamma.strata.product.TradeInfo;
import com.solum.xplain.extensions.utils.StandardIdUtils;
import com.solum.xplain.valuation.messages.trade.ValuationTradeInfo;
import java.util.Optional;
import org.springframework.lang.Nullable;

public class TradeInfoMapper {
  public static final TradeInfoMapper INSTANCE = new TradeInfoMapper();

  public TradeInfo toTradeInfo(ValuationTradeInfo info, String externalTradeId) {
    var tradeInfo =
        TradeInfo.builder()
            .id(tradeStandardId(externalTradeId))
            .tradeDate(info.getTradeDate())
            .settlementDate(info.getSettlementDate())
            .counterparty(counterpartyStandardId(info.getCounterParty()))
            .tradeTime(info.getTradeTime())
            .zone(info.getZoneId())
            .build();
    return Optional.of(tradeInfo).map(i -> withClientPv(i, info.getClientPv())).orElse(tradeInfo);
  }

  @Nullable
  private StandardId counterpartyStandardId(@Nullable String counterparty) {
    return counterparty == null ? null : StandardIdUtils.counterpartyStandardId(counterparty);
  }

  private TradeInfo withClientPv(TradeInfo info, Double clientPv) {
    return ofNullable(clientPv).map(pv -> info.withAttribute(CLIENT_PV, pv)).orElse(info);
  }
}
