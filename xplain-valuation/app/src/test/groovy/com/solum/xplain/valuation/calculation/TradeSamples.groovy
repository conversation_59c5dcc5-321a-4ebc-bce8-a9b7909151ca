package com.solum.xplain.valuation.calculation

import static com.opengamma.strata.basics.currency.Currency.AUD
import static com.opengamma.strata.basics.currency.Currency.CLP
import static com.opengamma.strata.basics.currency.Currency.CNY
import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING
import static com.opengamma.strata.basics.date.BusinessDayConventions.NO_ADJUST
import static com.opengamma.strata.basics.date.HolidayCalendarIds.EUTA
import static com.opengamma.strata.basics.date.HolidayCalendarIds.GBLO
import static com.opengamma.strata.basics.date.HolidayCalendarIds.NO_HOLIDAYS
import static com.opengamma.strata.basics.date.HolidayCalendarIds.SAT_SUN
import static com.opengamma.strata.product.swap.type.FixedIborSwapConventions.EUR_FIXED_1Y_EURIBOR_3M
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.EUR_FIXED_1Y_EONIA_OIS
import static com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions.GBP_FIXED_1Y_SONIA_OIS
import static com.opengamma.strata.product.swaption.CashSwaptionSettlementMethod.PAR_YIELD
import static com.solum.xplain.extensions.utils.StandardIdUtils.curveIdStandardId
import static com.solum.xplain.valuation.calculation.TradeUtils.CLIENT_PV
import static java.time.LocalDate.parse

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.StandardId
import com.opengamma.strata.basics.currency.AdjustablePayment
import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.currency.CurrencyAmount
import com.opengamma.strata.basics.currency.CurrencyPair
import com.opengamma.strata.basics.date.AdjustableDate
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.BusinessDayConventions
import com.opengamma.strata.basics.date.DayCount
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.date.DaysAdjustment
import com.opengamma.strata.basics.date.Tenor
import com.opengamma.strata.basics.index.IborIndex
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.index.PriceIndices
import com.opengamma.strata.basics.schedule.Frequency
import com.opengamma.strata.basics.schedule.PeriodicSchedule
import com.opengamma.strata.basics.schedule.RollConventions
import com.opengamma.strata.basics.schedule.StubConvention
import com.opengamma.strata.basics.value.ValueSchedule
import com.opengamma.strata.product.LegalEntityId
import com.opengamma.strata.product.SecurityId
import com.opengamma.strata.product.TradeInfo
import com.opengamma.strata.product.bond.FixedCouponBond
import com.opengamma.strata.product.bond.FixedCouponBondYieldConvention
import com.opengamma.strata.product.capfloor.IborCapFloor
import com.opengamma.strata.product.capfloor.IborCapFloorLeg
import com.opengamma.strata.product.capfloor.IborCapFloorTrade
import com.opengamma.strata.product.common.BuySell
import com.opengamma.strata.product.common.LongShort
import com.opengamma.strata.product.common.PayReceive
import com.opengamma.strata.product.common.PutCall
import com.opengamma.strata.product.credit.Cds
import com.opengamma.strata.product.credit.CdsIndex
import com.opengamma.strata.product.credit.CdsIndexTrade
import com.opengamma.strata.product.credit.CdsTrade
import com.opengamma.strata.product.credit.type.CdsConventions
import com.opengamma.strata.product.fra.Fra
import com.opengamma.strata.product.fra.FraDiscountingMethod
import com.opengamma.strata.product.fra.FraTrade
import com.opengamma.strata.product.fx.FxSingle
import com.opengamma.strata.product.fx.FxSingleTrade
import com.opengamma.strata.product.fx.FxSwap
import com.opengamma.strata.product.fx.FxSwapTrade
import com.opengamma.strata.product.fxopt.FxCollar
import com.opengamma.strata.product.fxopt.FxCollarTrade
import com.opengamma.strata.product.fxopt.FxVanillaOption
import com.opengamma.strata.product.fxopt.FxVanillaOptionTrade
import com.opengamma.strata.product.swap.CompoundingMethod
import com.opengamma.strata.product.swap.FixedRateCalculation
import com.opengamma.strata.product.swap.IborRateCalculation
import com.opengamma.strata.product.swap.NotionalSchedule
import com.opengamma.strata.product.swap.OvernightAccrualMethod
import com.opengamma.strata.product.swap.OvernightRateCalculation
import com.opengamma.strata.product.swap.PaymentSchedule
import com.opengamma.strata.product.swap.PriceIndexCalculationMethod
import com.opengamma.strata.product.swap.RateCalculation
import com.opengamma.strata.product.swap.RateCalculationSwapLeg
import com.opengamma.strata.product.swap.Swap
import com.opengamma.strata.product.swap.SwapLeg
import com.opengamma.strata.product.swap.SwapTrade
import com.opengamma.strata.product.swap.type.FixedInflationSwapConventions
import com.opengamma.strata.product.swap.type.FixedOvernightSwapConventions
import com.opengamma.strata.product.swap.type.FixedRateSwapLegConvention
import com.opengamma.strata.product.swap.type.IborRateSwapLegConvention
import com.opengamma.strata.product.swap.type.ImmutableFixedIborSwapConvention
import com.opengamma.strata.product.swap.type.ImmutableFixedInflationSwapConvention
import com.opengamma.strata.product.swap.type.ImmutableFixedOvernightSwapConvention
import com.opengamma.strata.product.swap.type.ImmutableIborIborSwapConvention
import com.opengamma.strata.product.swap.type.InflationRateSwapLegConvention
import com.opengamma.strata.product.swap.type.XCcyIborIborSwapConventions
import com.opengamma.strata.product.swaption.CashSwaptionSettlement
import com.opengamma.strata.product.swaption.Swaption
import com.opengamma.strata.product.swaption.SwaptionTrade
import com.solum.xplain.extensions.constants.OvernightIndexConstants
import com.solum.xplain.extensions.enums.CallPutType
import com.solum.xplain.extensions.product.ExtendedFixedOvernightSwapConventions
import com.solum.xplain.extensions.utils.StandardIdUtils
import com.solum.xplain.valuation.calculation.credit.CreditRatesSample
import java.time.LocalDate
import java.time.LocalTime
import java.time.Period
import java.time.ZoneId
import java.time.ZonedDateTime

class TradeSamples {
  final static MODIFIED_FOLLOWING_SAT_SUN = BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SAT_SUN)


  static FraTrade fraTradeSampleAUD6M(LocalDate tradeDate) {
    def fra = FraTrade.of(
      TradeInfo.of(tradeDate),
      Fra.builder()
      .buySell(BuySell.BUY)
      .dayCount(DayCounts.ACT_360)
      .currency(AUD)
      .fixedRate(0.05)
      .businessDayAdjustment(BusinessDayAdjustment.NONE)
      .notional(10000000)
      .startDate(LocalDate.of(2024,01,15))
      .endDate(LocalDate.of(2024,07,15))
      .paymentDate(AdjustableDate.of(LocalDate.of(2024,01,15)))
      .index(IborIndex.of("AUD-BBSW-6M"))
      .discounting(FraDiscountingMethod.AFMA)
      .build()
      )
    return fra
  }
  static SwaptionTrade cashSwaptionTrade(LocalDate tradeDate) {
    def fixedIborSwap = EUR_FIXED_1Y_EURIBOR_3M.createTrade(
      tradeDate,
      Period.ofMonths(6),
      Tenor.of(Period.ofYears(8)),
      BuySell.BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())

    return swaptionTrade(fixedIborSwap, tradeDate)
  }

  static SwaptionTrade cashSwaptionOisTrade(LocalDate tradeDate) {
    def fixedOvernightSwap = EUR_FIXED_1Y_EONIA_OIS.createTrade(
      tradeDate,
      Period.ofMonths(6),
      Tenor.of(Period.ofYears(8)),
      BuySell.BUY, 10_000_000d,
      0.025d,
      ReferenceData.standard())

    return swaptionTrade(fixedOvernightSwap, tradeDate)
  }

  static SwaptionTrade swaptionTrade(SwapTrade underlying, LocalDate tradeDate) {
    def swaption = Swaption.builder()
      .expiryDate(AdjustableDate.of(parse("2018-04-12"), MODIFIED_FOLLOWING_SAT_SUN))
      .expiryTime(LocalTime.NOON)
      .expiryZone(ZoneId.of("Europe/London"))
      .longShort(LongShort.LONG)
      .swaptionSettlement(CashSwaptionSettlement.of(parse("2018-05-23"), PAR_YIELD))
      .underlying(underlying.getProduct())
      .build()

    def premium = AdjustablePayment.of(CurrencyAmount.of(EUR, 10000), AdjustableDate.of(parse("2019-04-12"), MODIFIED_FOLLOWING_SAT_SUN))
    return SwaptionTrade.of(TradeInfo.builder()
      .tradeDate(tradeDate)
      .settlementDate(parse("2018-05-23"))
      .addAttribute(CLIENT_PV, -1550000d)
      .build(),
      swaption,
      premium)
  }

  static fxSingleEurReceive(LocalDate tradeDate) {
    fxSingleEurReceive(tradeDate, 10000)
  }

  static fxSingleEurReceive(LocalDate tradeDate, Double amount) {
    FxSingleTrade.of(TradeInfo.of(tradeDate),
      FxSingle.of(CurrencyAmount.of(EUR, amount),
      CurrencyAmount.of(USD, -amount * 1.1),
      tradeDate.plusYears(1),
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SAT_SUN)))
  }

  static fxSingleEurReceiveOvernightNodeTrade(LocalDate tradeDate, Double amount, int days) {
    FxSingleTrade.of(TradeInfo.of(tradeDate),
      FxSingle.of(CurrencyAmount.of(EUR, amount),
      CurrencyAmount.of(USD, -amount * 1.1),
      tradeDate.plusDays(days),
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SAT_SUN)))
  }

  static fxSingleEurReceivePaymentDate(LocalDate tradeDate) {
    FxSingleTrade.of(TradeInfo.of(tradeDate.minusYears(1))
      .withAttribute(CLIENT_PV, 1300d),
      FxSingle.of(CurrencyAmount.of(EUR, 10000),
      CurrencyAmount.of(USD, -11000),
      tradeDate,
      BusinessDayAdjustment.of(NO_ADJUST, NO_HOLIDAYS)))
  }

  static fxSwapOvernightNodeTrade(LocalDate tradeDate, Double amount, int days) {
    FxSwapTrade.of(TradeInfo.of(tradeDate),
      FxSwap.of(FxSingle.of(CurrencyAmount.of(EUR, amount),
      CurrencyAmount.of(USD, -amount * 1.1),
      tradeDate.plusDays(1),
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SAT_SUN)),
      FxSingle.of(CurrencyAmount.of(EUR, -amount),
      CurrencyAmount.of(USD, amount * 1.1),
      tradeDate.plusDays(days),
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SAT_SUN))))
  }

  static fxSwapEurReceive(LocalDate farDate) {
    FxSwapTrade.of(TradeInfo.of(farDate.minusYears(1))
      .withAttribute(CLIENT_PV, 1300d),
      FxSwap.of(FxSingle.of(CurrencyAmount.of(EUR, 10000), CurrencyAmount.of(USD, -11000), farDate.minusMonths(6), BusinessDayAdjustment.of(NO_ADJUST, NO_HOLIDAYS)),
      FxSingle.of(CurrencyAmount.of(EUR, -10000), CurrencyAmount.of(USD, 11000), farDate, BusinessDayAdjustment.of(NO_ADJUST, NO_HOLIDAYS))
      ))
  }

  static swapTrade(LocalDate tradeDate, Period periodToStart = Period.ofMonths(6)) {
    def trade = EUR_FIXED_1Y_EURIBOR_3M.createTrade(tradeDate, periodToStart, Tenor.of(Period
      .ofYears(8)),
      BuySell.BUY, 10_000_000d, 0.025d, ReferenceData.standard())
    trade.withInfo(trade.getInfo()
      .withAttribute(CLIENT_PV, -1550000d))
  }

  static clpSwapTrade(LocalDate tradeDate) {

    def iborleg = rateCalculationSwapLeg(CLP, PayReceive.PAY, OvernightRateCalculation.builder()
      .dayCount(DayCounts.ACT_360)
      .index(OvernightIndexConstants.CLP_TNA)
      .spread(ValueSchedule.of(0.0))
      .accrualMethod(OvernightAccrualMethod.COMPOUNDED)
      .rateCutOffDays(1)
      .build())
    def fixedLeg = rateCalculationSwapLeg(CLP, PayReceive.RECEIVE, FixedRateCalculation.builder()
      .dayCount(DayCounts.ACT_360)
      .rate(ValueSchedule.of(0.038845))
      .build())

    def swap = Swap.of(iborleg, fixedLeg)
    def trade = SwapTrade.builder()
      .info(TradeInfo.builder()
      .tradeDate(tradeDate)
      .build())
      .product(swap)
      .build()

    return trade
  }

  static cnySwapTrade(LocalDate tradeDate) {

    def iborleg = rateCalculationSwapLeg(
      CNY,
      PayReceive.PAY,
      IborRateCalculation.builder()
      .dayCount(DayCounts.ACT_360)
      .index(IborIndex.of("CNY-REPO-1W"))
      .build(),
      Frequency.ofWeeks(1),
      Frequency.TERM,
      tradeDate,
      tradeDate.plusMonths(2)
      )
    def fixedLeg = rateCalculationSwapLeg(CNY, PayReceive.RECEIVE, FixedRateCalculation.builder()
      .dayCount(DayCounts.ACT_360)
      .rate(ValueSchedule.of(0.038845))
      .build())

    def swap = Swap.of(iborleg, fixedLeg)
    def trade = SwapTrade.builder()
      .info(TradeInfo.builder()
      .tradeDate(tradeDate)
      .build())
      .product(swap)
      .build()

    return trade
  }


  static SwapLeg rateCalculationSwapLeg(
    Currency ccy,
    PayReceive payReceive,
    RateCalculation calculation,
    Frequency accrualFreq = Frequency.TERM,
    Frequency payFreq = Frequency.TERM,
    LocalDate startDate = LocalDate.parse("2022-11-29"),
    LocalDate endDate = LocalDate.parse("2026-11-29")
  ) {
    def swapLeg = RateCalculationSwapLeg.builder()
      .payReceive(payReceive)
      .accrualSchedule(
      PeriodicSchedule.builder()
      .startDate(startDate)
      .endDate(endDate)
      .frequency(accrualFreq)
      .businessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .rollConvention(RollConventions.NONE)
      .stubConvention(StubConvention.NONE)
      .build())
      .accrualScheduleOffset(DaysAdjustment.NONE)
      .paymentSchedule(
      PaymentSchedule.builder()
      .paymentFrequency(payFreq)
      .businessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .paymentDateOffset(DaysAdjustment.ofCalendarDays(3))
      .compoundingMethod(CompoundingMethod.NONE)
      .build())
      .notionalSchedule(
      NotionalSchedule.builder()
      .currency(ccy)
      .amount(ValueSchedule.of(10000000.0))
      .initialExchange(true)
      .finalExchange(true)
      .build())
      .calculation(calculation)
      .build()

    return swapLeg
  }

  static swapTradeUsd(LocalDate tradeDate) {
    def trade = EUR_FIXED_1Y_EURIBOR_3M.createTrade(tradeDate, Period.ofMonths(6), Tenor.of(Period
      .ofYears(8)),
      BuySell.BUY, 10_000_000d, 0.025d, ReferenceData.standard())
    trade.withInfo(trade.getInfo()
      .withAttribute(CLIENT_PV, -1550000d))
  }

  static swapTradeGbp(LocalDate tradeDate) {
    def trade = GBP_FIXED_1Y_SONIA_OIS.createTrade(tradeDate, Period.ZERO, Tenor.TENOR_1Y,
      BuySell.BUY, 10_000_000d, 0.000693d, ReferenceData.standard())
    trade.withInfo(trade.getInfo()
      .withAttribute(CLIENT_PV, -1550000d))
  }

  static swapTradeBrl(LocalDate tradeDate, double fixedLegNotional, double floatLegNotional) {
    def trade = ExtendedFixedOvernightSwapConventions.BRL_FIXED_TERM_CDI_OIS.createTrade(tradeDate, Period.ZERO, Tenor.TENOR_1Y,
      BuySell.BUY, 10_000_000d, 0.000693d, ReferenceData.standard())
    trade.withInfo(trade.getInfo()
      .withAttribute(CLIENT_PV, -1550000d))

    def fixedLegConvention = ExtendedFixedOvernightSwapConventions.BRL_FIXED_TERM_CDI_OIS.getFixedLeg()
    def floatLegConvention = ExtendedFixedOvernightSwapConventions.BRL_FIXED_TERM_CDI_OIS.getFloatingLeg()

    SwapLeg leg1 = fixedLegConvention.toLeg(tradeDate, tradeDate.plusYears(1.00 as long), PayReceive.ofPay(true), fixedLegNotional, 0.000693d)
    SwapLeg leg2 = floatLegConvention.toLeg(tradeDate, tradeDate.plusYears(1.00 as long), PayReceive.ofPay(false), floatLegNotional)
    return SwapTrade.builder()
      .info(trade.getInfo())
      .product(Swap.of(leg1, leg2))
      .build()
  }

  static swapTradeCross(LocalDate tradeDate) {
    XCcyIborIborSwapConventions.EUR_EURIBOR_3M_USD_LIBOR_3M.createTrade(tradeDate,
      Tenor.of(Period.ofYears(8)),
      BuySell.BUY, 10_000_000d, 11_000_000d, 0.025d, ReferenceData.standard())
  }

  static swapTradeInflation(LocalDate tradeDate) {
    FixedInflationSwapConventions.EUR_FIXED_ZC_EU_AI_CPI.createTrade(tradeDate, Tenor.of(Period
      .ofYears(8)), BuySell.BUY, 10_000_000d, 0.025d, ReferenceData.standard())
  }

  static swapTradeInflationUsd(LocalDate tradeDate) {
    FixedInflationSwapConventions.USD_FIXED_ZC_US_CPI.createTrade(tradeDate, Tenor.of(Period
      .ofYears(2)), BuySell.BUY, 10_000_000d, 0.025d, ReferenceData.standard())
  }

  static swapTradeUsdSofr(LocalDate startDate, LocalDate endDate) {
    def baseConvention = FixedOvernightSwapConventions.USD_FIXED_TERM_SOFR_OIS
    def averagedConvention = ImmutableFixedOvernightSwapConvention
      .of("USD-FIXED-TERM-SOFR-OIS-AVERAGED",
      baseConvention.fixedLeg,
      baseConvention.floatingLeg.toBuilder()
      .accrualMethod(OvernightAccrualMethod.AVERAGED)
      .build(),
      baseConvention.spotDateOffset
      )

    return averagedConvention.toTrade(startDate, startDate, endDate, BuySell.BUY, 10_000_000d, 0.025d)
  }

  static swapTradeInflationActDayCount(LocalDate tradeDate, Frequency payFreq = Frequency.TERM, Period periodToStart = Period.ofMonths(3), CompoundingMethod compoundingMethod = CompoundingMethod.STRAIGHT, Frequency accrualFreq = Frequency.P1M) {
    def fixedLegConvention = FixedRateSwapLegConvention.builder()
      .paymentFrequency(payFreq)
      .accrualFrequency(accrualFreq)
      .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .startDateBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .endDateBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .compoundingMethod(compoundingMethod)
      .dayCount(DayCounts.ACT_365F)
      .currency(EUR)
      .build()

    def convention = ImmutableFixedInflationSwapConvention.of("EUR-FIXED-ZC-EU-AI-CPI",
      fixedLegConvention,
      InflationRateSwapLegConvention.of(PriceIndices.EU_AI_CPI,
      periodToStart,
      PriceIndexCalculationMethod.MONTHLY,
      BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA)),
      DaysAdjustment.ofBusinessDays(2, EUTA))

    convention.createTrade(tradeDate,
      Tenor.of(Period.ofYears(8)),
      BuySell.BUY,
      10_000_000d,
      0.025d,
      ReferenceData.standard())
  }

  static swapFloatFloatTrade(LocalDate tradeDate) {
    ImmutableIborIborSwapConvention.of("EUR-EURIBOR-3M-EURIBOR-3M",
      IborRateSwapLegConvention.builder()
      .index(IborIndices.EUR_EURIBOR_3M)
      .paymentFrequency(Frequency.P6M)
      .compoundingMethod(CompoundingMethod.FLAT)
      .stubConvention(StubConvention.SHORT_INITIAL)
      .build(),
      IborRateSwapLegConvention.of(IborIndices.EUR_EURIBOR_3M))
      .createTrade(tradeDate, Period.ofMonths(6), Tenor.of(Period.ofYears(8)),
      BuySell.BUY, 10_000_000d, 0.025d, ReferenceData.standard())
  }

  static swapFixedFloatCompoundedTrade(LocalDate tradeDate, BuySell buySell, double fixedRate, Frequency accrualFreq, CompoundingMethod compoundingMethod = CompoundingMethod.FLAT) {
    def fixedLeg = FixedRateSwapLegConvention.builder()
      .paymentFrequency(Frequency.TERM)
      .accrualFrequency(accrualFreq)
      .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .startDateBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .endDateBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .compoundingMethod(compoundingMethod)
      .dayCount(DayCounts.ACT_365F)
      .currency(EUR)
      .build()

    ImmutableFixedIborSwapConvention.of("EUR-EURIBOR-3M-EURIBOR-3M",
      fixedLeg,
      IborRateSwapLegConvention.builder()
      .index(IborIndices.EUR_EURIBOR_3M)
      .paymentFrequency(Frequency.P6M)
      .compoundingMethod(CompoundingMethod.FLAT)
      .stubConvention(StubConvention.SHORT_INITIAL)
      .build())
      .createTrade(tradeDate, Period.ofMonths(6), Tenor.of(Period.ofYears(8)),
      buySell, 10_000_000d, fixedRate, ReferenceData.standard())
  }

  static capFloorTrade(LocalDate tradeDate) {
    capFloorTrade(tradeDate, PayReceive.RECEIVE)
  }

  static capFloorTrade(LocalDate tradeDate, PayReceive payReceive) {
    def builder = IborCapFloorLeg
      .builder()
      .payReceive(payReceive)
      .currency(EUR)
      .calculation(IborRateCalculation.builder().index(IborIndices.EUR_EURIBOR_3M)
      .spread(ValueSchedule.of(30))
      .build())
      .paymentSchedule(PeriodicSchedule.of(tradeDate,
      tradeDate.plusYears(1),
      Frequency.P3M,
      BusinessDayAdjustment.NONE,
      StubConvention.NONE,
      false))
      .capSchedule(ValueSchedule.ALWAYS_1)
      .paymentDateOffset(DaysAdjustment.NONE)
      .notional(ValueSchedule.of(20))

    IborCapFloorTrade.builder()
      .product(IborCapFloor.of(builder.build()))
      .premium(AdjustablePayment.of(EUR, 10, tradeDate.plusYears(1)))
      .info(TradeInfo.of(tradeDate)
      .withAttribute(CLIENT_PV, 1d))
      .build()
  }

  static cdsTrade() {
    cdsTrade(parse("2016-01-01"))
  }

  static cdsTrade(LocalDate tradeDate) {
    def cds = Cds.of(BuySell.BUY,
      CreditRatesSample.CDS_STANDARD_ID,
      EUR,
      10000,
      tradeDate,
      tradeDate.plusYears(1),
      Frequency.P3M,
      CdsConventions.EUR_GB_STANDARD.getSettlementDateOffset().getCalendar(),
      2)

    CdsTrade.builder()
      .product(cds)
      .upfrontFee(AdjustablePayment.of(EUR, 10, tradeDate.plusDays(3)))
      .info(TradeInfo.of(tradeDate)
      .withAttribute(CLIENT_PV, 1300d))
      .build()
  }

  static singlePeriodCdsTrade(LocalDate startDate, LocalDate endDate, BuySell buySell = BuySell.BUY) {
    def cds = Cds.of(buySell,
      CreditRatesSample.CDS_STANDARD_ID,
      EUR,
      10000,
      startDate,
      endDate,
      Frequency.P3M,
      CdsConventions.EUR_GB_STANDARD.getSettlementDateOffset().getCalendar(),
      0.01)

    CdsTrade.builder()
      .product(cds)
      .info(TradeInfo.of(startDate)
      .withAttribute(CLIENT_PV, 1300d))
      .build()
  }

  static creditIndexTrade(LocalDate tradeDate) {
    var calendar = CdsConventions.EUR_GB_STANDARD.getSettlementDateOffset().getCalendar()
    def cds = CdsIndex.of(BuySell.BUY,
      CreditRatesSample.CREDIT_INDEX_STANDARD_ID,
      [CreditRatesSample.CREDIT_INDEX_STANDARD_ID],
      EUR,
      10000,
      tradeDate,
      tradeDate.plusYears(1),
      Frequency.P3M,
      calendar,
      2)
    def upfront =
      AdjustablePayment.of(EUR, 10,
      AdjustableDate.of(tradeDate.plusDays(3),
      BusinessDayAdjustment.of(BusinessDayConventions.FOLLOWING, calendar)))
    CdsIndexTrade.builder()
      .product(cds)
      .upfrontFee(upfront)
      .info(TradeInfo.of(tradeDate)
      .withAttribute(CLIENT_PV, 1300d))
      .build()
  }

  static singlePeriodCreditIndexTrade(LocalDate startDate, LocalDate endDate, BuySell buySell = BuySell.BUY) {
    var calendar = CdsConventions.EUR_GB_STANDARD.getSettlementDateOffset().getCalendar()
    def cds = CdsIndex.of(buySell,
      CreditRatesSample.CREDIT_INDEX_STANDARD_ID,
      [CreditRatesSample.CREDIT_INDEX_STANDARD_ID],
      EUR,
      10000,
      startDate,
      endDate,
      Frequency.P3M,
      calendar,
      0.01)
    CdsIndexTrade.builder()
      .product(cds)
      .info(TradeInfo.of(startDate)
      .withAttribute(CLIENT_PV, 1300d))
      .build()
  }

  static fxOptTrade(LocalDate tradeDate, LocalDate expiryDate = parse("2018-08-12")) {
    fxOptTrade(tradeDate, expiryDate, fxSingleEurReceive(tradeDate).product)
  }

  static fxOptTrade(LocalDate tradeDate, LocalDate expiryDate, FxSingle fxSingle) {
    def fxOpt = FxVanillaOption.builder()
      .expiryDate(expiryDate)
      .expiryTime(LocalTime.NOON)
      .expiryZone(ZoneId.of("Europe/London"))
      .longShort(LongShort.LONG)
      .underlying(fxSingle)
      .build()

    FxVanillaOptionTrade.builder()
      .product(fxOpt)
      .info(TradeInfo.of(tradeDate).withId(StandardId.of("XPL", "TRADEID")))
      .premium(AdjustablePayment.of(EUR, 10, tradeDate.plusYears(1)))
      .build()
  }

  static fxCollarTrade(LocalDate tradeDate, LocalDate expiryDate = parse("2018-08-12")) {
    fxCollarTrade(tradeDate, expiryDate, fxSingleEurReceive(tradeDate).product)
  }

  static fxCollarTrade(LocalDate tradeDate, LocalDate expiryDate, FxSingle fxSingle) {

    def fxCollar = FxCollar.of(
      FxVanillaOption.of(
      LongShort.LONG,
      ZonedDateTime.of(expiryDate, LocalTime.of(2,2,2), ZoneId.of("Europe/London")),
      CurrencyPair.of(EUR, USD),
      PutCall.of(CallPutType.CALL.toString()),
      0.8,
      100,
      parse("2028-08-12")
      ),
      FxVanillaOption.of(
      LongShort.SHORT,
      ZonedDateTime.of(expiryDate, LocalTime.of(2,2,2), ZoneId.of("Europe/London")),
      CurrencyPair.of(EUR, USD),
      PutCall.of(CallPutType.PUT.toString()),
      0.9,
      100,
      parse("2028-08-12")
      )
      )

    FxCollarTrade.of((TradeInfo.of(tradeDate).withId(StandardId.of("XPL", "TRADEID"))),
      (fxCollar),
      (AdjustablePayment.of(EUR, 10, tradeDate.plusYears(1))))
  }

  static swapTradeUnpaidCoupon(LocalDate tradeDate, int fixedPayDateOffset, int floatPayDateOffset, int tenorYears = 1) {
    def fixedLegConvention = FixedRateSwapLegConvention.builder()
      .paymentFrequency(Frequency.P12M)
      .accrualFrequency(Frequency.P12M)
      .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .startDateBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .endDateBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .compoundingMethod(CompoundingMethod.STRAIGHT)
      .dayCount(DayCounts.ACT_365F)
      .currency(EUR)
      .paymentDateOffset(DaysAdjustment.ofBusinessDays(fixedPayDateOffset, EUTA))
      .build()
    def floatLegConvention = InflationRateSwapLegConvention.builder()
      .index(PriceIndices.EU_AI_CPI)
      .lag(Period.ofMonths(3))
      .indexCalculationMethod(PriceIndexCalculationMethod.MONTHLY)
      .notionalExchange(false)
      .paymentDateOffset(DaysAdjustment.ofBusinessDays(floatPayDateOffset, EUTA))
      .accrualBusinessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, EUTA))
      .build()

    def convention = ImmutableFixedInflationSwapConvention.of("EUR-FIXED-ZC-EU-AI-CPI",
      fixedLegConvention,
      floatLegConvention,
      DaysAdjustment.NONE)

    convention.createTrade(tradeDate,
      Tenor.of(Period.ofYears(tenorYears)),
      BuySell.BUY,
      10_000_000d,
      0.0001d,
      ReferenceData.standard())
  }

  static FixedCouponBond fixedCouponBond(
    LocalDate tradeDate,
    FixedCouponBondYieldConvention yieldConvention = FixedCouponBondYieldConvention.GB_BUMP_DMO,
    DayCount dayCount = DayCounts.ACT_ACT_ICMA) {

    var accrualSchedule = PeriodicSchedule.builder()
      .startDate(tradeDate)
      .endDate(tradeDate.plusYears(20))
      .frequency(Frequency.P6M)
      .businessDayAdjustment(BusinessDayAdjustment.of(MODIFIED_FOLLOWING, GBLO))
      .rollConvention(RollConventions.NONE)
      .stubConvention(StubConvention.SMART_INITIAL)
      .build()

    return FixedCouponBond.builder()
      .securityId(SecurityId.of(StandardIdUtils.tradeStandardId("EXT")))
      .currency(USD)
      .notional(1000000)
      .accrualSchedule(accrualSchedule)
      .fixedRate(0.0274d)
      .dayCount(dayCount)
      .yieldConvention(yieldConvention)
      .legalEntityId(LegalEntityId.of(curveIdStandardId("UKGT")))
      .settlementDateOffset(DaysAdjustment.NONE)
      .exCouponPeriod(DaysAdjustment.NONE)
      .build()
  }
}
