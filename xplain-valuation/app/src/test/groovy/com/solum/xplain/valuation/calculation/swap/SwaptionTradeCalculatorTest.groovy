package com.solum.xplain.valuation.calculation.swap


import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static spock.util.matcher.HamcrestMatchers.closeTo
import static spock.util.matcher.HamcrestSupport.that

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.pricer.rate.RatesProvider
import com.opengamma.strata.pricer.swaption.VolatilitySwaptionProductPricer
import com.opengamma.strata.pricer.swaption.VolatilitySwaptionTradePricer
import com.solum.xplain.valuation.calculation.CalibrationRatesSample
import com.solum.xplain.valuation.calculation.MarketDataSample
import com.solum.xplain.valuation.calculation.TradeSamples
import com.solum.xplain.valuation.calculation.ValuationOptions
import com.solum.xplain.valuation.calibration.vols.SabrVolatilitiesProvider
import com.solum.xplain.valuation.calibration.vols.VolatilitiesProvider
import java.time.LocalDate
import spock.lang.Specification

class SwaptionTradeCalculatorTest extends Specification {

  def "should calculate swaption metrics with SKEW vols"() {
    setup:
    def calculator = new SwaptionTradeCalculator(
    TradeSamples.cashSwaptionTrade(MarketDataSample.VAL_DT),
    ValuationOptions.newOf(EUR, USD, true, "EUR-EURIBOR-3M"),
    () -> CalibrationRatesSample.calibrationCurves(),
    CalibrationRatesSample.valuationRates(),
    skewVolatilitiesProvider()
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics.localCcy == EUR.getCode()
    that metrics.presentValue, closeTo(12351.13, 0.01)
    that metrics.presentValuePayLegCurrency, closeTo(9969.84, 0.01)
    that metrics.receiveLegPV, closeTo(9969.84, 0.01)
    metrics.payLegPV == null
    that metrics.differenceMetrics.absoluteDifference, closeTo(-1562351.13, 0.01)
    that metrics.dv01, closeTo(-1.76, 0.01)
    that metrics.dv01LocalCcy, closeTo(-1.42, 0.01)
    that metrics.br01, closeTo(3.47, 0.01)
    that metrics.br01LocalCcy, closeTo(2.80, 0.01)
    that metrics.infcsbr01, closeTo(3.47, 0.01)
    that metrics.infcsbr01LocalCcy, closeTo(2.80, 0.01)
    that metrics.differenceMetrics.clientPvPvDv01, closeTo(-884137.40, 0.01)
    that metrics.optionMetrics.pvDeltaLocalCcy, closeTo(2.82, 0.01)
    that metrics.optionMetrics.pvGammaLocalCcy, closeTo(0.17, 0.01)
    that metrics.optionMetrics.pvThetaLocalCcy, closeTo(-1.94, 0.01)
    that metrics.optionMetrics.pvVegaLocalCcy, closeTo(6.71, 0.01)
    that metrics.optionMetrics.pvDelta, closeTo(3.49, 0.01)
    that metrics.optionMetrics.pvGamma, closeTo(0.21, 0.01)
    that metrics.optionMetrics.pvTheta, closeTo(-2.40, 0.01)
    that metrics.optionMetrics.pvVega, closeTo(8.31, 0.01)
    metrics.dv01TradeValues.size() == 1
    metrics.br01TradeValues.size() == 1
    def tradeValue = metrics.dv01TradeValues[0]
    tradeValue.sensitivities.size() == 1
    tradeValue.sensitivities["EUR"].size() == 2
  }

  def "should calculate swaption metrics with SKEW vols and shifted rates"() {
    setup:
    def calculator = new SwaptionTradeCalculator(
    TradeSamples.cashSwaptionTrade(MarketDataSample.VAL_DT),
    ValuationOptions.newOf(EUR, USD, true, "EUR-EURIBOR-3M"),
    () -> CalibrationRatesSample.calibrationCurves(),
    CalibrationRatesSample.shiftedValuationRates(),
    skewVolatilitiesProvider()
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics.localCcy == EUR.getCode()
    that metrics.presentValue, closeTo(12351.13, 0.01)
    with(metrics.spot01TradeValues.get(0)) {
      currencyPair() == "EUR/USD"
      that spot01(), closeTo(0, 0.0001)
      that spot01TradeCcy(), closeTo(0, 0.0001)
      fxSpot() == 1.23885
    }
  }

  def "should calculate swaption metrics with SABR vols"() {
    setup:
    def calculator = new SwaptionTradeCalculator(
    TradeSamples.cashSwaptionTrade(MarketDataSample.VAL_DT),
    ValuationOptions.newOf(EUR, USD, true, "EUR-EURIBOR-3M"),
    () -> CalibrationRatesSample.calibrationCurves(),
    CalibrationRatesSample.valuationRates(),
    sabrVolatilitiesProvider()
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics.localCcy == EUR.getCode()
    that metrics.presentValue, closeTo(410891.67, 0.01)
    that metrics.presentValuePayLegCurrency, closeTo(331671.85, 0.01)
    that metrics.receiveLegPV, closeTo(331671.85, 0.01)
    metrics.payLegPV == null
    that metrics.differenceMetrics.absoluteDifference, closeTo(-1960891.67, 0.01)
    that metrics.dv01, closeTo(-23.47, 0.01)
    that metrics.dv01LocalCcy, closeTo(-18.94, 0.01)
    that metrics.br01, closeTo(2175.80, 0.01)
    that metrics.br01LocalCcy, closeTo(1756.30, 0.01)
    that metrics.infcsbr01, closeTo(2175.80, 0.01)
    that metrics.infcsbr01LocalCcy, closeTo(1756.30, 0.01)
    that metrics.differenceMetrics.clientPvPvDv01, closeTo(-83534.98, 0.01)
    that metrics.optionMetrics.pvDeltaLocalCcy, closeTo(1899.09, 0.01)
    that metrics.optionMetrics.pvGammaLocalCcy, closeTo(8.53, 0.01)
    that metrics.optionMetrics.pvThetaLocalCcy, closeTo(-2271.56, 0.01)
    that metrics.optionMetrics.pvVegaLocalCcy, closeTo(1587.17, 0.01)
    that metrics.optionMetrics.pvDelta, closeTo(2352.68, 0.01)
    that metrics.optionMetrics.pvGamma, closeTo(10.57, 0.01)
    that metrics.optionMetrics.pvTheta, closeTo(-2814.13, 0.01)
    that metrics.optionMetrics.pvVega, closeTo(1966.27, 0.01)
    metrics.dv01TradeValues.size() == 1
    metrics.br01TradeValues.size() == 1
    def tradeValue = metrics.dv01TradeValues[0]
    tradeValue.sensitivities.size() == 1
    tradeValue.sensitivities["EUR"].size() == 2
  }

  def "should calculate OIS swaption metrics with SKEW vols"() {
    setup:
    def calculator = new SwaptionTradeCalculator(
    TradeSamples.cashSwaptionOisTrade(MarketDataSample.VAL_DT),
    ValuationOptions.newOf(EUR, USD, true, "EUR-EONIA-OIS"),
    () -> CalibrationRatesSample.calibrationCurves(),
    CalibrationRatesSample.valuationRates(),
    skewOisVolatilitesProvider()
    )

    when:
    def metrics = calculator.calculate(ReferenceData.standard())

    then:
    metrics != null
    that metrics.presentValue, closeTo(12351.08, 0.01)
    that metrics.presentValuePayLegCurrency, closeTo(9969.80, 0.01)
  }


  VolatilitiesProvider skewVolatilitiesProvider() {
    def provider = Mock(VolatilitiesProvider)
    provider.productPricer() >> VolatilitySwaptionProductPricer.DEFAULT
    provider.tradePricer() >> VolatilitySwaptionTradePricer.DEFAULT
    provider.swaptionVolatilities(_ as LocalDate, _ as LocalDate, _ as RatesProvider) >> VolatilitiesSample.skewVols()
    provider
  }


  VolatilitiesProvider sabrVolatilitiesProvider() {
    def provider = Mock(VolatilitiesProvider)
    provider.productPricer() >> SabrVolatilitiesProvider.SABR_PRODUCT_PRICER
    provider.tradePricer() >> SabrVolatilitiesProvider.SABR_TRADE_PRICER
    provider.swaptionVolatilities(_ as LocalDate, _ as LocalDate, _ as RatesProvider) >> VolatilitiesSample.sabrVols()
    provider
  }

  VolatilitiesProvider skewOisVolatilitesProvider() {
    def provider = Mock(VolatilitiesProvider)
    provider.productPricer() >> VolatilitySwaptionProductPricer.DEFAULT
    provider.tradePricer() >> VolatilitySwaptionTradePricer.DEFAULT
    provider.swaptionVolatilities(_ as LocalDate, _ as LocalDate, _ as RatesProvider) >> VolatilitiesSample.skewOisVols()
    provider
  }
}
