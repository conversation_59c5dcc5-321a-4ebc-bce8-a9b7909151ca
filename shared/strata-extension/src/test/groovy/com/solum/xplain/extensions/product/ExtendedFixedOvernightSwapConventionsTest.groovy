package com.solum.xplain.extensions.product

import static com.opengamma.strata.basics.date.BusinessDayConventions.FOLLOWING
import static com.opengamma.strata.basics.date.BusinessDayConventions.MODIFIED_FOLLOWING
import static com.opengamma.strata.basics.date.HolidayCalendarIds.AUSY
import static com.opengamma.strata.basics.date.HolidayCalendarIds.BRBD
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.INMU_USNY
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.SGSI
import static com.solum.xplain.extensions.calendar.ExtendedHolidayCalendarIds.THBA_USNY

import com.opengamma.strata.basics.currency.Currency
import com.opengamma.strata.basics.date.BusinessDayAdjustment
import com.opengamma.strata.basics.date.DayCount
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.date.HolidayCalendarId
import com.opengamma.strata.basics.index.OvernightIndex
import com.opengamma.strata.basics.index.OvernightIndices
import com.opengamma.strata.basics.schedule.Frequency
import com.opengamma.strata.basics.schedule.StubConvention
import com.opengamma.strata.product.swap.FixedAccrualMethod
import com.opengamma.strata.product.swap.OvernightAccrualMethod
import com.solum.xplain.extensions.index.OffshoreIndices
import spock.lang.Specification

class ExtendedFixedOvernightSwapConventionsTest extends Specification {

  def "should return correctly constructed AUD_FIXED_TERM_AONIA_OIS convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.AUD_FIXED_TERM_AONIA_OIS

    then:
    convention.getName() == "AUD-FIXED-TERM-AONIA-OIS"
    convention.getSpotDateOffset().getDays() == 1
    convention.getFixedLeg().getCurrency() == Currency.AUD
    convention.getFloatingLeg().getPaymentDateOffset().getDays() == 2
    convention.getFixedLeg().getDayCount() == DayCounts.ACT_365F
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.DEFAULT
    convention.getFixedLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, AUSY)
    convention.getFloatingLeg().getPaymentDateOffset().getDays() == 2
    convention.getFloatingLeg().getIndex() == OvernightIndices.AUD_AONIA
    convention.getFloatingLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.COMPOUNDED
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFloatingLeg().getPaymentFrequency() == Frequency.TERM
  }

  def "should return correctly constructed AUD_FIXED_SHORTTERM_AONIA_OIS convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.AUD_FIXED_SHORTTERM_AONIA_OIS

    then:
    convention.getName() == "AUD-FIXED-SHORTTERM-AONIA-OIS"
    convention.getSpotDateOffset().getDays() == 1
    convention.getFixedLeg().getCurrency() == Currency.AUD
    convention.getFloatingLeg().getPaymentDateOffset().getDays() == 2
    convention.getFixedLeg().getDayCount() == DayCounts.ACT_365F
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.DEFAULT
    convention.getFixedLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(FOLLOWING, AUSY)
    convention.getFloatingLeg().getPaymentDateOffset().getDays() == 2
    convention.getFloatingLeg().getIndex() == OvernightIndices.AUD_AONIA
    convention.getFloatingLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.COMPOUNDED
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFloatingLeg().getPaymentFrequency() == Frequency.TERM
  }

  def "should return correctly constructed SGD_FIXED_TERM_SORA_OIS convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.SGD_FIXED_TERM_SORA_OIS

    then:
    convention.getName() == "SGD-FIXED-TERM-SORA-OIS"
    convention.getSpotDateOffset().getDays() == 2
    convention.getFixedLeg().getCurrency() == Currency.SGD
    convention.getFloatingLeg().getPaymentDateOffset().getDays() == 2
    convention.getFixedLeg().getDayCount() == DayCounts.ACT_365F
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.DEFAULT
    convention.getFixedLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SGSI)
    convention.getFloatingLeg().getPaymentDateOffset().getDays() == 2
    convention.getFloatingLeg().getIndex() == OvernightIndex.of("SGD-SORA")
    convention.getFloatingLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.COMPOUNDED
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFloatingLeg().getPaymentFrequency() == Frequency.TERM
  }

  def "should return correctly constructed SGD_FIXED_6M_SORA_OIS convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.SGD_FIXED_6M_SORA_OIS

    then:
    convention.getName() == "SGD-FIXED-6M-SORA-OIS"
    convention.getSpotDateOffset().getDays() == 2
    convention.getFixedLeg().getCurrency() == Currency.SGD
    convention.getFloatingLeg().getPaymentDateOffset().getDays() == 2
    convention.getFixedLeg().getDayCount() == DayCounts.ACT_365F
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.DEFAULT
    convention.getFixedLeg().getAccrualFrequency() == Frequency.P6M
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, SGSI)
    convention.getFloatingLeg().getPaymentDateOffset().getDays() == 2
    convention.getFloatingLeg().getIndex() == OvernightIndex.of("SGD-SORA")
    convention.getFloatingLeg().getStubConvention() == StubConvention.SHORT_INITIAL
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.COMPOUNDED
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.P6M
    convention.getFloatingLeg().getPaymentFrequency() == Frequency.P6M
  }

  def "should return correctly constructed BRL-FIXED-TERM-CDI-OIS convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.BRL_FIXED_TERM_CDI_OIS

    then:
    convention.getName() == "BRL-FIXED-TERM-CDI-OIS"
    convention.getFixedLeg().getDayCount() == DayCount.ofBus252(BRBD)
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE
    convention.getFixedLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(FOLLOWING, BRBD)
    convention.getFloatingLeg().getIndex() == OvernightIndices.BRL_CDI
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFloatingLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(FOLLOWING, BRBD)
  }

  def "should return correctly constructed COP-FIXED-TERM-OIBR-OIS convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.COP_FIXED_TERM_OIBR_OIS

    then:
    convention.getName() == "COP-FIXED-TERM-OIBR-OIS"
    convention.getFixedLeg().getDayCount() == DayCounts.ACT_360
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.DEFAULT
    convention.getFixedLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarId.of("COBO"))
    convention.getFloatingLeg().getIndex() == OvernightIndex.of("COP-OIBR")
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.COMPOUNDED
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFloatingLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarId.of("COBO"))
  }

  def "should return correctly constructed COP-FIXED-TERM-3M-OIS convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.COP_FIXED_3M_OIBR_OIS

    then:
    convention.getName() == "COP-FIXED-3M-OIBR-OIS"
    convention.getFixedLeg().getDayCount() == DayCounts.ACT_360
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.DEFAULT
    convention.getFixedLeg().getAccrualFrequency() == Frequency.P3M
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarId.of("COBO"))
    convention.getFloatingLeg().getIndex() == OvernightIndex.of("COP-OIBR")
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.COMPOUNDED
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.P3M
    convention.getFloatingLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarId.of("COBO"))
  }

  def "should return correctly constructed CLP-FIXED-TERM-TNA-OIS convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.CLP_FIXED_TERM_TNA_OIS

    then:
    convention.getName() == "CLP-FIXED-TERM-TNA-OIS"
    convention.getFixedLeg().getDayCount() == DayCounts.ACT_360
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.DEFAULT
    convention.getFixedLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarId.of("CLSA"))
    convention.getFloatingLeg().getIndex() == OvernightIndex.of("CLP-TNA")
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.COMPOUNDED
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.TERM
    convention.getFloatingLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarId.of("CLSA"))
  }

  def "should return correctly constructed CLP-FIXED-6M-TNA-OIS convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.CLP_FIXED_6M_TNA_OIS

    then:
    convention.getName() == "CLP-FIXED-6M-TNA-OIS"
    convention.getFixedLeg().getDayCount() == DayCounts.ACT_360
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.DEFAULT
    convention.getFixedLeg().getAccrualFrequency() == Frequency.P6M
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarId.of("CLSA"))
    convention.getFloatingLeg().getIndex() == OvernightIndex.of("CLP-TNA")
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.COMPOUNDED
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.P6M
    convention.getFloatingLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, HolidayCalendarId.of("CLSA"))
  }

  def "should return correctly constructed INR_FIXED_6M_OMIBOR_OIS_OFFSHORE convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.INR_FIXED_6M_OMIBOR_OIS_OFFSHORE

    then:
    convention.getName() == "INR-FIXED-6M-OMIBOR-OIS-OFFSHORE"
    convention.getFixedLeg().getDayCount() == DayCounts.ACT_365F
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.DEFAULT
    convention.getFixedLeg().getAccrualFrequency() == Frequency.P6M
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, INMU_USNY)
    convention.getFloatingLeg().getIndex() == OffshoreIndices.INR_OMIBOR_OFFSHORE
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.COMPOUNDED
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.P6M
    convention.getFloatingLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, INMU_USNY)
  }

  def "should return correctly constructed THB_FIXED_3M_THOR_OIS_OFFSHORE convention"() {
    when:
    def convention = ExtendedFixedOvernightSwapConventions.THB_FIXED_3M_THOR_OIS_OFFSHORE

    then:
    convention.getName() == "THB-FIXED-3M-THOR-OIS-OFFSHORE"
    convention.getFixedLeg().getDayCount() == DayCounts.ACT_365F
    convention.getFixedLeg().getAccrualMethod() == FixedAccrualMethod.DEFAULT
    convention.getFixedLeg().getAccrualFrequency() == Frequency.P3M
    convention.getFixedLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, THBA_USNY)
    convention.getFloatingLeg().getIndex() == OffshoreIndices.THB_THOR_OFFSHORE
    convention.getFloatingLeg().getAccrualMethod() == OvernightAccrualMethod.COMPOUNDED
    convention.getFloatingLeg().getAccrualFrequency() == Frequency.P3M
    convention.getFloatingLeg().getAccrualBusinessDayAdjustment() == BusinessDayAdjustment.of(MODIFIED_FOLLOWING, THBA_USNY)
  }
}
