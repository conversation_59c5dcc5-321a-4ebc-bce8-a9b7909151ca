# Example configuration for Redis Lock Manager
app:
  data-grid:
    redis:
      enabled: true
      lock-manager:
        # Number of threads for heartbeat management (default: 2)
        heartbeat-thread-pool-size: 4
        
        # Interval between heartbeat renewals (default: 7s)
        # Should be 60-70% of lock TTL for safety
        heartbeat-interval: PT6S
        
        # Timeout for graceful shutdown (default: 2s)
        shutdown-timeout: PT3S
        
        # Whether to log successful renewals (default: true)
        # Set to false in production to reduce log noise
        log-successful-renewals: false

spring:
  data:
    redis:
      client-name: my-app-instance
      # Multiple Redis instances for distributed locking
      cluster:
        nodes:
          - redis1:6379
          - redis2:6379
          - redis3:6379
