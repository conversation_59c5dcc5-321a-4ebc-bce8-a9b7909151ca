package com.solum.xplain.shared.datagrid.impl.redis;

import jakarta.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Manages Redis distributed locks with automatic heartbeat functionality for lock renewal.
 *
 * <p>The {@code RedisLockManager} provides a high-level interface for managing distributed locks
 * backed by Redis clusters. It automatically handles lock lifecycle management including:
 *
 * <ul>
 *   <li>Acquiring locks with automatic heartbeat initiation
 *   <li>Periodic lock renewal via scheduled heartbeat tasks
 *   <li>Manual lock release with heartbeat cleanup
 *   <li>Automatic cleanup during application shutdown
 *   <li>Thread-safe management of multiple concurrent locks
 * </ul>
 *
 * <p>This manager uses a scheduled thread pool to maintain heartbeats for active locks,
 * ensuring they remain valid for the duration of their use. Each lock is associated with
 * a {@link HeartbeatTask} that periodically calls {@link RedisClusterLock#renewLock()}
 * to extend the lock's TTL.
 *
 * <h3>Thread Safety</h3>
 * This class is thread-safe and can be used concurrently from multiple threads.
 * It uses {@link ConcurrentHashMap} to track active locks and their heartbeat tasks.
 *
 * <h3>Heartbeat Configuration</h3>
 * The heartbeat interval is set to 7 seconds (70% of the default 10-second TTL) to ensure
 * locks are renewed well before expiration while accounting for network latency and
 * processing delays.
 *
 * <h3>Usage Example</h3>
 * <pre>{@code
 * RedisLockManager lockManager = new RedisLockManager();
 * RedisClusterLock lock = new RedisClusterLock(connectionFactories, "my-resource", fencingService);
 *
 * if (lockManager.acquireWithHeartbeat(lock)) {
 *     try {
 *         // Perform critical section work
 *         // Lock will be automatically renewed via heartbeat
 *     } finally {
 *         lockManager.release(lock);
 *     }
 * }
 * }</pre>
 *
 * @see RedisClusterLock
 * @see HeartbeatTask
 * @since 1.0
 */
@Getter
@Slf4j
@Component
public class RedisLockManager {

  /**
   * Scheduled executor service for managing heartbeat tasks.
   * Uses a thread pool of size 2 with daemon threads named "lock-heartbeat".
   */
  private final ScheduledExecutorService heartbeatScheduler;

  /**
   * Thread-safe map tracking active locks and their associated heartbeat tasks.
   * Key: lock name, Value: heartbeat task managing the lock renewal.
   */
  private final ConcurrentHashMap<String, HeartbeatTask> activeLocks;

  /**
   * Internal class representing a heartbeat task for a Redis distributed lock.
   *
   * <p>Each {@code HeartbeatTask} is responsible for maintaining the validity of a single
   * distributed lock by periodically renewing its TTL. The task monitors the lock's state
   * and automatically terminates itself when the lock becomes invalid or expires.
   *
   * <p>Key responsibilities include:
   * <ul>
   *   <li>Periodically calling {@link RedisClusterLock#renewLock()} to extend TTL</li>
   *   <li>Monitoring lock validity via {@link RedisClusterLock#isCurrentLockValid()}</li>
   *   <li>Checking for lock expiration via {@link RedisClusterLock#isLockExpired()}</li>
   *   <li>Self-termination when lock becomes invalid or renewal fails</li>
   *   <li>Graceful cleanup of scheduled execution resources</li>
   * </ul>
   *
   * <p>The task uses volatile fields to ensure thread-safe state management across
   * the heartbeat scheduler thread and any threads that may call {@link #stop()}.
   *
   * @see RedisClusterLock#renewLock()
   * @see #renew()
   * @see #stop()
   */
  private static class HeartbeatTask {
    /** The Redis cluster lock instance being managed by this heartbeat task. */
    private final RedisClusterLock lock;

    /** The lock key identifier for logging and tracking purposes. */
    private final String lockKey;

    /**
     * The scheduled future representing the periodic heartbeat execution.
     * Used to cancel the scheduled task when stopping the heartbeat.
     */
    private volatile ScheduledFuture<?> future;

    /**
     * Flag indicating whether this heartbeat task has been stopped.
     * Volatile to ensure visibility across threads.
     */
    private volatile boolean stopped = false;

    /**
     * Constructs a new heartbeat task for the specified lock.
     *
     * @param lock the Redis cluster lock to manage
     * @param lockKey the lock key identifier for logging
     */
    HeartbeatTask(RedisClusterLock lock, String lockKey) {
      this.lock = lock;
      this.lockKey = lockKey;
    }

    /**
     * Sets the scheduled future for this heartbeat task.
     * This is called after the task is scheduled to allow for later cancellation.
     *
     * @param future the scheduled future representing the periodic execution
     */
    void setFuture(ScheduledFuture<?> future) {
      this.future = future;
    }

    /**
     * Performs a single heartbeat renewal attempt for the associated lock.
     *
     * <p>This method is called periodically by the scheduler to maintain the lock's validity.
     * It performs the following checks and actions:
     *
     * <ol>
     *   <li>Checks if the task has been stopped - returns early if so</li>
     *   <li>Validates the current lock is still valid and not expired</li>
     *   <li>Attempts to renew the lock via {@link RedisClusterLock#renewLock()}</li>
     *   <li>Stops the heartbeat if renewal fails or lock becomes invalid</li>
     *   <li>Logs appropriate messages for success, failure, and error conditions</li>
     * </ol>
     *
     * <p>If any exception occurs during renewal, the heartbeat is stopped to prevent
     * further attempts with a potentially corrupted state.
     *
     * @see RedisClusterLock#isCurrentLockValid()
     * @see RedisClusterLock#isLockExpired()
     * @see RedisClusterLock#renewLock()
     */
    void renew() {
      if (stopped) return;

      try {
        if (!lock.isCurrentLockValid() || lock.isLockExpired()) {
          log.warn("Lock no longer valid or expired, stopping heartbeat: {}", lockKey);
          stop();
          return;
        }

        boolean renewed = lock.renewLock();
        if (!renewed) {
          log.warn("Failed to renew lock, stopping heartbeat: {}", lockKey);
          stop();
        } else {
          log.info("Successfully renewed lock: {}", lockKey);
        }
      } catch (Exception e) {
        log.error("Error renewing Redis lock {}: {}", lockKey, e.getMessage(), e);
        stop();
      }
    }

    /**
     * Stops this heartbeat task and cancels its scheduled execution.
     *
     * <p>This method sets the stopped flag to prevent further renewal attempts
     * and cancels the associated {@link ScheduledFuture} if it exists and hasn't
     * already been cancelled.
     *
     * <p>The method is idempotent - calling it multiple times has no additional effect.
     * The cancellation is performed with {@code mayInterruptIfRunning = false} to
     * avoid interrupting a renewal operation that may be in progress.
     */
    void stop() {
      stopped = true;
      if (future != null && !future.isCancelled()) {
        future.cancel(false);
      }
    }
  }

  /**
   * Constructs a new RedisLockManager with default configuration.
   *
   * <p>Initializes the heartbeat scheduler with a thread pool of size 2 using daemon threads
   * named "lock-heartbeat". Daemon threads ensure the JVM can shut down gracefully even if
   * heartbeat tasks are still running.
   *
   * <p>Also initializes the concurrent map for tracking active locks and their heartbeat tasks.
   */
  public RedisLockManager() {
    this.heartbeatScheduler =
        Executors.newScheduledThreadPool(
            2,
            r -> {
              Thread t = new Thread(r, "lock-heartbeat");
              t.setDaemon(true);
              return t;
            });

    this.activeLocks = new ConcurrentHashMap<>();
  }

  /**
   * Attempts to acquire a distributed lock and starts automatic heartbeat renewal.
   *
   * <p>This method tries to acquire the specified lock immediately (non-blocking).
   * If successful, it automatically starts a heartbeat task to periodically renew
   * the lock, ensuring it remains valid for the duration of its use.
   *
   * <p>The heartbeat will continue until the lock is explicitly released via
   * {@link #release(RedisClusterLock)} or the application shuts down.
   *
   * @param lock the Redis cluster lock to acquire
   * @return {@code true} if the lock was successfully acquired and heartbeat started,
   *         {@code false} if the lock could not be acquired
   * @see #startHeartbeat(RedisClusterLock)
   * @see #release(RedisClusterLock)
   */
  public boolean acquireWithHeartbeat(RedisClusterLock lock) {
    if (lock.tryLock()) {
      startHeartbeat(lock);
      log.info("Acquired Redis lock: {}", lock.getName());
      return true;
    }
    return false;
  }

  /**
   * Attempts to acquire a distributed lock with a timeout and starts automatic heartbeat renewal.
   *
   * <p>This method tries to acquire the specified lock within the given timeout period.
   * If successful, it automatically starts a heartbeat task to periodically renew
   * the lock, ensuring it remains valid for the duration of its use.
   *
   * <p>The method will retry lock acquisition until either the lock is obtained or
   * the timeout expires. The heartbeat will continue until the lock is explicitly
   * released via {@link #release(RedisClusterLock)} or the application shuts down.
   *
   * @param lock the Redis cluster lock to acquire
   * @param timeout the maximum time to wait for lock acquisition
   * @param unit the time unit of the timeout argument
   * @return {@code true} if the lock was successfully acquired within the timeout
   *         and heartbeat started, {@code false} if the timeout expired
   * @see #startHeartbeat(RedisClusterLock)
   * @see #release(RedisClusterLock)
   */
  public boolean acquireWithHeartbeat(RedisClusterLock lock, long timeout, TimeUnit unit) {
    if (lock.tryLock(timeout, unit)) {
      startHeartbeat(lock);
      log.debug("Acquired Redis lock with timeout: {}", lock.getName());
      return true;
    }
    return false;
  }

  /**
   * Releases a distributed lock and stops its associated heartbeat task.
   *
   * <p>This method performs a complete cleanup of the specified lock by:
   * <ol>
   *   <li>Stopping the heartbeat task to prevent further renewal attempts</li>
   *   <li>Removing the lock from the active locks tracking map</li>
   *   <li>Calling {@link RedisClusterLock#unlock()} to release the actual lock</li>
   * </ol>
   *
   * <p>The method is safe to call even if the lock was not acquired through this manager
   * or if the heartbeat was already stopped. Any errors during lock release are logged
   * but do not prevent heartbeat cleanup.
   *
   * <p><strong>Important:</strong> Always call this method in a finally block or
   * try-with-resources pattern to ensure proper cleanup even if exceptions occur
   * during the critical section.
   *
   * @param lock the Redis cluster lock to release
   * @see #acquireWithHeartbeat(RedisClusterLock)
   * @see #acquireWithHeartbeat(RedisClusterLock, long, TimeUnit)
   */
  public void release(RedisClusterLock lock) {
    String lockKey = lock.getName();

    // Stop heartbeat
    HeartbeatTask task = activeLocks.remove(lockKey);
    if (task != null) {
      task.stop();
    }

    // Release the lock
    try {
      lock.unlock();
      log.info("Released Redis lock: {}", lockKey);
    } catch (Exception e) {
      log.warn("Error releasing Redis lock {}: {}", lockKey, e.getMessage(), e);
    }
  }

  /**
   * Starts a heartbeat task for the specified lock to enable automatic renewal.
   *
   * <p>This method creates and schedules a {@link HeartbeatTask} that will periodically
   * call {@link RedisClusterLock#renewLock()} to extend the lock's TTL. The heartbeat
   * runs at a fixed interval of 7 seconds (70% of the default 10-second TTL) to ensure
   * the lock is renewed well before expiration.
   *
   * <p>The heartbeat task will automatically stop itself if:
   * <ul>
   *   <li>Lock renewal fails</li>
   *   <li>The lock becomes invalid (fencing token mismatch)</li>
   *   <li>The lock expires</li>
   *   <li>An exception occurs during renewal</li>
   * </ul>
   *
   * <p>This method is typically called automatically by
   * {@link #acquireWithHeartbeat(RedisClusterLock)} methods and should not be
   * called directly unless you have acquired the lock through other means.
   *
   * @param lock the Redis cluster lock for which to start heartbeat renewal
   * @throws IllegalStateException if a heartbeat is already active for this lock
   * @see HeartbeatTask
   * @see #release(RedisClusterLock)
   */
  public void startHeartbeat(RedisClusterLock lock) {
    String lockKey = lock.getName();

    long renewalIntervalMs = 7000; // ~70% of default TTL (10s)

    HeartbeatTask task = new HeartbeatTask(lock, lockKey);
    ScheduledFuture<?> future =
        heartbeatScheduler.scheduleAtFixedRate(
            task::renew, renewalIntervalMs, renewalIntervalMs, TimeUnit.MILLISECONDS);

    task.setFuture(future);
    activeLocks.put(lockKey, task);
    log.info("Started heartbeat for Redis lock: {}", lockKey);
  }


  /**
   * Gracefully shuts down the RedisLockManager and releases all active locks.
   *
   * <p>This method is automatically called during Spring application shutdown due to
   * the {@link PreDestroy} annotation. It performs the following cleanup operations:
   *
   * <ol>
   *   <li>Stops all active heartbeat tasks to prevent further renewal attempts</li>
   *   <li>Releases all locks currently held by this manager instance</li>
   *   <li>Clears the active locks tracking map</li>
   *   <li>Shuts down the heartbeat scheduler thread pool</li>
   *   <li>Waits up to 2 seconds for graceful termination</li>
   *   <li>Forces shutdown if graceful termination times out</li>
   * </ol>
   *
   * <p>Any errors during individual lock release are logged but do not prevent
   * the shutdown process from continuing. This ensures that the application can
   * shut down cleanly even if some Redis instances are unavailable.
   *
   * <p><strong>Thread Safety:</strong> This method is thread-safe and can be called
   * multiple times without adverse effects.
   *
   * @see PreDestroy
   * @see #release(RedisClusterLock)
   */
  @PreDestroy
  public void shutdown() {
    log.info("Shutting down RedisLockManager with {} active locks", activeLocks.size());

    // Stop all heartbeats and release associated locks
    for (String lockKey : activeLocks.keySet()) {
      HeartbeatTask task = activeLocks.remove(lockKey);
      if (task != null) {
        task.stop();
        try {
          task.lock.unlock();
          log.info("Released lock during shutdown: {}", lockKey);
        } catch (Exception e) {
          log.warn("Failed to release lock {} during shutdown: {}", lockKey, e.getMessage(), e);
        }
      }
    }

    activeLocks.clear();
    heartbeatScheduler.shutdown();

    try {
      if (!heartbeatScheduler.awaitTermination(2, TimeUnit.SECONDS)) {
        heartbeatScheduler.shutdownNow();
      }
    } catch (InterruptedException e) {
      heartbeatScheduler.shutdownNow();
      Thread.currentThread().interrupt();
    }

    log.info("RedisLockManager shutdown complete");
  }
}
