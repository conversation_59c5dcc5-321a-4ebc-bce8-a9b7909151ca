package com.solum.xplain.shared.datagrid.impl.redis;

import java.time.Duration;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for Redis Lock Manager.
 *
 * <p>These properties control the behavior of the Redis distributed lock manager,
 * including heartbeat timing, thread pool sizing, and other operational parameters.
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.data-grid.redis.lock-manager")
public class RedisLockManagerProperties {

  /**
   * Number of threads in the heartbeat scheduler pool.
   * Default: 2 threads (sufficient for most use cases)
   */
  private int heartbeatThreadPoolSize = 2;

  /**
   * Interval between heartbeat renewals.
   * Should be significantly less than lock TTL (recommended: 60-70% of TTL).
   * Default: 7 seconds (70% of default 10s TTL)
   */
  private Duration heartbeatInterval = Duration.ofSeconds(7);

  /**
   * Maximum time to wait for graceful shutdown of the heartbeat scheduler.
   * Default: 2 seconds
   */
  private Duration shutdownTimeout = Duration.ofSeconds(2);

  /**
   * Whether to log successful heartbeat renewals.
   * Set to false in production to reduce log noise.
   * Default: true
   */
  private boolean logSuccessfulRenewals = true;
}
