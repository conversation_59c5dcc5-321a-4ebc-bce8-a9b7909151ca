package com.solum.xplain.shared.datagrid;

import com.solum.xplain.shared.datagrid.impl.redis.RedisClusterLock;
import com.solum.xplain.shared.datagrid.impl.redis.RedisLockManager;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class ManagedClusterLock implements ClusterLock {
  private final RedisClusterLock lock;
  private final RedisLockManager lockManager;

  @Override
  public String getName() {
    return lock.getName();
  }

  @Override
  public void lock() {
    boolean acquired = lockManager.acquireWithHeartbeat(lock);
    if (!acquired) {
      throw new RuntimeException("Failed to acquire Redis cluster lock: " + getName());
    }
  }

  @Override
  public void lockInterruptibly() {
    boolean acquired = lockManager.acquireWithHeartbeat(lock, 2, TimeUnit.SECONDS);
    if (!acquired) {
      throw new RuntimeException(
          "Failed to acquire Redis cluster lock (interruptibly): " + getName());
    }
  }

  @Override
  public boolean tryLock() {
    return lockManager.acquireWithHeartbeat(lock);
  }

  @Override
  public boolean tryLock(long time, TimeUnit unit) {
    return lockManager.acquireWithHeartbeat(lock, time, unit);
  }

  @Override
  public void unlock() {
    lockManager.release(lock);
  }

  @Override
  public Condition newCondition() {
    throw new UnsupportedOperationException("Conditions are not supported for RedisClusterLock");
  }
}
