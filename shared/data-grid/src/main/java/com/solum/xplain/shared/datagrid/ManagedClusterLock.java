package com.solum.xplain.shared.datagrid;

import com.solum.xplain.shared.datagrid.impl.redis.RedisClusterLock;
import com.solum.xplain.shared.datagrid.impl.redis.RedisLockManager;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class ManagedClusterLock implements ClusterLock {
  private final RedisClusterLock lock;
  private final RedisLockManager lockManager;

  @Override
  public String getName() {
    return lock.getName();
  }

  @Override
  public void lock() {
    // Block indefinitely until lock is acquired (per Lock contract)
    while (!lockManager.acquireWithHeartbeat(lock)) {
      try {
        Thread.sleep(100); // Brief pause between attempts
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        throw new RuntimeException("Interrupted while acquiring lock: " + getName(), e);
      }
    }
  }

  @Override
  public void lockInterruptibly() throws InterruptedException {
    // Use the underlying lock's lockInterruptibly method which properly handles timeouts
    lock.lockInterruptibly();
    // Start heartbeat after successful acquisition
    lockManager.startHeartbeat(lock);
  }

  @Override
  public boolean tryLock() {
    return lockManager.acquireWithHeartbeat(lock);
  }

  @Override
  public boolean tryLock(long time, TimeUnit unit) {
    return lockManager.acquireWithHeartbeat(lock, time, unit);
  }

  @Override
  public void unlock() {
    lockManager.release(lock);
  }

  @Override
  public Condition newCondition() {
    throw new UnsupportedOperationException("Conditions are not supported for RedisClusterLock");
  }
}
