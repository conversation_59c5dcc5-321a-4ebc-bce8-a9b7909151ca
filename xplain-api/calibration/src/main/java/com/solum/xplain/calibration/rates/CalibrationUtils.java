package com.solum.xplain.calibration.rates;

import static com.solum.xplain.core.common.CollectionUtils.convertCollectionTo;
import static java.util.stream.Collectors.toList;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.LinkedListMultimap;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.index.Index;
import com.opengamma.strata.collect.timeseries.LocalDateDoubleTimeSeries;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.data.MarketDataFxRateProvider;
import com.opengamma.strata.market.curve.Curve;
import com.opengamma.strata.market.curve.CurveName;
import com.opengamma.strata.market.curve.RatesCurveGroupDefinition;
import com.opengamma.strata.pricer.rate.ImmutableRatesProvider;
import com.opengamma.strata.pricer.rate.ImmutableRatesProviderBuilder;
import com.opengamma.strata.pricer.rate.RatesProvider;
import com.solum.xplain.calibration.rates.spot01.ShiftedFxRatesProvider;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.extensions.volsshift.ShiftedFxMarketData;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CalibrationUtils {

  private static final String CURVE_NOT_FOUND = "Curve with name %s not found!";

  public static Curve ratesProviderCurve(RatesProvider provider, CurveName name) {
    return provider
        .findData(name)
        .orElseThrow(
            () -> new IllegalArgumentException(String.format(CURVE_NOT_FOUND, name.getName())));
  }

  /**
   * Creates a combined result for the rates calibration, merging all the results from the
   * individual calibration sets for a single bundle (single discount currency).
   *
   * <p>If we are doing local discounting, then we drop any index curves from the results which are
   * not used for discounting that calibration set. This is to avoid merging index curves which were
   * discounted in a different currency during calibration. If the calibration set is single
   * currency then all index curves are kept.
   *
   * @param results the list of calibration set results to combine
   * @param discountCcy the currency used for discounting, or null if using local discounting
   * @param marketData the market data used for calibration
   * @param referenceData the reference data used for calibration
   * @return a combined result containing all curves and rates from the calibration sets
   */
  public static CalibrationCombinedResultRates calibrationCombinedResultRates(
      List<CalibrationSetResult> results,
      Currency discountCcy,
      MarketData marketData,
      ReferenceData referenceData) {

    var sorted = sortedResults(results, c -> c.getDefinition().getEntries().size());
    var rates = convertCollectionTo(sorted, CalibrationSetResult::getRatesResult);
    var isdaRates = convertCollectionTo(sorted, CalibrationSetResult::getIsdaRatesSupplier);

    RatesCalibrationResult combinedRates =
        combineRatesResult(marketData, rates, discountCcy == null);
    Set<CurveName> allRatesCurves = combinedRates.getRatesProvider().getCurves().keySet();
    var allCurves =
        results.stream()
            .map(CalibrationSetResult::getDefinition)
            .map(RatesCurveGroupDefinition::getCurveDefinitions)
            .flatMap(Collection::stream)
            .filter(curveDef -> allRatesCurves.contains(curveDef.getName()))
            .collect(Collectors.toSet());
    return new CalibrationCombinedResultRates(
        allCurves,
        combinedRates,
        combineIsdaRatesSupplier(marketData, isdaRates, discountCcy == null),
        referenceData);
  }

  /** Final merge of all calibration results after each bundle has been calibrated. */
  public static CalibrationCombinedResultRates mergeCalibrationCombinedRates(
      List<CalibrationCombinedResultRates> results,
      MarketData marketData,
      ReferenceData referenceData) {
    var allCurves =
        results.stream()
            .map(CalibrationCombinedResultRates::getCurves)
            .flatMap(Collection::stream)
            .collect(Collectors.toSet());

    // Get the results bundles sorted by the number of curves in each set descending order.
    var sorted = sortedResults(results, c -> c.getCurves().size());
    var rates = convertCollectionTo(sorted, CalibrationCombinedResultRates::getRates);
    var isdaRates = convertCollectionTo(sorted, CalibrationCombinedResultRates::getIsdaRates);

    return new CalibrationCombinedResultRates(
        allCurves,
        combineRatesResult(marketData, rates, false),
        combineIsdaRatesSupplier(marketData, isdaRates, false),
        referenceData);
  }

  private static RatesCalibrationResult combineRatesResult(
      MarketData marketData,
      List<RatesCalibrationResult> rates,
      boolean dropNonDiscountingIndexCurves) {
    var ratesProviders = convertCollectionTo(rates, RatesCalibrationResult::getRatesProvider);
    var combinedRatesProvider =
        combineDefaultRatesProvider(marketData, ratesProviders, dropNonDiscountingIndexCurves);
    var combinedShiftedRates =
        combineShiftedFxRatesProvider(marketData, rates, dropNonDiscountingIndexCurves);
    return new RatesCalibrationResult(combinedRatesProvider, combinedShiftedRates);
  }

  private static List<ShiftedFxRatesProvider> combineShiftedFxRatesProvider(
      MarketData marketData,
      List<RatesCalibrationResult> ratesResults,
      boolean dropNonDiscountingIndexCurves) {
    var shiftedRatesMultiMap = LinkedListMultimap.<CurrencyPair, ImmutableRatesProvider>create();

    ratesResults.forEach(
        result ->
            result
                .getShiftedRatesProviders()
                .forEach(
                    fx -> shiftedRatesMultiMap.put(fx.getCurrencyPair(), fx.getRatesProvider())));
    return shiftedRatesMultiMap.asMap().entrySet().stream()
        .map(
            entry ->
                new ShiftedFxRatesProvider(
                    entry.getKey(),
                    combineDefaultRatesProvider(
                        ShiftedFxMarketData.shiftedFxMarketData(entry.getKey(), marketData),
                        entry.getValue(),
                        dropNonDiscountingIndexCurves)))
        .toList();
  }

  private static ImmutableRatesProvider combineDefaultRatesProvider(
      MarketData marketData,
      Collection<ImmutableRatesProvider> sortedRates,
      boolean dropNonDiscountingIndexCurves) {
    ImmutableRatesProviderBuilder merged =
        ImmutableRatesProvider.builder(marketData.getValuationDate());
    merged.fxRateProvider(MarketDataFxRateProvider.of(marketData));
    for (ImmutableRatesProvider rates : sortedRates) {
      log.debug(
          "Merging rates provider for valuation date: {} with discount currencies {}, discount curves {} and index curves {}",
          rates.getValuationDate(),
          rates.getDiscountCurrencies(),
          rates.getDiscountCurves().values().stream().map(Curve::getName).toList(),
          rates.getIndexCurves().values().stream().map(Curve::getName).toList());
      ImmutableMap<Currency, Curve> discounts = rates.getDiscountCurves();
      for (Map.Entry<Currency, Curve> entry : discounts.entrySet()) {
        merged.discountCurve(entry.getKey(), entry.getValue());
      }
      ImmutableMap<Index, Curve> indices = rates.getIndexCurves();
      for (Map.Entry<Index, Curve> entry : indices.entrySet()) {
        if (dropNonDiscountingIndexCurves) {
          if (rates.getDiscountCurrencies().size() > 1
              && !discounts.containsValue(entry.getValue())) {
            log.trace(
                "Skipping index curve for index {} as it is not a discount curve for this multi-currency {} rates provider",
                entry.getKey(),
                rates.getDiscountCurrencies());
            continue;
          }
        }
        merged.indexCurve(entry.getKey(), entry.getValue());
      }
      Map<Index, LocalDateDoubleTimeSeries> timeSeries = rates.getTimeSeries();
      for (Map.Entry<Index, LocalDateDoubleTimeSeries> entry : timeSeries.entrySet()) {
        merged.timeSeries(entry.getKey(), entry.getValue());
      }
    }
    ImmutableRatesProvider build = merged.build();
    log.info(
        "Merged rates provider for valuation date: {} with discount curves {} and index curves {}",
        build.getValuationDate(),
        build.getDiscountCurves().values().stream().map(Curve::getName).toList(),
        build.getIndexCurves().values().stream().map(Curve::getName).toList());
    return build;
  }

  private static Supplier<Either<ErrorItem, RatesCalibrationResult>> combineIsdaRatesSupplier(
      MarketData marketData,
      List<Supplier<Either<ErrorItem, RatesCalibrationResult>>> isdaSuppliers,
      boolean dropNonDiscountingIndexCurves) {
    return () ->
        isdaSuppliers.stream()
            .map(Supplier::get)
            .collect(Collectors.collectingAndThen(toList(), Eithers::sequenceRight))
            .map(ImmutableList::copyOf)
            .map(rates -> combineRatesResult(marketData, rates, dropNonDiscountingIndexCurves));
  }

  /**
   * Retrieves and combines results, sorting them in descending order based on the number of
   * entries. The sorting ensures that curve sets with the most curves appear first, followed by
   * those with fewer curves. This order is preferred because curve sets with the most curves are
   * considered less precise.
   *
   * @return A sorted list of results in descending order, with the curve sets having the most
   *     curves appearing first.
   */
  private static <T> List<T> sortedResults(List<T> results, ToIntFunction<T> entriesSizeFunction) {
    return results.stream()
        .sorted(Comparator.comparingInt(entriesSizeFunction).reversed())
        .toList();
  }
}
