package com.solum.xplain.calibration.curve.charts.calculator;

import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.market.ShiftType;
import com.opengamma.strata.market.ValueType;
import com.opengamma.strata.market.curve.interpolator.BoundCurveInterpolator;
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolator;
import com.opengamma.strata.market.curve.interpolator.CurveInterpolator;
import com.opengamma.strata.product.swap.PriceIndexCalculationMethod;
import com.solum.xplain.calibration.curve.charts.value.CurveChartGeneratorData;
import com.solum.xplain.calibration.rates.CalibrationCurveUtils;
import com.solum.xplain.calibration.rates.curvepoint.CurvePointCalculator;
import com.solum.xplain.calibration.rates.curvepoint.CurveValueCalculatorDelegate;
import com.solum.xplain.calibration.rates.curvepoint.InflationCurvePointCalculator;
import com.solum.xplain.calibration.rates.curvepoint.InterestRatesCurvePointCalculator;
import com.solum.xplain.core.common.value.CalculatedValueAtDate;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.utils.DayCountUtils;
import java.time.LocalDate;
import java.util.function.ToDoubleFunction;
import org.springframework.stereotype.Service;

@Service
public class ChartCurvePointCalculatorFactory {
  private static final PriceIndexCalculationMethod DEFAULT_CALCULATION_METHOD =
      PriceIndexCalculationMethod.MONTHLY; // For chart generation we always use monthly
  private static final DayCount DEFAULT_DAY_COUNT = Curve.DEFAULT_DAY_COUNT;

  public CurvePointCalculator calculator(CurveChartGeneratorData generatorData) {
    var valuationDate = generatorData.valuationDate();
    var xValueFunction = xValueFunction(generatorData);
    var interpolator = buildInterpolator(generatorData, xValueFunction);
    if (ValueType.PRICE_INDEX.equals(generatorData.curveDetails().yValueType())) {
      var laggedMonths =
          generatorData.nodeValues().stream()
              .map(CalculatedValueAtDate::getDate)
              .map(xValueFunction::applyAsDouble)
              .map(Double::longValue)
              .filter(x -> x < 0)
              .toList();

      var valueCalculator =
          new InflationCurveValueCalculator(
              laggedMonths,
              interpolator,
              DoubleArray.copyOf(generatorData.inflationSeasonalityAdjustment()),
              ShiftType.valueOf(generatorData.inflationAdjustmentType()));
      return new InflationCurvePointCalculator(
          valuationDate, DEFAULT_CALCULATION_METHOD, valueCalculator);
    }
    return new InterestRatesCurvePointCalculator(
        valuationDate,
        DEFAULT_DAY_COUNT,
        new CurveValueCalculatorDelegate<>(interpolator::interpolate));
  }

  private BoundCurveInterpolator buildInterpolator(
      CurveChartGeneratorData chartGeneratorData, ToDoubleFunction<LocalDate> xValueFunction) {
    var curve = chartGeneratorData.curveDetails();
    var nodeValues = chartGeneratorData.nodeValues();

    var xValues =
        nodeValues.stream()
            .map(CalculatedValueAtDate::getDate)
            .map(xValueFunction::applyAsDouble)
            .toList();
    var yValues = nodeValues.stream().map(CalculatedValueAtDate::getCalculatedValue).toList();

    return CurveInterpolator.of(curve.interpolator())
        .bind(
            DoubleArray.copyOf(xValues),
            DoubleArray.copyOf(yValues),
            CurveExtrapolator.of(curve.extrapolatorLeft()),
            CurveExtrapolator.of(curve.extrapolatorLeft()));
  }

  private ToDoubleFunction<LocalDate> xValueFunction(CurveChartGeneratorData generatorData) {
    if (ValueType.MONTHS.equals(generatorData.curveDetails().xValueType())) {
      return date -> CalibrationCurveUtils.monthsBetween(date, generatorData.valuationDate());
    }
    return date ->
        DayCountUtils.yearFraction(generatorData.valuationDate(), date, DEFAULT_DAY_COUNT);
  }
}
