package com.solum.xplain.core.curvegroup.volatilityfx

import static com.solum.xplain.core.common.value.NewVersionFormV2.ROOT_DATE
import static com.solum.xplain.core.curveconfiguration.CurveConfigurationMarketValueFullViewBuilder.withKeyAndVal
import static com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroupBuilder.curveGroup
import static java.time.LocalDate.now
import static java.time.LocalDate.parse
import static org.springframework.data.domain.Sort.Direction.DESC
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityBuilder
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNode
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNodeBuilder
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityForm
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityNodeForm
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityView
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilitySkewForm
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.MarketDataGroup
import com.solum.xplain.core.mdvalue.entity.MarketDataValue
import com.solum.xplain.core.users.UserBuilder
import jakarta.annotation.Resource
import java.time.LocalDate
import java.time.LocalDateTime
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class CurveGroupFxVolatilityRepositoryTest extends IntegrationSpecification {
  static def STATE_DATE = BitemporalDate.newOf(now(), LocalDateTime.now().plusDays(1))
  static def VALID_FROM = parse("2020-01-01")
  static def FUTURE_VALID_FROM = parse("2100-01-01")
  static def EUR_USD_2Y_NODE_FORM = new CurveGroupFxVolatilityNodeForm(expiry: "2Y", domesticCurrency: "EUR", foreignCurrency: "USD")
  static def EUR_USD_SKEW_FORM = new FxVolatilitySkewForm(domesticCurrency: "EUR", foreignCurrency: "USD", delta1: 1, delta2: 2)


  @Resource
  CurveGroupFxVolatilityRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), MarketDataGroup)
    operations.remove(new Query(), MarketDataValue)
    operations.remove(new Query(), CurveGroupFxVolatility)
    operations.remove(new Query(), CurveGroup)
  }

  def "should create volatility"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def form = new CurveGroupFxVolatilityForm(nodes: [EUR_USD_2Y_NODE_FORM],
    skews: [EUR_USD_SKEW_FORM],
    versionForm: NewVersionFormV2.newDefault())

    when:
    def result = repository.createVolatility(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    // New major ROOT version
    assertAutofilledFields(loaded[0])
    assertNodes(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].entityId == group.id
  }

  def "should insert volatility with archived from ROOT date"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def form = new CurveGroupFxVolatilityForm(nodes: [EUR_USD_2Y_NODE_FORM],
    versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).build())

    when:
    def result = repository.createVolatility(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New major version
    assertAutofilledFields(loaded[0])
    assertNodes(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].entityId == group.id

    // New major ROOT ARCHIVED version
    assertAutofilledFields(loaded[1])
    assertNodes(loaded[1], form)
    loaded[1].state == State.ARCHIVED
    loaded[1].validFrom == ROOT_DATE
    loaded[1].comment == form.versionForm.comment
    loaded[1].entityId == group.id
  }

  @Unroll
  def "should update on insert if volatility for group already exists with state #initialState"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def volatility = new CurveGroupFxVolatilityBuilder()
      .state(initialState)
      .entityId(group.id)
      .build()
    operations.insert(volatility)

    def form = new CurveGroupFxVolatilityForm(nodes: [EUR_USD_2Y_NODE_FORM], //Updated,
    skews: [EUR_USD_SKEW_FORM], // Updated
    versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).stateDate(VALID_FROM).build())

    when:
    def result = repository.createVolatility(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New major version
    assertAutofilledFields(loaded[0])
    assertNodes(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].entityId == group.id

    // Initial major ROOT version
    volatility.valueEquals(loaded[1])

    where:
    initialState << [State.ACTIVE, State.ARCHIVED, State.DELETED]
  }

  @Unroll
  def "should update volatility when #fieldName value is changed to #currentValue"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .build()
    operations.insert(volatility)

    def form = new CurveGroupFxVolatilityForm(versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).stateDate(VALID_FROM).build())
    form[fieldName] = currentValue

    when:
    def result = repository.createVolatility(group.id, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2
    loaded[0][fieldName] == currentValue
    loaded[1][fieldName] == previousValue

    where:
    fieldName                 | currentValue   | previousValue
    "timeInterpolator"        | "StepUpper"    | "TimeSquare"
    "timeExtrapolatorLeft"    | "Linear"       | "Flat"
    "strikeExtrapolatorRight" | "Interpolator" | "Flat"
    "strikeInterpolator"      | "TimeSquare"   | "Linear"
    "strikeExtrapolatorLeft"  | "Interpolator" | "Flat"
    "strikeExtrapolatorRight" | "Linear"       | "Flat"
  }

  def "should update volatility"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .build()
    operations.insert(volatility)

    def form = new CurveGroupFxVolatilityForm(nodes: [EUR_USD_2Y_NODE_FORM], //Updated,
    skews: [EUR_USD_SKEW_FORM], // Updated
    versionForm: NewVersionFormV2.builder().validFrom(VALID_FROM).comment("Comment").build())

    when:
    def result = repository.updateVolatility(group.id, volatility.validFrom, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New minor version
    assertAutofilledFields(loaded[0])
    assertNodes(loaded[0], form)
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].entityId == group.id

    // Initial major ROOT version
    volatility.valueEquals(loaded[1])
  }

  def "should update volatility and remove future versions"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .build()
    operations.insert(volatility)

    def volatilityInFuture = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .validFrom(FUTURE_VALID_FROM)
      .build()
    operations.insert(volatilityInFuture)

    def form = new CurveGroupFxVolatilityForm(nodes: [EUR_USD_2Y_NODE_FORM], //Updated
    versionForm: NewVersionFormV2.builder()
    .validFrom(VALID_FROM)
    .futureVersionsAction(FutureVersionsAction.DELETE)
    .build())
    when:
    def result = repository.updateVolatility(group.id, volatility.validFrom, form)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 4

    // Future Major (DELETED)
    loaded[0].validFrom == FUTURE_VALID_FROM
    loaded[0].state == State.DELETED

    // Future major
    loaded[1].validFrom == FUTURE_VALID_FROM
    loaded[1].state == State.ACTIVE

    // New major
    loaded[2].validFrom == VALID_FROM
    loaded[2].state == State.ACTIVE

    // Initial major (ROOT)
    loaded[3].validFrom == ROOT_DATE
    loaded[3].state == State.ACTIVE
  }

  def "should not update volatility when no changes"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .build()
    operations.insert(volatility)

    def form = new CurveGroupFxVolatilityForm(timeInterpolator: volatility.getTimeInterpolator(),
    timeExtrapolatorLeft: volatility.getTimeExtrapolatorLeft(),
    timeExtrapolatorRight: volatility.getTimeExtrapolatorRight(),
    strikeInterpolator: volatility.getStrikeInterpolator(),
    strikeExtrapolatorLeft: volatility.getStrikeExtrapolatorLeft(),
    strikeExtrapolatorRight: volatility.getStrikeExtrapolatorRight(),
    nodes: [], // as in volatility
    versionForm: NewVersionFormV2.builder().validFrom(volatility.validFrom).build())

    when:
    def result = repository.updateVolatility(group.id, volatility.validFrom, form)

    then:
    result.isRight()
    def entityId = result.right().get() as EntityId

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 1

    loaded[0].valueEquals(volatility)
  }

  def "should correctly update nodes deltas"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .nodes([
        new CurveGroupFxVolatilityNode(expiry: "2Y",
        domesticCurrency: "EUR",
        foreignCurrency: "USD",
        delta1: 1,
        delta2: 2),
        new CurveGroupFxVolatilityNode(expiry: "2Y",
        domesticCurrency: "EUR",
        foreignCurrency: "GBP",
        delta1: 3,
        delta2: 4)
      ] as Set)
      .build()
    operations.insert(volatility)

    def form = new CurveGroupFxVolatilityForm(nodes: [
      new CurveGroupFxVolatilityNodeForm(expiry: "2Y",
      domesticCurrency: "EUR",
      foreignCurrency: "USD"),
      new CurveGroupFxVolatilityNodeForm(expiry: "2Y",
      domesticCurrency: "EUR",
      foreignCurrency: "GBP")
    ],
    skews: [
      new FxVolatilitySkewForm(domesticCurrency: "EUR",
      foreignCurrency: "USD",
      delta1: 10,
      delta2: 20),
      new FxVolatilitySkewForm(domesticCurrency: "EUR",
      foreignCurrency: "SEK",
      delta1: 100,
      delta2: 200) // Does not map to node
    ], // Updated
    versionForm: NewVersionFormV2.newDefault())

    when:
    def result = repository.updateVolatility(group.id, volatility.validFrom, form)

    then:
    result.isRight()
    def entityId = result.getOrNull()

    def loaded = allSortedValidFromDesc(entityId.id)
    loaded.size() == 2

    // New minor version
    assertAutofilledFields(loaded[0])
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == form.versionForm.validFrom
    loaded[0].comment == form.versionForm.comment
    loaded[0].entityId == group.id
    loaded[0].nodes.size() == 2

    def node_EUR_USD = loaded[0].nodes.find { it.foreignCurrency == "USD" }
    node_EUR_USD.domesticCurrency == volatility.nodes[0].domesticCurrency
    node_EUR_USD.foreignCurrency == volatility.nodes[0].foreignCurrency
    node_EUR_USD.delta1 == form.skews[0].delta1
    node_EUR_USD.delta2 == form.skews[0].delta2

    def node_EUR_GBP = loaded[0].nodes.find { it.foreignCurrency == "GBP" }
    node_EUR_GBP.domesticCurrency == volatility.nodes[1].domesticCurrency
    node_EUR_GBP.foreignCurrency == volatility.nodes[1].foreignCurrency
    node_EUR_GBP.delta1 == null
    node_EUR_GBP.delta2 == null

    // Initial major ROOT version
    volatility.valueEquals(loaded[1])
  }

  def "should load active volatility view"() {
    setup:
    def group = curveGroup()
    operations.insert(group)
    def givenTimeInterpolator = "TimeSquare"
    def givenTimeExtrapolatorLeft = "Flat"
    def givenTimeExtrapolatorRight = "Flat"
    def givenStrikeInterpolator = "Linear"
    def givenStrikeExtrapolatorLeft = "Flat"
    def givenStrikeExtrapolatorRight = "Flat"

    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .timeInterpolator(givenTimeInterpolator)
      .timeExtrapolatorLeft(givenTimeExtrapolatorLeft)
      .timeExtrapolatorRight(givenTimeExtrapolatorRight)
      .strikeInterpolator(givenStrikeInterpolator)
      .strikeExtrapolatorLeft(givenStrikeExtrapolatorLeft)
      .strikeExtrapolatorRight(givenStrikeExtrapolatorRight)
      .nodes([new CurveGroupFxVolatilityNode(expiry: "2Y", domesticCurrency: "EUR", foreignCurrency: "USD")] as Set)
      .build()
    operations.insert(volatility)

    when:
    def result = repository.getActiveVolatilityView(group.id, now())

    then:
    result.isRight()

    def view = result.right().get() as CurveGroupFxVolatilityView
    with(view) {
      validFrom == volatility.validFrom
      state == State.ACTIVE
      entityId == group.id
      comment == volatility.comment
      modifiedBy == user.name
      modifiedAt != null
      recordDate != null
      numberOfFxVols == volatility.nodes.size()
      timeInterpolator == givenTimeInterpolator
      timeExtrapolatorLeft == givenTimeExtrapolatorLeft
      timeExtrapolatorRight == givenTimeExtrapolatorRight
      strikeInterpolator == givenStrikeInterpolator
      strikeExtrapolatorLeft == givenStrikeExtrapolatorLeft
      strikeExtrapolatorRight == givenStrikeExtrapolatorRight
    }
  }

  def "should load volatility versions"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .build()
    operations.insert(volatility)

    def volatilityInFuture = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .nodes([new CurveGroupFxVolatilityNode(expiry: "2Y", domesticCurrency: "EUR", foreignCurrency: "USD")] as Set)
      .validFrom(FUTURE_VALID_FROM)
      .build()
    operations.insert(volatilityInFuture)

    when:
    def result = repository.getVolatilityVersionViews(group.id)

    then:
    result.size() == 2
    // Future major
    result[0].validFrom == volatilityInFuture.validFrom
    result[0].state == volatilityInFuture.state
    result[0].entityId == volatilityInFuture.entityId
    result[0].comment == volatilityInFuture.comment
    result[0].modifiedBy == user.name
    result[0].modifiedAt != null
    result[0].recordDate != null
    result[0].numberOfFxVols == volatilityInFuture.nodes.size()

    // Initial major (ROOT)
    result[1].validFrom == volatility.validFrom
    result[1].state == volatility.state
    result[1].entityId == volatility.entityId
    result[1].comment == volatility.comment
    result[1].modifiedBy == user.name
    result[1].modifiedAt != null
    result[1].recordDate != null
    result[1].numberOfFxVols == volatility.nodes.size()
  }

  def "should load volatility future versions dates list"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def volatility = new CurveGroupFxVolatilityBuilder().entityId(group.id).build()
    def futureVolatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .state(State.ARCHIVED)
      .validFrom(FUTURE_VALID_FROM)
      .build()
    operations.insertAll([volatility, futureVolatility])

    when:
    def result = repository.getFutureVersions(group.id, volatility.validFrom)

    then:
    result.dates == [futureVolatility.validFrom]
  }

  def "should load volatility nodes count"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .nodes([
        new CurveGroupFxVolatilityNode(expiry: "2Y",
        domesticCurrency: "EUR",
        foreignCurrency: "USD")
      ] as Set)
      .build()
    operations.insert(volatility)

    when:
    def result = repository.getVolatilityNodesCount(CurveGroupEntryFilter.singleGroup(now(), group.id))

    then:
    result.size() == 1
    result[0].curveGroupId == group.id
    result[0].count == 1
  }

  def "should load volatility nodes"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def node1 = new CurveGroupFxVolatilityNodeBuilder().expiry("1Y").build()
    def node2 = new CurveGroupFxVolatilityNodeBuilder().expiry("2M").build()
    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .nodes([node1, node2] as Set)
      .build()
    operations.insert(volatility)

    when:
    def result = repository.getVolatilityNodes(group.id, STATE_DATE)

    then:
    result.size() == 2
    result.contains(node1)
    result.contains(node2)
  }

  def "should load volatility nodes views"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def node1 = new CurveGroupFxVolatilityNodeBuilder().expiry("1Y").delta1(1).delta2(2).build()
    def node2 = new CurveGroupFxVolatilityNodeBuilder().expiry("2M").delta1(10).delta2(20).build()
    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .nodes([node1, node2] as Set)
      .build()
    operations.insert(volatility)

    when:
    def result = repository.getVolatilityNodesViews(group.id, now())

    then:
    result.versionDate == volatility.validFrom
    result.list.size() == volatility.nodes.size()

    def node_EUR_USD_1Y_value = result.list.find { it.expiry == "1Y" }
    node_EUR_USD_1Y_value.expiry == "1Y"
    node_EUR_USD_1Y_value.domesticCurrency == "EUR"
    node_EUR_USD_1Y_value.foreignCurrency == "USD"
    node_EUR_USD_1Y_value.nodeId == "1Y_EUR/USDV"
    node_EUR_USD_1Y_value.delta1 == 1
    node_EUR_USD_1Y_value.delta2 == 2

    def node_EUR_USD_2M_value = result.list.find { it.expiry == "2M" }
    node_EUR_USD_2M_value.expiry == "2M"
    node_EUR_USD_2M_value.domesticCurrency == "EUR"
    node_EUR_USD_2M_value.foreignCurrency == "USD"
    node_EUR_USD_2M_value.nodeId == "2M_EUR/USDV"
    node_EUR_USD_2M_value.delta1 == 10
    node_EUR_USD_2M_value.delta2 == 20
  }


  def "should load volatility node values views"() {
    setup:
    def volAtm2m = withKeyAndVal("2M_EUR/USDV", BigDecimal.valueOf(10))
    def volAtm1y = withKeyAndVal("1Y_EUR/USDV", BigDecimal.valueOf(20))
    def volD1S = withKeyAndVal("1Y_EUR/USD10B", BigDecimal.valueOf(30))
    def volD1R = withKeyAndVal("1Y_EUR/USD10R", BigDecimal.valueOf(40))
    def volD2S = withKeyAndVal("1Y_EUR/USD20B", BigDecimal.valueOf(50))
    def volD2R = withKeyAndVal("1Y_EUR/USD20R", BigDecimal.valueOf(60))

    def marketDataVolatilities = [(volAtm2m.key): volAtm2m,
      (volAtm1y.key): volAtm1y,
      (volD1S.key)  : volD1S,
      (volD1R.key)  : volD1R,
      (volD2S.key)  : volD2S,
      (volD2R.key)  : volD2R]

    def group = curveGroup()
    operations.insert(group)

    def valuationDate = LocalDate.of(2023, 1, 1)
    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .nodes([
        new CurveGroupFxVolatilityNode(expiry: "1Y",
        domesticCurrency: "EUR",
        foreignCurrency: "USD",
        delta1: 10,
        delta2: 20,),
        new CurveGroupFxVolatilityNode(expiry: "2M",
        domesticCurrency: "EUR",
        foreignCurrency: "USD")
      ] as Set).build()
    operations.insert(volatility)

    when:
    def result = repository.getVolatilityNodesValuesViews(group.id, now(), valuationDate, marketDataVolatilities)

    then:
    result.versionDate == volatility.validFrom
    result.list.size() == volatility.nodes.size()

    def node_EUR_USD_1Y_value = result.list.find { it.expiry == "1Y" }
    node_EUR_USD_1Y_value.expiry == "1Y"
    // Valuation date is 2023-01-01, so spot date is 2023-01-03 (2023-01-02 is not an EU holiday, 2023-01-03 is not a US holiday)
    // 1Y settlement date is 2024-01-03 therefore expiry date is 2023-12-29
    node_EUR_USD_1Y_value.expiryDate == LocalDate.of(2023, 12, 29)
    node_EUR_USD_1Y_value.domesticCurrency == "EUR"
    node_EUR_USD_1Y_value.foreignCurrency == "USD"
    node_EUR_USD_1Y_value.nodeId == "1Y_EUR/USDV"
    node_EUR_USD_1Y_value.delta1 == 10
    node_EUR_USD_1Y_value.delta2 == 20
    node_EUR_USD_1Y_value.value == 20d
    node_EUR_USD_1Y_value.deltaVolatility1StrangleValue == 30d
    node_EUR_USD_1Y_value.deltaVolatility1RiskReversalValue == 40d
    node_EUR_USD_1Y_value.deltaVolatility2StrangleValue == 50d
    node_EUR_USD_1Y_value.deltaVolatility2RiskReversalValue == 60d

    def node_EUR_USD_2M_value = result.list.find { it.expiry == "2M" }
    node_EUR_USD_2M_value.expiry == "2M"
    node_EUR_USD_2M_value.domesticCurrency == "EUR"
    node_EUR_USD_2M_value.foreignCurrency == "USD"
    node_EUR_USD_2M_value.nodeId == "2M_EUR/USDV"
    node_EUR_USD_2M_value.delta1 == null
    node_EUR_USD_2M_value.delta2 == null
    node_EUR_USD_2M_value.value == 10d
    node_EUR_USD_2M_value.deltaVolatility1StrangleValue == null
    node_EUR_USD_2M_value.deltaVolatility1RiskReversalValue == null
    node_EUR_USD_2M_value.deltaVolatility2StrangleValue == null
    node_EUR_USD_2M_value.deltaVolatility2RiskReversalValue == null
  }

  def "should load volatility node skews (deltas) views"() {
    setup:
    def group = curveGroup()
    operations.insert(group)

    def node1 = new CurveGroupFxVolatilityNodeBuilder().expiry("1Y").build()
    def node2 = new CurveGroupFxVolatilityNodeBuilder().expiry("2M").delta1(null).build()
    def node3 = new CurveGroupFxVolatilityNodeBuilder().foreignCurrency("GBP").build()
    def nodeWithNodeDeltas = new CurveGroupFxVolatilityNodeBuilder()
      .foreignCurrency("AUD")
      .delta1(null)
      .delta2(null)
      .build() // Should not be returned

    def volatility = new CurveGroupFxVolatilityBuilder()
      .entityId(group.id)
      .nodes([node1, node2, node3, nodeWithNodeDeltas] as Set)
      .build()
    operations.insert(volatility)

    when:
    def result = repository.getVolatilitySkewViews(group.id, now())

    then:
    result.size() == 2
    result[0].domesticCurrency == "EUR"
    result[0].foreignCurrency == "GBP"
    result[0].delta1 == 1
    result[0].delta2 == 2
    result[0].numberOfPoints == 1
    result[1].domesticCurrency == "EUR"
    result[1].foreignCurrency == "USD"
    result[1].delta1 == 1
    result[1].delta2 == 2
    result[1].numberOfPoints == 2
  }


  def static assertNodes(CurveGroupFxVolatility volatility, CurveGroupFxVolatilityForm form) {
    volatility.nodes.size() == (form.nodes == null ? 0 : form.nodes.size())
    volatility.nodes.each { node ->
      null != form.getNodes().find { nodeForm ->
        nodeForm.with {
          expiry == node.expiry
          domesticCurrency == node.domesticCurrency
          foreignCurrency == node.foreignCurrency
        }
      }
    }
    volatility.nodes.each { node ->
      null != form.getSkews().find { skewForm ->
        skewForm.with {
          domesticCurrency == node.domesticCurrency
          foreignCurrency == node.foreignCurrency
          delta1 == node.delta1
          delta2 == node.delta2
        }
      }
    }
  }

  def assertAutofilledFields(CurveGroupFxVolatility volatility) {
    volatility.id != null
    volatility.recordDate != null
    volatility.modifiedAt != null
    volatility.modifiedBy != null
    volatility.modifiedBy.name == user.name
  }

  private List<CurveGroupFxVolatility> allSortedValidFromDesc(String entityId) {
    operations
      .query(CurveGroupFxVolatility).matching(query(where("entityId").is(entityId)).with(Sort.by(DESC, "validFrom", "recordDate")))
      .all()
  }
}
