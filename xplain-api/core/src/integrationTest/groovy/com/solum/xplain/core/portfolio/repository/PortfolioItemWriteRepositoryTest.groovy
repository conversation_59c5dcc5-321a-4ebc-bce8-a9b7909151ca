package com.solum.xplain.core.portfolio.repository

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.MAX_DATE
import static com.solum.xplain.core.common.versions.VersionedDataAggregations.MAX_DATE_TIME
import static com.solum.xplain.core.portfolio.CoreProductType.CAP_FLOOR
import static com.solum.xplain.core.portfolio.CoreProductType.FXFWD
import static com.solum.xplain.core.portfolio.CoreProductType.IRS
import static com.solum.xplain.core.portfolio.PortfolioBuilder.portfolio
import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.allocationTrade
import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.irsPortfolioItem
import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.toData
import static com.solum.xplain.core.users.UserBuilder.user
import static java.time.LocalDate.now
import static java.time.LocalDate.ofEpochDay
import static java.time.LocalDate.parse

import com.opengamma.strata.basics.date.BusinessDayConventions
import com.opengamma.strata.basics.date.DayCounts
import com.opengamma.strata.basics.index.IborIndices
import com.opengamma.strata.basics.schedule.StubConvention
import com.opengamma.strata.product.common.PayReceive
import com.solum.xplain.core.common.value.ArchiveEntityForm
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion
import com.solum.xplain.core.company.entity.CompanyReference
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.MarketDataSample
import com.solum.xplain.core.portfolio.ClientMetrics
import com.solum.xplain.core.portfolio.Portfolio
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.PortfolioItemEntity
import com.solum.xplain.core.portfolio.event.PortfolioArchived
import com.solum.xplain.core.portfolio.event.PortfolioItemsStatesUpdated
import com.solum.xplain.core.portfolio.form.CapFloorTradeForm
import com.solum.xplain.core.portfolio.form.ClientMetricsForm
import com.solum.xplain.core.portfolio.form.ExternalIdentifierForm
import com.solum.xplain.core.portfolio.form.FxForwardTradeForm
import com.solum.xplain.core.portfolio.form.IrsTradeForm
import com.solum.xplain.core.portfolio.form.OnboardingDetailsForm
import com.solum.xplain.core.portfolio.form.SwapLegForm
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier
import com.solum.xplain.core.portfolio.trade.OnboardingDetails
import com.solum.xplain.core.portfolio.trade.TradeValue
import com.solum.xplain.core.portfolio.value.CalculationType
import com.solum.xplain.core.portfolio.value.CurrencyAmountForm
import com.solum.xplain.core.portfolio.value.OnboardingVerificationResult
import com.solum.xplain.core.teams.Team
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType
import com.solum.xplain.extensions.enums.CapFloorType
import com.solum.xplain.extensions.enums.PositionType
import jakarta.annotation.Resource
import java.time.Clock
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.mock.DetachedMockFactory

@SpringBootTest
@ActiveProfiles("test")
class PortfolioItemWriteRepositoryTest extends IntegrationSpecification {
  @Resource
  PortfolioItemWriteRepository portfolioItemWriteRepository
  @Resource
  MongoOperations operations
  @Resource
  ApplicationEventPublisher publisher

  def creator = user("userId")

  def setup() {
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), PortfolioItemEntity)
    operations.remove(new Query(), PortfolioItem)
    operations.remove(new Query(), Portfolio)
    operations.remove(new Query(), Team)
  }

  def "should insert swap trade"() {
    setup:
    def portfolio = portfolio(new AuditUser("id", "username", "Full Name"))
    operations.insert(portfolio)
    def form = new IrsTradeForm(
    externalTradeId: "extId",
    leg1: new SwapLegForm(
    payReceive: PayReceive.PAY.name(),
    accrualFrequency: "P3M",
    paymentFrequency: "P3M",
    paymentOffsetDays: 0,
    notionalCurrency: "EUR",
    notionalValue: 1000,
    calculationType: CalculationType.FIXED,
    calculationFixedDayCount: DayCounts.ACT_360.name,
    calculationFixedRateInitialValue: 0.01
    ),
    leg2: new SwapLegForm(
    payReceive: PayReceive.RECEIVE.name(),
    accrualFrequency: "P3M",
    paymentFrequency: "P3M",
    paymentOffsetDays: 0,
    notionalCurrency: "EUR",
    notionalValue: 1000,
    calculationType: CalculationType.IBOR,
    calculationIborDayCount: DayCounts.ACT_360.name,
    calculationIborIndex: IborIndices.EUR_EURIBOR_3M.name,
    calculationIborFixingDateOffsetDays: 0,
    calculationIborSpreadInitialValue: 0
    ),
    endDate: parse("2017-01-01"),
    startDate: parse("2020-01-01"),
    stubConvention: StubConvention.LONG_FINAL.getName(),
    businessDayConvention: BusinessDayConventions.MODIFIED_FOLLOWING.name,
    businessDayAdjustmentType: BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT.name(),
    clientMetrics: new ClientMetricsForm(presentValue: 10.0),
    description: "Comment",
    onboardingDetails: new OnboardingDetailsForm(dealCost: 1, accountingCost: 2, xplainCostCheck: true),
    externalIdentifiers: [new ExternalIdentifierForm("id", "source")],
    versionForm: new NewVersionFormV2("Comment", ofEpochDay(0), ofEpochDay(0), FutureVersionsAction.DELETE)
    )

    1 * publisher.publishEvent({
      it ->
      it instanceof PortfolioItemsStatesUpdated
      && ((PortfolioItemsStatesUpdated) it).getChanges().size() == 1
      && ((PortfolioItemsStatesUpdated) it).getChanges()[0].valuationDataKey().endsWith("_extId")
    })

    when:
    def result = portfolioItemWriteRepository.insert(portfolio.id, form)

    then:
    result.isRight()
    def allItems = operations.query(PortfolioItem).all()
    allItems.size() == 1
    def item = allItems[0]
    item.entityId != null
    item.validFrom == ofEpochDay(0)
    item.validTo == null
    item.recordFrom != null
    item.recordTo == null
    item.validities.size() == 1
    item.validities[0].getValidTo() == MAX_DATE
    item.validities[0].getRecordFrom() != null
    item.validities[0].getRecordTo() == MAX_DATE_TIME
    item.productType == IRS
    item.clientMetrics == new ClientMetrics(presentValue: 10.0)
    item.onboardingDetails == new OnboardingDetails(dealCost: 1, accountingCost: 2, xplainCostCheck: true, vendorCheck: false, marketConfCheck: false)
    item.externalIdentifiers == [new ExternalIdentifier("id", "source")]
    item.modifiedAt != null
    item.modifiedBy.name == "Full Name"
    item.description == "Comment"
  }

  def "should insert cap floor trade"() {
    setup:
    def portfolio = portfolio(new AuditUser("id", "username", "Full Name"))
    operations.insert(portfolio)
    def form = new CapFloorTradeForm(externalTradeId: "extId",
    position: PositionType.SELL.name(),
    type: CapFloorType.CAP.name(),
    strike: 1,
    premiumDateConvention: BusinessDayConventions.MODIFIED_FOLLOWING.name,
    businessDayConvention: BusinessDayConventions.MODIFIED_FOLLOWING.name,
    premiumDate: parse("2016-01-01"),
    premiumValue: 2,
    startDate: parse("2016-01-02"),
    endDate: parse("2017-01-02"),
    notionalCurrency: EUR.code,
    notionalValue: 3,
    calculationIborDayCount: DayCounts.ACT_360,
    calculationIborIndex: IborIndices.EUR_EURIBOR_1M,
    calculationIborSpreadInitialValue: 5,
    paymentOffsetDays: 0,
    clientMetrics: new ClientMetricsForm(presentValue: 10.0),
    description: "Comment",
    versionForm: new NewVersionFormV2("Comment", ofEpochDay(0), ofEpochDay(0), FutureVersionsAction.DELETE))

    when:
    def result = portfolioItemWriteRepository.insert(portfolio.id, form)

    then:
    result.isRight()
    def allItems = operations.query(PortfolioItem).all()
    allItems.size() == 1
    def item = allItems[0]
    item.entityId != null
    item.validFrom == ofEpochDay(0)
    item.validTo == null
    item.recordFrom != null
    item.recordTo == null
    item.validities.size() == 1
    item.validities[0].getValidTo() == MAX_DATE
    item.validities[0].getRecordFrom() != null
    item.validities[0].getRecordTo() == MAX_DATE_TIME
    item.productType == CAP_FLOOR
    item.clientMetrics == new ClientMetrics(presentValue: 10.0)
    item.modifiedAt != null
    item.modifiedBy.name == "Full Name"
    item.description == "Comment"
  }

  def "should insert fx forward trade"() {
    setup:
    def portfolio = portfolio(new AuditUser("id", "username", "Full Name"))
    operations.insert(portfolio)
    def form = new FxForwardTradeForm(externalTradeId: "extId",
    paymentDate: parse("2017-01-01"),
    businessDayConvention: BusinessDayConventions.MODIFIED_FOLLOWING.name,
    domesticCurrencyAmount: new CurrencyAmountForm(amount: 10,
    currency: "EUR"),
    foreignCurrencyAmount: new CurrencyAmountForm(amount: -20,
    currency: "USD"),
    clientMetrics: new ClientMetricsForm(presentValue: 10.0),
    description: "Comment",
    versionForm: new NewVersionFormV2("Comment", ofEpochDay(0), ofEpochDay(0), FutureVersionsAction.DELETE))

    when:
    def result = portfolioItemWriteRepository.insert(portfolio.id, form)

    then:
    result.isRight()
    def allItems = operations.query(PortfolioItem).all()
    allItems.size() == 1
    def item = allItems[0]
    item.entityId != null
    item.validFrom == ofEpochDay(0)
    item.validTo == null
    item.recordFrom != null
    item.recordTo == null
    item.validities.size() == 1
    item.validities[0].getValidTo() == MAX_DATE
    item.validities[0].getRecordFrom() != null
    item.validities[0].getRecordTo() == MAX_DATE_TIME
    item.productType == FXFWD
    item.clientMetrics == new ClientMetrics(presentValue: 10.0)
    item.modifiedAt != null
    item.modifiedBy.name == "Full Name"
    item.description == "Comment"
  }

  def "should update portfolio item with swap trade"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def item = irsPortfolioItem(portfolio.id)
    def data = toData(item)
    operations.save(data)

    item.productType = IRS
    def form = new IrsTradeForm(externalTradeId: "tradeId",
    leg1: new SwapLegForm(payReceive: PayReceive.PAY.name(),
    accrualFrequency: "P3M",
    paymentFrequency: "P3M",
    paymentOffsetDays: 0,
    notionalCurrency: "EUR",
    notionalValue: 1000,
    calculationType: CalculationType.FIXED,
    calculationFixedDayCount: DayCounts.ACT_360.name,
    calculationFixedRateInitialValue: 0.01

    ),
    leg2: new SwapLegForm(payReceive: PayReceive.RECEIVE.name(),
    accrualFrequency: "P3M",
    paymentFrequency: "P3M",
    paymentOffsetDays: 0,
    notionalCurrency: "EUR",
    notionalValue: 1000,
    calculationType: CalculationType.IBOR,
    calculationIborDayCount: DayCounts.ACT_360.name,
    calculationIborIndex: IborIndices.EUR_EURIBOR_3M.name,
    calculationIborFixingDateOffsetDays: 0,
    calculationIborSpreadInitialValue: 0

    ),
    endDate: parse("2017-01-01"),
    startDate: parse("2020-01-01"),
    stubConvention: StubConvention.LONG_FINAL.getName(),
    businessDayConvention: BusinessDayConventions.MODIFIED_FOLLOWING.name,
    businessDayAdjustmentType: BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT.name(),
    clientMetrics: new ClientMetricsForm(presentValue: 10.0),
    description: "Comment",
    versionForm: new NewVersionFormV2("version comment", MarketDataSample.VAL_DT, MarketDataSample.VAL_DT, FutureVersionsAction.DELETE))
    0 * publisher.publishEvent({ it -> it instanceof PortfolioItemsStatesUpdated })

    when:
    def result = portfolioItemWriteRepository.update(portfolio.id, data.id, item.validFrom, form)
    def loaded = operations.query(PortfolioItem).all().sort({ it.getRecordFrom() })
    then:
    result.isRight()
    loaded.size() == 2
    loaded[1].productType == IRS
    loaded[1].clientMetrics == new ClientMetrics(presentValue: 10.0)
    loaded[1].modifiedAt != null
    loaded[1].modifiedBy.name == "Full Name"
    loaded[1].description == "Comment"
    loaded[1].recordTo == null
    loaded[1].validFrom == MarketDataSample.VAL_DT
    loaded[1].validTo == null
    loaded[1].recordFrom != null
    loaded[1].recordTo == null
    loaded[1].validities.size() == 1
    loaded[1].validities[0].getValidTo() == MAX_DATE
    loaded[1].validities[0].getRecordFrom() != null
    loaded[1].validities[0].getRecordTo() == MAX_DATE_TIME
    loaded[1].comment == "version comment"
    loaded[1].state == State.ACTIVE
  }

  def "should update portfolio item onboarding checks"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def item = irsPortfolioItem(portfolio.id)
    item.setOnboardingDetails(new OnboardingDetails(xplainCostCheck: true, vendorCheck: true))
    def data = toData(item)
    operations.save(data)

    def verificationResult = new OnboardingVerificationResult(true, false, false)
    def form = new NewVersionFormV2("version comment", item.validFrom, item.validFrom, FutureVersionsAction.KEEP)

    when:
    def result = portfolioItemWriteRepository.updateConformityChecks(portfolio.id, data.id, item.validFrom, item.recordFrom, verificationResult, form)
    def loaded = operations.query(PortfolioItem).all().sort({ it.getRecordFrom() })

    then:
    result.isRight()
    loaded.size() == 2
    loaded[1].onboardingDetails == new OnboardingDetails(xplainCostCheck: false, vendorCheck: true, xplainCheckVerified: LocalDate.now())
  }

  def "should archive portfolio item"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def item = irsPortfolioItem(portfolio.id)
    def data = toData(item)
    data.getVersions().add(new EmbeddedVersion<TradeValue>(now().plusDays(1), item.getRecordFrom(), item.getState(), item.getComment(), null, data.getVersions()[0].getValue()))
    operations.insertAll([data])

    1 * publisher.publishEvent({
      it ->
      it instanceof PortfolioItemsStatesUpdated
      && ((PortfolioItemsStatesUpdated) it).getChanges().size() == 4
      && ((PortfolioItemsStatesUpdated) it).getChanges()[0].valuationDataKey().endsWith(data.getExternalTradeId())
    })

    when:
    def result = portfolioItemWriteRepository.archiveItem(portfolio.id,
    data.id,
    item.validFrom,
    new ArchiveEntityForm(new NewVersionFormV2("Comment", now(), now(), FutureVersionsAction.DELETE)))

    then:
    result.isRight()
    def items = operations.findAll(PortfolioItem)
    items.size() == 4
    items.stream()
    .filter({
      v -> v.getId() != item.getId()
    })
    .filter({
      v -> v.state == State.ARCHIVED
    })
    .filter({
      v -> v.validFrom == now()
    })
    .filter({
      v -> v.recordFrom != item.getRecordFrom()
    })
    .findFirst()
    .isPresent()
    items.stream()
    .filter({
      v -> v.getId() != item.getId()
    })
    .filter({
      v -> v.state == State.DELETED
    })
    .filter({
      v -> v.validFrom == now().plusDays(1)
    })
    .findFirst()
    .isPresent()
  }

  def "should mark trades as portfolio archived"() {
    setup:
    def view = irsPortfolioItem()
    def entity = toData(view)
    def viewOtherPortfolio = irsPortfolioItem("000000000000000000000002")
    def entityOtherPortfolio = toData(viewOtherPortfolio)
    operations.save(view)
    operations.save(entity)
    operations.save(viewOtherPortfolio)
    operations.save(entityOtherPortfolio)

    def now = LocalDateTime.of(2022, 1, 1, 1, 1)
    def clock = Clock.fixed(now.toInstant(ZoneOffset.UTC), ZoneOffset.UTC)
    portfolioItemWriteRepository.setClock(clock)

    when:
    portfolioItemWriteRepository.onPortfolioArchived(PortfolioArchived.newOf(Mock(CompanyReference, {
      getEntityId() >> "000000000000000000000001"
    }), view.getPortfolioId().toString()))

    then:
    def views = operations.findAll(PortfolioItem)
    views.size() == 2
    views.find { it.getPortfolioId() == view.portfolioId }.getPortfolioArchivedAt() == now
    views.find { it.getPortfolioId() != view.portfolioId }.getPortfolioArchivedAt() == null

    def entities = operations.findAll(PortfolioItemEntity)
    entities.size() == 2
    entities.find { it.getPortfolioId() == entity.portfolioId }.getPortfolioArchivedAt() == now
    entities.find { it.getPortfolioId() != entity.portfolioId }.getPortfolioArchivedAt() == null
  }

  def "should delete portfolio item version"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    def item1_1 = irsPortfolioItem(portfolio.id)
    def item2_1 = irsPortfolioItem(portfolio.id, "anotherTradeId")

    def data1 = toData(item1_1)
    def data1Value = data1.getVersions()[0].getValue()
    data1.getVersions().add(new EmbeddedVersion<TradeValue>(MarketDataSample.VAL_DT, LocalDateTime.now(), item1_1.getState(), item1_1.getComment(), null, data1Value))
    data1.getVersions().add(new EmbeddedVersion<TradeValue>(MarketDataSample.VAL_DT, LocalDateTime.now(), item1_1.getState(), item1_1.getComment(), null, data1Value))
    def data2 = toData(item2_1, MarketDataSample.VAL_DT)
    operations.insertAll([data1, data2])

    1 * publisher.publishEvent({
      it ->
      it instanceof PortfolioItemsStatesUpdated
      && ((PortfolioItemsStatesUpdated) it).getChanges().size() == 3
      && ((PortfolioItemsStatesUpdated) it).getChanges()[0].valuationDataKey().endsWith(data1.getExternalTradeId())
    })

    when:
    def result = portfolioItemWriteRepository.deleteItem(portfolio.id,
    data1.id,
    MarketDataSample.VAL_DT)

    then:
    result.isRight()
    result.getOrNull().id == data1.id
    def loaded = operations.query(PortfolioItem).all()
    loaded.size() == 4
    loaded.stream()
    .filter({
      p -> p.state == State.DELETED
    })
    .filter({
      p -> p.validFrom == MarketDataSample.VAL_DT
    })
    .count() == 1

    loaded.stream()
    .filter({
      p -> p.state == State.ACTIVE
    })
    .count() == 3
  }

  def "should update entities views after reference trade updates"() {
    setup:
    def portfolio = portfolio()
    operations.insert(portfolio)
    operations.save(toData(allocationTrade(portfolio.id, "allocated")))

    when:
    portfolioItemWriteRepository.updateViewsAfterReferenceTradesUpdated(["refId"])

    then:
    def loaded = operations.query(PortfolioItem).all()
    loaded.size() == 1
    loaded[0].getExternalTradeId() == "allocated"
  }

  def "should stream all entities for update"() {
    setup:
    def entity = toData(irsPortfolioItem())
    operations.save(entity)

    when:
    def result = portfolioItemWriteRepository.streamEntitiesForUpdate([entity.getPortfolioId()], MarketDataSample.VAL_DT).toList()

    then:
    result.size() == 1
    result[0].getEntity() == entity
    result[0].getVersion() == entity.getVersions()[0]
  }

  def "should fetch all entities for update"() {
    setup:
    def entity = toData(irsPortfolioItem())
    operations.save(entity)

    when:
    def result = portfolioItemWriteRepository.entitiesForUpdate(entity.getPortfolioId(), MarketDataSample.VAL_DT)

    then:
    result.size() == 1
    result[0].getEntity() == entity
    result[0].getVersion() == entity.getVersions()[0]
  }

  def "should fetch only allocated entities for update"() {
    setup:
    def entity = toData(irsPortfolioItem())
    def entityOtherPortfolio = toData(irsPortfolioItem("000000000000000000000002"))
    def allocated = toData(allocationTrade())
    def allocatedOtherPortfolio = toData(allocationTrade("000000000000000000000002"))
    operations.save(entity)
    operations.save(entityOtherPortfolio)
    operations.save(allocated)
    operations.save(allocatedOtherPortfolio)

    when:
    def result = portfolioItemWriteRepository.entitiesForUpdate(null, MarketDataSample.VAL_DT, true, null)

    then:
    result.size() == 2
    result[0].getEntity() == allocated
    result[0].getVersion() == allocated.getVersions()[0]
  }

  def "should fetch only allocated entity by refTradeId for update"() {
    setup:
    def entity = toData(irsPortfolioItem())
    def allocated1 = toData(allocationTrade())
    def trade = allocationTrade()
    trade.getAllocationTradeDetails().setReferenceTradeId("TESTID")
    def allocated2 = toData(trade)
    operations.save(entity)
    operations.save(allocated1)
    operations.save(allocated2)

    when:
    def result = portfolioItemWriteRepository.entitiesForUpdate(null, MarketDataSample.VAL_DT, true, "TESTID")

    then:
    result.size() == 1
    result[0].getEntity() == allocated2
    result[0].getVersion() == allocated2.getVersions()[0]
  }

  @TestConfiguration
  static class PortfolioItemWriteRepositoryTestConfig {

    private DetachedMockFactory factory = new DetachedMockFactory()

    @Bean
    @Primary
    ApplicationEventPublisher publisher() {
      factory.Mock(ApplicationEventPublisher)
    }
  }
}
