package com.solum.xplain.core.market.repository

import static com.solum.xplain.core.classifiers.pricingslots.PricingSlot.LDN_1200
import static com.solum.xplain.core.classifiers.pricingslots.PricingSlot.LDN_1615
import static org.springframework.data.domain.Sort.Direction.ASC

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.value.AllowedCompaniesForm
import com.solum.xplain.core.common.value.AllowedTeamsForm
import com.solum.xplain.core.company.CompanyBuilder
import com.solum.xplain.core.company.entity.Company
import com.solum.xplain.core.company.entity.CompanyReference
import com.solum.xplain.core.company.events.CompanyUpdated
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.market.MarketDataGroup
import com.solum.xplain.core.market.MarketDataGroupBuilder
import com.solum.xplain.core.market.filter.MarketDataGroupFilter
import com.solum.xplain.core.market.value.MarketDataGroupForm
import com.solum.xplain.core.mdvalue.entity.MarketDataValue
import com.solum.xplain.core.teams.TeamBuilder
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.core.users.events.UserUpdated
import com.solum.xplain.core.users.value.EditUserForm
import com.solum.xplain.shared.utils.filter.FilterOperation
import com.solum.xplain.shared.utils.filter.SimpleFilterClause
import com.solum.xplain.shared.utils.filter.TableFilter
import jakarta.annotation.Resource
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles
import spock.lang.Unroll

@SpringBootTest
@ActiveProfiles("test")
class MarketDataGroupRepositoryTest extends IntegrationSpecification {

  @Resource
  MarketDataGroupRepository repository

  @Resource
  AuthenticationContext userRepository

  @Resource
  MongoOperations operations

  XplainPrincipal ownerUser
  XplainPrincipal teamMember
  static team = new TeamBuilder().id(new ObjectId()).name("Test team").build()

  def setup() {
    ownerUser = UserBuilder.userWithNameAndTeams("ownerId", "Owner Name", [team.getId()])
    teamMember = UserBuilder.userWithNameAndTeams("teamMemberId", "Team Member", [team.getId()])

    def auth = new TestingAuthenticationToken(ownerUser, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), MarketDataGroup)
    operations.remove(new Query(), MarketDataValue)
    operations.remove(new Query(), Company)
  }

  def "should serialize and store market data"() {
    setup:
    def definition = new MarketDataGroupBuilder().build()
    operations.insert(definition)
    when:
    def loaded = repository.dataGroupName(definition.id)
    then:
    loaded.get().id == definition.id
    loaded.get().name == definition.name
  }

  def "should return market group list"() {
    setup:
    def company = new Company(name: "CompanyName", externalCompanyId: "companyExtId", id: "id")
    def company2 = new Company(name: "CompanyName2", externalCompanyId: "companyExtId2", id: "id2")
    operations.insertAll([company, company2])
    def marketDataGroup = new MarketDataGroupBuilder()
      .name("Test")
      .pricingSlot(LDN_1200)
      .teamIds(ownerUser.teams)
      .companies([CompanyReference.of(company), CompanyReference.of(company2)])
      .allowAllCompanies(false)
      .build()

    operations.insert(marketDataGroup)
    when:
    def result = repository.groupScrollable(
      ownerUser,
      ScrollRequest.of(0, 10, Sort.by(ASC, "id")),
      new MarketDataGroupFilter(false, []),
      new TableFilter([
        new SimpleFilterClause("updatedAt", FilterOperation.GREATER_THAN,
        marketDataGroup.modifiedAt.toLocalDate().toString())
      ])
      )

    then:
    result.content.size() == 1
    result.getContent().get(0).name == "Test"
    result.getContent().get(0).pricingSlot == LDN_1200
    result.getContent().get(0).createdBy == ownerUser.name
    result.getContent().get(0).createdAt != null
    result.getContent().get(0).teamIds.size() == 1
    result.getContent().get(0).teamIds[0] == team.id.toString()
    result.getContent().get(0).companyNames.size() == 2
    result.getContent().get(0).companyExternalIds.size() == 2
    result.getContent().get(0).companyIds.size() == 2
    result.getContent().get(0).companyNames[0] == "CompanyName"
    result.getContent().get(0).companyNames[1] == "CompanyName2"
  }

  @Unroll
  def "should return filtered market group list"() {
    setup:
    def mdg = insertMarketGroup()
    def mdg2 = new MarketDataGroupBuilder()
      .name("Test")
      .teamIds(ownerUser.teams)
      .companies([new CompanyReference(entityId: "000000000000000000000000")])
      .allowAllCompanies(false)
      .build()
    def mdg3 = new MarketDataGroupBuilder()
      .name("Test")
      .teamIds(ownerUser.teams)
      .allowAllCompanies(true)
      .build()

    operations.insertAll([mdg2, mdg3])

    when:
    def result = repository.groupScrollable(
      ownerUser,
      ScrollRequest.of(0, 10, Sort.by(ASC, "id")),
      filter,
      new TableFilter([
        new SimpleFilterClause("updatedAt", FilterOperation.GREATER_THAN,
        mdg.modifiedAt.toLocalDate().toString())
      ])
      )

    then:
    result.content.size() == size

    where:
    filter                                                         | size
    new MarketDataGroupFilter(false, [])                           | 3
    new MarketDataGroupFilter(false, ["000000000000000000000000"]) | 2
    new MarketDataGroupFilter(false, ["111111111111111111111111"]) | 1
  }

  def "should return market data group condensed list"() {
    setup:
    def marketDataGroup = new MarketDataGroup(
      name: "Test",
      teamIds: ownerUser.teams,
      companies: [new CompanyReference(entityId: "000000000000000000000000")]
      )

    operations.insert(marketDataGroup)

    when:
    def result = repository.groupNameList(
      ownerUser,
      companyIds
      )

    then:
    result.size() == 1
    result.get(0).name == "Test"
    result.get(0).id == marketDataGroup.id

    where:
    companyIds                   | _
    null                         | _
    ["000000000000000000000000"] | _
  }

  def "should return market data group"() {
    setup:
    def group = insertMarketGroup()

    when:
    def result = repository.userDataGroup(ownerUser, group.id)

    then:
    result.isRight()
    def groupView = result.getOrNull()
    groupView.name == "Test"
    groupView.companyNames[0] == "CompanyName"
    groupView.companyIds[0] != null
    groupView.teamIds.size() == 1
    groupView.teamIds[0] == team.id.toString()
    groupView.modifiedBy == "Owner Name"
    groupView.updatedAt != null
    groupView.auditLogs.size() == 0
  }

  def "should return market data group entity"() {
    setup:
    def group = insertMarketGroup()

    when:
    def result = repository.validEntity(group.id)

    then:
    result.isRight()
    result.getOrNull().id == group.id
    result.getOrNull().name == "Test"
    result.getOrNull().companies.size() == 1
    result.getOrNull().allowAllCompanies != null
    result.getOrNull().teamIds.size() == 1
    result.getOrNull().teamIds[0] == team.id
    result.getOrNull().modifiedBy.name == "Owner Name"
    result.getOrNull().modifiedAt != null
  }

  def "should return error when archived"() {
    setup:
    def group = new MarketDataGroupBuilder()
      .name("Test")
      .teamIds(ownerUser.teams)
      .archived(true)
      .allowAllCompanies(false)
      .build()

    when:
    def result = repository.validEntity(group.id)

    then:
    result.isLeft()
    result.left().get() == Error.OBJECT_NOT_FOUND.entity("Object not found [MarketDataGroupView]")
  }

  def "should insert market data group"() {
    setup:
    def teamId = new ObjectId()
    def company = new Company()
    company.setName("CompanyName")
    company.setExternalCompanyId("extId")
    operations.insert(company)
    def form = new MarketDataGroupForm("Name", LDN_1200, new AllowedCompaniesForm(false, [company.id]),
    new AllowedTeamsForm(false, [teamId.toString()]))

    when:
    def result = repository.insert(form)

    then:
    result.isRight()
    result.getOrNull().id != null
    def loaded = operations.findById(result.getOrNull().id, MarketDataGroup)
    loaded.name == "Name"
    loaded.pricingSlot == LDN_1200
    loaded.id != null
    loaded.modifiedBy.username == ownerUser.name
    loaded.modifiedAt != null
    loaded.createdBy == AuditUser.of(ownerUser)
    loaded.createdAt != null
    loaded.teamIds == [teamId]
    loaded.companies[0].entityId == form.allowedCompaniesForm.getCompanyIds()[0]
    loaded.companies[0].name == "CompanyName"
    loaded.companies[0].externalCompanyId == "extId"
  }

  def "should insert market data group without company and team"() {
    setup:
    def form = new MarketDataGroupForm("Name", LDN_1615, new AllowedCompaniesForm(true, null),
      new AllowedTeamsForm(true, null))

    when:
    def result = repository.insert(form)

    then:
    result.isRight()
    result.getOrNull().id != null
    def loaded = operations.findById(result.getOrNull().id, MarketDataGroup)
    loaded.name == "Name"
    loaded.pricingSlot == LDN_1615
    loaded.id != null
    loaded.modifiedBy.username == ownerUser.name
    loaded.modifiedAt != null
    loaded.teamIds == []
    loaded.companies == null
  }

  def "should archive market data group"() {
    setup:
    def group = insertMarketGroup()

    when:
    def result = repository.archive(group.id)

    then:
    result.isRight()
    result.getOrNull().id == group.id
    def updated = operations.findById(group.id, MarketDataGroup)
    updated.archived
    updated.auditLogs.size() == 1
  }

  def "should update market data group"() {
    setup:
    def marketGroup = insertMarketGroup()

    def userTeams = ownerUser.teams.collect({ it.toString() })
    def form = new MarketDataGroupForm(
      "Test2",
      null,
      new AllowedCompaniesForm(false, [ObjectId.get().toHexString()]),
      new AllowedTeamsForm(true, userTeams)
      )

    when:
    def result = repository.update(form, marketGroup.id)

    then:
    result.isRight()
    result.getOrNull().id == marketGroup.id

    when:
    def updatedGroup = operations.findById(marketGroup.id, MarketDataGroup)

    then:
    updatedGroup != null
    updatedGroup.name == "Test2"
    updatedGroup.pricingSlot == null
    updatedGroup.teamIds == ownerUser.teams
    updatedGroup.auditLogs.size() == 1
  }

  def insertMarketGroup() {
    def company = new Company(name: "CompanyName", externalCompanyId: "companyExtId", id: "id")
    operations.insert(company)
    def marketDataGroup = new MarketDataGroupBuilder()
      .name("Test")
      .teamIds(ownerUser.teams)
      .companies([CompanyReference.of(company)])
      .allowAllCompanies(false)
      .build()

    operations.insert(marketDataGroup)

    return marketDataGroup
  }


  def "should update modifier name on user update event"() {
    setup:
    def marketDataGroup = insertMarketGroup()

    def form = new EditUserForm(
      name: "Updated name",
      username: "updated_username"
      )

    when:
    repository.onEvent(new UserUpdated(ownerUser.id, form))

    then:
    def result = operations.findById(marketDataGroup.id, MarketDataGroup)
    result.modifiedBy.name == "Updated name"
    result.modifiedBy.username == "updated_username"
  }


  @Unroll
  def "should check if curve exists with name #name id #id exlcluding #excludeSelfId then #result"() {
    setup:
    def marketDataGroup = new MarketDataGroupBuilder()
      .id(id)
      .name("sameName")
      .teamIds(ownerUser.teams)
      .build()

    def marketDataGroup2 = new MarketDataGroupBuilder()
      .name("archivedName")
      .teamIds(ownerUser.teams)
      .archived(true)
      .build()

    operations.insertAll([marketDataGroup, marketDataGroup2])

    expect:
    repository.existsByName(name, excludeSelfId) == result

    where:
    name           | id   | excludeSelfId | result
    "name"         | "id" | null          | false
    "sameName"     | "id" | null          | true
    "sameName"     | "id" | "id"          | false
    "archivedName" | "id" | null          | false
  }

  def "should update company reference on company update"() {
    setup:
    def company = CompanyBuilder.company()
    operations.insert(company)

    def mdg = new MarketDataGroupBuilder()
      .name("Test")
      .teamIds(ownerUser.teams)
      .companies([
        new CompanyReference(
        entityId: company.id,
        externalCompanyId: "extId",
        name: "companyName")
      ])
      .allowAllCompanies(false)
      .build()
    operations.insert(mdg)

    when:
    repository.onEvent(new CompanyUpdated(company.id))

    then:
    def result = operations.findById(mdg.id, MarketDataGroup)
    result.companies[0].externalCompanyId == "externalCompanyId"
    result.companies[0].name == "name"
  }

  def "should return error on used company"() {
    setup:
    def company = CompanyBuilder.company()
    operations.insert(company)

    def mdg = new MarketDataGroupBuilder()
      .name("Test")
      .teamIds(ownerUser.teams)
      .companies([
        new CompanyReference(
        entityId: company.id,
        externalCompanyId: "extId",
        name: "companyName")
      ])
      .allowAllCompanies(false)
      .build()

    def archivedMdg = new MarketDataGroupBuilder()
      .name("Archived")
      .teamIds(ownerUser.teams)
      .companies([
        new CompanyReference(
        entityId: company.id,
        externalCompanyId: "extId",
        name: "companyName")
      ])
      .allowAllCompanies(false)
      .archived(true)
      .build()
    operations.insertAll([mdg, archivedMdg])

    expect:
    repository.companyDependencyErrors(company.id) == [Error.OBJECT_IN_USE.entity("Market Data Group: Test")]
    repository.companyDependencyErrors("random") == []
  }
}
