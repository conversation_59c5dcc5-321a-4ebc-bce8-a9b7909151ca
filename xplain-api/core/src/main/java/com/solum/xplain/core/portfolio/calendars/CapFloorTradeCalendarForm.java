package com.solum.xplain.core.portfolio.calendars;

import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.portfolio.validation.CapFloorTradeIndicesSupplier;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springdoc.core.annotations.ParameterObject;

@Data
@ParameterObject
public class CapFloorTradeCalendarForm {

  @NotEmpty
  @ValidStringSet(CapFloorTradeIndicesSupplier.class)
  private final String iborIndex;

  @NotNull private final Boolean isOffshore;
}
