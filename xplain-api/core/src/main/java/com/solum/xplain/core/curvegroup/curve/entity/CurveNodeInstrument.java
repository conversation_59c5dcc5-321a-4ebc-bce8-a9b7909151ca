package com.solum.xplain.core.curvegroup.curve.entity;

import static com.solum.xplain.core.classifiers.CurveNodeTypes.FRA_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FUTURE_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IMM_FRA_NODE;
import static com.solum.xplain.core.utils.TenorUtils.sumOfMonths;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.curvegroup.ParsableTenor;
import com.solum.xplain.core.utils.TenorUtils;

public interface CurveNodeInstrument extends ParsableTenor {

  String getConvention();

  String getSerialFuture();

  String getFraSettlement();

  String getType();

  String getPeriod();

  default String getInstrument() {
    if (FRA_NODE.equals(getType())) {
      var fraPeriod = TenorUtils.fromForwardRateAgreement(getConvention());
      var sumOfMonths =
          sumOfMonths(fraPeriod.toString(), getFraSettlement()).map(Tenor::toString).orElse("");
      return getFraSettlement() + "x" + sumOfMonths;
    } else if (IBOR_FUTURE_NODE.equals(getType()) || IMM_FRA_NODE.equals(getType())) {
      return getSerialFuture();
    } else {
      return getPeriod();
    }
  }

  default String getTenor() {
    return getPeriod();
  }
}
