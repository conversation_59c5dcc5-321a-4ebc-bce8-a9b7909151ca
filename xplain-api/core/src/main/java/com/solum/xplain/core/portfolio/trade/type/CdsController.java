package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.CdsTradeForm;
import com.solum.xplain.core.portfolio.value.CdsTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/cds")
@AllArgsConstructor
public class CdsController implements BespokeTradeTypedController<CdsTradeForm, CdsTradeView> {

  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, CdsTradeView> toViewFunction(PortfolioItem e) {
    return CdsTradeView.of(e);
  }
}
