package com.solum.xplain.core.curvegroup.curve.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = UniqueCurveNodesValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface UniqueCurveNodes {
  String message() default "{com.solum.xplain.api.curve.validation" + ".UniqueCurveNodes.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
