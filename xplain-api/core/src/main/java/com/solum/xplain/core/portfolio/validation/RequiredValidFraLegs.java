package com.solum.xplain.core.portfolio.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = RequiredValidFraLegsValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface RequiredValidFraLegs {

  String message() default
      "{com.solum.xplain.api.portfolio.validation.RequiredValidFraLegs" + ".message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
