package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.portfolio.CoreProductType;

public class Sens01Value {

  private Sens01Value() {}

  public static Double sensitivity01Value(
      CoreProductType tradeInfoTradeType,
      Double metricsDv01,
      Double metricsBr01,
      Double metricsInf01,
      Double metricsCs01) {
    return switch (tradeInfoTradeType) {
      case IRS, FRA, CAP_FLOOR, SWAPTION -> metricsDv01;
      case XCCY, FXFWD, FXOPT, FXSWAP, FXCOLLAR -> metricsBr01;
      case INFLATION -> metricsInf01;
      case CDS, CREDIT_INDEX, CREDIT_INDEX_TRANCHE -> metricsCs01;
      case LOAN_NOTE -> null;
    };
  }
}
