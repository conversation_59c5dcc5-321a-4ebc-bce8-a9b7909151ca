package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.portfolio.value.PortfolioItemRefDetailsView;
import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;

public interface ReferenceTradesProvider {

  Map<String, PortfolioItemRefDetailsView> fetchActiveReferenceTradeExternalIds(
      LocalDate stateDate);

  Optional<PortfolioItemRefDetailsView> fetchReferenceTrade(String id, LocalDate stateDate);

  boolean existsActiveReferenceTrade(String id, LocalDate stateDate, String skipEntityId);
}
