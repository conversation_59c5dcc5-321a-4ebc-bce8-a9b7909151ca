package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.portfolio.CoreProductType;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class PortfolioItemCalculatedExcMngmntView {
  private String tradeInfoExternalTradeId;
  private CoreProductType tradeInfoTradeType;
  private Double metricsPresentValue;
  private Double metricsPresentValuePayLegCurrency;
  private Double metricsDv01;
  private Double metricsDv01LocalCcy;
  private Double metricsBr01;
  private Double metricsBr01LocalCcy;
  private Double metricsInf01;
  private Double metricsInf01LocalCcy;
  private Double metricsCs01;
  private Double metricsCs01LocalCcy;
  private Double metricsPvVega;
  private Double metricsPvVegaLocalCcy;
  private Double metricsBreakevenParRate;
  private Double metricsBreakevenImpliedVol;

  public Double delta(boolean inLocalCcy) {
    Double resolvedDv01 = inLocalCcy ? metricsDv01LocalCcy : metricsDv01;
    Double resolvedBr01 = inLocalCcy ? metricsBr01LocalCcy : metricsBr01;
    Double resolvedInf01 = inLocalCcy ? metricsInf01LocalCcy : metricsInf01;
    Double resolvedCs01 = inLocalCcy ? metricsCs01LocalCcy : metricsCs01;

    return Sens01Value.sensitivity01Value(
        tradeInfoTradeType, resolvedDv01, resolvedBr01, resolvedInf01, resolvedCs01);
  }
}
