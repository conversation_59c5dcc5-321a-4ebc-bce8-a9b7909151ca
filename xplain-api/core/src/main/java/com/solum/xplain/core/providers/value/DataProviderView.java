package com.solum.xplain.core.providers.value;

import static com.solum.xplain.core.providers.DataProvider.INTERNAL_PROVIDER_CODES;

import com.solum.xplain.core.audit.value.AuditLogView;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@EqualsAndHashCode(exclude = {"createdAt", "lastModifiedAt"})
public class DataProviderView {
  private String id;
  private String name;
  private String externalId;
  private String creatorName;
  private String creatorId;
  private String modifiedByName;
  private LocalDateTime createdAt;
  private LocalDateTime lastModifiedAt;
  private List<String> types;
  private List<AuditLogView> auditLogs = new ArrayList<>();

  public boolean isInternalProvider() {
    return INTERNAL_PROVIDER_CODES.contains(externalId);
  }
}
