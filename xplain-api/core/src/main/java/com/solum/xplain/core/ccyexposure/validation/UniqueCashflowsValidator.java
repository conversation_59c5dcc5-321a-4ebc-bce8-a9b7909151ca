package com.solum.xplain.core.ccyexposure.validation;

import static java.util.stream.Collectors.groupingBy;

import com.solum.xplain.core.ccyexposure.value.CashflowView;
import com.solum.xplain.core.ccyexposure.value.CcyExposureWithCashflows;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class UniqueCashflowsValidator
    implements ConstraintValidator<UniqueCashflows, CcyExposureWithCashflows> {

  private static final String DUPLICATE_ERROR_TEMPLATE = "Duplicate cashflow date";

  public boolean isValid(CcyExposureWithCashflows form, ConstraintValidatorContext context) {
    if (form.getCashflows() == null
        || form.getCashflows().stream().anyMatch(n -> n.getDate() == null)) {
      return true;
    }
    var hasDuplicates =
        form.getCashflows().stream().collect(groupingBy(CashflowView::getDate)).entrySet().stream()
            .anyMatch(e -> e.getValue().size() > 1);

    if (hasDuplicates) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(DUPLICATE_ERROR_TEMPLATE)
          .addPropertyNode("cashflows")
          .addConstraintViolation();
      return false;
    }

    return true;
  }
}
