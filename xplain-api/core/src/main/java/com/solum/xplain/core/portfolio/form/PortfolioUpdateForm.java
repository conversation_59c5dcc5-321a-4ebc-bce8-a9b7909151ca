package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.common.validation.ValidObjectId;
import com.solum.xplain.core.common.value.AllowedTeamsForm;
import com.solum.xplain.core.company.form.CompanyTeamPortfolio;
import com.solum.xplain.core.company.validation.ValidCompanyId;
import com.solum.xplain.core.company.validation.ValidCompanyTeams;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@ValidCompanyTeams
public class PortfolioUpdateForm implements CompanyTeamPortfolio {

  @Valid @NotNull private AllowedTeamsForm allowedTeamsForm;

  @NotEmpty @ValidObjectId @ValidCompanyId private String companyId;

  @NotEmpty @ValidObjectId private String entityId;

  @NotEmpty private String name;
  private String description;
}
