package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.CreditIndexTradeForm;
import com.solum.xplain.core.portfolio.value.CreditIndexTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/credit-index")
@AllArgsConstructor
public class CreditIndexController
    implements BespokeTradeTypedController<CreditIndexTradeForm, CreditIndexTradeView> {
  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, CreditIndexTradeView> toViewFunction(PortfolioItem e) {
    return CreditIndexTradeView.of(e);
  }
}
