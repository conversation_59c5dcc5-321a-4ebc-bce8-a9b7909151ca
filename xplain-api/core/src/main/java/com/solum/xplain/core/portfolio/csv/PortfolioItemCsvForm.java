package com.solum.xplain.core.portfolio.csv;

import com.solum.xplain.core.common.versions.embedded.ResolvableEmbeddedVersionValue;
import com.solum.xplain.core.portfolio.PortfolioItemUniqueKey;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PortfolioItemCsvForm
    implements ResolvableEmbeddedVersionValue<TradeValue, PortfolioItemUniqueKey> {
  private final PortfolioItemUniqueKey uniqueKey;
  private final TradeValue item;

  public static PortfolioItemCsvForm newOf(String portfolioId, String id, TradeValue value) {
    return new PortfolioItemCsvForm(PortfolioItemUniqueKey.newOf(portfolioId, id), value);
  }

  public PortfolioItemUniqueKey getUniqueKey() {
    return uniqueKey;
  }

  @Override
  public TradeValue toVersionValue() {
    return item;
  }

  @Override
  public PortfolioItemUniqueKey getEntityId() {
    return getUniqueKey();
  }
}
