package com.solum.xplain.core.providers;

import static com.solum.xplain.core.common.CollectionUtils.join;
import static com.solum.xplain.core.providers.DataProvider.INTERNAL_PROVIDER_CODES;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ImportErrorUtils;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.LoggingImportService;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.core.providers.csv.DataProviderCsvLoader;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class DataProviderCsvImportService extends LoggingImportService {
  private final DataProviderRepository providerRepository;
  private final DataProviderCsvLoader loader;

  public DataProviderCsvImportService(
      AuditEntryService auditEntryService,
      DataProviderRepository providerRepository,
      DataProviderCsvLoader loader) {
    super(auditEntryService);
    this.providerRepository = providerRepository;
    this.loader = loader;
  }

  @Override
  protected String getCollection() {
    return DataProvider.DATA_PROVIDER_COLLECTION;
  }

  @Override
  protected String getObjectName() {
    return "Data providers";
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadMarketDataProviders(
      byte[] csvBytes, ParsingMode parsingMode, DuplicateAction duplicateAction) {
    return loader
        .parse(csvBytes, parsingMode)
        .map(providers -> importProviders(providers, duplicateAction))
        .fold(
            err -> toErrorReturn(duplicateAction, err),
            result -> toReturn(duplicateAction, result));
  }

  private ImportResult importProviders(
      CsvParserResult<DataProvider> result, DuplicateAction action) {
    var importLogs = importProviders(result.getParsedLines(), action);
    return new ImportResult(importLogs, result.getWarnings());
  }

  private List<LogItem> importProviders(List<DataProvider> providers, DuplicateAction action) {
    var importItems =
        ImportItems.<DataProvider, String, DataProvider>builder()
            .existingActiveItems(fetchDataProviders())
            .existingItemToKeyFn(DataProvider::getExternalId)
            .importItems(providers)
            .importItemToKeyFn(DataProvider::getExternalId)
            .build();

    return switch (action) {
      case ERROR -> onError(importItems);
      case REPLACE_DELETE -> onReplaceDelete(importItems);
      case REPLACE -> onReplace(importItems);
      case APPEND_DELETE -> onAppendDelete(importItems);
      case APPEND -> onAppend(importItems);
    };
  }

  private List<LogItem> onError(ImportItems<DataProvider, String, DataProvider> importItems) {
    var errors = validateItems(importItems);
    return asLogItems(errors);
  }

  private List<LogItem> onReplaceDelete(
      ImportItems<DataProvider, String, DataProvider> importItems) {
    var appendResult = append(importItems);
    var replaceResult = replace(importItems);
    var archiveResult = archive(importItems);
    return join(appendResult, replaceResult, archiveResult);
  }

  private List<LogItem> onReplace(ImportItems<DataProvider, String, DataProvider> importItems) {
    var appendResult = append(importItems);
    var replaceResult = replace(importItems);
    return join(appendResult, replaceResult);
  }

  private List<LogItem> onAppendDelete(
      ImportItems<DataProvider, String, DataProvider> importItems) {
    var appendResult = append(importItems);
    var archiveResult = archive(importItems);
    return join(appendResult, archiveResult);
  }

  private List<LogItem> onAppend(ImportItems<DataProvider, String, DataProvider> importItems) {
    return append(importItems);
  }

  private List<LogItem> append(ImportItems<DataProvider, String, DataProvider> importItems) {
    return importItems.getNewKeys().stream().map(k -> appendItem(k, importItems)).toList();
  }

  private List<LogItem> replace(ImportItems<DataProvider, String, DataProvider> importItems) {
    return importItems.getDuplicateKeys().stream().map(k -> replaceItem(k, importItems)).toList();
  }

  private List<LogItem> archive(ImportItems<DataProvider, String, DataProvider> importItems) {
    return importItems.getSpareKeys().stream().map(k -> archiveItem(k, importItems)).toList();
  }

  private LogItem appendItem(
      String key, ImportItems<DataProvider, String, DataProvider> importItems) {
    var i = importItems.importItem(key);
    var eitherId = insert(i);
    return createInsertLogItem(key, eitherId);
  }

  private LogItem replaceItem(
      String key, ImportItems<DataProvider, String, DataProvider> importItems) {
    var e = importItems.existingItem(key);
    var i = importItems.importItem(key);
    var eitherId = update(e, i);
    return createUpdateLogItem(key, eitherId);
  }

  private LogItem archiveItem(
      String key, ImportItems<DataProvider, String, DataProvider> importItems) {
    var e = importItems.existingItem(key);
    return createArchiveLogItem(key, archive(e.getId()));
  }

  private List<ErrorItem> validateItems(
      ImportItems<DataProvider, String, DataProvider> importItems) {
    return Stream.of(
            errorStream(importItems.getDuplicateKeys(), ImportErrorUtils::duplicateItem),
            errorStream(importItems.getSpareKeys(), ImportErrorUtils::missingItem))
        .flatMap(Function.identity())
        .toList();
  }

  private Stream<ErrorItem> errorStream(Set<String> keys, Function<String, ErrorItem> function) {
    return keys.stream().map(function);
  }

  private Either<ErrorItem, EntityId> update(DataProvider oldProvider, DataProvider newProvider) {
    return providerRepository.update(oldProvider.getId(), newProvider);
  }

  private Either<ErrorItem, EntityId> archive(String id) {
    return providerRepository.archive(id);
  }

  private Either<ErrorItem, EntityId> insert(DataProvider provider) {
    return providerRepository.save(provider);
  }

  private List<DataProvider> fetchDataProviders() {
    return providerRepository.dataProvidersList().stream()
        .filter(v -> !INTERNAL_PROVIDER_CODES.contains(v.getExternalId()))
        .toList();
  }
}
