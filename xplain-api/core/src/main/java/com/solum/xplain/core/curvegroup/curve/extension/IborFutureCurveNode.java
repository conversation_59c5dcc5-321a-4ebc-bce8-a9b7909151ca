/*
 * Copyright (C) 2015 - present by OpenGamma Inc. and the OpenGamma group of companies
 *
 * Please see distribution for license.
 */

package com.solum.xplain.core.curvegroup.curve.extension;

import static com.solum.xplain.core.classifiers.Constants.BANK_BILL_90_DAY_FUTURES;

import com.google.common.collect.ImmutableSet;
import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.FxRateProvider;
import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.date.HolidayCalendarIds;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.date.TenorAdjustment;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.collect.ArgChecker;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.data.ObservableId;
import com.opengamma.strata.market.ValueType;
import com.opengamma.strata.market.curve.CurveNode;
import com.opengamma.strata.market.curve.CurveNodeDate;
import com.opengamma.strata.market.curve.CurveNodeDateOrder;
import com.opengamma.strata.market.observable.QuoteId;
import com.opengamma.strata.market.param.DatedParameterMetadata;
import com.opengamma.strata.market.param.YearMonthDateParameterMetadata;
import com.opengamma.strata.product.SecurityId;
import com.opengamma.strata.product.index.IborFutureTrade;
import com.opengamma.strata.product.index.ResolvedIborFutureTrade;
import com.opengamma.strata.product.index.type.IborFutureTemplate;
import com.solum.xplain.extensions.product.ExtendedIborContractSpecs;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.ToString;

@ToString
@Builder
@EqualsAndHashCode
@AllArgsConstructor
public class IborFutureCurveNode implements CurveNode {

  @NonNull private final IborFutureTemplate template;
  @NonNull private final QuoteId rateId;
  private final double additionalSpread;
  @NonNull private final String label;
  @Builder.Default private final CurveNodeDate date = CurveNodeDate.END;
  @Builder.Default private final CurveNodeDateOrder dateOrder = CurveNodeDateOrder.DEFAULT;

  public static IborFutureCurveNode of(IborFutureTemplate template, QuoteId rateId) {
    return of(template, rateId, 0d);
  }

  public static IborFutureCurveNode of(
      IborFutureTemplate template, QuoteId rateId, double additionalSpread) {

    return of(template, rateId, additionalSpread, "");
  }

  public static IborFutureCurveNode of(
      IborFutureTemplate template, QuoteId rateId, double additionalSpread, String label) {

    return new IborFutureCurveNode(
        template, rateId, additionalSpread, label, CurveNodeDate.END, CurveNodeDateOrder.DEFAULT);
  }

  // -------------------------------------------------------------------------
  @Override
  public Set<ObservableId> requirements() {
    return ImmutableSet.of(rateId);
  }

  /**
   * Returns the date used for calculation zero-coupon rates.
   *
   * @param valuationDate - the valuation date
   * @param refData - the reference data
   * @return the date
   */
  @Override
  public LocalDate date(LocalDate valuationDate, ReferenceData refData) {
    // for 90 day bank bill futures (and CAD), we use the last fixing date, plus the tenor,
    // as per convention for these futures contracts
    if (List.of(
            ExtendedIborContractSpecs.AUD_BBSW_3M_QUARTERLY_FUTURE_NAME,
            ExtendedIborContractSpecs.AUD_BBSW_3M_QUARTERLY_FUTURE_SD_NAME,
            ExtendedIborContractSpecs.NZD_BKBM_3M_QUARTERLY_FUTURE_NAME,
            ExtendedIborContractSpecs.CAD_CDOR_3M_IMM_MSE_NAME)
        .contains(template.getContractSpec().getName())) {
      return calculateZeroCouponDateEnd(calculateLastFixingDate(valuationDate, refData), refData);
    }
    return futureEndInfoDate(valuationDate, refData);
  }

  /**
   * Returns date that represents the delivery date (IMM date) plus the index tenor, used for
   * information purposes only. In some cases, this date is different to the date used for
   * zero-coupon rate calculations, which is returned by {@link #date(LocalDate, ReferenceData)}.
   *
   * @param valuationDate - the valuation date
   * @param refData - the reference data
   * @return the future end date
   */
  public LocalDate futureEndInfoDate(LocalDate valuationDate, ReferenceData refData) {
    LocalDate referenceDate = template.calculateReferenceDateFromTradeDate(valuationDate, refData);
    return date.calculate(
        () -> calculateFutureEndInfoDate(referenceDate, refData),
        () -> calculateLastFixingDate(valuationDate, refData));
  }

  @Override
  public DatedParameterMetadata metadata(LocalDate valuationDate, ReferenceData refData) {
    // x-array dates should be fixing date + standard fixing offset (from ibor fixing)
    LocalDate nodeDate = date(valuationDate, refData);
    LocalDate referenceDate = template.calculateReferenceDateFromTradeDate(valuationDate, refData);
    if (label.isEmpty()) {
      return YearMonthDateParameterMetadata.of(nodeDate, YearMonth.from(referenceDate));
    }
    return YearMonthDateParameterMetadata.of(nodeDate, YearMonth.from(referenceDate), label);
  }

  /**
   * Calculates the end date for the purposes of zero-coupon rate calculation.
   *
   * @param referenceDate - the reference date
   * @param refData - the reference data
   * @return the end date
   */
  private LocalDate calculateZeroCouponDateEnd(LocalDate referenceDate, ReferenceData refData) {
    return template.getIndex().calculateMaturityFromEffective(referenceDate, refData);
  }

  /**
   * Calculates the future end date based on the reference date. This is used for information
   * purposes only, and is not used in zero-coupon rate calculations.
   *
   * @param referenceDate - the reference date
   * @param refData - the reference data
   * @return the future end date
   */
  private LocalDate calculateFutureEndInfoDate(LocalDate referenceDate, ReferenceData refData) {
    // for 90 day bank bill futures, add 90 days instead of tenor (= 3M), since
    // this is a conventional way of representing future end dates for these contracts
    if (BANK_BILL_90_DAY_FUTURES.contains(template.getContractSpec().getName())) {
      var index = template.getIndex();
      var effectiveBusinessDay = getEffectiveBusinessDay(index, referenceDate, refData);
      var maturityOffset = generate90DayEquivalentAdjustment(index);
      return maturityOffset.adjust(effectiveBusinessDay, refData);
    }
    return template.getIndex().calculateMaturityFromEffective(referenceDate, refData);
  }

  private LocalDate getEffectiveBusinessDay(
      IborIndex index, LocalDate referenceDate, ReferenceData refData) {
    HolidayCalendarId cal = index.getEffectiveDateOffset().getResultCalendar();
    if (cal == HolidayCalendarIds.NO_HOLIDAYS) {
      cal = index.getFixingCalendar();
    }
    HolidayCalendar resolvedHolidayCalendar = cal.resolve(refData);
    return resolvedHolidayCalendar.nextOrSame(referenceDate);
  }

  private TenorAdjustment generate90DayEquivalentAdjustment(IborIndex index) {
    var ogMaturityOffset = index.getMaturityDateOffset();
    var ogAdditionConvention = ogMaturityOffset.getAdditionConvention();
    var ogAdjustment = ogMaturityOffset.getAdjustment();

    // set tenor to 90 days
    var tenor = Tenor.ofDays(90);

    return TenorAdjustment.of(tenor, ogAdditionConvention, ogAdjustment);
  }

  // calculate the last fixing date
  private LocalDate calculateLastFixingDate(LocalDate valuationDate, ReferenceData refData) {
    SecurityId secId = SecurityId.of(rateId.getStandardId()); // quote must also be security
    IborFutureTrade trade = template.createTrade(valuationDate, secId, 1, 1, refData);
    return trade.getProduct().getFixingDate();
  }

  @Override
  public IborFutureTrade trade(double quantity, MarketData marketData, ReferenceData refData) {
    LocalDate valuationDate = marketData.getValuationDate();
    double price = marketPrice(marketData) + additionalSpread;
    SecurityId secId = SecurityId.of(rateId.getStandardId()); // quote must also be security
    return template.createTrade(valuationDate, secId, quantity, price, refData);
  }

  @Override
  public ResolvedIborFutureTrade resolvedTrade(
      double quantity, MarketData marketData, ReferenceData refData) {
    return trade(quantity, marketData, refData).resolve(refData);
  }

  @Override
  public ResolvedIborFutureTrade sampleResolvedTrade(
      LocalDate valuationDate, FxRateProvider fxProvider, ReferenceData refData) {

    SecurityId secId = SecurityId.of(rateId.getStandardId()); // quote must also be security
    IborFutureTrade trade = template.createTrade(valuationDate, secId, 1d, 1d, refData);
    return trade.resolve(refData);
  }

  @Override
  public double initialGuess(MarketData marketData, ValueType valueType) {
    double rate = 1d - marketPrice(marketData);
    if (ValueType.ZERO_RATE.equals(valueType) || ValueType.FORWARD_RATE.equals(valueType)) {
      return rate;
    }
    if (ValueType.DISCOUNT_FACTOR.equals(valueType)) {
      return 1d;
    }
    return 0d;
  }

  // check if market value is correct
  private double marketPrice(MarketData marketData) {
    double price = marketData.getValue(rateId);
    ArgChecker.isTrue(
        price < 2,
        "Price must be in decimal form, such as 0.993 for a 0.7% rate, but was: {}",
        price);
    return price;
  }

  public IborFutureTemplate getTemplate() {
    return template;
  }

  public QuoteId getRateId() {
    return rateId;
  }

  @Override
  public String getLabel() {
    return label;
  }

  public CurveNodeDate getDate() {
    return date;
  }

  @Override
  public CurveNodeDateOrder getDateOrder() {
    return dateOrder;
  }
}
