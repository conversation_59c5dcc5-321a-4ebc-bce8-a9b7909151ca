package com.solum.xplain.core.portfolio.trade.details;

import static com.solum.xplain.core.portfolio.trade.details.TradeDetailsUtils.JOIN_ON;
import static com.solum.xplain.core.portfolio.trade.details.TradeDetailsUtils.resolveLegIndices;
import static com.solum.xplain.core.portfolio.trade.details.TradeDetailsUtils.tradeCcyOrPayLegPriorityNotional;

import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.details.ProductDetailsResolver;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class XccyDetailsResolver implements ProductDetailsResolver {

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.XCCY);
  }

  @Override
  public String resolveUnderlying(ProductType productType, TradeDetails tradeDetails) {
    return resolveLegIndices(tradeDetails)
        .map(TradeLegDetails::getCurrency)
        .collect(Collectors.joining(JOIN_ON));
  }

  @Override
  public double resolveNotional(TradeDetails tradeDetails) {
    return tradeCcyOrPayLegPriorityNotional(tradeDetails);
  }
}
