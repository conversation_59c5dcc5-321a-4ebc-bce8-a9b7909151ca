package com.solum.xplain.core.ccyexposure.value;

import com.solum.xplain.core.common.versions.State;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@NoArgsConstructor(access = AccessLevel.PROTECTED) // for tests
@AllArgsConstructor
public class CashflowView implements HasCashflow {
  private String entityId;

  private LocalDate validFrom;
  private LocalDateTime recordDate;

  private String modifiedBy;
  private LocalDateTime modifiedAt;

  private String name;
  private String comment;
  private State state;

  private String ccyExposureId;

  private LocalDate date;
  private Double amount;
}
