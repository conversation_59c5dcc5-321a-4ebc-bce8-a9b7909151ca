package com.solum.xplain.core.curvegroup.ratefx;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;

import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.ratefx.csv.FxRatesNodeCsvMapper;
import com.solum.xplain.core.curvegroup.ratefx.entity.CurveGroupFxRatesNode;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeValueView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.InstrumentMarketKeyDefinitionExportService;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class CurveGroupFxRatesExportService {

  private final CurveGroupRepository curveGroupRepository;
  private final CurveGroupFxRatesRepository repository;
  private final InstrumentMarketKeyDefinitionExportService definitionExportService;
  private final MarketDataQuotesSupport marketDataQuotesSupport;

  public CurveGroupFxRatesExportService(
      CurveGroupRepository curveGroupRepository,
      CurveGroupFxRatesRepository repository,
      InstrumentMarketKeyDefinitionExportService definitionExportService,
      MarketDataQuotesSupport marketDataQuotesSupport) {
    this.curveGroupRepository = curveGroupRepository;
    this.repository = repository;
    this.definitionExportService = definitionExportService;
    this.marketDataQuotesSupport = marketDataQuotesSupport;
  }

  public Either<ErrorItem, FileResponseEntity> getFxRatesCsvBytes(
      String groupId, CurveConfigMarketStateForm stateForm, List<String> selectedColumns) {
    var mapper = new FxRatesNodeCsvMapper(selectedColumns);
    var fileName = nameWithTimeStamp("FXRates", stateForm.getStateDate());
    return curveGroupRepository
        .getGroup(groupId)
        .map(g -> nodeViews(groupId, stateForm).getList())
        .map(
            nodes -> {
              var rows = nodes.stream().map(mapper::toCsvRow).toList();
              var csvFile = new CsvOutputFile(mapper.header(), rows);
              return FileResponseEntity.csvFile(csvFile.writeToByteArray(), fileName);
            });
  }

  private VersionedList<CurveGroupFxRatesNodeValueView> nodeViews(
      String groupId, CurveConfigMarketStateForm stateForm) {
    return repository.getRatesNodesValuesViews(
        groupId, stateForm.getStateDate(), marketDataQuotesSupport.getFullQuotes(stateForm));
  }

  public Either<ErrorItem, FileResponseEntity> getFxRatesMdkDefinitionCsvBytes(
      String groupId, LocalDate stateDate, String curveConfigurationId) {
    return curveGroupRepository
        .getGroup(groupId)
        .map(g -> repository.getRatesNodes(groupId, new BitemporalDate(stateDate)))
        .flatMap(
            nodes ->
                definitionExportService.instrumentMdkDefinitions(
                    "FXRates",
                    nodes,
                    CurveGroupFxRatesNode::allInstruments,
                    r -> "",
                    stateDate,
                    curveConfigurationId));
  }
}
