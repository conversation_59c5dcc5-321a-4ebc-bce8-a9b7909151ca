package com.solum.xplain.core.portfolio.calendars;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidSwapTradeLegCalendarFormValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ValidSwapTradeLegCalendarForm {
  String message() default
      "{com.solum.xplain.api.portfolio.calendars" + ".ValidSwapTradeLegCalendarForm.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
