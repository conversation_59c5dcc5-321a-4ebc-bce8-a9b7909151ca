package com.solum.xplain.core.portfolio.trade;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewSubfield;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class ExternalIdentifier implements Serializable {
  public static class FieldName {
    public static final String EXTERNAL_IDENTIFIERS_ID = "externalIdentifier";
  }

  @ConfigurableViewField(FieldName.EXTERNAL_IDENTIFIERS_ID)
  @ConfigurableViewQuery(sortable = true)
  private String identifier;

  @ConfigurableViewSubfield(classifier = "identifierSource")
  private String externalSourceId;
}
