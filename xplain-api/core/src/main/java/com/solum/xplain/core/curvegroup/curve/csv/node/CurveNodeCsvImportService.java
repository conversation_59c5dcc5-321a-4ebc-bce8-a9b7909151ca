package com.solum.xplain.core.curvegroup.curve.csv.node;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.GroupedItemsImportService;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.NamedList;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository;
import com.solum.xplain.core.curvegroup.curve.CurveMapper;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curve.value.CurveForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveSearch;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurveNodeCsvImportService
    extends GroupedItemsImportService<Curve, CurveNodeForm, CurveNodeKey> {

  private final CurveNodesCsvLoader csvLoader;
  private final CurveMapper curveMapper;
  private final CurveGroupCurveRepository repository;

  public CurveNodeCsvImportService(
      AuditEntryService auditEntryService,
      CurveNodesCsvLoader csvLoader,
      CurveMapper curveMapper,
      CurveGroupCurveRepository repository) {
    super(auditEntryService);
    this.csvLoader = csvLoader;
    this.curveMapper = curveMapper;
    this.repository = repository;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importForCurve(
      String groupId,
      String curveId,
      LocalDate versionDate,
      ImportOptions importOptions,
      byte[] bytes) {
    return fetchCurve(groupId, curveId, versionDate)
        .map(
            c ->
                maybeImport(
                    importOptions.parsingMode(),
                    csvLoader.parseForCurve(bytes, c, importOptions.getDuplicateAction()),
                    curvesNodes -> importNodes(List.of(c), importOptions, curvesNodes)))
        .fold(
            e -> toErrorReturn(importOptions.getDuplicateAction(), List.of(e)),
            result -> toReturn(importOptions.getDuplicateAction(), result));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importForAll(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    var curves = fetchCurves(groupId, importOptions.getStateDate());
    var logItems =
        maybeImport(
            importOptions.parsingMode(),
            csvLoader.parse(bytes, curves, importOptions.getDuplicateAction()),
            curvesNodes -> importNodes(curves, importOptions, curvesNodes));
    return toReturn(importOptions.getDuplicateAction(), logItems);
  }

  protected List<LogItem> importNodes(
      List<Curve> curves, ImportOptions importOptions, List<NamedList<CurveNodeForm>> curveNodes) {
    return curveNodes.stream()
        .map(
            nn -> {
              var curve = findCurve(curves, nn.getName());
              var importItems =
                  ImportItems.<CurveNodeForm, CurveNodeKey, CurveNodeForm>builder()
                      .existingActiveItems(nodeForms(curve))
                      .existingItemToKeyFn(CurveNodeKey::from)
                      .importItems(nn.getItems())
                      .importItemToKeyFn(CurveNodeKey::from)
                      .build();
              return importForEntity(curve, importItems, importOptions);
            })
        .flatMap(Collection::stream)
        .toList();
  }

  private List<CurveNodeForm> nodeForms(Curve curve) {
    return curve.getNodes().stream().map(curveMapper::toNodeForm).toList();
  }

  @Override
  public String getCollection() {
    return Curve.CURVE_COLLECTION;
  }

  @Override
  public String getObjectName() {
    return "IR + Inflation curves' nodes";
  }

  @Override
  protected String entityIdentifier(Curve curve) {
    return curve.getName();
  }

  @Override
  protected Either<ErrorItem, EntityId> update(
      Curve curve, List<CurveNodeForm> nodeForms, NewVersionFormV2 versionForm) {
    CurveForm form = curveMapper.toForm(curve);
    form.setVersionForm(versionForm);
    form.setNodes(nodeForms);
    return update(curve, form);
  }

  @Override
  protected boolean hasFutureVersions(Curve curve, LocalDate stateDate) {
    var search = new CurveSearch(curve.getName(), stateDate);
    return !IterableUtils.isEmpty(
        repository.getFutureVersions(curve.getCurveGroupId(), search).getDates());
  }

  private Either<ErrorItem, EntityId> update(Curve e, CurveUpdateForm f) {
    return repository.updateCurve(e.getCurveGroupId(), e.getEntityId(), e.getValidFrom(), f);
  }

  private List<Curve> fetchCurves(String groupId, LocalDate stateDate) {
    return repository.getActiveCurves(groupId, BitemporalDate.newOf(stateDate));
  }

  private Either<ErrorItem, Curve> fetchCurve(String groupId, String curveId, LocalDate stateDate) {
    return repository.getActiveCurve(groupId, curveId, BitemporalDate.newOf(stateDate));
  }

  private Curve findCurve(List<Curve> curves, String existingCurveName) {
    return IterableUtils.find(curves, c -> Objects.equals(c.getName(), existingCurveName));
  }
}
