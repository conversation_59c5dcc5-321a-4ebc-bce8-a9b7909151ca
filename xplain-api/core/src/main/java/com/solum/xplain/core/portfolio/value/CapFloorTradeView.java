package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.CoreProductType.CAP_FLOOR;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.CapFloorTradeForm;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CapFloorTradeView extends CapFloorTradeForm implements TradeView {

  private String updatedBy;
  private LocalDateTime updatedAt;
  private String tradeId;
  private String calendar;

  public static Either<ErrorItem, CapFloorTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == CAP_FLOOR) {
      var view = new CapFloorTradeView();
      view.updateCommonView(item);
      view.withTradeInfo(item.getTradeDetails());
      view.withTradeDetails(item.getTradeDetails());
      return Either.right(view);

    } else {
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Product type is not " + "expected " + item.getProductType()));
    }
  }

  private void withTradeDetails(TradeDetails details) {
    this.setType(details.getOptionTradeDetails().getCapFloorType().name());
    this.setPosition(details.getPositionType().name());
    this.setStrike(details.getOptionTradeDetails().getStrike());
    this.setStartDate(details.getStartDate());
    this.setEndDate(details.getEndDate());
    this.setPremiumDate(details.getOptionTradeDetails().getPremiumDate());
    this.setPremiumValue(details.getOptionTradeDetails().getPremiumValue());
    this.setPremiumDateConvention(details.getOptionTradeDetails().getPremiumDateConvention());
    this.setBusinessDayConvention(details.getBusinessDayConvention());
    this.setCalendar(details.getCalendar());
    details
        .tradePositionLeg()
        .ifPresent(
            leg -> {
              this.setExtLegIdentifier(leg.getExtLegIdentifier());
              var index = leg.getIndex();
              this.setCalculationIborDayCount(leg.getDayCount());
              this.setCalculationIborIndex(index);
              this.setAccrualFrequency(leg.getAccrualFrequency());
              this.setPaymentFrequency(leg.getPaymentFrequency());
              this.setCompoundingMethod(leg.getPaymentCompounding());
              this.setNotionalCurrency(leg.getCurrency());
              this.setNotionalValue(leg.getNotional());
              this.setCalculationIborSpreadInitialValue(leg.getInitialValue());
              this.setCalculationIborFixingDateOffsetDays(leg.getFixingDateOffsetDays());
              this.setIsOffshore(BooleanUtils.isTrue(leg.getIsOffshore()));
            });
  }
}
