package com.solum.xplain.core.curvegroup.volatility;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_VOL_SURFACE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_MARKET_DATA_KEY;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_VOL_SURFACE;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.lock.XplainLock.CURVE_CONFIGURATION_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.Filtered;
import com.solum.xplain.core.common.Sorted;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityMatrixConfiguration;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityMatrixValues;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityMatrixConfiguration;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityMatrixValues;
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewMatrixConfiguration;
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewMatrixValues;
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewView;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceForm;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceSearch;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceUpdateForm;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/curve-group/{groupId}/volatility")
public class CurveGroupVolatilityController {

  private final CurveGroupVolatilityService service;
  private final CurveGroupVolatilityImportService importService;
  private final CurveGroupVolatilityExportService exportService;

  public CurveGroupVolatilityController(
      CurveGroupVolatilityService service,
      CurveGroupVolatilityImportService importService,
      CurveGroupVolatilityExportService exportService) {
    this.service = service;
    this.importService = importService;
    this.exportService = exportService;
  }

  @Operation(summary = "Creates new or updates existing surface")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> createSurface(
      @PathVariable("groupId") String groupId, @Valid @RequestBody VolatilitySurfaceForm form) {
    return eitherErrorItemResponse(service.createSurface(groupId, form));
  }

  @Operation(summary = "Updates surface")
  @PutMapping("/{surfaceId}/{version}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> updateSurface(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      @Valid @RequestBody VolatilitySurfaceUpdateForm form) {
    return eitherErrorItemResponse(service.updateSurface(groupId, surfaceId, version, form));
  }

  @Operation(summary = "Archives (sets status to ARCHIVED) surface")
  @PutMapping("/{surfaceId}/{version}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> archiveSurface(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      @Valid @RequestBody ArchiveEntityForm form) {
    return eitherErrorItemResponse(service.archiveSurface(groupId, surfaceId, version, form));
  }

  @Operation(summary = "Deletes (sets status to DELETED) surface")
  @PutMapping("/{surfaceId}/{version}/delete")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<EntityId> deleteSurface(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version) {
    return eitherErrorItemResponse(service.deleteSurface(groupId, surfaceId, version));
  }

  @Operation(summary = "Gets all surfaces")
  @GetMapping
  @CommonErrors
  @Sorted
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<List<VolatilitySurfaceView>> getAllSurfaces(
      @SortDefault(sort = VolatilitySurfaceView.Fields.name) Sort sort,
      @PathVariable("groupId") String groupId,
      @RequestParam LocalDate stateDate,
      @RequestParam(required = false) boolean withArchived) {
    return eitherErrorItemResponse(
        service.getSurfaces(groupId, BitemporalDate.newOf(stateDate), withArchived, sort));
  }

  @Operation(summary = "Gets surface")
  @GetMapping("/{surfaceId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<VolatilitySurfaceView> getSurface(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(service.getSurface(groupId, surfaceId, stateDate));
  }

  @Operation(summary = "Gets surface versions")
  @GetMapping("/{surfaceId}/versions")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<List<VolatilitySurfaceView>> getSurfaceVersions(
      @PathVariable("groupId") String groupId, @PathVariable("surfaceId") String surfaceId) {
    return eitherErrorItemResponse(service.getSurfaceVersions(groupId, surfaceId));
  }

  @Operation(summary = "Gets future versions dates")
  @GetMapping("/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<DateList> getSurfaceFutureVersionsDates(
      @PathVariable("groupId") String groupId, @Valid VolatilitySurfaceSearch search) {
    return eitherErrorItemResponse(service.getSurfaceFutureVersions(groupId, search));
  }

  @Operation(summary = "Gets surface ATM matrix")
  @GetMapping("/{surfaceId}/{version}/nodes")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<VolatilityMatrixConfiguration> getSurfaceAtmMatrix(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version) {
    return eitherErrorItemResponse(
        service.getSurfaceAtmMatrixConfiguration(groupId, surfaceId, version));
  }

  @Operation(summary = "Gets surface ATM matrix values")
  @GetMapping("/{surfaceId}/{version}/nodes/values")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<VolatilityMatrixValues> getSurfaceAtmMatrixValues(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemResponse(
        service.getSurfaceAtmMatrixValues(groupId, surfaceId, version, stateForm));
  }

  @Operation(summary = "Gets surface Cap/Floor matrix")
  @GetMapping("/{surfaceId}/{version}/caplet-nodes")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<CapletVolatilityMatrixConfiguration> getSurfaceCapletMatrix(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version) {
    return eitherErrorItemResponse(
        service.getSurfaceCapletMatrixConfiguration(groupId, surfaceId, version));
  }

  @Operation(summary = "Gets surface Cap/Floor matrix values")
  @GetMapping("/{surfaceId}/{version}/caplet-nodes/values")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<CapletVolatilityMatrixValues> getSurfaceCapletMatrixValues(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemResponse(
        service.getSurfaceCapletMatrixValues(groupId, surfaceId, version, stateForm));
  }

  @Operation(summary = "Gets surface Absolute strike/Moneyness skews)")
  @GetMapping("/{surfaceId}/{version}/surface-skews")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<List<VolatilitySurfaceSkewView>> getSurfaceSkews(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version) {
    return eitherErrorItemResponse(service.getSurfaceSkews(groupId, surfaceId, version));
  }

  @Operation(summary = "Gets surface Absolute strike/Moneyness skew matrix")
  @GetMapping("/{surfaceId}/{version}/surface-skews/{surfaceSkewId}/nodes")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<VolatilitySurfaceSkewMatrixConfiguration> getSurfaceSkewMatrix(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      @PathVariable("surfaceSkewId") String surfaceSkewId) {
    return eitherErrorItemResponse(
        service.getSurfaceSkewMatrixConfiguration(groupId, surfaceId, version, surfaceSkewId));
  }

  @Operation(summary = "Gets surface Absolute strike/Moneyness skew nodes values")
  @GetMapping("/{surfaceId}/{version}/surface-skews/{surfaceSkewId}/nodes/values")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<VolatilitySurfaceSkewMatrixValues> getSurfaceSkewMatrixValues(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      @PathVariable("surfaceSkewId") String surfaceSkewId,
      @ParameterObject CurveConfigMarketStateForm stateForm) {
    return eitherErrorItemResponse(
        service.getSurfaceSkewMatrixValues(groupId, surfaceId, version, surfaceSkewId, stateForm));
  }

  @Operation(summary = "Uploads surfaces CSV file")
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadSurfacesCsv(
      @PathVariable("groupId") String groupId,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadSurfaces(groupId, importOptions, file.getBytes()));
  }

  @Operation(summary = "Uploads all surfaces ATM Matrices nodes CSV file")
  @PostMapping(value = "/nodes/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadAllSurfacesNodesCsv(
      @PathVariable("groupId") String groupId,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadNodes(groupId, importOptions, file.getBytes()));
  }

  @Operation(summary = "Uploads surface ATM Matrix nodes CSV file")
  @PostMapping(
      value = "/{surfaceId}/{version}/nodes/upload",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadSurfaceNodesCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadNodesForSurface(
            groupId, surfaceId, version, importOptions, file.getBytes()));
  }

  @Operation(summary = "Uploads all surfaces Cap/Floor matrices nodes CSV file")
  @PostMapping(value = "/caplet-nodes/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadAllSurfacesCapletNodesCsv(
      @PathVariable("groupId") String groupId,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadCapletNodes(groupId, importOptions, file.getBytes()));
  }

  @Operation(summary = "Uploads surface Cap/Floor matrix nodes CSV file")
  @PostMapping(
      value = "/{surfaceId}/{version}/caplet-nodes/upload",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadSurfaceCapletNodesCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadCapletNodesForSurface(
            groupId, surfaceId, version, importOptions, file.getBytes()));
  }

  @Operation(summary = "Uploads all surfaces Absolute strike/Moneyness skews CSV file")
  @PostMapping(value = "/surface-skews/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadAllSurfacesSkewsCsv(
      @PathVariable("groupId") String groupId,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadSkews(groupId, importOptions, file.getBytes()));
  }

  @Operation(summary = "Uploads surface Absolute strike/Moneyness skew nodes CSV file")
  @PostMapping(
      value = "/{surfaceId}/{version}/surface-skews/upload",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_VOL_SURFACE)
  @RequireLock(name = CURVE_CONFIGURATION_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadSurfaceSkewsCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadSkewsForSurface(
            groupId, surfaceId, version, importOptions, file.getBytes()));
  }

  @Operation(summary = "Gets all surfaces CSV")
  @GetMapping("/csv")
  @Filtered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<ByteArrayResource> getAllSurfacesCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam LocalDate stateDate,
      TableFilter tableFilter,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.getAllSurfacesCsvBytes(groupId, stateDate, tableFilter, selectedColumns));
  }

  @Operation(summary = "Gets all surfaces ATM Matrices nodes CSV")
  @GetMapping("/nodes/csv")
  @CommonErrors
  @Filtered
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<ByteArrayResource> getAllSurfacesNodesCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam("stateDate") LocalDate stateDate,
      CurveConfigMarketStateForm stateForm,
      TableFilter tableFilter,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.getAllSurfacesNodesCsvBytes(
            groupId, BitemporalDate.newOf(stateDate), stateForm, tableFilter, selectedColumns));
  }

  @Operation(summary = "Gets surface ATM Matrix nodes CSV")
  @GetMapping("/{surfaceId}/{version}/nodes/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<ByteArrayResource> getSurfaceNodesCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      CurveConfigMarketStateForm stateForm,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.getSurfaceNodesCsvBytes(
            groupId, surfaceId, version, stateForm, selectedColumns));
  }

  @Operation(summary = "Gets all surfaces Cap/Floor Matrices nodes CSV")
  @GetMapping("/caplet-nodes/csv")
  @CommonErrors
  @Filtered
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<ByteArrayResource> getAllSurfacesCapletNodesCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam("stateDate") LocalDate stateDate,
      CurveConfigMarketStateForm stateForm,
      TableFilter tableFilter,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.getAllSurfacesCapletNodesCsvBytes(
            groupId, stateDate, stateForm, tableFilter, selectedColumns));
  }

  @Operation(summary = "Gets surface Cap/Floor Matrix nodes CSV")
  @GetMapping("/{surfaceId}/{version}/caplet-nodes/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<ByteArrayResource> getSurfaceCapletNodesCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      CurveConfigMarketStateForm stateForm,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.getSurfaceCapletNodesCsvBytes(
            groupId, surfaceId, version, stateForm, selectedColumns));
  }

  @Operation(summary = "Gets all surfaces Absolute strike/Moneyness skews CSV")
  @GetMapping("/surface-skews/csv")
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<ByteArrayResource> getAllSurfacesSkewsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam("stateDate") LocalDate stateDate,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.getAllSurfacesSkewsCsvBytes(groupId, stateDate, selectedColumns));
  }

  @Operation(summary = "Gets surface Absolute strike/Moneyness skews CSV")
  @GetMapping("/{surfaceId}/{version}/surface-skews/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VOL_SURFACE)
  public ResponseEntity<ByteArrayResource> getSurfaceSkewsCsv(
      @PathVariable("groupId") String groupId,
      @PathVariable("surfaceId") String surfaceId,
      @PathVariable("version") LocalDate version,
      @RequestParam LocalDate stateDate,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.getSurfaceSkewsCsvBytes(
            groupId, surfaceId, version, stateDate, selectedColumns));
  }

  @Operation(summary = "Gets surfaces ATM Matrices nodes MDK definitions CSV")
  @GetMapping("/nodes/mdk-definitions")
  @PreAuthorize(AUTHORITY_VIEW_MARKET_DATA_KEY)
  public ResponseEntity<ByteArrayResource> getSurfacesNodesMdkDefinitionsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam("stateDate") LocalDate stateDate,
      @RequestParam List<String> surfaceIds,
      @RequestParam(required = false) String configurationId) {
    return eitherErrorItemFileResponse(
        exportService.getSurfacesNodesMdkDefinitions(
            groupId, stateDate, surfaceIds, configurationId));
  }

  @Operation(summary = "Gets surfaces Capt/Floor Matrices nodes MDK definitions CSV")
  @GetMapping("/caplet-nodes/mdk-definitions")
  @PreAuthorize(AUTHORITY_VIEW_MARKET_DATA_KEY)
  public ResponseEntity<ByteArrayResource> getSurfacesCapletNodesMdkDefinitionsCSV(
      @PathVariable("groupId") String groupId,
      @RequestParam("stateDate") LocalDate stateDate,
      @RequestParam List<String> surfaceIds,
      @RequestParam(required = false) String configurationId) {
    return eitherErrorItemFileResponse(
        exportService.getSurfacesCapletMdkDefinitions(
            groupId, stateDate, surfaceIds, configurationId));
  }

  @Operation(summary = "Gets surfaces Moneyness/Absolute strike skews MDK definitions CSV")
  @GetMapping("/surface-skews/mdk-definitions")
  @PreAuthorize(AUTHORITY_VIEW_MARKET_DATA_KEY)
  public ResponseEntity<ByteArrayResource> getSurfacesSkewsMdkDefinitionsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam("stateDate") LocalDate stateDate,
      @RequestParam List<String> surfaceIds,
      @RequestParam(required = false) String configurationId) {
    return eitherErrorItemFileResponse(
        exportService.getSurfacesSkewsMdkDefinitions(
            groupId, stateDate, surfaceIds, configurationId));
  }

  @Operation(summary = "Get surfaces all MDK definitions CSVs")
  @GetMapping("/mdk-definitions")
  @PreAuthorize(AUTHORITY_VIEW_MARKET_DATA_KEY)
  public ResponseEntity<ByteArrayResource> getSurfacesAllMdkDefinitionsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam("stateDate") LocalDate stateDate,
      @RequestParam List<String> surfaceIds,
      @RequestParam(required = false) String configurationId) {
    return eitherErrorItemFileResponse(
        exportService.getSurfacesMdkDefinitions(groupId, stateDate, surfaceIds, configurationId));
  }
}
