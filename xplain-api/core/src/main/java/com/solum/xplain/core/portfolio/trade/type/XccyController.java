package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.XccyTradeForm;
import com.solum.xplain.core.portfolio.value.XccyTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/xccy")
@AllArgsConstructor
public class XccyController implements BespokeTradeTypedController<XccyTradeForm, XccyTradeView> {

  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, XccyTradeView> toViewFunction(PortfolioItem e) {
    return XccyTradeView.of(e);
  }
}
