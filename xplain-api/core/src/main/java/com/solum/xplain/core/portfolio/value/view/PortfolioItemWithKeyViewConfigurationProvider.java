package com.solum.xplain.core.portfolio.value.view;

import com.solum.xplain.core.portfolio.value.PortfolioItemWithKeyView;
import com.solum.xplain.core.viewconfig.provider.PaletteService;
import com.solum.xplain.core.viewconfig.provider.StandardViewConfigurationProvider;
import org.springframework.stereotype.Component;

@Component
public class PortfolioItemWithKeyViewConfigurationProvider
    extends StandardViewConfigurationProvider {

  public PortfolioItemWithKeyViewConfigurationProvider(PaletteService paletteService) {
    super(
        PortfolioItemWithKeyView.class,
        paletteService,
        "66290f6f3d0c5109c3f62509",
        DefaultColumnsBuilder::new,
        null,
        null);
  }
}
