package com.solum.xplain.core.curvegroup.curve.validation;

import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import java.util.Collection;
import java.util.function.Supplier;
import java.util.stream.Stream;

public class CurveTypesSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return Stream.of(CurveType.values()).map(Enum::name).toList();
  }
}
