package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.portfolio.form.PortfolioCreateForm;
import lombok.experimental.FieldNameConstants;

/**
 * Represents a surrogate unique identifier of a portfolio.
 *
 * <p>Company and entity ids are *internal ids.*, while portfolio id is an actual external
 * identifier.
 */
@FieldNameConstants
public record PortfolioUniqueKey(String companyId, String entityId, String externalPortfolioId) {

  public static PortfolioUniqueKey fromImportPortfolio(ImportPortfolio importPortfolio) {
    return new PortfolioUniqueKey(
        importPortfolio.getCompanyId(),
        importPortfolio.getEntityId(),
        importPortfolio.getExternalPortfolioId());
  }

  public static PortfolioUniqueKey fromForm(PortfolioCreateForm form) {
    return new PortfolioUniqueKey(
        form.getCompanyId(), form.getEntityId(), form.getExternalPortfolioId());
  }
}
