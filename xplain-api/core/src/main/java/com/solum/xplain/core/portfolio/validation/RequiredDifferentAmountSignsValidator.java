package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.SingleExchangeFxTradeForm;
import com.solum.xplain.core.portfolio.value.CurrencyAmountForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredDifferentAmountSignsValidator
    implements ConstraintValidator<RequiredDifferentAmountSigns, SingleExchangeFxTradeForm> {

  public boolean isValid(SingleExchangeFxTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }
    var domestic = form.getDomesticCurrencyAmount();
    var foreign = form.getForeignCurrencyAmount();
    if (hasAmount(domestic) && hasAmount(foreign) && hasSameSigns(domestic, foreign)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate("RequiredDifferentAmountSigns")
          .addPropertyNode("foreignCurrencyAmount")
          .addPropertyNode("amount")
          .addConstraintViolation();

      return false;
    }

    return true;
  }

  private boolean hasAmount(CurrencyAmountForm amountForm) {
    return amountForm != null && amountForm.getAmount() != null && amountForm.getAmount() != 0d;
  }

  private boolean hasSameSigns(CurrencyAmountForm f1, CurrencyAmountForm f2) {
    return Math.signum(f1.getAmount()) == Math.signum(f2.getAmount());
  }
}
