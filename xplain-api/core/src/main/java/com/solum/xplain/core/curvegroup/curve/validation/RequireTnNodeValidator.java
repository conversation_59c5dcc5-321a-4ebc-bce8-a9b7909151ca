package com.solum.xplain.core.curvegroup.curve.validation;

import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.opengamma.strata.basics.date.MarketTenor;
import com.opengamma.strata.product.fx.type.FxSwapConvention;
import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curve.entity.CurveNodeInstrument;
import com.solum.xplain.core.curvegroup.curve.value.CurveForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class RequireTnNodeValidator implements ConstraintValidator<RequireTnNode, CurveUpdateForm> {
  private static final String REQUIRED_TN_NODE_ERROR_MESSAGE =
      "Curve %s requires a TN node. Please add a TN node to the curve or remove ON node.";

  private final RequestPathVariablesSupport pathVariablesSupport;
  private final CurveGroupCurveRepository curveGroupCurveRepository;

  @Override
  public boolean isValid(CurveUpdateForm curveUpdateForm, ConstraintValidatorContext context) {
    var curveName = resolveCurveName(curveUpdateForm);
    if (curveName != null && CollectionUtils.isNotEmpty(curveUpdateForm.getNodes())) {
      var tenors =
          curveUpdateForm.getNodes().stream()
              .map(CurveNodeInstrument::getTenor)
              .collect(Collectors.toSet());

      return validate(curveName, tenors)
          .map(
              errorItem -> {
                context.disableDefaultConstraintViolation();
                context
                    .buildConstraintViolationWithTemplate(errorItem.getDescription())
                    .addConstraintViolation();
                return false;
              })
          .orElse(true);
    }
    return true;
  }

  @Nullable
  private String resolveCurveName(CurveUpdateForm curveUpdateForm) {
    if (curveUpdateForm instanceof CurveForm form) {
      return form.getName();
    }
    var groupId = pathVariablesSupport.getPathVariable("groupId");
    var curveId = pathVariablesSupport.getPathVariable("curveId");
    var stateDate =
        Optional.ofNullable(curveUpdateForm.getVersionForm())
            .map(NewVersionFormV2::getStateDate)
            .map(BitemporalDate::newOf)
            .orElse(null);
    if (allNotNull(curveId, groupId, stateDate)) {
      return curveGroupCurveRepository
          .getActiveCurve(
              groupId,
              curveId,
              BitemporalDate.newOf(curveUpdateForm.getVersionForm().getStateDate()))
          .map(Curve::getName)
          .toOptional()
          .orElse(null);
    }
    return null;
  }

  public static Optional<ErrorItem> validate(String curveName, Set<String> nodeTenors) {
    if (nodeTenors.contains(MarketTenor.ON.toString())) {
      var isRequiredFxCurve =
          ConventionalCurveConfigurations.lookupByName(curveName).stream()
              .map(ConventionalCurveConvention::getNodeConventions)
              .flatMap(Collection::stream)
              .filter(FxSwapConvention.class::isInstance)
              .map(FxSwapConvention.class::cast)
              .map(FxSwapConvention::getSpotDateOffset)
              .anyMatch(spotDateOffset -> spotDateOffset.getDays() == 2);
      if (isRequiredFxCurve && !nodeTenors.contains(MarketTenor.TN.toString())) {
        return Optional.of(
            Error.IMPORT_ERROR.entity(String.format(REQUIRED_TN_NODE_ERROR_MESSAGE, curveName)));
      }
    }
    return Optional.empty();
  }
}
