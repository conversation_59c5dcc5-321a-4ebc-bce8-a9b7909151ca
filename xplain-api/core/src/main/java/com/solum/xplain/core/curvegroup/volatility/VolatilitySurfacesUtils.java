package com.solum.xplain.core.curvegroup.volatility;

import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class VolatilitySurfacesUtils {

  public static List<VolatilitySurface> filterSurfaces(
      List<VolatilitySurface> surfaces, List<String> surfaceIds) {
    if (CollectionUtils.isEmpty(surfaceIds)) {
      return surfaces;
    }
    return surfaces.stream().filter(c -> surfaceIds.contains(c.getEntityId())).toList();
  }
}
