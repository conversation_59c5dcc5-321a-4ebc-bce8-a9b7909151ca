package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.form.OnboardingDetailsForm.fromDetails;

import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.ClientMetricsForm;
import com.solum.xplain.core.portfolio.form.CustomTradeFieldForm;
import com.solum.xplain.core.portfolio.form.ExternalIdentifierForm;
import com.solum.xplain.core.portfolio.form.OnboardingDetailsForm;
import java.time.LocalDateTime;
import java.util.List;

public interface TradeView {

  String getExternalTradeId();

  void setExternalTradeId(String externalTradeId);

  String getTradeId();

  void setTradeId(String tradeId);

  String getUpdatedBy();

  void setUpdatedBy(String updatedBy);

  LocalDateTime getUpdatedAt();

  void setUpdatedAt(LocalDateTime updatedAt);

  String getDescription();

  void setDescription(String description);

  NewVersionFormV2 getVersionForm();

  void setVersionForm(NewVersionFormV2 versionForm);

  ClientMetricsForm getClientMetrics();

  void setClientMetrics(ClientMetricsForm clientMetrics);

  void setOnboardingDetails(OnboardingDetailsForm clientMetrics);

  void setExternalIdentifiers(List<ExternalIdentifierForm> externalIdentifiers);

  void setCustomFields(List<CustomTradeFieldForm> customFields);

  default void updateCommonView(VersionedTradeEntity item) {
    setUpdatedBy(item.getModifiedBy() == null ? null : item.getModifiedBy().getName());
    setUpdatedAt(item.getModifiedAt());
    setDescription(item.getDescription());
    setTradeId(item.getEntityId());
    setExternalTradeId(item.getExternalTradeId());
    setVersionForm(
        NewVersionFormV2.builder().validFrom(item.getVersion()).comment(item.getComment()).build());
    setClientMetrics(new ClientMetricsForm(item.getClientMetrics()));
    setOnboardingDetails(fromDetails(item.getOnboardingDetails()));
    setExternalIdentifiers(ExternalIdentifierForm.fromEntities(item.getExternalIdentifiers()));
    setCustomFields(CustomTradeFieldForm.fromEntities(item.getCustomFields()));
  }
}
