package com.solum.xplain.core.curvegroup.curve.value;

import static com.solum.xplain.core.classifiers.CurveNodeTypes.FRA_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.FX_SWAP_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.TERM_DEPOSIT_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.isImmNode;
import static com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle.BRL;
import static com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle.MARKET_TENOR;
import static com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle.Y;
import static com.solum.xplain.core.utils.TenorUtils.parseMarketTenorString;
import static com.solum.xplain.core.utils.TenorUtils.sumOfMonths;

import com.opengamma.strata.basics.date.MarketTenor;
import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.FraSettlementSupplier;
import com.solum.xplain.core.common.validation.SerialFutureSupplier;
import com.solum.xplain.core.common.validation.ValidPeriod;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.curvegroup.curve.entity.CurveNodeInstrument;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormBrlFixedOvernightSwapGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormFixedIborSwapGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormFixedInflationSwapGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormFixedOvernightSwapGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormFraGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormFxSwapGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormGroupProvider;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormIborFixingDepositGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormIborFutureGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormIborIborSwapGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormImmFraGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormOvernightIborBasisSwapGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormTermDepositGroup;
import com.solum.xplain.core.curvegroup.curve.validation.nodegroups.CurveNodeFormXCcyIborIborSwapGroup;
import com.solum.xplain.core.utils.TenorUtils;
import com.solum.xplain.extensions.product.ExtendedTermDepositConventions;
import io.atlassian.fugue.Checked;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Null;
import java.util.Optional;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@GroupSequenceProvider(CurveNodeFormGroupProvider.class)
public class CurveNodeForm implements CurveNodeInstrument {

  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "curveNodeType")
  @NotEmpty
  private String type;

  @NotEmpty private String convention;

  @NotEmpty(
      groups = {
        CurveNodeFormFixedInflationSwapGroup.class,
        CurveNodeFormFxSwapGroup.class,
        CurveNodeFormXCcyIborIborSwapGroup.class,
        CurveNodeFormOvernightIborBasisSwapGroup.class,
        CurveNodeFormIborIborSwapGroup.class,
        CurveNodeFormFixedIborSwapGroup.class,
        CurveNodeFormFixedOvernightSwapGroup.class,
        CurveNodeFormBrlFixedOvernightSwapGroup.class,
      })
  @Null(
      groups = {
        CurveNodeFormTermDepositGroup.class,
        CurveNodeFormIborFutureGroup.class,
        CurveNodeFormFraGroup.class,
        CurveNodeFormIborFixingDepositGroup.class,
        CurveNodeFormImmFraGroup.class
      })
  @ValidPeriod(
      groups = {
        CurveNodeFormXCcyIborIborSwapGroup.class,
        CurveNodeFormOvernightIborBasisSwapGroup.class,
        CurveNodeFormIborIborSwapGroup.class,
        CurveNodeFormFixedIborSwapGroup.class,
        CurveNodeFormFixedOvernightSwapGroup.class
      })
  @ValidPeriod(
      groups = {
        CurveNodeFormBrlFixedOvernightSwapGroup.class,
      },
      style = BRL)
  @ValidPeriod(
      groups = {CurveNodeFormFxSwapGroup.class},
      style = MARKET_TENOR)
  @ValidPeriod(
      groups = {CurveNodeFormFixedInflationSwapGroup.class},
      style = Y)
  private String period;

  @Null(
      groups = {
        CurveNodeFormFraGroup.class,
        CurveNodeFormIborFixingDepositGroup.class,
        CurveNodeFormFixedInflationSwapGroup.class,
        CurveNodeFormFxSwapGroup.class,
        CurveNodeFormXCcyIborIborSwapGroup.class,
        CurveNodeFormOvernightIborBasisSwapGroup.class,
        CurveNodeFormIborIborSwapGroup.class,
        CurveNodeFormFixedIborSwapGroup.class,
        CurveNodeFormFixedOvernightSwapGroup.class,
        CurveNodeFormBrlFixedOvernightSwapGroup.class
      })
  @NotEmpty(groups = {CurveNodeFormIborFutureGroup.class, CurveNodeFormImmFraGroup.class})
  @ValidStringSet(SerialFutureSupplier.class)
  private String serialFuture;

  @Null(
      groups = {
        CurveNodeFormTermDepositGroup.class,
        CurveNodeFormIborFutureGroup.class,
        CurveNodeFormImmFraGroup.class,
        CurveNodeFormIborFixingDepositGroup.class,
        CurveNodeFormFixedInflationSwapGroup.class,
        CurveNodeFormFxSwapGroup.class,
        CurveNodeFormXCcyIborIborSwapGroup.class,
        CurveNodeFormOvernightIborBasisSwapGroup.class,
        CurveNodeFormIborIborSwapGroup.class,
        CurveNodeFormFixedIborSwapGroup.class,
        CurveNodeFormFixedOvernightSwapGroup.class,
        CurveNodeFormBrlFixedOvernightSwapGroup.class
      })
  @NotEmpty(groups = {CurveNodeFormFraGroup.class})
  @ValidStringSet(FraSettlementSupplier.class)
  private String fraSettlement;

  public Optional<String> identifier() {
    if (FRA_NODE.equals(getType())) {
      return Checked.now(() -> TenorUtils.fromForwardRateAgreement(convention))
          .toOptional()
          .map(Tenor::toString)
          .flatMap(p -> sumOfMonths(p, getFraSettlement()))
          .map(Tenor::normalized)
          .map(Tenor::toString);
    } else if (isImmNode(getType())) {
      return Optional.ofNullable(getSerialFuture())
          .map(v -> StringUtils.substringAfterLast(v, "+"))
          .filter(StringUtils::isNotEmpty);
    } else if (FX_SWAP_NODE.equalsIgnoreCase(getType())) {
      return parseMarketTenorString(getPeriod());
    } else if (StringUtils.equals(
        convention, ExtendedTermDepositConventions.INR_DEPOSIT_T0.getName())) {
      return Optional.of(MarketTenor.ON.getCode());
    } else if (TERM_DEPOSIT_NODE.equalsIgnoreCase(getType())) {
      return Optional.ofNullable(getConvention());
    } else {
      return Checked.now(() -> TenorUtils.parseTenor(getPeriod()))
          .toOptional()
          .map(TenorUtils.SafeTenorWrapper::getTenor);
    }
  }
}
