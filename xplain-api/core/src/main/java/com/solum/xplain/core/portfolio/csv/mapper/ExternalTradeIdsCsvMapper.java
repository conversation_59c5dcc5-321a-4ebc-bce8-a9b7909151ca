package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.EXTERNAL_TRADE_ID_PREFIX;

import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ExternalTradeIdsCsvMapper {
  public static final ExternalTradeIdsCsvMapper MAPPER = new ExternalTradeIdsCsvMapper();

  public List<CsvField> mapExternalIds(List<ExternalIdentifier> externalIdentifiers) {
    if (externalIdentifiers == null) {
      return List.of();
    }
    return externalIdentifiers.stream().map(this::toCsvField).toList();
  }

  public List<String> formatToHeaders(List<String> externalSourceIds) {
    return externalSourceIds.stream().map(this::toSourceHeader).toList();
  }

  private CsvField toCsvField(ExternalIdentifier externalIdentifier) {
    var header = toSourceHeader(externalIdentifier.getExternalSourceId());
    return new CsvField(header, externalIdentifier.getIdentifier());
  }

  private String toSourceHeader(String header) {
    return EXTERNAL_TRADE_ID_PREFIX + header;
  }
}
