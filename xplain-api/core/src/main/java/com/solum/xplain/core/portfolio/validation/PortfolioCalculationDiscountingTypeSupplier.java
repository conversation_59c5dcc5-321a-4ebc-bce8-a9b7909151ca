package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import java.util.Collection;
import java.util.function.Supplier;
import java.util.stream.Stream;

public class PortfolioCalculationDiscountingTypeSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return Stream.of(CalculationDiscountingType.values()).map(Enum::name).toList();
  }
}
