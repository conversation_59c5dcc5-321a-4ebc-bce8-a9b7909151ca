package com.solum.xplain.core.portfolio.event;

import com.solum.xplain.core.company.entity.CompanyReference;
import java.util.Collection;
import java.util.List;
import org.jspecify.annotations.NullMarked;

@NullMarked
public record PortfolioAdded(Collection<String> companyIds) implements CompanyPortfoliosModified {
  public PortfolioAdded(String companyId) {
    this(List.of(companyId));
  }

  public PortfolioAdded(CompanyReference reference) {
    this(reference.getEntityId());
  }

  public static PortfolioAdded newOf(CompanyReference reference) {
    return new PortfolioAdded(reference);
  }
}
