package com.solum.xplain.core.curvegroup.ratefx.value;

import com.solum.xplain.core.common.versions.State;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CurveGroupFxRatesView {
  // Versioned entity fields
  private String entityId;
  private LocalDate validFrom;
  private String comment;
  private LocalDateTime recordDate;
  private State state;
  private String modifiedBy;
  private LocalDateTime modifiedAt;

  // Rates fields - calculated
  private Integer numberOfFxRates;
}
