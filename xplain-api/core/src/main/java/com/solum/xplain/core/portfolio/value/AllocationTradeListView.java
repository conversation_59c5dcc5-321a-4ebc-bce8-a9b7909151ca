package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.portfolio.ClientMetrics;
import com.solum.xplain.core.portfolio.trade.OnboardingDetails;
import com.solum.xplain.extensions.enums.PositionType;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class AllocationTradeListView {

  private String tradeId;
  private String externalTradeId;

  private String externalPortfolioId;
  private String portfolioId;
  private String externalCompanyId;
  private String externalEntityId;

  private LocalDate version;
  private LocalDate validFrom;
  private Double allocationNotional;
  private PositionType positionType;
  private String tradeCounterparty;
  private CounterpartyType tradeCounterpartyType;
  private ClientMetrics clientMetrics;
  private OnboardingDetails onboardingDetails;

  private String resolvedTradeCounterparty;
  private CounterpartyType resolvedTradeCounterpartyType;
  private ClientMetrics resolvedClientMetrics;
  private LocalDate tradeDate;
  private LocalDate resolvedTradeDate;
  private String description;
  private String resolvedDescription;
  private String optionPosition;
  private String protection;
}
