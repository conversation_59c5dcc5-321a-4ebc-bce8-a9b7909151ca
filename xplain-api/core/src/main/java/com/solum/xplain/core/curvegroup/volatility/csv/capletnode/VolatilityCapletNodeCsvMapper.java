package com.solum.xplain.core.curvegroup.volatility.csv.capletnode;

import static com.solum.xplain.core.curvegroup.volatility.csv.capletnode.VolatilityCapletNodeCsvLoader.CURVE_NAME;
import static com.solum.xplain.core.curvegroup.volatility.csv.capletnode.VolatilityCapletNodeCsvLoader.MATURITY_FIELD;
import static com.solum.xplain.core.curvegroup.volatility.csv.capletnode.VolatilityCapletNodeCsvLoader.STRIKE_FIELD;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeValueView;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView;
import java.util.List;

public class VolatilityCapletNodeCsvMapper extends CsvMapper<CapletVolatilityNodeValueView> {

  private static final String VALUE_FIELD = "Value";

  private static final List<CsvColumn<CapletVolatilityNodeValueView>> COLUMNS =
      List.of(
          CsvColumn.text(
              CapletVolatilityNodeValueView.Fields.tenor,
              MATURITY_FIELD,
              CapletVolatilityNodeValueView::getTenor),
          CsvColumn.bigDecimal(
              CapletVolatilityNodeValueView.Fields.strike,
              STRIKE_FIELD,
              CapletVolatilityNodeValueView::getStrike),
          CsvColumn.decimal(
                  CapletVolatilityNodeValueView.Fields.value,
                  VALUE_FIELD,
                  CapletVolatilityNodeValueView::getValue)
              .optional());

  public VolatilityCapletNodeCsvMapper(List<String> selectedColumns) {
    super(COLUMNS, selectedColumns);
  }

  @Override
  public List<String> header() {
    ImmutableList.Builder<String> builder = ImmutableList.builder();
    if (containsField(VersionedNamedEntity.Fields.name)) {
      builder.add(CURVE_NAME);
    }
    builder.addAll(super.header());
    return builder.build();
  }

  public CsvRow toCsvRow(String surfaceName, CapletVolatilityNodeValueView node) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(CURVE_NAME, surfaceName));
    builder.add(new CsvField(MATURITY_FIELD, node.getTenor()));
    builder.add(new CsvField(STRIKE_FIELD, node.getStrike()));
    if (node.getValue() != null) {
      builder.add(new CsvField(VALUE_FIELD, node.getValue()));
    }
    if (containsField(VolatilitySurfaceView.Fields.name)) {
      builder.add(new CsvField(CURVE_NAME, surfaceName));
    }
    builder.addAll(super.toCsvFields(node));
    return new CsvRow(builder.build());
  }
}
