package com.solum.xplain.core.portfolio.builder;

import static com.solum.xplain.core.portfolio.builder.validation.TradeBuilderValidatorUtils.validateTradeCurrency;
import static com.solum.xplain.core.portfolio.calendars.TradeCalendarUtils.getFxTradeCalendar;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.core.portfolio.trade.OptionTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.extensions.enums.CallPutType;
import com.solum.xplain.extensions.enums.PositionType;
import java.time.LocalDate;
import java.time.LocalTime;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.lang.Nullable;

@SuperBuilder
@ToString
@EqualsAndHashCode
@Getter
public abstract class AbstractResolvableFxDetails implements ResolvableTradeDetails {
  @NonNull private final Currency receiveCurrency;
  @Nullable protected final Double receiveCurrencyAmount;
  @NonNull private final Currency payCurrency;
  @Nullable protected final Double payCurrencyAmount;
  @NonNull private final String businessDayConvention;
  @NonNull private final LocalDate paymentDate;
  @NonNull private final PositionType positionType;
  @NonNull private final LocalDate expiryDate;
  @NonNull private final LocalTime expiryTime;
  @NonNull private final String expiryZone;
  @NonNull private final Currency premiumCurrency;
  @NonNull private final Double premiumValue;
  @NonNull private final LocalDate premiumDate;
  @NonNull private final String premiumDateConvention;
  @Nullable protected final CallPutType callPutType;
  @Nullable private final Double strike;
  private final String payLegExtIdentifier;
  private final String receiveLegExtIdentifier;

  @Override
  public TradeDetails toTradeDetails(TradeInfoDetails tradeInfo) {
    validateTradeCurrency(
        tradeInfo.getTradeCurrency(), payCurrency.getCode(), receiveCurrency.getCode());
    var calendar = getFxTradeCalendar(receiveCurrency.getCode(), payCurrency.getCode());
    TradeDetails tradeDetails = TradeDetails.newOf(tradeInfo);
    tradeDetails.setBusinessDayConvention(businessDayConvention);
    tradeDetails.setCalendar(calendar.getName());
    tradeDetails.setEndDate(paymentDate);
    tradeDetails.setPositionType(positionType);
    tradeDetails.setReceiveLeg(
        tradeLegDetails(
            PayReceive.RECEIVE, receiveCurrency, receiveCurrencyAmount, receiveLegExtIdentifier));
    tradeDetails.setPayLeg(
        tradeLegDetails(PayReceive.PAY, payCurrency, payCurrencyAmount, payLegExtIdentifier));
    tradeDetails.setOptionTradeDetails(optionDetails());
    return tradeDetails;
  }

  protected OptionTradeDetails baseOptionDetails() {
    OptionTradeDetails optionDetails = new OptionTradeDetails();
    optionDetails.setExpiryDate(getExpiryDate());
    optionDetails.setExpiryTime(getExpiryTime());
    optionDetails.setExpiryZone(getExpiryZone());
    optionDetails.setPremiumCurrency(getPremiumCurrency().getCode());
    optionDetails.setPremiumValue(getPremiumValue());
    optionDetails.setPremiumDate(getPremiumDate());
    optionDetails.setPremiumDateConvention(getPremiumDateConvention());
    optionDetails.setStrike(manualOrCalculatedStrike());
    optionDetails.setCallPutType(getCallPutType());
    return optionDetails;
  }

  protected abstract void additionalOptionDetails(OptionTradeDetails optionDetails);

  protected OptionTradeDetails optionDetails() {
    OptionTradeDetails optionDetails = baseOptionDetails();
    additionalOptionDetails(optionDetails);
    return optionDetails;
  }

  protected double manualOrCalculatedStrike() {
    if (payCurrencyAmount != null && receiveCurrencyAmount != null) {
      return callPutType == CallPutType.PUT
          ? Math.abs(receiveCurrencyAmount / payCurrencyAmount)
          : Math.abs(payCurrencyAmount / receiveCurrencyAmount);
    }
    return strike != null ? strike : 0d;
  }

  protected TradeLegDetails tradeLegDetails(
      PayReceive payReceive, Currency currency, Double amount, String legIdentifier) {
    TradeLegDetails legDetails = new TradeLegDetails();
    legDetails.setExtLegIdentifier(legIdentifier);
    legDetails.setPayReceive(payReceive);
    legDetails.setCurrency(currency.getCode());
    legDetails.setNotional(amount);
    return legDetails;
  }
}
