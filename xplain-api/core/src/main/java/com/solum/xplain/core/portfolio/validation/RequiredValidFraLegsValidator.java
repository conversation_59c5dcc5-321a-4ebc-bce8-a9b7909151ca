package com.solum.xplain.core.portfolio.validation;

import static com.solum.xplain.core.portfolio.value.CalculationType.FIXED;
import static com.solum.xplain.core.portfolio.value.CalculationType.IBOR;
import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import com.solum.xplain.core.portfolio.value.CalculationType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.EnumSet;
import java.util.Set;

public class RequiredValidFraLegsValidator
    implements ConstraintValidator<RequiredValidFraLegs, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {

    if (form == null) {
      return true;
    }
    var leg1 = form.getLeg1();
    var leg2 = form.getLeg2();
    if (allNotNull(leg1, leg2)) {
      Set<CalculationType> calculationTypes =
          EnumSet.of(leg1.getCalculationType(), leg2.getCalculationType());

      if (!(calculationTypes.contains(FIXED) && calculationTypes.contains(IBOR))) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
            .addPropertyNode("leg1")
            .addPropertyNode("calculationType")
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
