package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.portfolio.CoreProductType.FXOPT;
import static org.slf4j.LoggerFactory.getLogger;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.core.common.validation.BusinessDayConventionsSupplier;
import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.PositionTypeBuySupplier;
import com.solum.xplain.core.common.validation.PositionTypeSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.validation.ValidZone;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableFxOptionDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.ExpiryDateBeforePaymentDate;
import com.solum.xplain.core.portfolio.validation.RequiredDifferentAmountSigns;
import com.solum.xplain.core.portfolio.validation.RequiredDifferentCurrencies;
import com.solum.xplain.core.portfolio.validation.ValidCallPut;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.FxCollarReferenceTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.FxOptionReferenceTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.core.portfolio.value.CurrencyAmountForm;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import com.solum.xplain.extensions.enums.CallPutType;
import com.solum.xplain.extensions.enums.PositionType;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import java.time.LocalDate;
import java.time.LocalTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@RequiredDifferentCurrencies
@RequiredDifferentAmountSigns
@ExpiryDateBeforePaymentDate
@ValidCallPut
public class FxOptionTradeForm extends CommonFxTradeForm
    implements ParsableToTradeValue, SingleExchangeFxTradeForm {

  private static final Logger LOG = getLogger(FxOptionTradeForm.class);

  @NotEmpty
  @ValidStringSet(PositionTypeSupplier.class)
  @ValidStringSet(value = PositionTypeBuySupplier.class, groups = ReferenceTradeGroup.class)
  private String position;

  @Null(groups = FxCollarReferenceTradeGroup.class)
  @NotNull(groups = {FxOptionReferenceTradeGroup.class, BespokeTradeGroup.class})
  private CallPutType callPutType;

  @NotNull private LocalTime expiryTime;

  @NotNull private LocalDate expiryDate;

  @NotEmpty @ValidZone private String expiryZone;

  @NotNull private LocalDate premiumDate;

  @ValidStringSet(BusinessDayConventionsSupplier.class)
  @NotEmpty
  private String premiumDateConvention;

  @Valid @NotNull private CurrencyAmountForm premiumValue;

  @ValidStringSet(CurrenciesSupplier.class)
  private String tradeCurrency;

  public static Logger getLOG() {
    return LOG;
  }

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return vanillaOptionTradeDetails(toTradeInfo())
        .map(details -> defaultTradeValue(FXOPT, details));
  }

  @Override
  protected String tradeCurrency() {
    if (StringUtils.isNotEmpty(tradeCurrency)) {
      return tradeCurrency;
    }
    return super.tradeCurrency();
  }

  private Either<ErrorItem, TradeDetails> vanillaOptionTradeDetails(TradeInfoDetails tradeInfo) {
    try {
      var builder =
          ResolvableFxOptionDetails.builder()
              .businessDayConvention(getBusinessDayConvention())
              .paymentDate(getPaymentDate())
              .positionType(PositionType.valueOf(position))
              .expiryDate(expiryDate)
              .expiryTime(expiryTime)
              .expiryZone(expiryZone)
              .premiumDate(premiumDate)
              .payLegExtIdentifier(StringUtils.upperCase(getPayLegExtIdentifier()))
              .receiveLegExtIdentifier(StringUtils.upperCase(getReceiveLegExtIdentifier()))
              .premiumCurrency(Currency.of(premiumValue.getCurrency()))
              .premiumValue(premiumValue.getAmount())
              .premiumDateConvention(premiumDateConvention);
      if (getFxRate() != null) {
        var baseCurrency = Currency.of(getDomesticCurrencyAmount().getCurrency());
        var counterCurrency = Currency.of(getForeignCurrencyAmount().getCurrency());
        var payCcy =
            switch (callPutType) {
              case PUT -> baseCurrency;
              case CALL -> counterCurrency;
            };
        var recCcy = CurrencyPair.of(baseCurrency, counterCurrency).other(payCcy);
        return Either.right(
            builder
                .callPutType(callPutType)
                .strike(getFxRate())
                .payCurrency(payCcy)
                .receiveCurrency(recCcy)
                .build()
                .toTradeDetails(tradeInfo));
      }
      return Steps.begin(payCurrencyAmount())
          .then(this::receiveCurrencyAmount)
          .yield(
              (pay, receive) ->
                  builder
                      .receiveCurrency(Currency.of(receive.getCurrency()))
                      .receiveCurrencyAmount(receive.getAmount())
                      .payCurrency(Currency.of(pay.getCurrency()))
                      .payCurrencyAmount(pay.getAmount())
                      .callPutType(callPutType)
                      .build()
                      .toTradeDetails(tradeInfo));
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return Either.left(new ErrorItem(Error.CONVERSION_ERROR, ex.getMessage()));
    }
  }
}
