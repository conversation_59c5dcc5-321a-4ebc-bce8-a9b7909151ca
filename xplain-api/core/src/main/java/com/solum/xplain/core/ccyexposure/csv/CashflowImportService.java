package com.solum.xplain.core.ccyexposure.csv;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.ccyexposure.CcyExposureRepository;
import com.solum.xplain.core.ccyexposure.entity.Cashflow;
import com.solum.xplain.core.ccyexposure.entity.CcyExposure;
import com.solum.xplain.core.ccyexposure.repository.CcyExposureCashflowRepository;
import com.solum.xplain.core.ccyexposure.value.CashflowForm;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.BaseVersionedImportService;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.ItemsGroupCsvResult;
import com.solum.xplain.core.common.csv.NamedList;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import jakarta.inject.Provider;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CashflowImportService
    extends BaseVersionedImportService<CashflowForm, CashflowDateItemKey, Cashflow> {

  private final Provider<CashflowCsvLoader> csvLoaderProvider;
  private final CcyExposureRepository ccyExposureRepository;
  private final CcyExposureCashflowRepository ccyExposureCashflowRepository;

  protected CashflowImportService(
      AuditEntryService auditEntryService,
      Provider<CashflowCsvLoader> csvLoaderProvider,
      CcyExposureRepository ccyExposureRepository,
      CcyExposureCashflowRepository ccyExposureCashflowRepository) {
    super(auditEntryService);
    this.csvLoaderProvider = csvLoaderProvider;
    this.ccyExposureRepository = ccyExposureRepository;
    this.ccyExposureCashflowRepository = ccyExposureCashflowRepository;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importForAll(
      ImportOptions importOptions, byte[] bytes) {
    var exposures = ccyExposureRepository.getActiveExposures();
    return importForExposures(exposures, importOptions, bytes);
  }

  public Either<List<ErrorItem>, List<EntityId>> importForSingleExposure(
      String ccyExposureId, LocalDate versionDate, ImportOptions importOptions, byte[] bytes) {
    return importForExposures(
        List.of(ccyExposureRepository.findByIdAndArchivedIsFalse(ccyExposureId)),
        importOptions,
        bytes);
  }

  private Either<List<ErrorItem>, List<EntityId>> importForExposures(
      List<CcyExposure> exposures, ImportOptions importOptions, byte[] bytes) {
    return csvLoaderProvider
        .get()
        .parse(bytes, exposures, importOptions.getDuplicateAction())
        .toEither(true)
        .map(parsed -> importItems(exposures, toParsedCsvResult(parsed), importOptions))
        .fold(
            err -> toErrorReturn(importOptions.getDuplicateAction(), err),
            importResult -> toReturn(importOptions.getDuplicateAction(), importResult));
  }

  private CsvParserResult<CashflowForm> toParsedCsvResult(
      ItemsGroupCsvResult<CashflowForm> itemsGroupCsvResult) {

    List<CashflowForm> items =
        itemsGroupCsvResult.getNamedLists().stream()
            .map(NamedList::getItems)
            .flatMap(List::stream)
            .toList();
    return new CsvParserResult<CashflowForm>(
        items, ((List<ErrorItem>) (List<?>) itemsGroupCsvResult.getWarnings())); // FIXME Nasty cast
  }

  private ImportResult importItems(
      List<CcyExposure> exposures,
      CsvParserResult<CashflowForm> parserResult,
      ImportOptions importOptions) {
    var importItems = buildImportItems(exposures, importOptions.getStateDate(), parserResult);
    var importLogs = importItems(importOptions, importItems);
    return new ImportResult(importLogs, parserResult.getWarnings());
  }

  private ImportItems<CashflowForm, CashflowDateItemKey, Cashflow> buildImportItems(
      List<CcyExposure> exposures, LocalDate stateDate, CsvParserResult<CashflowForm> forms) {
    var existingItems =
        ccyExposureCashflowRepository.getActiveItemsByCcyExposureIdIn(
            exposures.stream().map(CcyExposure::getId).toList(), stateDate);
    return ImportItems.<CashflowForm, CashflowDateItemKey, Cashflow>builder()
        .existingActiveItems(existingItems)
        .existingItemToKeyFn(c -> new CashflowDateItemKey(c.getCcyExposureId(), c.getDate()))
        .importItems(forms.getParsedLines())
        .importItemToKeyFn(i -> new CashflowDateItemKey(i.getCcyExposureId(), i.getDate()))
        .build();
  }

  @Override
  protected String getIdentifier(CashflowDateItemKey key) {
    return key.getIdentifier();
  }

  @Override
  protected Either<ErrorItem, EntityId> insert(String parentId, CashflowForm form) {
    return ccyExposureCashflowRepository.create(form);
  }

  @Override
  protected Either<ErrorItem, EntityId> update(Cashflow entity, CashflowForm form) {
    return ccyExposureCashflowRepository.update(entity.getEntityId(), entity.getValidFrom(), form);
  }

  @Override
  protected Either<ErrorItem, EntityId> archive(Cashflow entity, ArchiveEntityForm f) {
    return ccyExposureCashflowRepository.archive(entity.getEntityId(), entity.getValidFrom(), f);
  }

  @Override
  protected boolean hasFutureVersions(
      String groupId, CashflowDateItemKey key, LocalDate stateDate) {
    //    var searchForm = new XxSearchForm(key, stateDate);
    return false; // ccyExposureCashflowRepository.futureVersions(searchForm).getDates().isEmpty();
  }

  @Override
  protected String getCollection() {
    return Cashflow.CASHFLOW_COLLECTION;
  }

  @Override
  protected String getObjectName() {
    return "Cashflow";
  }
}
