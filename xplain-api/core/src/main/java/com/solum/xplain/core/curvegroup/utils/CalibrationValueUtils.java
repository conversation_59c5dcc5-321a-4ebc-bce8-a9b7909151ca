package com.solum.xplain.core.curvegroup.utils;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toUnmodifiableMap;

import com.solum.xplain.core.common.value.CalculatedValueAtDate;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateKey;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CalibrationValueUtils {

  public static Map<LocalDate, ChartPoint> chartPoints(
      CurveGroupView curveGroup,
      CurveConfigMarketStateForm stateForm,
      CalculationDiscountingType calibrationCurrency,
      List<ChartPoint> rawChartPoints) {
    return stateForm
        .toKey()
        .filter(key -> hasCalibration(curveGroup, key, calibrationCurrency))
        .map(key -> toMapByDate(rawChartPoints))
        .orElse(Map.of());
  }

  public static Map<LocalDate, CalculatedValueAtDate> calibrationValues(
      List<CalculatedValueAtDate> rawDiscountFactorPoints) {
    return toMapByDate(rawDiscountFactorPoints);
  }

  private static boolean hasCalibration(CurveGroupView curveGroup, CurveConfigMarketStateKey key) {
    return Objects.equals(key.getStateDate().getActualDate(), curveGroup.getCalibrationDate())
        && Objects.equals(key.getMarketDataGroupId(), curveGroup.getCalibrationMarketDataGroupId())
        && Objects.equals(
            key.getMarketDataSource(), curveGroup.getCalibrationMarketDataSourceType())
        && Objects.equals(key.getConfigurationId(), curveGroup.getCalibrationCurveConfigId())
        && Objects.equals(key.getPriceRequirements(), curveGroup.getCalibrationPriceRequirements());
  }

  private static boolean hasCalibration(
      CurveGroupView curveGroup,
      CurveConfigMarketStateKey key,
      CalculationDiscountingType calibrationCurrency) {
    if (calibrationCurrency != null) {
      return hasCalibration(curveGroup, key)
          && Objects.equals(calibrationCurrency, curveGroup.getCalibrationCurrency());
    }
    return hasCalibration(curveGroup, key);
  }

  private static <T extends CalculatedValueAtDate> Map<LocalDate, T> toMapByDate(List<T> items) {
    return items.stream().collect(toUnmodifiableMap(T::getDate, identity(), (a, b) -> a));
  }
}
