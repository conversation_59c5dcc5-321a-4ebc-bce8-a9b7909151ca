package com.solum.xplain.core.curvegroup.ratefx.csv;

import com.solum.xplain.core.common.csv.NodeKey;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeForm;
import com.solum.xplain.core.curvegroup.volatilityfx.HasFxPair;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class FxRatesNodeUniqueKey implements NodeKey, HasFxPair {
  private final String domesticCurrency;
  private final String foreignCurrency;

  public static FxRatesNodeUniqueKey from(CurveGroupFxRatesNodeForm form) {
    return new FxRatesNodeUniqueKey(form.getDomesticCurrency(), form.getForeignCurrency());
  }

  @Override
  public String getDomesticCurrency() {
    return domesticCurrency;
  }

  @Override
  public String getForeignCurrency() {
    return foreignCurrency;
  }

  @Override
  public String getIdentifier() {
    return getFxPair();
  }

  @Override
  public String getEntityTypeName() {
    return "Rate";
  }

  @Override
  public String getParentEntityTypeName() {
    return "Rates";
  }
}
