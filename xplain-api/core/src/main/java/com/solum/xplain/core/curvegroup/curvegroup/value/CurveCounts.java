package com.solum.xplain.core.curvegroup.curvegroup.value;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class CurveCounts {
  private final Integer numberOfCurves;
  private final Integer numberOfVolatilitySurfaces;
  private final Integer numberOfCreditCurves;
  private final Integer numberOfBondCurves;
  private final Integer numberOfFxVolatilities;
  private final Integer numberOfFxRateNodes;
  private final Integer numberOfInflationVolatilities;

  public static CurveCounts empty() {
    return new CurveCounts(0, 0, 0, 0, 0, 0, 0);
  }
}
