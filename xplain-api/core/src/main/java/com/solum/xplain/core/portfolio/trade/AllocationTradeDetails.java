package com.solum.xplain.core.portfolio.trade;

import com.solum.xplain.core.portfolio.ClientMetrics;
import com.solum.xplain.core.portfolio.value.CounterpartyType;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import com.solum.xplain.extensions.enums.PositionType;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class AllocationTradeDetails {

  @ConfigurableViewQuery(sortable = true)
  private String referenceTradeId;

  private Double allocationNotional;
  private PositionType positionType;
  private String counterParty;
  private CounterpartyType counterPartyType;
  private ClientMetrics clientMetrics;
  private LocalDate tradeDate;
  private String description;
  @ConfigurableViewIgnore private String csaDiscountingGroup;
  private String optionPosition;
  private String protection;
}
