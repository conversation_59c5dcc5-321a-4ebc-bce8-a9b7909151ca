package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.portfolio.CoreProductType.CREDIT_INDEX_TRANCHE;

import com.solum.xplain.core.common.creditindex.HasCreditTranche;
import com.solum.xplain.core.common.creditindex.ValidCreditIndexTranche;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.builder.ResolvableCreditIndexDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import io.atlassian.fugue.Either;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@NoArgsConstructor
@ValidCreditIndexTranche
public class CreditIndexTrancheTradeForm extends CreditIndexTradeForm
    implements ParsableToTradeValue, HasCreditTranche {

  @NotEmpty private String creditIndexTranche;

  public CreditIndexTrancheTradeForm(VersionedTradeEntity entity) {
    super(entity);
    this.creditIndexTranche =
        entity.getTradeDetails().getCreditTradeDetails().getCreditIndexTranche();
  }

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return toTrade(toTradeInfo()).map(details -> defaultTradeValue(CREDIT_INDEX_TRANCHE, details));
  }

  @Override
  protected ResolvableCreditIndexDetails.ResolvableCreditIndexDetailsBuilder builder() {
    return super.builder().creditIndexTranche(getCreditIndexTranche());
  }
}
