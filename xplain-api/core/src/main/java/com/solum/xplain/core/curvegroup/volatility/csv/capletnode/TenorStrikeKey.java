package com.solum.xplain.core.curvegroup.volatility.csv.capletnode;

import com.solum.xplain.core.common.csv.NodeKey;
import com.solum.xplain.core.curvegroup.ParsableTenor;
import com.solum.xplain.core.curvegroup.volatility.entity.CapletVolatilityNode;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeForm;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class TenorStrikeKey implements ParsableTenor, NodeKey {
  private final String tenor;
  private final BigDecimal strike;

  private TenorStrikeKey(String tenor, BigDecimal strike) {
    this.tenor = tenor;
    this.strike = strike != null ? strike.stripTrailingZeros() : null;
  }

  public static TenorStrikeKey from(CapletVolatilityNodeForm form) {
    return new TenorStrikeKey(form.getTenor(), form.getStrike());
  }

  public static TenorStrikeKey from(CapletVolatilityNode entity) {
    return new TenorStrikeKey(entity.getTenor(), entity.getStrike());
  }

  @Override
  public String getIdentifier() {
    return String.format("%s_%s", getTenor(), getStrike());
  }
}
