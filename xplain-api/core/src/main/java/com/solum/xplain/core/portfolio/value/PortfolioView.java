package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.audit.value.AuditLogView;
import com.solum.xplain.core.common.team.WithTeamsView;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.jspecify.annotations.NonNull;

@Data
@FieldNameConstants
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PortfolioView extends PortfolioCondensedView implements WithTeamsView {

  private String name;
  private String companyName;
  private String entityName;
  private @NonNull List<String> teamIds = new ArrayList<>();
  private Boolean allowAllTeams;
  private String creatorId;
  private String creatorName;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private String modifiedBy;
  private boolean editable = true;
  private LocalDateTime calculatedAt;
  private String calculatedBy;
  private LocalDate valuationDate;
  private String description;
  private boolean archived;
  private List<AuditLogView> auditLogs = new ArrayList<>();
}
