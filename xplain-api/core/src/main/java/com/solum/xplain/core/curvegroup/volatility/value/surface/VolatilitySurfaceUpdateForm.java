package com.solum.xplain.core.curvegroup.volatility.value.surface;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CAPLET_VALUATION_MODEL_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_INTERPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SURFACE_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.VOLATILITY_TYPE_CLASSIFIER;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.UniqueValues;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.value.HasVersionForm;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.curvegroup.volatility.validation.VolatilitySurfaceUpdateFormGroupProvider;
import com.solum.xplain.core.curvegroup.volatility.validation.groups.VolatilitySurfaceATMGroup;
import com.solum.xplain.core.curvegroup.volatility.validation.groups.VolatilitySurfaceNonSabrFormGroup;
import com.solum.xplain.core.curvegroup.volatility.validation.groups.VolatilitySurfaceSabrFormGroup;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeForm;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeForm;
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewForm;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@GroupSequenceProvider(VolatilitySurfaceUpdateFormGroupProvider.class)
public class VolatilitySurfaceUpdateForm implements HasVersionForm {

  @NotEmpty
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_INTERPOLATOR_CLASSIFIER)
  @JsonProperty("xInterpolator")
  private String xInterpolator;

  @NotEmpty
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = SURFACE_EXTRAPOLATOR_CLASSIFIER)
  @JsonProperty("xExtrapolatorLeft")
  private String xExtrapolatorLeft;

  @NotEmpty
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = SURFACE_EXTRAPOLATOR_CLASSIFIER)
  @JsonProperty("xExtrapolatorRight")
  private String xExtrapolatorRight;

  @NotEmpty
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_INTERPOLATOR_CLASSIFIER)
  @JsonProperty("yInterpolator")
  private String yInterpolator;

  @NotEmpty
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = SURFACE_EXTRAPOLATOR_CLASSIFIER)
  @JsonProperty("yExtrapolatorLeft")
  private String yExtrapolatorLeft;

  @NotEmpty
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = SURFACE_EXTRAPOLATOR_CLASSIFIER)
  @JsonProperty("yExtrapolatorRight")
  private String yExtrapolatorRight;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = VOLATILITY_TYPE_CLASSIFIER)
  private String skewType;

  @NotNull private Boolean sabr;

  @NotNull(groups = VolatilitySurfaceSabrFormGroup.class)
  @Null(groups = VolatilitySurfaceNonSabrFormGroup.class)
  private Double sabrBeta;

  @NotNull(groups = VolatilitySurfaceSabrFormGroup.class)
  @Null(groups = VolatilitySurfaceNonSabrFormGroup.class)
  private Double sabrShift;

  @NotEmpty
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CAPLET_VALUATION_MODEL_CLASSIFIER)
  private String capletValuationModel;

  @Valid @NotNull private NewVersionFormV2 versionForm;

  @Valid @UniqueValues private List<VolatilityNodeForm> nodes;

  @Valid
  @UniqueValues
  @Null(groups = VolatilitySurfaceATMGroup.class)
  private List<VolatilitySurfaceSkewForm> skewNodes;

  @Valid @UniqueValues private List<CapletVolatilityNodeForm> capletVolatilities;
}
