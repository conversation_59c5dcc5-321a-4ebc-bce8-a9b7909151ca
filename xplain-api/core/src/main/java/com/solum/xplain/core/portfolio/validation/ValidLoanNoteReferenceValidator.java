package com.solum.xplain.core.portfolio.validation;

import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.curvegroup.conventions.bond.BondCurveConventions;
import com.solum.xplain.core.portfolio.form.LoanNoteTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidLoanNoteReferenceValidator
    implements ConstraintValidator<ValidLoanNoteReference, LoanNoteTradeForm> {

  @Override
  public boolean isValid(LoanNoteTradeForm value, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    if (allNotNull(value.getCurrency(), value.getReference())) {
      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode("reference")
          .addConstraintViolation();

      return BondCurveConventions.getCurves().stream()
          .filter(v -> v.getName().equals(value.getReference()))
          .anyMatch(v -> v.getCurrency().getCode().equals(value.getCurrency()));
    }
    return true;
  }
}
