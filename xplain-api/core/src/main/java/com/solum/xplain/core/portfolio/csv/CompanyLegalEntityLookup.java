package com.solum.xplain.core.portfolio.csv;

import com.solum.xplain.core.company.entity.CompanyLegalEntity;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CompanyLegalEntityLookup {
  private final Map<String, String> entitiesMap;

  public static CompanyLegalEntityLookup newOf(List<CompanyLegalEntity> entities) {
    var map =
        entities.stream()
            .collect(
                Collectors.toMap(CompanyLegalEntity::getExternalId, CompanyLegalEntity::getId));
    return new CompanyLegalEntityLookup(map);
  }

  public static CompanyLegalEntityLookup empty() {
    return new CompanyLegalEntityLookup(Map.of());
  }

  public Set<String> validExternalIds() {
    return entitiesMap.keySet();
  }

  public String entityId(String externalId) {
    return entitiesMap.get(externalId);
  }
}
