package com.solum.xplain.core.portfolio.trade;

import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.ProductTypeProvider;
import java.util.Arrays;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class CoreProductTypeProvider implements ProductTypeProvider {

  @Override
  public List<ProductType> products() {
    return Arrays.asList(CoreProductType.values());
  }
}
