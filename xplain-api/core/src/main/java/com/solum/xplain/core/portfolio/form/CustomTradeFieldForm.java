package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.customfield.validation.ValidCustomFieldName;
import com.solum.xplain.core.portfolio.trade.CustomTradeField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.experimental.FieldNameConstants;

@FieldNameConstants
public record CustomTradeFieldForm(
    @Schema(description = "External field identifier") @NotEmpty @ValidCustomFieldName
        String externalFieldId,
    @Schema(description = "Custom field value") @NotEmpty String value) {

  public static List<CustomTradeFieldForm> fromEntities(List<CustomTradeField> identifiers) {
    if (identifiers == null) {
      return List.of();
    }
    return identifiers.stream()
        .map(i -> new CustomTradeFieldForm(i.getExternalFieldId(), i.getValue()))
        .toList();
  }

  public CustomTradeField toField() {
    return new CustomTradeField(externalFieldId, value);
  }
}
