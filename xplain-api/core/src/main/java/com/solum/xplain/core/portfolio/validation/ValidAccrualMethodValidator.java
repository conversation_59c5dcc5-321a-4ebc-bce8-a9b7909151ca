package com.solum.xplain.core.portfolio.validation;

import static com.opengamma.strata.product.swap.OvernightAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE;

import com.opengamma.strata.product.swap.FixedAccrualMethod;
import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import com.solum.xplain.core.portfolio.value.CalculationType;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Objects;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;

public class ValidAccrualMethodValidator
    implements ConstraintValidator<ValidAccrualMethod, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    if (form != null
        && swapLegsStream(form).anyMatch(v -> v.getCalculationType() == CalculationType.FIXED)) {
      var hasFixedAccrual =
          swapLegsStream(form)
              .filter(v -> v.getCalculationType() == CalculationType.FIXED)
              .map(SwapLegForm::getCalculationFixedAccrualMethod)
              .anyMatch(
                  v ->
                      StringUtils.equals(
                          FixedAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE.toString(), v));

      var hasOvernightAnnualAccrualMethod =
          swapLegsStream(form)
              .filter(v -> v.getCalculationType() == CalculationType.OVERNIGHT)
              .map(SwapLegForm::getCalculationOvernightAccrualMethod)
              .filter(StringUtils::isNotEmpty)
              .anyMatch(
                  method ->
                      StringUtils.equals(method, OVERNIGHT_COMPOUNDED_ANNUAL_RATE.toString()));
      return hasFixedAccrual == hasOvernightAnnualAccrualMethod;
    }
    return true;
  }

  private Stream<SwapLegForm> swapLegsStream(SwapTradeForm form) {
    return Stream.of(form.getLeg1(), form.getLeg2()).filter(Objects::nonNull);
  }
}
