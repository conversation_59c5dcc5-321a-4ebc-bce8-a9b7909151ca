package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.common.validation.ValidIdentifierSource;
import com.solum.xplain.core.common.validation.identifier.ValidExternalIdentifier;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.Data;

@Data
public class ExternalIdentifierForm {
  @NotEmpty
  @ValidExternalIdentifier
  @Schema(description = "External identifier")
  private final String identifier;

  @NotEmpty
  @ValidIdentifierSource
  @Schema(description = "Source of external identifier")
  private final String externalSourceId;

  public static List<ExternalIdentifierForm> fromEntities(List<ExternalIdentifier> identifiers) {
    if (identifiers == null) {
      return List.of();
    }
    return identifiers.stream()
        .map(i -> new ExternalIdentifierForm(i.getIdentifier(), i.getExternalSourceId()))
        .toList();
  }

  public ExternalIdentifier toIdentifier() {
    return new ExternalIdentifier(identifier, externalSourceId);
  }
}
