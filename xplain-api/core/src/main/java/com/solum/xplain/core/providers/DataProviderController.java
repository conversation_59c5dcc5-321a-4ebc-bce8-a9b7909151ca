package com.solum.xplain.core.providers;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_DATA_PROVIDER;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_DATA_PROVIDER;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.lock.XplainLock.DATA_PROVIDERS_LOCK_ID;
import static com.solum.xplain.core.providers.enums.DataProviderType.MARKET;
import static com.solum.xplain.core.providers.enums.DataProviderType.VALUATION;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.SortedFiltered;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.providers.value.DataProviderCreateForm;
import com.solum.xplain.core.providers.value.DataProviderUpdateForm;
import com.solum.xplain.core.providers.value.DataProviderView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/data-providers")
public class DataProviderController {

  private final DataProviderControllerService service;

  private final DataProviderCsvImportService uploadService;

  public DataProviderController(
      DataProviderControllerService service, DataProviderCsvImportService uploadService) {
    this.service = service;
    this.uploadService = uploadService;
  }

  @Operation(summary = "Get all providers")
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_DATA_PROVIDER)
  public List<DataProviderView> getAll(
      @RequestParam boolean archived,
      TableFilter tableFilter,
      @SortDefault(sort = {DataProviderView.Fields.name, DataProviderView.Fields.externalId})
          Sort sort) {
    return service.list(tableFilter, sort, archived);
  }

  @Operation(summary = "Get all market data providers")
  @CommonErrors
  @GetMapping("/market")
  @PreAuthorize(AUTHORITY_VIEW_DATA_PROVIDER)
  public List<DataProviderView> getAllMarketProviders() {
    return service.listByType(MARKET);
  }

  @Operation(summary = "Get all trade data providers")
  @CommonErrors
  @GetMapping("/trade")
  @PreAuthorize(AUTHORITY_VIEW_DATA_PROVIDER)
  public List<DataProviderView> getAllTradeProviders() {
    return service.listByType(VALUATION);
  }

  @Operation(summary = "Create data provider")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_DATA_PROVIDER)
  @RequireLock(name = DATA_PROVIDERS_LOCK_ID)
  public ResponseEntity<EntityId> createDataProvider(
      @Valid @RequestBody DataProviderCreateForm form) {
    return eitherErrorItemResponse(service.insert(form));
  }

  @Operation(summary = "Update data provider")
  @CommonErrors
  @PutMapping("/{id}")
  @PreAuthorize(AUTHORITY_MODIFY_DATA_PROVIDER)
  @RequireLock(name = DATA_PROVIDERS_LOCK_ID)
  public ResponseEntity<EntityId> updateDataProvider(
      @PathVariable String id, @Valid @RequestBody DataProviderUpdateForm form) {
    return eitherErrorItemResponse(service.update(id, form));
  }

  @Operation(summary = "Archive data provider")
  @CommonErrors
  @PutMapping("/{id}/archive")
  @PreAuthorize(AUTHORITY_MODIFY_DATA_PROVIDER)
  @RequireLock(name = DATA_PROVIDERS_LOCK_ID)
  public ResponseEntity<EntityId> archiveDataProvider(@PathVariable String id) {
    return eitherErrorItemResponse(service.archive(id));
  }

  @Operation(summary = "Archive all data providers")
  @CommonErrors
  @PutMapping("/archive-all")
  @PreAuthorize(AUTHORITY_MODIFY_DATA_PROVIDER)
  @RequireLock(name = DATA_PROVIDERS_LOCK_ID)
  public List<EntityId> archiveDataProviders(TableFilter tableFilter) {
    return service.archiveAll(tableFilter);
  }

  @Operation(summary = "Upload data providers CSV file")
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_DATA_PROVIDER)
  @RequireLock(name = DATA_PROVIDERS_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadDataProviders(
      @RequestPart MultipartFile file,
      @RequestParam(required = false, defaultValue = "STRICT") ParsingMode parsingMode,
      @RequestParam DuplicateAction duplicates)
      throws IOException {
    return eitherErrorItemsResponse(
        uploadService.uploadMarketDataProviders(file.getBytes(), parsingMode, duplicates));
  }

  @Operation(summary = "Get providers list in csv format")
  @CommonErrors
  @GetMapping("/providers-csv")
  @SortedFiltered
  @PreAuthorize(AUTHORITY_VIEW_DATA_PROVIDER)
  public ResponseEntity<ByteArrayResource> getProvidersCsv(
      @RequestParam(required = false) List<String> selectedColumns,
      @RequestParam LocalDate stateDate,
      TableFilter tableFilter,
      Sort sort) {
    return service
        .getMarketDataProviders(selectedColumns, tableFilter, sort, stateDate)
        .toResponse();
  }
}
