package com.solum.xplain.core.curvegroup.curvebond.value;

import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class BondCurveNodeCalculatedView {

  private String key;
  private LocalDate maturityDate;
  private String cusip;
  private Double coupon;

  private String quoteId;
  private String dataSource;
  private String ticker;
  private BigDecimal marketValue;
  private BigDecimal marketValueAsk;
  private BigDecimal marketValueMid;
  private BigDecimal marketValueBid;
  private InstrumentPriceType calibrationPriceType;
}
