package com.solum.xplain.core.portfolio.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidAccrualPeriodValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ValidAccrualPeriod {
  String message() default
      "{com.solum.xplain.api.portfolio.validation" + ".ValidAccrualPeriod.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
