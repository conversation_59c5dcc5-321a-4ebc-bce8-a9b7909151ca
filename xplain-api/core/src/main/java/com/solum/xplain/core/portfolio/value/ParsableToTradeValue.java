package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import io.atlassian.fugue.Either;

public interface ParsableToTradeValue {

  NewVersionFormV2 getVersionForm();

  String getExternalTradeId();

  Either<ErrorItem, TradeValue> toTradeValue();
}
