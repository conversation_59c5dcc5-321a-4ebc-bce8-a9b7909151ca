package com.solum.xplain.core.ccyexposure.repository.fragment;

import com.solum.xplain.core.ccyexposure.entity.CcyExposure;
import com.solum.xplain.core.ccyexposure.value.CcyExposureCreateForm;
import com.solum.xplain.core.ccyexposure.value.CcyExposureUpdateForm;
import com.solum.xplain.core.common.diff.DiffAuditedRepository;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

public interface CcyExposureQueries
    extends DiffAuditedRepository<CcyExposureCreateForm, CcyExposureUpdateForm, CcyExposure> {

  List<CcyExposure> getActiveExposures();

  Either<ErrorItem, CcyExposure> getCcyExposure(String exposureId);

  /** for cashflows */
  List<CcyExposure> ccyExposures(
      @NotEmpty String exposureCurrency, @NotNull List<String> exposureIds);
}
