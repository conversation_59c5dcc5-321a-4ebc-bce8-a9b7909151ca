package com.solum.xplain.core.ccyexposure.value;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@NoArgsConstructor // Must provide no-arg constructor for Lombok when using @Nonnull
public class CcyExposureView {

  // AuditableDiffable fields
  @NotNull private String id;
  @NotNull private String modifiedBy;
  @NotNull private LocalDateTime modifiedAt;
  @NotNull private Boolean archived;

  // CcyExposure fields
  @NotNull private String name;
  @NotNull private String currency;
  private String description;
}
