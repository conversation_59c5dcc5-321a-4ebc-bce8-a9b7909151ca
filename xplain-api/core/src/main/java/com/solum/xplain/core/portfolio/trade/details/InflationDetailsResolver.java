package com.solum.xplain.core.portfolio.trade.details;

import static com.solum.xplain.core.curvegroup.conventions.ClearingHouse.resolveFromCounterparty;
import static com.solum.xplain.core.portfolio.trade.details.TradeDetailsUtils.tradeCcyOrPayLegPriorityNotional;

import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.conventions.index.InflationCurveConventions;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.details.ProductDetailsResolver;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Component;

@Component
public class InflationDetailsResolver implements ProductDetailsResolver {

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.INFLATION);
  }

  @Override
  public String resolveUnderlying(ProductType productType, TradeDetails tradeDetails) {
    var clearingHouse = resolveFromCounterparty(tradeDetails.getInfo().getCounterPartyType());
    return tradeDetails
        .legsStream()
        .filter(c -> c.getType() == CalculationType.INFLATION)
        .map(TradeLegDetails::getIndex)
        .map(index -> InflationCurveConventions.findByIndexAndClearingHouse(index, clearingHouse))
        .flatMap(Optional::stream)
        .map(CurveConvention::getName)
        .findFirst()
        .orElse(null);
  }

  @Override
  public double resolveNotional(TradeDetails tradeDetails) {
    return tradeCcyOrPayLegPriorityNotional(tradeDetails);
  }
}
