package com.solum.xplain.core.portfolio.trade;

import static com.solum.xplain.extensions.constants.PermissibleCurrencies.FX_SWAP_PAIRS;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.BusinessDayConvention;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType;
import com.solum.xplain.extensions.enums.PositionType;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Data
@FieldNameConstants
public class TradeDetails implements Serializable {
  private PositionType positionType;

  private TradeLegDetails payLeg;
  private TradeLegDetails receiveLeg;
  private TradeInfoDetails info;
  private OptionTradeDetails optionTradeDetails;
  private CreditTradeDetails creditTradeDetails;
  private LoanNoteTradeDetails loanNoteTradeDetails;
  private GenericTradeDetails customTradeDetails;
  private HedgeTradeDetails hedgeTradeDetails;

  private String calendar;
  private String businessDayConvention;
  private BusinessDayAdjustmentType businessDayAdjustmentType;

  private Boolean notionalScheduleInitialExchange;
  private Boolean notionalScheduleFinalExchange;

  private String stubConvention;
  private LocalDate startDate;
  private LocalDate endDate;
  private LocalDate firstRegularStartDate;
  private LocalDate lastRegularEndDate;

  private String rollConvention;
  private Double fxRate;
  private Double nearDateFxRate;

  public static TradeDetails newOf(TradeInfoDetails tradeInfoDetails) {
    var details = new TradeDetails();
    details.setInfo(tradeInfoDetails);
    return details;
  }

  public Optional<TradeLegDetails> tradePositionLeg() {
    if (positionType == PositionType.SELL) {
      return Optional.ofNullable(receiveLeg);
    } else if (positionType == PositionType.BUY) {
      return Optional.of(payLeg);
    }
    return Optional.empty();
  }

  public Optional<TradeLegDetails> leg(PayReceive payReceive) {
    return Optional.ofNullable(payReceive)
        .map(
            it ->
                switch (it) {
                  case PAY -> getPayLeg();
                  case RECEIVE -> getReceiveLeg();
                });
  }

  @NonNull
  public Currency tradeCurrency() {
    return Currency.of(info.getTradeCurrency());
  }

  @Nullable
  public CurrencyPair currencyPair() {
    var ccyCount =
        Stream.of(payLeg, receiveLeg)
            .filter(Objects::nonNull)
            .filter(l -> isNotEmpty(l.getCurrency()))
            .map(TradeLegDetails::currency)
            .distinct()
            .count();
    if (ccyCount > 1) {
      return FX_SWAP_PAIRS.stream()
          .filter(cp -> cp.contains(payLeg.currency()) && cp.contains(receiveLeg.currency()))
          .findAny()
          .orElse(CurrencyPair.of(payLeg.currency(), receiveLeg.currency()));
    }

    return null;
  }

  public BusinessDayAdjustment businessDayAdjustment(HolidayCalendarId calendarId) {
    return Optional.ofNullable(businessDayConvention)
        .map(BusinessDayConvention::of)
        .map(con -> BusinessDayAdjustment.of(con, calendarId))
        .orElse(BusinessDayAdjustment.NONE);
  }

  public Stream<TradeLegDetails> legsStream() {
    return Stream.of(payLeg, receiveLeg).filter(Objects::nonNull);
  }
}
