package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.error.Error.UNEXPECTED_TYPE;
import static com.solum.xplain.core.portfolio.value.FxFormUtils.assignFxFields;
import static com.solum.xplain.extensions.enums.CallPutType.PUT;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.FxCollarTradeForm;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FxCollarTradeView extends FxCollarTradeForm implements TradeView {
  private String tradeId;
  private String updatedBy;
  private LocalDateTime updatedAt;
  private String calendar;

  public static Either<ErrorItem, FxCollarTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == CoreProductType.FXCOLLAR) {
      var view = new FxCollarTradeView();
      view.updateCommonView(item);
      view.withTradeInfo(item.getTradeDetails());
      view.withTradeDetails(item.getTradeDetails());
      var callPutType = item.getTradeDetails().getOptionTradeDetails().getCallPutType();
      var baseCcy =
          callPutType == PUT
              ? item.getTradeDetails().getPayLeg().getCurrency()
              : item.getTradeDetails().getReceiveLeg().getCurrency();
      assignFxFields(item.getTradeDetails(), view, baseCcy);
      view.setTradeCurrency(item.getTradeDetails().getInfo().getTradeCurrency());
      view.setCallPutType(callPutType);
      view.setFxRate(item.getTradeDetails().getOptionTradeDetails().getStrike());
      view.setPaymentDate(item.getTradeDetails().getEndDate());
      view.setBusinessDayConvention(item.getTradeDetails().getBusinessDayConvention());
      view.setCalendar(item.getTradeDetails().getCalendar());
      return Either.right(view);
    } else {
      return Either.left(
          new ErrorItem(UNEXPECTED_TYPE, "Product type is not expected " + item.getProductType()));
    }
  }

  private void withTradeDetails(TradeDetails source) {
    this.setPosition(source.getPositionType().name());
    this.setExpiryDate(source.getOptionTradeDetails().getExpiryDate());
    this.setExpiryTime(source.getOptionTradeDetails().getExpiryTime());
    this.setExpiryZone(source.getOptionTradeDetails().getExpiryZone());
    this.setPremiumDate(source.getOptionTradeDetails().getPremiumDate());
    this.setPremiumValue(
        CurrencyAmountForm.of(
            source.getOptionTradeDetails().getPremiumCurrency(),
            source.getOptionTradeDetails().getPremiumValue()));
    this.setPremiumDateConvention(source.getOptionTradeDetails().getPremiumDateConvention());
    this.setOtherOptionFxRate(source.getOptionTradeDetails().getOtherOptionStrike());
    this.setOtherOptionCounterNotional(
        source.getOptionTradeDetails().getOtherOptionCounterNotional());
    this.setPayLegExtIdentifier(source.getPayLeg().getExtLegIdentifier());
    this.setReceiveLegExtIdentifier(source.getReceiveLeg().getExtLegIdentifier());
  }
}
