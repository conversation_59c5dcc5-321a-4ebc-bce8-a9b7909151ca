package com.solum.xplain.core.portfolio.trade;

import com.solum.xplain.extensions.enums.CallPutType;
import com.solum.xplain.extensions.enums.CapFloorType;
import jakarta.annotation.Nullable;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class OptionTradeDetails implements Serializable {

  private CapFloorType capFloorType;
  private CallPutType callPutType;

  /** The time on the expiry date when the option contract expires */
  private LocalTime expiryTime;

  /** The time zone of the expiry time */
  private String expiryZone;

  /** The date on which the option contract expires */
  private LocalDate expiryDate;

  /**
   * The convention to account for when the expiry date is not a business day. E.g. "Modified
   * Following"
   */
  private String expiryDateConvention;

  /** The date on which the premium is paid/received */
  private LocalDate premiumDate;

  private String premiumDateConvention;

  /** the price paid (BUY) or received (SELL) for this option contract */
  private Double premiumValue;

  /** the currency of the price of this option contract */
  private String premiumCurrency;

  /**
   * Whether the underlying swap (physical) actually happens or a cash equivalent is used, or some
   * hybrid
   */
  private String swaptionSettlementType;

  /** The strike price of the option contract. For Fx this will be the exchange rate */
  private double strike;

  /** The counter notional of the other option when this is a collar */
  private @Nullable Double otherOptionCounterNotional;

  /** The Fx rate of the other option when this is a collar */
  private @Nullable Double otherOptionStrike;
}
