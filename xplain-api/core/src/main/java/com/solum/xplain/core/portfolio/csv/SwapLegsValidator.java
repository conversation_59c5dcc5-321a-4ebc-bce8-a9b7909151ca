package com.solum.xplain.core.portfolio.csv;

import static com.opengamma.strata.product.swap.OvernightAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static com.solum.xplain.core.portfolio.CoreProductType.FRA;
import static com.solum.xplain.core.portfolio.CoreProductType.IRS;
import static com.solum.xplain.core.portfolio.CoreProductType.SWAPTION;
import static com.solum.xplain.core.portfolio.CoreProductType.XCCY;
import static com.solum.xplain.core.portfolio.value.CalculationType.FIXED;
import static com.solum.xplain.core.portfolio.value.CalculationType.IBOR;
import static com.solum.xplain.core.portfolio.value.CalculationType.INFLATION;
import static com.solum.xplain.core.portfolio.value.CalculationType.OVERNIGHT;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.lang.String.format;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.opengamma.strata.collect.io.CsvRow;
import com.opengamma.strata.product.swap.FixedAccrualMethod;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.builder.ResolvableFixedLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableOvernightLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeLegDetails;
import com.solum.xplain.core.portfolio.validation.ValidOffshoreLegsValidator;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.extensions.enums.PositionType;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class SwapLegsValidator {

  public static final String REQUIRES_INFLATION_LEG = "Inflation must contain Inflation leg.";
  private static final String INVALID_FIXED_ACCRUAL_METHOD =
      "Fixed accrual method must be set when overnight accrual method is %s.";
  private static final String INVALID_CURRENCIES = "%s requires same currency for both legs.";
  private static final String INVALID_IRS_LEGS = "IRS must not contain both %s legs.";
  private static final String INVALID_FRA_LEGS = "FRA can only contain Fixed and Ibor, legs";
  private static final String INVALID_FRA_POSITION = "FRA with %s requires %s Ibor leg";
  private static final String INVALID_SWAPTION_LEGS =
      "SWAPTION can only have Fixed/Ibor or Fixed/Overnight legs.";
  private static final String INVALID_NOTIONAL = "%s requires same notional for both legs.";
  private static final String INVALID_DAY_COUNTS = "%s requires same day count for both legs.";
  private static final String NO_INFLATION_LEG = "%s must not contain inflation legs.";
  private static final String NO_OVERNIGHT_LEG = "%s must not contain overnight legs.";
  private static final String REQUIRES_DIFFERENT_CURRENCIES =
      "XCCY requires different currency for both legs.";

  public static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateLegs(
      CoreProductType productType,
      @Nullable PositionType positionType,
      List<ResolvableTradeLegDetails> legs,
      CsvRow row) {
    return validateLegsSize(legs, row)
        .flatMap(l -> validateLegIdentifiers(l, row))
        .flatMap(l -> validatePayReceive(l, row))
        .flatMap(l -> validateAccrualMethod(l, row))
        .flatMap(l -> validateProductTypeLegs(productType, positionType, l, row))
        .flatMap(l -> validateOffshoreFlags(l, row));
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateLegsSize(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return Eithers.cond(
        legs.size() == 2, errorItem("Required 2 legs. Got: " + legs.size(), row), legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateOffshoreFlags(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    var iborOvernightLegs =
        legs.stream()
            .filter(v -> v.getCalculationType() == IBOR || v.getCalculationType() == OVERNIGHT)
            .toList();
    var legsDiffer =
        iborOvernightLegs.stream()
            .map(ResolvableTradeLegDetails::toTradeLegDetails)
            .map(v -> isTrue(v.getIsOffshore()))
            .reduce(Boolean::logicalXor)
            .orElse(false);
    if (iborOvernightLegs.size() == 2 && isTrue(legsDiffer)) {
      return left(
          errorItem(String.format(ValidOffshoreLegsValidator.OFFSHORE_FIELDS_DIFFER_ERROR), row));
    }
    return Either.right(legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateLegIdentifiers(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    var legIds =
        legs.stream()
            .map(ResolvableTradeLegDetails::getExtLegIdentifier)
            .filter(Objects::nonNull)
            .toList();
    if (legIds.size() == 2 && StringUtils.equals(legIds.get(0), legIds.get(1))) {
      return left(
          errorItem(
              String.format(
                  "Required different LEG IDENTIFIERS. Got: %s, %s", legIds.get(0), legIds.get(1)),
              row));
    }
    return right(legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validatePayReceive(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    if (legs.get(0).getPayReceive() == legs.get(1).getPayReceive()) {
      return left(
          errorItem(
              String.format(
                  "Required PAY and RECEIVE legs. Got: %s,%s",
                  legs.get(0).getPayReceive(), legs.get(1).getPayReceive()),
              row));
    }
    return right(legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateAccrualMethod(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    var fixedAccrual =
        legs.stream()
            .filter(v -> v.getCalculationType() == FIXED)
            .findFirst()
            .filter(ResolvableFixedLeg.class::isInstance)
            .map(ResolvableFixedLeg.class::cast)
            .map(ResolvableFixedLeg::getAccrualMethod)
            .filter(FixedAccrualMethod.OVERNIGHT_COMPOUNDED_ANNUAL_RATE::equals);
    var overnightAccrual =
        legs.stream()
            .filter(v -> v.getCalculationType() == CalculationType.OVERNIGHT)
            .findFirst()
            .filter(ResolvableOvernightLeg.class::isInstance)
            .map(ResolvableOvernightLeg.class::cast)
            .map(ResolvableOvernightLeg::getOvernightAccrualMethod)
            .filter(OVERNIGHT_COMPOUNDED_ANNUAL_RATE::equals);

    // both legs must have OVERNIGHT_COMPOUNDED_ANNUAL_RATE accrual method when one leg is fixed
    if (overnightAccrual.isPresent() != fixedAccrual.isPresent()
        && legs.stream().anyMatch(v -> v.getCalculationType() == CalculationType.FIXED)) {
      return left(
          errorItem(
              String.format(INVALID_FIXED_ACCRUAL_METHOD, OVERNIGHT_COMPOUNDED_ANNUAL_RATE), row));
    }
    return right(legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateProductTypeLegs(
      CoreProductType productType,
      @Nullable PositionType positionType,
      List<ResolvableTradeLegDetails> legs,
      CsvRow row) {
    return switch (productType) {
      case SWAPTION -> validateSwaptionLegs(legs, row);
      case IRS -> validateIrsLegs(legs, row);
      case FRA -> validateFraLegs(legs, row, positionType);
      case XCCY -> validateXccyLegs(legs, row);
      case INFLATION -> validateInflationLegs(legs, row);
      default -> right(legs);
    };
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateInflationLegs(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return sameCurrencies(CoreProductType.INFLATION, legs, row)
        .flatMap(l -> validInflationLegs(l, row));
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateXccyLegs(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return differentXccyCurrencies(legs, row).flatMap(l -> noInflationLegs(XCCY, legs, row));
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateIrsLegs(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return sameCurrencies(IRS, legs, row)
        .flatMap(l -> notSameIrsLegs(FIXED, l, row))
        .flatMap(l -> noInflationLegs(IRS, l, row));
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateFraLegs(
      List<ResolvableTradeLegDetails> legs, CsvRow row, PositionType positionType) {
    return sameCurrencies(FRA, legs, row)
        .flatMap(l -> notSameFraLegs(FIXED, l, row))
        .flatMap(l -> noOvernightLegs(FRA, l, row))
        .flatMap(l -> noInflationLegs(FRA, l, row))
        .flatMap(l -> positionTypeMatchesFraLegs(positionType, legs, row))
        .flatMap(l -> sameNotionals(FRA, legs, row))
        .flatMap(l -> sameDayCounts(FRA, legs, row));
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validateSwaptionLegs(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return sameCurrencies(SWAPTION, legs, row)
        .flatMap(l -> sameNotionals(SWAPTION, legs, row))
        .flatMap(l -> validSwaptionLegTypes(legs, row));
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validInflationLegs(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    var legTypes = legs.stream().map(ResolvableTradeLegDetails::getCalculationType).toList();
    if (!legTypes.contains(INFLATION)) {
      return left(errorItem(REQUIRES_INFLATION_LEG, row));
    }
    return right(legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> notSameIrsLegs(
      CalculationType calculationType, List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return Eithers.cond(
        !legs.stream()
            .map(ResolvableTradeLegDetails::getCalculationType)
            .allMatch(calculationType::equals),
        errorItem(String.format(INVALID_IRS_LEGS, calculationType.getLabel()), row),
        legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> notSameFraLegs(
      CalculationType calculationType, List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return Eithers.cond(
        !legs.stream()
            .map(ResolvableTradeLegDetails::getCalculationType)
            .allMatch(calculationType::equals),
        errorItem(String.format(INVALID_FRA_LEGS, calculationType.getLabel()), row),
        legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> positionTypeMatchesFraLegs(
      PositionType positionType, List<ResolvableTradeLegDetails> legs, CsvRow row) {
    Assert.notNull(positionType, "positionType must not be null");

    return Eithers.cond(
        // BUY = PAY FIXED, RECEIVE [BUY.toPayReceive()] IBOR
        // SELL = PAY [SELL.toPayReceive()] IBOR, RECEIVE FIXED
        // So the position.toPayReceive() should always be on the IBOR leg.
        legs.stream()
            .allMatch(
                leg ->
                    leg.getPayReceive() != positionType.toPayReceive()
                        ^ leg.getCalculationType() == IBOR),
        errorItem(
            String.format(
                INVALID_FRA_POSITION, positionType.name(), positionType.toPayReceive().toString()),
            row),
        legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> sameCurrencies(
      CoreProductType productType, List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return same(productType, legs, row, ResolvableTradeLegDetails::getCurrency, INVALID_CURRENCIES);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> differentXccyCurrencies(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return Eithers.cond(
        !Objects.equals(legs.get(0).getCurrency(), legs.get(1).getCurrency()),
        errorItem(REQUIRES_DIFFERENT_CURRENCIES, row),
        legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> validSwaptionLegTypes(
      List<ResolvableTradeLegDetails> legs, CsvRow row) {
    var legTypes = legs.stream().map(ResolvableTradeLegDetails::getCalculationType).toList();
    return Eithers.cond(
        legTypes.contains(FIXED) && CollectionUtils.containsAny(legTypes, OVERNIGHT, IBOR),
        errorItem(INVALID_SWAPTION_LEGS, row),
        legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> sameNotionals(
      CoreProductType productType, List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return same(productType, legs, row, ResolvableTradeLegDetails::getNotional, INVALID_NOTIONAL);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> sameDayCounts(
      CoreProductType productType, List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return same(productType, legs, row, ResolvableTradeLegDetails::getDayCount, INVALID_DAY_COUNTS);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> same(
      CoreProductType productType,
      List<ResolvableTradeLegDetails> legs,
      CsvRow row,
      Function<ResolvableTradeLegDetails, ?> valueFn,
      String errorMessage) {
    return Eithers.cond(
        Objects.equals(valueFn.apply(legs.get(0)), valueFn.apply(legs.get(1))),
        errorItem(String.format(errorMessage, productType), row),
        legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> noInflationLegs(
      CoreProductType productType, List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return Eithers.cond(
        legs.stream()
            .map(ResolvableTradeLegDetails::getCalculationType)
            .noneMatch(INFLATION::equals),
        errorItem(String.format(NO_INFLATION_LEG, productType), row),
        legs);
  }

  private static Either<ErrorItem, List<ResolvableTradeLegDetails>> noOvernightLegs(
      CoreProductType productType, List<ResolvableTradeLegDetails> legs, CsvRow row) {
    return Eithers.cond(
        legs.stream()
            .map(ResolvableTradeLegDetails::getCalculationType)
            .noneMatch(OVERNIGHT::equals),
        errorItem(String.format(NO_OVERNIGHT_LEG, productType), row),
        legs);
  }

  private static ErrorItem errorItem(String errorMessage, CsvRow row) {
    return PARSING_ERROR.entity(
        format("Error at line number %d. Error: %s", row.lineNumber(), errorMessage));
  }
}
