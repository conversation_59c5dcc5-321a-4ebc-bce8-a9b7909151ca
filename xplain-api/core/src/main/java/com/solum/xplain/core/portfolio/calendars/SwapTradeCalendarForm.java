package com.solum.xplain.core.portfolio.calendars;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springdoc.core.annotations.ParameterObject;

@ParameterObject
@ToString
@EqualsAndHashCode
public class SwapTradeCalendarForm {

  @Valid @NotNull private SwapTradeLegCalendarForm leg1form;

  @Valid @NotNull private SwapTradeLegCalendarForm leg2form;

  public SwapTradeLegCalendarForm getLeg1form() {
    return leg1form;
  }

  public void setLeg1form(SwapTradeLegCalendarForm leg1form) {
    this.leg1form = leg1form;
  }

  public SwapTradeLegCalendarForm getLeg2form() {
    return leg2form;
  }

  public void setLeg2form(SwapTradeLegCalendarForm leg2form) {
    this.leg2form = leg2form;
  }
}
