package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.portfolio.CompanyPortfolio;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class UniquePortfolioExtIdValidator
    implements ConstraintValidator<UniquePortfolioExtId, CompanyPortfolio> {
  private final PortfolioRepository portfolioRepository;
  private final RequestPathVariablesSupport variablesSupport;

  private boolean includeSelfId;

  public UniquePortfolioExtIdValidator(
      PortfolioRepository portfolioRepository, RequestPathVariablesSupport variablesSupport) {
    this.portfolioRepository = portfolioRepository;
    this.variablesSupport = variablesSupport;
  }

  @Override
  public void initialize(UniquePortfolioExtId constraintAnnotation) {
    this.includeSelfId = constraintAnnotation.includeSelfId();
  }

  @Override
  public boolean isValid(CompanyPortfolio form, ConstraintValidatorContext context) {

    if (StringUtils.isAnyEmpty(
        form.getCompanyId(), form.getEntityId(), form.getExternalPortfolioId())) {
      return true;
    }
    String portfolioId = includeSelfId ? variablesSupport.getPathVariable("id") : null;
    if (portfolioRepository.existsByExternalIdExcludingSelf(
        form.getCompanyId(), form.getEntityId(), form.getExternalPortfolioId(), portfolioId)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode("externalPortfolioId")
          .addConstraintViolation();
      return false;
    }
    return true;
  }
}
