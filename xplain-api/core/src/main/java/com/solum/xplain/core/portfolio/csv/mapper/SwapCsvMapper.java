package com.solum.xplain.core.portfolio.csv.mapper;

import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvMapper;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class SwapCsvMapper implements ProductCsvMapper {

  private final CommonSwapCsvMapper commonMapper;

  @Override
  public List<ProductType> productTypes() {
    return List.of(
        CoreProductType.INFLATION, CoreProductType.IRS, CoreProductType.XCCY, CoreProductType.FRA);
  }

  @Override
  public Iterable<CsvField> toCsvFields(TradeDetails details) {
    return commonMapper.toCsvFields(details);
  }
}
