package com.solum.xplain.core.ccyexposure.repository.fragment;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.solum.xplain.core.ccyexposure.CcyExposureCashflowMapper;
import com.solum.xplain.core.ccyexposure.entity.Cashflow;
import com.solum.xplain.core.ccyexposure.value.CashflowForm;
import com.solum.xplain.core.ccyexposure.value.CashflowSearchForm;
import com.solum.xplain.core.ccyexposure.value.CashflowView;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

@Component
public class CcyExposureCashflowQueriesImpl extends GenericUniqueVersionedEntityRepository<Cashflow>
    implements CcyExposureCashflowQueries {

  private final CcyExposureCashflowMapper mapper;

  protected CcyExposureCashflowQueriesImpl(
      MongoOperations mongoOperations, CcyExposureCashflowMapper mapper) {
    super(mongoOperations, mapper);
    this.mapper = mapper;
  }

  @Override
  protected Criteria uniqueEntityCriteria(Cashflow entity) {
    return Criteria.where(Cashflow.Fields.ccyExposureId)
        .is(entity.getCcyExposureId())
        .and(Cashflow.Fields.date)
        .is(entity.getDate());
  }

  public Either<ErrorItem, Cashflow> getActiveItemByEntityId(String entityId, LocalDate stateDate) {
    return entity(entityId, BitemporalDate.newOf(stateDate), active());
  }

  @Override
  public Either<ErrorItem, EntityId> create(CashflowForm f) {
    Cashflow cashflows = mapper.fromForm(f, Cashflow.newOf());
    return insert(cashflows, f.getVersionForm());
  }

  @Override
  public Either<ErrorItem, EntityId> update(String entityId, LocalDate version, CashflowForm form) {
    return entityExact(entityId, version)
        // TODO: SXSD-8692 should the mapper not overwrite the immutable fields - name,
        // ccyExposureId, date?
        .map(f -> update(f, form.getVersionForm(), e -> mapper.fromForm(form, e)));
  }

  @Override
  public List<Cashflow> getActiveItemsByCcyExposureIdIn(
      List<String> ccyExposureIds, LocalDate stateDate) {
    return entities(
        BitemporalDate.newOf(stateDate),
        active(),
        where(Cashflow.Fields.ccyExposureId).in(ccyExposureIds));
  }

  @Override
  public List<CashflowView> findAllVersions(String entityId) {
    return entityVersions(entityId).stream().map(mapper::toView).toList();
  }

  @Override
  public DateList futureVersions(String ccyExposureId, CashflowSearchForm form) {
    var criteria =
        where(Cashflow.Fields.ccyExposureId)
            .is(ccyExposureId)
            .and(Cashflow.Fields.date)
            .is(form.date());
    return futureVersionsByCriteria(criteria, form.stateDate());
  }

  protected Criteria entityIdInCriteria(List<String> entityIds) {
    return where(VersionedEntity.Fields.entityId).in(entityIds);
  }
}
