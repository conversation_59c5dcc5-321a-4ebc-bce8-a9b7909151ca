package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.value.HasPaymentPeriod;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidPaymentPeriodValidator
    implements ConstraintValidator<ValidPaymentPeriod, HasPaymentPeriod> {

  public boolean isValid(HasPaymentPeriod form, ConstraintValidatorContext context) {
    if (form.getStartDate() != null
        && form.getEndDate() != null
        && !form.getEndDate().isAfter(form.getStartDate())) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate("ValidPaymentPeriod")
          .addPropertyNode("endDate")
          .addConstraintViolation();

      return false;
    }
    return true;
  }
}
