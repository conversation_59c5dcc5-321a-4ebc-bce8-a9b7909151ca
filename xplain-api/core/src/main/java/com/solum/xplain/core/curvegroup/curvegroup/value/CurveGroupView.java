package com.solum.xplain.core.curvegroup.curvegroup.value;

import com.solum.xplain.core.audit.value.AuditLogView;
import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CurveGroupView {
  private String id;
  private String name;
  private String creatorId;
  private String creatorName;
  private String modifiedBy;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;
  private boolean editable = true;
  private CalibrationStatus calibrationStatus;
  private LocalDateTime calibratedAt;
  private LocalDate calibrationDate;
  private String calibrationMarketDataGroupId;
  private String calibrationMarketDataGroupName;
  private String calibrationCurveConfigId;
  private InstrumentPriceRequirements calibrationPriceRequirements;
  private MarketDataSourceType calibrationMarketDataSourceType;
  private CalculationDiscountingType calibrationCurrency;
  private CalculationStrippingType calibrationStrippingType;
  private boolean archived;
  private List<AuditLogView> auditLogs;
}
