package com.solum.xplain.core.curvegroup.volatility.csv.surface;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CAPLET_VALUATION_MODEL_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_INTERPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SURFACE_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.VOLATILITY_TYPE_CLASSIFIER;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseDouble;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static io.atlassian.fugue.Either.left;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.curvegroup.volatility.validation.VolatilitySurfaceNameSupplier;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceForm;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.function.Function;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class VolatilitySurfaceCsvLoader extends GenericCsvLoader<VolatilitySurfaceForm, String> {

  static final String NAME_FIELD = "Name";
  static final String X_INTERPOLATOR_FIELD = "Tenor/Strike Interpolator";
  static final String X_EXTRAPOLATOR_LEFT_FIELD = "Tenor/Strike Extrapolator Left";
  static final String X_EXTRAPOLATOR_RIGHT_FIELD = "Tenor/Strike Extrapolator Right";
  static final String Y_INTERPOLATOR_FIELD = "Expiry/Maturity Interpolator";
  static final String Y_EXTRAPOLATOR_LEFT_FIELD = "Expiry/Maturity Extrapolator Left";
  static final String Y_EXTRAPOLATOR_RIGHT_FIELD = "Expiry/Maturity Extrapolator Right";
  static final String SKEW_INPUT_TYPE = "Skew Input Type";
  static final String CAPLET_VALUATION_MODEL = "Caplet Valuation Model";
  static final String SABR_MODE = "Is SABR";
  static final String SABR_BETA = "SABR Beta";
  static final String SABR_SHIFT = "SABR Shift";

  private static final List<String> VOLATILITY_SURFACE_CSV_HEADERS =
      List.of(
          NAME_FIELD,
          X_INTERPOLATOR_FIELD,
          X_EXTRAPOLATOR_LEFT_FIELD,
          X_EXTRAPOLATOR_RIGHT_FIELD,
          Y_INTERPOLATOR_FIELD,
          Y_EXTRAPOLATOR_LEFT_FIELD,
          Y_EXTRAPOLATOR_RIGHT_FIELD,
          SKEW_INPUT_TYPE,
          CAPLET_VALUATION_MODEL,
          SABR_MODE,
          SABR_BETA,
          SABR_SHIFT);
  private static final ClassifierSupplier CAPLET_VALUATION_MODEL_SUPPLIER =
      new ClassifierSupplier(CAPLET_VALUATION_MODEL_CLASSIFIER);
  private static final ClassifierSupplier VOLATILITY_TYPE_SUPPLIER =
      new ClassifierSupplier(VOLATILITY_TYPE_CLASSIFIER);
  private static final ClassifierSupplier CURVE_INTERPOLATOR_SUPPLIER =
      new ClassifierSupplier(CURVE_INTERPOLATOR_CLASSIFIER);
  private static final ClassifierSupplier SURFACE_EXTRAPOLATOR_SUPPLIER =
      new ClassifierSupplier(SURFACE_EXTRAPOLATOR_CLASSIFIER);

  @Override
  protected CsvParserResultBuilder<VolatilitySurfaceForm, String> createResult(
      ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        VolatilitySurfaceForm::getName, Function.identity(), parsingMode.failOnError());
  }

  @Override
  protected List<String> getFileHeaders() {
    return VOLATILITY_SURFACE_CSV_HEADERS;
  }

  @Override
  protected Either<ErrorItem, VolatilitySurfaceForm> parseLine(@NonNull CsvRow row) {
    try {
      VolatilitySurfaceForm form = new VolatilitySurfaceForm();
      form.setName(validateValue(row.getValue(NAME_FIELD), new VolatilitySurfaceNameSupplier()));
      form.setXInterpolator(
          validateValue(row.getValue(X_INTERPOLATOR_FIELD), CURVE_INTERPOLATOR_SUPPLIER));
      form.setXExtrapolatorLeft(
          validateValue(row.getValue(X_EXTRAPOLATOR_LEFT_FIELD), SURFACE_EXTRAPOLATOR_SUPPLIER));
      form.setXExtrapolatorRight(
          validateValue(row.getValue(X_EXTRAPOLATOR_RIGHT_FIELD), SURFACE_EXTRAPOLATOR_SUPPLIER));
      form.setYInterpolator(
          validateValue(row.getValue(Y_INTERPOLATOR_FIELD), CURVE_INTERPOLATOR_SUPPLIER));
      form.setYExtrapolatorLeft(
          validateValue(row.getValue(Y_EXTRAPOLATOR_LEFT_FIELD), SURFACE_EXTRAPOLATOR_SUPPLIER));
      form.setYExtrapolatorRight(
          validateValue(row.getValue(Y_EXTRAPOLATOR_RIGHT_FIELD), SURFACE_EXTRAPOLATOR_SUPPLIER));
      form.setSkewType(validateValue(row.getValue(SKEW_INPUT_TYPE), VOLATILITY_TYPE_SUPPLIER));
      form.setCapletValuationModel(
          validateValue(row.getValue(CAPLET_VALUATION_MODEL), CAPLET_VALUATION_MODEL_SUPPLIER));
      boolean isSabr = row.findValue(SABR_MODE).map(CsvLoaderUtils::parseBoolean).orElse(false);
      form.setSabr(isSabr);
      if (isSabr) {
        form.setSabrBeta(parseDouble(row.getValue(SABR_BETA)));
        form.setSabrShift(parseDouble(row.getValue(SABR_SHIFT)));
      }
      form.setVersionForm(NewVersionFormV2.newDefault());

      return Either.right(form);
    } catch (RuntimeException e) {
      return left(rowParsingError(row, e));
    }
  }
}
