package com.solum.xplain.core.curvegroup.curvegroup.value;

import com.google.common.collect.ImmutableSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CurveGroupsCounts {
  private final Map<String, CurveCounts> counts;

  @Builder
  public CurveGroupsCounts(
      List<CurveGroupEntryCount> curvesCounts,
      List<CurveGroupEntryCount> surfacesCounts,
      List<CurveGroupEntryCount> creditCurvesCounts,
      List<CurveGroupEntryCount> fxVolsCounts,
      List<CurveGroupEntryCount> fxRatesCounts,
      List<CurveGroupEntryCount> bondCurveCounts) {
    var curvesMap = toCountsMap(curvesCounts);
    var surfacesMap = toCountsMap(surfacesCounts);
    var creditsMap = toCountsMap(creditCurvesCounts);
    var bondsMap = toCountsMap(bondCurveCounts);
    var fxVolsMap = toCountsMap(fxVolsCounts);
    var fxRatesMap = toCountsMap(fxRatesCounts);

    counts =
        ImmutableSet.<String>builder()
            .addAll(curvesMap.keySet())
            .addAll(surfacesMap.keySet())
            .addAll(creditsMap.keySet())
            .addAll(bondsMap.keySet())
            .addAll(fxVolsMap.keySet())
            .addAll(fxRatesMap.keySet())
            .build()
            .stream()
            .collect(
                Collectors.toMap(
                    Function.identity(),
                    cg ->
                        toCurveGroupCounts(
                            cg,
                            curvesMap,
                            surfacesMap,
                            creditsMap,
                            bondsMap,
                            fxVolsMap,
                            fxRatesMap)));
  }

  public static CurveGroupsCounts empty() {
    return new CurveGroupsCounts(Map.of());
  }

  private static Map<String, Integer> toCountsMap(List<CurveGroupEntryCount> counts) {
    return counts.stream()
        .collect(
            Collectors.toMap(
                CurveGroupEntryCount::getCurveGroupId, CurveGroupEntryCount::getCount));
  }

  private CurveCounts toCurveGroupCounts(
      String curveGroupId,
      Map<String, Integer> curvesMap,
      Map<String, Integer> surfacesMap,
      Map<String, Integer> creditsMap,
      Map<String, Integer> bondsMap,
      Map<String, Integer> fxVolsMap,
      Map<String, Integer> fxRatesMap) {
    ToIntFunction<Map<String, Integer>> countsFn = map -> map.getOrDefault(curveGroupId, 0);

    return CurveCounts.builder()
        .numberOfCurves(countsFn.applyAsInt(curvesMap))
        .numberOfVolatilitySurfaces(countsFn.applyAsInt(surfacesMap))
        .numberOfCreditCurves(countsFn.applyAsInt(creditsMap))
        .numberOfBondCurves(countsFn.applyAsInt(bondsMap))
        .numberOfFxRateNodes(countsFn.applyAsInt(fxRatesMap))
        .numberOfFxVolatilities(countsFn.applyAsInt(fxVolsMap))
        .build();
  }

  public CurveCounts countsForGroup(String curveGroupId) {
    return counts.getOrDefault(curveGroupId, CurveCounts.empty());
  }
}
