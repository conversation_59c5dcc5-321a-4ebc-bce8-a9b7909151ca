package com.solum.xplain.core.curvegroup.curve.csv.curve;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_DISCOUNT_FACTOR_INTERP_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_INTERPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_NODE_CLASH_ACTION_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DEFAULT_CURVE_VALUE_TYPE_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DISCOUNT_FACTOR_CURVE_INTERPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.INFLATION_CURVE_VALUE_TYPE_CLASSIFIER;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations.lookupKeyByName;

import com.opengamma.strata.collect.io.CsvRow;
import com.opengamma.strata.market.ValueType;
import com.solum.xplain.core.classifiers.Constants;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.curvegroup.curve.CurvesUtils;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import com.solum.xplain.core.curvegroup.curve.validation.CurveTypesSupplier;
import com.solum.xplain.core.curvegroup.curve.validation.ValidCurveNameValidator;
import com.solum.xplain.core.curvegroup.curve.value.CurveForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.utils.FrequencyUtils;
import com.solum.xplain.extensions.curve.ExtendedCurveInterpolators;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class CurveCsvLoader extends GenericCsvLoader<CurveForm, String> {

  static final String NAME_FIELD = "Name";
  static final String CURVE_TYPE_FIELD = "Curve Type";
  static final String INTERPOLATOR_FIELD = "Interpolator";
  static final String INTERPOLATION_TYPE = "Interpolation Type";
  static final String EXTRAPOLATOR_LEFT_FIELD = "Extrapolator Left";
  static final String EXTRAPOLATOR_RIGHT_FIELD = "Extrapolator Right";
  static final String MINIMUM_NODE_GAP_FIELD = "Minimum Node Gap";
  static final String NODE_CLASH_ACTION_FIELD = "Node Clash Action";

  private static final ClassifierSupplier NODE_CLASH_ACTION_SUPPLIER =
      new ClassifierSupplier(CURVE_NODE_CLASH_ACTION_CLASSIFIER);
  private static final ClassifierSupplier DEFAULT_VALUE_TYPE_SUPPLIER =
      new ClassifierSupplier(DEFAULT_CURVE_VALUE_TYPE_CLASSIFIER);
  private static final ClassifierSupplier INFLATION_VALUE_TYPE_SUPPLIER =
      new ClassifierSupplier(INFLATION_CURVE_VALUE_TYPE_CLASSIFIER);
  private static final ClassifierSupplier DISCOUNT_FACTOR_CURVE_INTERPOLATORS =
      new ClassifierSupplier(DISCOUNT_FACTOR_CURVE_INTERPOLATOR_CLASSIFIER);
  private static final ClassifierSupplier CURVE_INTERPOLATOR_SUPPLIER =
      new ClassifierSupplier(CURVE_INTERPOLATOR_CLASSIFIER);
  private static final ClassifierSupplier CURVE_EXTRAPOLATOR_SUPPLIER =
      new ClassifierSupplier(CURVE_EXTRAPOLATOR_CLASSIFIER);
  private static final ClassifierSupplier CURVE_EXTRAPOLATOR_FLAT_SUPPLIER =
      new ClassifierSupplier(CURVE_DISCOUNT_FACTOR_INTERP_EXTRAPOLATOR_CLASSIFIER);

  @Override
  protected CsvParserResultBuilder<CurveForm, String> createResult(ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        f -> lookupKeyByName(f.getName()).orElse(f.getName()),
        Function.identity(),
        parsingMode.failOnError());
  }

  @Override
  protected List<String> getFileHeaders() {
    return List.of(
        NAME_FIELD,
        CURVE_TYPE_FIELD,
        INTERPOLATOR_FIELD,
        INTERPOLATION_TYPE,
        EXTRAPOLATOR_LEFT_FIELD,
        EXTRAPOLATOR_RIGHT_FIELD,
        MINIMUM_NODE_GAP_FIELD,
        NODE_CLASH_ACTION_FIELD);
  }

  @Override
  protected Either<ErrorItem, CurveForm> parseLine(@NonNull CsvRow row) {
    try {
      String nameStr = row.getValue(NAME_FIELD);
      String typeStr = validateValue(row.getValue(CURVE_TYPE_FIELD), new CurveTypesSupplier());
      String interpolatorStr = row.getValue(INTERPOLATOR_FIELD);
      String extrapolatorLeftStr = row.getValue(EXTRAPOLATOR_LEFT_FIELD);
      String extrapolatorRightStr = row.getValue(EXTRAPOLATOR_RIGHT_FIELD);

      CurveForm curve = new CurveForm();
      curve.setName(validateName(typeStr, nameStr));
      curve.setCurveType(typeStr);
      curve.setVersionForm(NewVersionFormV2.newDefault());
      curve.setYInterpolationMethod(parseInterpolationMethod(typeStr, row));
      curve.setInterpolator(parseInterpolator(curve.getYInterpolationMethod(), interpolatorStr));
      curve.setExtrapolatorLeft(parseExtrapolator(extrapolatorLeftStr, curve.getInterpolator()));
      curve.setExtrapolatorRight(parseExtrapolator(extrapolatorRightStr, curve.getInterpolator()));
      if (CurvesUtils.is3MCurve(curve.getName())) {
        row.findValue(NODE_CLASH_ACTION_FIELD)
            .map(val -> validateValue(val, NODE_CLASH_ACTION_SUPPLIER))
            .ifPresent(curve::setClashAction);
        row.findValue(MINIMUM_NODE_GAP_FIELD)
            .map(
                val ->
                    CsvLoaderUtils.parseFrequency(
                        val, MINIMUM_NODE_GAP_FIELD, Constants.MIN_NODE_GAPS))
            .map(FrequencyUtils::toStringNoPrefix)
            .ifPresent(curve::setMinGap);
      }
      return Either.right(curve);
    } catch (RuntimeException e) {
      return Either.left(rowParsingError(row, e));
    }
  }

  private String parseExtrapolator(String extrapolatorStr, String interpolatorStr) {
    if (interpolatorStr.equals(ExtendedCurveInterpolators.LOG_LINEAR_DISCOUNT_FACTOR.getName())) {
      return validateValue(extrapolatorStr, CURVE_EXTRAPOLATOR_FLAT_SUPPLIER);
    }
    return validateValue(extrapolatorStr, CURVE_EXTRAPOLATOR_SUPPLIER);
  }

  private String parseInterpolator(String interpolationMethod, String interpolatorStr) {
    if (interpolationMethod.equals(ValueType.DISCOUNT_FACTOR.getName())) {
      return validateValue(interpolatorStr, DISCOUNT_FACTOR_CURVE_INTERPOLATORS);
    }
    return validateValue(interpolatorStr, CURVE_INTERPOLATOR_SUPPLIER);
  }

  private String parseInterpolationMethod(String typeStr, CsvRow row) {
    var type = CurveType.valueOf(typeStr);
    if (type == CurveType.INFLATION_INDEX) {
      return valueType(row)
          .map(t -> validateValue(t, INTERPOLATION_TYPE, INFLATION_VALUE_TYPE_SUPPLIER))
          .orElse(ValueType.PRICE_INDEX.getName());
    }
    return valueType(row)
        .map(t -> validateValue(t, INTERPOLATION_TYPE, DEFAULT_VALUE_TYPE_SUPPLIER))
        .orElse(ValueType.ZERO_RATE.getName());
  }

  private Optional<String> valueType(CsvRow row) {
    return row.findValue(INTERPOLATION_TYPE);
  }

  private String validateName(String type, String name) {
    if (!ValidCurveNameValidator.isNameValidForType(name, type)) {
      throw new IllegalArgumentException(
          ValidCurveNameValidator.invalidNameForTypeErrorMessage(name, type));
    }
    return name;
  }
}
