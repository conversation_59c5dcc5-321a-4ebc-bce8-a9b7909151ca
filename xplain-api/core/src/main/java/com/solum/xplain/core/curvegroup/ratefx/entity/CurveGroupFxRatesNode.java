package com.solum.xplain.core.curvegroup.ratefx.entity;

import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_RATES;
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FX_RATE;
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofFx;

import com.solum.xplain.core.curvegroup.HasInstruments;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvegroup.volatilityfx.HasFxPair;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CurveGroupFxRatesNode implements HasInstruments, HasFxPair {

  private static final String FX_RATE_MD_KEY_PATTERN = "%s/%s";
  private static final String FX_RATE_MD_NAME_PATTERN = "%S/%S SPOT";

  private String domesticCurrency;
  private String foreignCurrency;

  @Override
  public List<InstrumentDefinition> allInstruments() {
    return List.of(ofFx(FX_RATES, getKey(), FX_RATE, getKey(), getName()));
  }

  public String getKey() {
    return String.format(FX_RATE_MD_KEY_PATTERN, getDomesticCurrency(), getForeignCurrency());
  }

  public String getName() {
    return String.format(FX_RATE_MD_NAME_PATTERN, getDomesticCurrency(), getForeignCurrency());
  }
}
