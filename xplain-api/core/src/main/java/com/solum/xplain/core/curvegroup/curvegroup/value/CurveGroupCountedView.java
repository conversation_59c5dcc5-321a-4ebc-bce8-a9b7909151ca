package com.solum.xplain.core.curvegroup.curvegroup.value;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CurveGroupCountedView extends CurveGroupView {
  private Integer numberOfCurves;
  private Integer numberOfVolatilitySurfaces;
  private Integer numberOfCreditCurves;
  private Integer numberOfBondCurves;
  private Integer numberOfFxVolatilities;
  private Integer numberOfFxRateNodes;
}
