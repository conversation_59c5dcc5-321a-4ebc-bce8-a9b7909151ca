package com.solum.xplain.core.portfolio.value.item;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewMapping;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import com.solum.xplain.extensions.enums.PositionType;
import java.time.LocalDate;
import lombok.Data;

@Data
public class TradeDetailsView {
  @ConfigurableViewQuery(sortable = true)
  private PositionType positionType;

  @ConfigurableViewField(prefix = "payLeg.")
  @ConfigurableViewMapping(from = "currency", to = "currency")
  @ConfigurableViewMapping(
      from = "currency",
      to = "nearReceiveCurrency",
      deriveAs = "Use as far receive currency")
  private TradeLegDetailsView payLeg;

  @ConfigurableViewField(prefix = "receiveLeg.")
  @ConfigurableViewMapping(from = "currency", to = "currency")
  @ConfigurableViewMapping(
      from = "currency",
      to = "nearPayCurrency",
      deriveAs = "Use as far pay currency")
  private TradeLegDetailsView receiveLeg;

  @ConfigurableViewField(prefix = "info.")
  private TradeInfoDetailsView info;

  @ConfigurableViewField(prefix = "optionTradeDetails.")
  private OptionTradeDetailsView optionTradeDetails;

  @ConfigurableViewField(prefix = "creditTradeDetails.")
  private CreditTradeDetailsView creditTradeDetails;

  @ConfigurableViewField(prefix = "loanNoteTradeDetails.")
  private LoanNoteTradeDetailsView loanNoteTradeDetails;

  @ConfigurableViewField(prefix = "customTradeDetails.")
  private CustomTradeDetailsView customTradeDetails;

  private String businessDayConvention;

  private Boolean notionalScheduleInitialExchange;
  private Boolean notionalScheduleFinalExchange;

  private String stubConvention;

  @ConfigurableViewQuery(sortable = true)
  private LocalDate startDate;

  @ConfigurableViewQuery(sortable = true)
  private LocalDate endDate;

  private LocalDate firstRegularStartDate;
  private LocalDate lastRegularEndDate;

  private String rollConvention;
  private Double fxRate;
}
