package com.solum.xplain.core.company.mapper;

import static org.mapstruct.factory.Mappers.getMapper;

import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.EntityReferenceMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.VersionedEntityMapper;
import com.solum.xplain.core.company.entity.CompanyValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettings;
import com.solum.xplain.core.company.entity.ValuationSettingsMarketDataGroup;
import com.solum.xplain.core.company.entity.ValuationSettingsNames;
import com.solum.xplain.core.company.form.ValuationSettingsForm;
import com.solum.xplain.core.company.value.CompanyValuationSettingsView;
import com.solum.xplain.core.company.value.ValuationSettingsView;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    componentModel = "default",
    uses = {
      ObjectIdMapper.class,
      EntityReferenceMapper.class,
      AuditUserMapper.class,
      InstrumentPriceRequirementsMapper.class
    })
public interface ValuationSettingsMapper extends VersionedEntityMapper<CompanyValuationSettings> {

  ValuationSettingsMapper INSTANCE = getMapper(ValuationSettingsMapper.class);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "curveConfiguration.name", source = "names.curveConfigurationName")
  @Mapping(target = "nonFxCurveConfiguration.name", source = "names.nonFxCurveConfigurationName")
  @Mapping(target = "nonFxCurveConfiguration.entityId", source = "form.nonFxCurveConfigurationId")
  @Mapping(target = "curveConfiguration.entityId", source = "form.curveConfigurationId")
  @Mapping(target = "marketDataGroup", expression = "java(marketDataGroup)")
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(target = "strippingType", source = "form.curveDiscountingForm.strippingType")
  @Mapping(target = "discountingType", source = "form.curveDiscountingForm.discountingType")
  @Mapping(target = "triangulationCcy", source = "form.curveDiscountingForm.triangulationCcy")
  @Mapping(target = "useCsaDiscounting", source = "form.curveDiscountingForm.useCsaDiscounting")
  CompanyValuationSettings fromForm(
      ValuationSettingsForm form,
      ValuationSettingsNames names,
      ValuationSettingsMarketDataGroup marketDataGroup,
      @MappingTarget CompanyValuationSettings versioned);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", source = "state")
  @Mapping(
      target = "curveConfiguration.name",
      source = "currentWithDefaults.curveConfiguration.name")
  @Mapping(
      target = "curveConfiguration.entityId",
      source = "currentWithDefaults.curveConfiguration.entityId")
  @Mapping(
      target = "nonFxCurveConfiguration.name",
      source = "currentWithDefaults.nonFxCurveConfiguration.name")
  @Mapping(
      target = "nonFxCurveConfiguration.entityId",
      source = "currentWithDefaults.nonFxCurveConfiguration.entityId")
  CompanyValuationSettings fromDefaultToBespoke(
      CompanyValuationSettings currentWithDefaults, State state);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "curveConfiguration", expression = "java(null)")
  @Mapping(target = "nonFxCurveConfiguration", expression = "java(null)")
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(target = "marketDataGroup", expression = "java(marketDataGroup)")
  @Mapping(target = "strippingType", source = "form.curveDiscountingForm.strippingType")
  @Mapping(target = "discountingType", source = "form.curveDiscountingForm.discountingType")
  @Mapping(target = "triangulationCcy", source = "form.curveDiscountingForm.triangulationCcy")
  @Mapping(target = "useCsaDiscounting", source = "form.curveDiscountingForm.useCsaDiscounting")
  CompanyValuationSettings fromFormToDefault(
      ValuationSettingsForm form,
      ValuationSettingsMarketDataGroup marketDataGroup,
      @MappingTarget CompanyValuationSettings versioned);

  @Mapping(target = "nonFxCurveConfigurationId", source = "nonFxCurveConfiguration.entityId")
  @Mapping(target = "curveConfigurationId", source = "curveConfiguration.entityId")
  @Mapping(target = "curveConfigurationName", source = "curveConfiguration.name")
  @Mapping(target = "nonFxCurveConfigurationName", source = "nonFxCurveConfiguration.name")
  @Mapping(target = "marketDataGroupId", source = "marketDataGroup.marketDataGroupId")
  @Mapping(target = "marketDataGroupName", source = "marketDataGroup.marketDataGroupName")
  @Mapping(target = "companyId", source = "entityId")
  CompanyValuationSettingsView toView(CompanyValuationSettings versioned);

  @Mapping(target = "nonFxCurveConfigurationId", source = "nonFxCurveConfiguration.entityId")
  @Mapping(target = "curveConfigurationId", source = "curveConfiguration.entityId")
  @Mapping(target = "curveConfigurationName", source = "curveConfiguration.name")
  @Mapping(target = "nonFxCurveConfigurationName", source = "nonFxCurveConfiguration.name")
  @Mapping(target = "marketDataGroupId", source = "marketDataGroup.marketDataGroupId")
  @Mapping(target = "marketDataGroupName", source = "marketDataGroup.marketDataGroupName")
  ValuationSettingsView toBaseView(ValuationSettings settings);
}
