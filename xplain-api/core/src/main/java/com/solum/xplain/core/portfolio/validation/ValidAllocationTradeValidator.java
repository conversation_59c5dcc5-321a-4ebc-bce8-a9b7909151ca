package com.solum.xplain.core.portfolio.validation;

import static java.lang.String.format;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.solum.xplain.core.portfolio.ReferenceTradesProvider;
import com.solum.xplain.core.portfolio.form.AllocationTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ValidAllocationTradeValidator
    implements ConstraintValidator<ValidAllocationTrade, AllocationTradeForm> {

  private static final String MESSAGE_TEMPLATE =
      "{com.solum.xplain.api.portfolio.validation.ValidAllocationTrade.%s}";
  private final ReferenceTradesProvider referenceTradesProvider;

  @Override
  public boolean isValid(AllocationTradeForm form, ConstraintValidatorContext context) {
    var refId = form.getReferenceTradeId();
    if (isEmpty(refId)) {
      return true;
    }

    var stateDate = form.getVersionForm().getStateDate();
    var refTrade = referenceTradesProvider.fetchReferenceTrade(refId, stateDate);
    if (refTrade.isEmpty()) {
      return true;
    }

    if (refTrade.get().getProductType().isBuySell()) {
      if (form.getPositionType() == null) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate(format(MESSAGE_TEMPLATE, "positionNotNull"))
            .addPropertyNode("positionType")
            .addConstraintViolation();
        return false;
      }
    } else if (form.getPositionType() != null) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(format(MESSAGE_TEMPLATE, "positionNull"))
          .addPropertyNode("positionType")
          .addConstraintViolation();
      return false;
    }

    return true;
  }
}
