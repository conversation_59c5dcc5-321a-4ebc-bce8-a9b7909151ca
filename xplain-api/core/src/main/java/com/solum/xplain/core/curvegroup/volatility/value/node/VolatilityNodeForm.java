package com.solum.xplain.core.curvegroup.volatility.value.node;

import static com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle.YMW;

import com.solum.xplain.core.common.validation.ValidPeriod;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class VolatilityNodeForm {

  @NotEmpty
  @ValidPeriod(style = YMW)
  private String tenor;

  @NotEmpty
  @ValidPeriod(style = YMW)
  private String expiry;
}
