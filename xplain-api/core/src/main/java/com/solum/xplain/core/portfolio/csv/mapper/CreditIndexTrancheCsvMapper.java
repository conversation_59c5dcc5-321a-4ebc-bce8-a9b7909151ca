package com.solum.xplain.core.portfolio.csv.mapper;

import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvMapper;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class CreditIndexTrancheCsvMapper implements ProductCsvMapper {
  private final CommonCreditCsvMapper creditTradeCsvMapper;

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.CREDIT_INDEX_TRANCHE);
  }

  @Override
  public Iterable<CsvField> toCsvFields(TradeDetails details) {
    return creditTradeCsvMapper.toCsvFields(CoreProductType.CREDIT_INDEX_TRANCHE, details);
  }
}
