package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.ReferenceTradesProvider;
import com.solum.xplain.core.portfolio.form.AllocationTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class ValidReferenceTradeIdValidator
    implements ConstraintValidator<ValidReferenceTradeId, AllocationTradeForm> {

  private final ReferenceTradesProvider referenceTradesProvider;

  @Override
  public boolean isValid(AllocationTradeForm form, ConstraintValidatorContext context) {
    var vf = form.getVersionForm().getValidFrom();
    return referenceTradesProvider.existsActiveReferenceTrade(form.getReferenceTradeId(), vf, null);
  }
}
