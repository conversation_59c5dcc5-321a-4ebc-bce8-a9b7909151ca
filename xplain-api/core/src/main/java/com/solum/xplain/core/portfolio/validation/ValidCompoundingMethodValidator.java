package com.solum.xplain.core.portfolio.validation;

import static com.opengamma.strata.product.swap.CompoundingMethod.NONE;

import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.solum.xplain.core.portfolio.form.SwapLegForm;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.ObjectUtils;

public class ValidCompoundingMethodValidator
    implements ConstraintValidator<ValidCompoundingMethod, SwapLegForm> {
  public static final String INVALID_COMPOUNDING =
      "Compounding must be None when accrual and payment frequencies " + "are equal";

  public static boolean validateCompoundingMethod(
      Frequency accrualFreq, Frequency paymentFreq, CompoundingMethod compoundingMethod) {
    var hasEqualFrequencies = Objects.equals(accrualFreq.normalized(), paymentFreq.normalized());
    return !hasEqualFrequencies || NONE.equals(compoundingMethod);
  }

  @Override
  public boolean isValid(SwapLegForm value, ConstraintValidatorContext context) {
    if (ObjectUtils.anyNull(
        value,
        value.getCompoundingMethod(),
        value.getAccrualFrequency(),
        value.getPaymentFrequency())) {
      return true;
    }
    return Steps.begin(parseFrequency(value.getAccrualFrequency()))
        .then(() -> parseFrequency(value.getPaymentFrequency()))
        .then(() -> parseCompounding(value.getCompoundingMethod()))
        .yield(ValidCompoundingMethodValidator::validateCompoundingMethod)
        .orElse(true);
  }

  private Optional<Frequency> parseFrequency(String frequencyStr) {
    return Checked.now(() -> Frequency.parse(frequencyStr)).toOptional();
  }

  private Optional<CompoundingMethod> parseCompounding(String compounding) {
    return Checked.now(() -> CompoundingMethod.of(compounding)).toOptional();
  }
}
