package com.solum.xplain.core.curvegroup.curvebond.validation;

import static com.solum.xplain.core.curvegroup.conventions.bond.BondCurveConventions.findBondCurveByName;
import static com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository.uniqueEntityCriteria;
import static org.apache.commons.lang3.ObjectUtils.anyNull;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.common.validation.UniqueEntitySupport;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
public class ValidBondCurveNameValidator
    implements ConstraintValidator<ValidBondCurveName, BondCurveForm> {

  private static final String INVALID_NAME_ERROR = "Invalid bond curve name : %s";
  private static final String NOT_UNIQUE_NAME_ERROR = "Bond curve %s already exists at %s date";

  private final RequestPathVariablesSupport requestPathSupport;
  private final UniqueEntitySupport uniqueSupport;

  @Override
  public boolean isValid(BondCurveForm value, ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    }
    return isNameValid(value, context) && isNameUnique(value, context);
  }

  private boolean isNameValid(BondCurveForm form, ConstraintValidatorContext context) {
    var name = form.getName();
    if (name == null) {
      return true;
    }

    if (findBondCurveByName(name).isEmpty()) {
      addError(context, String.format(INVALID_NAME_ERROR, name));
      return false;
    }
    return true;
  }

  private boolean isNameUnique(BondCurveForm form, ConstraintValidatorContext context) {
    String groupId = requestPathSupport.getPathVariable("groupId");
    var name = form.getName();
    if (anyNull(groupId, name, form.getVersionForm())) {
      return true;
    }

    var validFrom = form.getVersionForm().getValidFrom();
    if (validFrom == null) {
      return true;
    }

    if (uniqueSupport.existsByCriteria(
        validFrom, uniqueEntityCriteria(groupId, name), BondCurve.class)) {
      addError(context, String.format(NOT_UNIQUE_NAME_ERROR, name, validFrom));
      return false;
    }
    return true;
  }

  private void addError(ConstraintValidatorContext context, String message) {
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate(message)
        .addPropertyNode("name")
        .addConstraintViolation();
  }
}
