package com.solum.xplain.core.ccyexposure.repository.fragment;

import static com.solum.xplain.core.common.Archivable.Fields.archived;
import static org.springframework.data.domain.Sort.unsorted;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.ccyexposure.CcyExposureMapper;
import com.solum.xplain.core.ccyexposure.entity.CcyExposure;
import com.solum.xplain.core.ccyexposure.value.CcyExposureCreateForm;
import com.solum.xplain.core.ccyexposure.value.CcyExposureUpdateForm;
import com.solum.xplain.core.ccyexposure.value.CcyExposureView;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.diff.AuditableDiffable;
import com.solum.xplain.core.common.diff.DiffAuditedRepositoryImpl;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.customfield.CustomFieldName;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import jakarta.annotation.Nonnull;
import java.time.LocalDate;
import java.util.List;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

@Component
public class CcyExposureQueriesImpl
    extends DiffAuditedRepositoryImpl<CcyExposureCreateForm, CcyExposureUpdateForm, CcyExposure>
    implements CcyExposureQueries {

  private final ConversionService conversionService;
  private final CcyExposureMapper mapper;

  public CcyExposureQueriesImpl(
      MongoOperations mongoOperations,
      CcyExposureMapper mapper,
      AuditingHandler auditingHandler,
      ConversionService conversionService) {
    super(mongoOperations, CcyExposure.class, "Ccy Exposure", mapper, auditingHandler);
    this.conversionService = conversionService;
    this.mapper = mapper;
  }

  @Override
  protected EntityId entityId(CcyExposure entity) {
    return EntityId.entityId(entity.getId());
  }

  public List<CcyExposureView> getCcyExposureViews(boolean withArchived) {
    return list(TableFilter.emptyTableFilter(), unsorted(), withArchived);
  }

  public List<CcyExposureView> list(TableFilter tableFilter, Sort sort, boolean archived) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(where(CustomFieldName.Fields.archived).is(archived)))
            .add(projectToView())
            .add(match(tableFilter.criteria(CcyExposureView.class, conversionService)));

    if (!sort.equals(unsorted())) {
      operations.add(sort(sort));
    } else {
      operations.add(sort(Sort.by(CcyExposureView.Fields.name)));
    }
    return mongoOperations
        .aggregate(newAggregation(CcyExposure.class, operations.build()), CcyExposureView.class)
        .getMappedResults();
  }

  @Nonnull
  private static ProjectionOperation projectToView() {
    return project()
        .and(CcyExposure.Fields.name)
        .as(CcyExposureView.Fields.name)
        .and(CcyExposure.Fields.currency)
        .as(CcyExposureView.Fields.currency)
        .and(CcyExposure.Fields.description)
        .as(CcyExposureView.Fields.description);
  }

  protected Criteria active() {
    return Criteria.where(archived).is(false);
  }

  public List<CcyExposure> ccyExposures(String currency, List<String> exposureIds) {
    Criteria criteria =
        active()
            .and(CcyExposure.Fields.currency)
            .is(currency)
            .and(AuditableDiffable.Fields.id)
            .in(exposureIds);

    return entities(criteria);
  }

  public List<CcyExposureView> getCcyExposureViews(
      LocalDate stateDate, VersionedEntityFilter filter) {
    return entities(filter.criteria()).stream().map(mapper::toView).toList();
  }

  public Either<ErrorItem, CcyExposureView> getCcyExposureView(String ccyExposureId) {
    return entity(ccyExposureId).map(mapper::toView);
  }

  public Either<ErrorItem, CcyExposure> getCcyExposure(String id) {
    return entity(id);
  }

  public Either<ErrorItem, CcyExposureView> getActiveCcyExposureView(String ccyExposureId) {
    return getCcyExposure(ccyExposureId).map(mapper::toView);
  }

  public List<CcyExposure> getActiveExposures() {
    return entities(active());
  }
}
