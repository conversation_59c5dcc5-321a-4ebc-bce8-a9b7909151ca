package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.FxSwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.ObjectUtils;

/** Validates that near date is strictly before far date. */
public class RequiredValidNearFarDateOrderValidator
    implements ConstraintValidator<RequiredValidNearFarDateOrder, FxSwapTradeForm> {

  public boolean isValid(FxSwapTradeForm form, ConstraintValidatorContext context) {
    if (form == null || ObjectUtils.anyNull(form.getPaymentDate(), form.getNearLegPaymentDate())) {
      return true;
    }

    var nearDate = form.getNearLegPaymentDate();
    var farDate = form.getPaymentDate();

    return nearDate.isBefore(farDate);
  }
}
