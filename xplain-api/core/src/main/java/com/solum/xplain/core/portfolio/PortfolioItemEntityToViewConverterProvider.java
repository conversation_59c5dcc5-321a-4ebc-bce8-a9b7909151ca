package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.common.versions.embedded.convert.EmbeddedVersionEntityToViewConverter;
import com.solum.xplain.core.common.versions.embedded.convert.EmbeddedVersionEntityToViewConverterProvider;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class PortfolioItemEntityToViewConverterProvider
    implements EmbeddedVersionEntityToViewConverterProvider<
        TradeValue, PortfolioItemEntity, PortfolioItem> {

  private final EnhanceableTradeConverterProvider enhanceableTradeConverterProvider;

  @Override
  public EmbeddedVersionEntityToViewConverter<TradeValue, PortfolioItemEntity, PortfolioItem>
      provideForValues(List<TradeValue> values, List<PortfolioItemEntity> portfolioItemEntities) {
    return enhanceableTradeConverterProvider.provideConverter(values, portfolioItemEntities);
  }
}
