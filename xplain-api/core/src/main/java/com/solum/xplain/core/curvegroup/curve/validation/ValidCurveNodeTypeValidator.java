package com.solum.xplain.core.curvegroup.curve.validation;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveView;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.stereotype.Component;

@Component
public class ValidCurveNodeTypeValidator extends BaseValidCurveNodesValidator
    implements ConstraintValidator<ValidCurveNodesTypes, CurveUpdateForm> {

  public ValidCurveNodeTypeValidator(
      RequestPathVariablesSupport requestPathVariablesSupport,
      CurveGroupCurveRepository repository) {
    super(requestPathVariablesSupport, repository);
  }

  @Override
  public boolean isValid(CurveUpdateForm form, ConstraintValidatorContext context) {
    return isCurveNodesValid(form, context);
  }

  @Override
  protected boolean isNodeFormValid(CurveView curve, CurveNodeForm form) {
    if (isNotEmpty(form.getType())) {
      return ConventionalCurveConfigurations.lookupByName(curve.getName(), curve.getCurveType())
          .map(CurveConvention::getAllPermissibleNodeTypes)
          .map(types -> types.contains(form.getType()))
          .orElse(true);
    }
    return true;
  }

  @Override
  protected void addViolation(ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate("NotValid")
        .addPropertyNode("type")
        .addConstraintViolation();
  }
}
