package com.solum.xplain.core.curvegroup.curvebond.csv.node;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.GroupedItemsImportService;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.NamedList;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvebond.BondCurveMapper;
import com.solum.xplain.core.curvegroup.curvebond.BondCurveRepository;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveSearch;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveUpdateForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.IterableUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class BondCurveNodeCsvImportService
    extends GroupedItemsImportService<BondCurve, BondCurveNodeForm, BondCurveNodeKey> {

  private final BondCurveNodesCsvLoader csvLoader;
  private final BondCurveMapper curveMapper;
  private final BondCurveRepository repository;

  public BondCurveNodeCsvImportService(
      AuditEntryService auditEntryService,
      BondCurveNodesCsvLoader csvLoader,
      BondCurveMapper curveMapper,
      BondCurveRepository repository) {
    super(auditEntryService);
    this.csvLoader = csvLoader;
    this.curveMapper = curveMapper;
    this.repository = repository;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importForCurve(
      String groupId,
      String curveId,
      LocalDate versionDate,
      ImportOptions importOptions,
      byte[] bytes) {
    return fetchCurve(groupId, curveId, versionDate)
        .map(
            c ->
                maybeImport(
                    importOptions.parsingMode(),
                    csvLoader.parseForCurve(bytes, c, importOptions.getDuplicateAction()),
                    curvesNodes -> importNodes(List.of(c), importOptions, curvesNodes)))
        .fold(
            e -> toErrorReturn(importOptions.getDuplicateAction(), List.of(e)),
            result -> toReturn(importOptions.getDuplicateAction(), result));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importForAll(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    var curves = fetchCurves(groupId, importOptions.getStateDate());
    var importResult =
        maybeImport(
            importOptions.parsingMode(),
            csvLoader.parse(bytes, curves, importOptions.getDuplicateAction()),
            curvesNodes -> importNodes(curves, importOptions, curvesNodes));
    return toReturn(importOptions.getDuplicateAction(), importResult);
  }

  protected List<LogItem> importNodes(
      List<BondCurve> curves,
      ImportOptions importOptions,
      List<NamedList<BondCurveNodeForm>> curveNodes) {
    return curveNodes.stream()
        .map(
            nn -> {
              var curve = findCurve(curves, nn.getName());
              var importItems =
                  ImportItems.<BondCurveNodeForm, BondCurveNodeKey, BondCurveNodeForm>builder()
                      .existingActiveItems(nodeForms(curve))
                      .existingItemToKeyFn(BondCurveNodeKey::from)
                      .importItems(nn.getItems())
                      .importItemToKeyFn(BondCurveNodeKey::from)
                      .build();
              return importForEntity(curve, importItems, importOptions);
            })
        .flatMap(Collection::stream)
        .toList();
  }

  private List<BondCurveNodeForm> nodeForms(BondCurve curve) {
    return curve.getNodes().stream().map(curveMapper::toNodeForm).toList();
  }

  @Override
  public String getCollection() {
    return BondCurve.BOND_CURVE_COLLECTION;
  }

  @Override
  public String getObjectName() {
    return "Bond curves' nodes";
  }

  @Override
  protected String entityIdentifier(BondCurve curve) {
    return curve.getName();
  }

  @Override
  protected Either<ErrorItem, EntityId> update(
      BondCurve curve, List<BondCurveNodeForm> nodeForms, NewVersionFormV2 versionForm) {
    BondCurveForm form = curveMapper.toForm(curve);
    form.setVersionForm(versionForm);
    form.setNodes(nodeForms);
    return update(curve, form);
  }

  @Override
  protected boolean hasFutureVersions(BondCurve curve, LocalDate stateDate) {
    var search = new BondCurveSearch(curve.getName(), stateDate);
    return !IterableUtils.isEmpty(
        repository.getFutureVersions(curve.getCurveGroupId(), search).getDates());
  }

  private Either<ErrorItem, EntityId> update(BondCurve e, BondCurveUpdateForm f) {
    return repository.updateCurve(e.getCurveGroupId(), e.getEntityId(), e.getValidFrom(), f);
  }

  private List<BondCurve> fetchCurves(String groupId, LocalDate stateDate) {
    return repository.getActiveCurves(groupId, BitemporalDate.newOf(stateDate));
  }

  private Either<ErrorItem, BondCurve> fetchCurve(
      String groupId, String curveId, LocalDate stateDate) {
    return repository.getActiveCurve(groupId, curveId, BitemporalDate.newOf(stateDate));
  }

  private BondCurve findCurve(List<BondCurve> curves, String existingCurveName) {
    return IterableUtils.find(curves, c -> Objects.equals(c.getName(), existingCurveName));
  }
}
