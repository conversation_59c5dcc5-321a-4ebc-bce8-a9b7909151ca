package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.portfolio.PortfolioTeamFilter.emptyFilter;
import static com.solum.xplain.core.portfolio.value.PortfolioFilter.activePortfolios;
import static com.solum.xplain.core.portfolio.value.PortfolioFilter.byPortfolioUniqueExternalIds;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.util.Collections.emptyList;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ImportErrorUtils;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.LoggingImportService;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import com.solum.xplain.core.portfolio.csv.PortfolioCsvLoaderFactory;
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.ImportPortfolio;
import com.solum.xplain.core.portfolio.value.PortfolioUniqueKey;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@NullMarked
public class PortfolioUploadService extends LoggingImportService {
  private static final String UNAUTHORIZED_PORTFOLIO = "Unable to modify portfolio without access";
  private final AuthenticationContext authenticationContext;
  private final PortfolioRepository portfolioRepository;
  private final PortfolioCsvLoaderFactory loaderFactory;
  private final PortfolioTeamFilterProvider teamFilterProvider;

  public PortfolioUploadService(
      PortfolioRepository portfolioRepository,
      AuthenticationContext authenticationContext,
      PortfolioCsvLoaderFactory loaderFactory,
      AuditEntryService auditEntryService,
      PortfolioTeamFilterProvider teamFilterProvider) {
    super(auditEntryService);
    this.portfolioRepository = portfolioRepository;
    this.authenticationContext = authenticationContext;
    this.loaderFactory = loaderFactory;
    this.teamFilterProvider = teamFilterProvider;
  }

  @Override
  protected String getCollection() {
    return Portfolio.PORTFOLIO_COLLECTION;
  }

  @Override
  protected String getObjectName() {
    return "Portfolio";
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadPortfolios(
      ParsingMode parsingMode, DuplicateAction duplicateAction, byte[] bytes) {
    return uploadPortfolios(
            authenticationContext.currentUser(), parsingMode, duplicateAction, bytes)
        .fold(
            err -> toErrorReturn(duplicateAction, err),
            importResult -> toReturn(duplicateAction, importResult));
  }

  private Either<List<ErrorItem>, ImportResult> uploadPortfolios(
      XplainPrincipal user,
      ParsingMode parsingMode,
      DuplicateAction duplicateAction,
      byte[] bytes) {
    var loader = loaderFactory.getLoader(user);
    return loader.parse(bytes, parsingMode).map(l -> importResult(user, duplicateAction, l));
  }

  private ImportResult importResult(
      XplainPrincipal user,
      DuplicateAction duplicateAction,
      CsvParserResult<PortfolioCreateForm> parserResult) {
    var importLogs = importPortfolios(user, duplicateAction, parserResult.getParsedLines());
    return new ImportResult(importLogs, parserResult.getWarnings());
  }

  private List<LogItem> importPortfolios(
      XplainPrincipal user, DuplicateAction action, List<PortfolioCreateForm> portfolioForms) {
    var allKeys = portfolioForms.stream().map(PortfolioUniqueKey::fromForm).collect(toSet());

    List<ImportPortfolio> currentPortfolios;
    if (action == DuplicateAction.APPEND_DELETE
        || action == DuplicateAction.REPLACE_DELETE
        || action == DuplicateAction.ERROR) {
      currentPortfolios = allExistingPortfolios();
    } else {
      currentPortfolios = existingPortfolios(allKeys);
    }

    var importItems =
        ImportItems.<PortfolioCreateForm, PortfolioUniqueKey, ImportPortfolio>builder()
            .existingActiveItems(currentPortfolios)
            .existingItemToKeyFn(PortfolioUniqueKey::fromImportPortfolio)
            .importItems(portfolioForms)
            .importItemToKeyFn(PortfolioUniqueKey::fromForm)
            .build();
    return switch (action) {
      case ERROR -> onError(importItems);
      case REPLACE_DELETE ->
          modifyWithAccessCheck(
              user, importItems.getImportKeys(), () -> onReplaceDelete(importItems));
      case REPLACE ->
          modifyWithAccessCheck(user, importItems.getImportKeys(), () -> onReplace(importItems));
      case APPEND_DELETE ->
          modifyWithAccessCheck(
              user, importItems.getImportKeys(), () -> onAppendDelete(importItems));
      case APPEND -> onAppend(importItems);
    };
  }

  private List<LogItem> modifyWithAccessCheck(
      XplainPrincipal user, Set<PortfolioUniqueKey> keys, Supplier<List<LogItem>> resultSupplier) {
    return validateImportKeys(user, keys)
        .fold(l -> asLogItems(List.of(l)), r -> resultSupplier.get());
  }

  private Either<ErrorItem, Set<PortfolioUniqueKey>> validateImportKeys(
      XplainPrincipal user, Set<PortfolioUniqueKey> importKeys) {
    var portfolioTeamFilter = teamFilterProvider.provideFilter(user);
    int accessibleDuplicateCount =
        portfolioRepository.duplicatePortfoliosCount(importKeys, portfolioTeamFilter);
    int allDuplicatesCount =
        portfolioRepository.duplicatePortfoliosCount(importKeys, emptyFilter());

    return Eithers.cond(
        accessibleDuplicateCount == allDuplicatesCount,
        Error.OPERATION_NOT_ALLOWED.entity(UNAUTHORIZED_PORTFOLIO),
        importKeys);
  }

  private List<LogItem> onError(
      ImportItems<PortfolioCreateForm, PortfolioUniqueKey, ImportPortfolio> importItems) {
    var errors =
        Stream.of(
                errorStream(importItems.getDuplicateKeys(), ImportErrorUtils::duplicateItem),
                errorStream(importItems.getSpareKeys(), ImportErrorUtils::missingItem))
            .flatMap(identity())
            .toList();
    return asLogItems(errors);
  }

  private Stream<ErrorItem> errorStream(
      Set<PortfolioUniqueKey> keys, Function<String, ErrorItem> function) {
    return keys.stream().map(k -> function.apply(k.externalPortfolioId()));
  }

  private List<LogItem> onReplaceDelete(
      ImportItems<PortfolioCreateForm, PortfolioUniqueKey, ImportPortfolio> importItems) {
    var appendResult = append(importItems);
    var replaceResult = replace(importItems);
    var archiveResult = archive(importItems);
    return CollectionUtils.join(appendResult, replaceResult, archiveResult);
  }

  private List<LogItem> onReplace(
      ImportItems<PortfolioCreateForm, PortfolioUniqueKey, ImportPortfolio> importItems) {
    var appendResult = append(importItems);
    var replaceResult = replace(importItems);
    return CollectionUtils.join(appendResult, replaceResult);
  }

  private List<LogItem> onAppendDelete(
      ImportItems<PortfolioCreateForm, PortfolioUniqueKey, ImportPortfolio> importItems) {
    var appendResult = append(importItems);
    var archiveResult = archive(importItems);
    return CollectionUtils.join(appendResult, archiveResult);
  }

  private List<LogItem> onAppend(
      ImportItems<PortfolioCreateForm, PortfolioUniqueKey, ImportPortfolio> importItems) {
    return append(importItems);
  }

  private List<LogItem> append(
      ImportItems<PortfolioCreateForm, PortfolioUniqueKey, ImportPortfolio> importItems) {
    if (importItems.getNewKeys().isEmpty()) {
      return emptyList();
    }
    var insertItemsStream = importItems.getNewKeys().stream().map(importItems::importItem);
    return bulkAppendItem(insertItemsStream);
  }

  private List<LogItem> replace(
      ImportItems<PortfolioCreateForm, PortfolioUniqueKey, ImportPortfolio> importItems) {
    if (importItems.getDuplicateKeys().isEmpty()) {
      return emptyList();
    }
    var replaceItems =
        importItems.getDuplicateKeys().stream()
            .collect(Collectors.toMap(importItems::existingItem, importItems::importItem));
    return bulkReplaceItem(replaceItems);
  }

  private List<LogItem> archive(
      ImportItems<PortfolioCreateForm, PortfolioUniqueKey, ImportPortfolio> importItems) {
    if (importItems.getSpareKeys().isEmpty()) {
      return emptyList();
    }
    var archiveItems = importItems.getSpareKeys().stream().map(importItems::existingItem);
    return bulkArchiveItem(archiveItems);
  }

  private List<LogItem> bulkAppendItem(Stream<PortfolioCreateForm> list) {
    var result = portfolioRepository.bulkInsert(list);
    return result.entrySet().stream()
        .map(item -> createInsertLogItem(getIdentifier(item.getKey()), item.getValue()))
        .toList();
  }

  private List<LogItem> bulkReplaceItem(
      Map<ImportPortfolio, PortfolioCreateForm> portfoliosToUpdate) {
    var ids =
        portfoliosToUpdate.entrySet().stream()
            .collect(toMap(item -> item.getKey().id(), Entry::getValue));
    var updatedEntities =
        portfolioRepository.bulkUpdate(ids).stream().map(EntityId::getId).collect(toSet());

    var result = new HashMap<PortfolioUniqueKey, Either<ErrorItem, EntityId>>();
    for (var entry : portfoliosToUpdate.entrySet()) {
      if (updatedEntities.contains(entry.getKey().id())) {
        result.put(entry.getKey().key(), right(entityId(entry.getKey().id())));
      } else {
        result.put(entry.getKey().key(), left(OBJECT_NOT_FOUND.entity("Portfolio not found")));
      }
    }
    return result.entrySet().stream()
        .map(item -> createUpdateLogItem(getIdentifier(item.getKey()), item.getValue()))
        .toList();
  }

  private List<LogItem> bulkArchiveItem(Stream<ImportPortfolio> portfolioToArchive) {
    var keyMap = portfolioToArchive.collect(toMap(ImportPortfolio::id, identity()));
    var resultEntities = portfolioRepository.bulkArchive(keyMap.keySet());
    return resultEntities.stream()
        .map(
            resultEntity -> {
              if (resultEntity.isRight()) {
                var archivedEntity = keyMap.get(resultEntity.right().get().getId());
                return createArchiveLogItem(
                    getIdentifier(archivedEntity.key()), right(entityId(archivedEntity.id())));
              } else {
                var errorEntity = keyMap.get(resultEntity.left().get().getId());
                return createArchiveLogItem(
                    getIdentifier(errorEntity.key()),
                    left(OBJECT_NOT_FOUND.entity("Portfolio not found")));
              }
            })
        .toList();
  }

  private String getIdentifier(PortfolioUniqueKey key) {
    return key.externalPortfolioId();
  }

  private List<ImportPortfolio> existingPortfolios(Set<PortfolioUniqueKey> importKeys) {
    return portfolioRepository.activeImportPortfoliosList(byPortfolioUniqueExternalIds(importKeys));
  }

  private List<ImportPortfolio> allExistingPortfolios() {
    return portfolioRepository.activeImportPortfoliosList(activePortfolios());
  }
}
