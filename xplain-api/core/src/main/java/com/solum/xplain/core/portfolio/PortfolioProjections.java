package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;

import com.solum.xplain.core.portfolio.trade.CreditTradeDetails;
import com.solum.xplain.core.portfolio.trade.OptionTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.portfolio.value.PortfolioItemFlatView;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;

public class PortfolioProjections {

  private PortfolioProjections() {}

  public static ProjectionOperation tradeDetailsProjection(String parent) {
    return project()
        .and(joinPaths(parent, TradeDetails.Fields.startDate))
        .as(PortfolioItemFlatView.Fields.tradeInfoStartDate)
        .and(joinPaths(parent, TradeDetails.Fields.endDate))
        .as(PortfolioItemFlatView.Fields.tradeInfoEndDate)
        .and(joinPaths(parent, TradeDetails.Fields.payLeg, TradeLegDetails.Fields.currency))
        .as(PortfolioItemFlatView.Fields.payLegCurrency)
        .and(joinPaths(parent, TradeDetails.Fields.payLeg, TradeLegDetails.Fields.notional))
        .as(PortfolioItemFlatView.Fields.payLegNotionalValue)
        .and(joinPaths(parent, TradeDetails.Fields.payLeg, TradeLegDetails.Fields.nearNotional))
        .as(PortfolioItemFlatView.Fields.payLegNearNotionalValue)
        .and(joinPaths(parent, TradeDetails.Fields.payLeg, TradeLegDetails.Fields.initialValue))
        .as(PortfolioItemFlatView.Fields.payLegRateMargin)
        .and(joinPaths(parent, TradeDetails.Fields.payLeg, TradeLegDetails.Fields.index))
        .as(PortfolioItemFlatView.Fields.payLegIndex)
        .and(joinPaths(parent, TradeDetails.Fields.payLeg, TradeLegDetails.Fields.accrualFrequency))
        .as(PortfolioItemFlatView.Fields.payLegFrequency)
        .and(joinPaths(parent, TradeDetails.Fields.payLeg, TradeLegDetails.Fields.dayCount))
        .as(PortfolioItemFlatView.Fields.payLegDayCount)
        .and(joinPaths(parent, TradeDetails.Fields.payLeg, TradeLegDetails.Fields.extLegIdentifier))
        .as(PortfolioItemFlatView.Fields.payLegExtIdentifier)
        .and(joinPaths(parent, TradeDetails.Fields.receiveLeg, TradeLegDetails.Fields.currency))
        .as(PortfolioItemFlatView.Fields.receiveLegCurrency)
        .and(joinPaths(parent, TradeDetails.Fields.receiveLeg, TradeLegDetails.Fields.notional))
        .as(PortfolioItemFlatView.Fields.receiveLegNotionalValue)
        .and(joinPaths(parent, TradeDetails.Fields.receiveLeg, TradeLegDetails.Fields.nearNotional))
        .as(PortfolioItemFlatView.Fields.receiveLegNearNotionalValue)
        .and(joinPaths(parent, TradeDetails.Fields.receiveLeg, TradeLegDetails.Fields.initialValue))
        .as(PortfolioItemFlatView.Fields.receiveLegRateMargin)
        .and(joinPaths(parent, TradeDetails.Fields.receiveLeg, TradeLegDetails.Fields.index))
        .as(PortfolioItemFlatView.Fields.receiveLegIndex)
        .and(
            joinPaths(
                parent, TradeDetails.Fields.receiveLeg, TradeLegDetails.Fields.accrualFrequency))
        .as(PortfolioItemFlatView.Fields.receiveLegFrequency)
        .and(joinPaths(parent, TradeDetails.Fields.receiveLeg, TradeLegDetails.Fields.dayCount))
        .as(PortfolioItemFlatView.Fields.receiveLegDayCount)
        .and(
            joinPaths(
                parent, TradeDetails.Fields.receiveLeg, TradeLegDetails.Fields.extLegIdentifier))
        .as(PortfolioItemFlatView.Fields.receiveLegExtIdentifier)
        .and(joinPaths(parent, TradeDetails.Fields.info, TradeInfoDetails.Fields.counterParty))
        .as(PortfolioItemFlatView.Fields.tradeInfoCounterparty)
        .and(joinPaths(parent, TradeDetails.Fields.info, TradeInfoDetails.Fields.counterPartyType))
        .as(PortfolioItemFlatView.Fields.tradeInfoCounterpartyType)
        .and(joinPaths(parent, TradeDetails.Fields.info, TradeInfoDetails.Fields.tradeDate))
        .as(PortfolioItemFlatView.Fields.tradeInfoTradeDate)
        .and(joinPaths(parent, TradeDetails.Fields.info, TradeInfoDetails.Fields.tradeCurrency))
        .as(PortfolioItemFlatView.Fields.tradeInfoTradeCurrency)
        .and(joinPaths(parent, TradeDetails.Fields.info, TradeInfoDetails.Fields.settlementDate))
        .as(PortfolioItemFlatView.Fields.tradeInfoSettlementDate)
        .and(
            joinPaths(
                parent, TradeDetails.Fields.info, TradeInfoDetails.Fields.csaDiscountingGroup))
        .as(PortfolioItemFlatView.Fields.tradeInfoCsaDiscountingGroup)
        .and(joinPaths(parent, TradeDetails.Fields.positionType))
        .as(PortfolioItemFlatView.Fields.tradeInfoPosition)
        .and(
            joinPaths(
                parent,
                TradeDetails.Fields.optionTradeDetails,
                OptionTradeDetails.Fields.expiryDate))
        .as(PortfolioItemFlatView.Fields.tradeInfoExpiryDate)
        .and(
            joinPaths(
                parent,
                TradeDetails.Fields.optionTradeDetails,
                OptionTradeDetails.Fields.premiumDate))
        .as(PortfolioItemFlatView.Fields.tradeInfoPremiumDate)
        .and(
            propertyName(
                parent,
                TradeDetails.Fields.optionTradeDetails,
                OptionTradeDetails.Fields.premiumCurrency))
        .as(PortfolioItemFlatView.Fields.tradeInfoPremiumValueCurrency)
        .and(
            joinPaths(
                parent,
                TradeDetails.Fields.optionTradeDetails,
                OptionTradeDetails.Fields.premiumValue))
        .as(PortfolioItemFlatView.Fields.tradeInfoPremiumValueAmount)
        .and(
            joinPaths(
                parent,
                TradeDetails.Fields.optionTradeDetails,
                OptionTradeDetails.Fields.callPutType))
        .as(PortfolioItemFlatView.Fields.tradeInfoCallPutType)
        .and(
            joinPaths(
                parent,
                TradeDetails.Fields.optionTradeDetails,
                OptionTradeDetails.Fields.capFloorType))
        .as(PortfolioItemFlatView.Fields.tradeInfoType)
        .and(
            joinPaths(
                parent, TradeDetails.Fields.optionTradeDetails, OptionTradeDetails.Fields.strike))
        .as(PortfolioItemFlatView.Fields.tradeInfoStrike)
        .and(
            joinPaths(
                parent,
                TradeDetails.Fields.optionTradeDetails,
                OptionTradeDetails.Fields.otherOptionStrike))
        .as(PortfolioItemFlatView.Fields.tradeInfoOtherOptionStrike)
        .and(
            joinPaths(
                parent,
                TradeDetails.Fields.optionTradeDetails,
                OptionTradeDetails.Fields.otherOptionCounterNotional))
        .as(PortfolioItemFlatView.Fields.tradeInfoOtherOptionCounterNotional)
        .and(
            joinPaths(
                parent,
                TradeDetails.Fields.optionTradeDetails,
                OptionTradeDetails.Fields.otherOptionStrike))
        .as(PortfolioItemFlatView.Fields.tradeInfoOtherOptionStrike)
        .and(
            propertyName(
                parent, TradeDetails.Fields.creditTradeDetails, CreditTradeDetails.Fields.upfront))
        .as(PortfolioItemFlatView.Fields.tradeInfoUpfrontFee)
        .and(
            propertyName(
                parent,
                TradeDetails.Fields.creditTradeDetails,
                CreditTradeDetails.Fields.reference))
        .as(PortfolioItemFlatView.Fields.tradeInfoCreditReference)
        .and(
            propertyName(
                parent,
                TradeDetails.Fields.creditTradeDetails,
                CreditTradeDetails.Fields.seniority))
        .as(PortfolioItemFlatView.Fields.tradeInfoSeniority)
        .and(
            propertyName(
                parent,
                TradeDetails.Fields.creditTradeDetails,
                CreditTradeDetails.Fields.docClause))
        .as(PortfolioItemFlatView.Fields.tradeInfoDocClause)
        .and(
            propertyName(
                parent, TradeDetails.Fields.creditTradeDetails, CreditTradeDetails.Fields.sector))
        .as(PortfolioItemFlatView.Fields.tradeInfoSector)
        .and(
            propertyName(
                parent,
                TradeDetails.Fields.creditTradeDetails,
                CreditTradeDetails.Fields.entityLongName))
        .as(PortfolioItemFlatView.Fields.tradeInfoCreditEntityLongName);
  }
}
