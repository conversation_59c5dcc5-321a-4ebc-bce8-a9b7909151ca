package com.solum.xplain.core.portfolio.value.view;

import com.solum.xplain.core.viewconfig.provider.AbstractColumnsBuilder;
import java.util.List;
import java.util.Set;
import java.util.function.Predicate;

public class DefaultColumnsBuilder extends AbstractColumnsBuilder {

  private static final Set<String> CUSTOM_TRADES_FIELDS =
      Set.of(
          "trade.customTradeDetails.assetType",
          "trade.customTradeDetails.subAssetType",
          "trade.customTradeDetails.notional",
          "trade.customTradeDetails.underlying",
          "trade.customTradeDetails.optionPosition",
          "trade.creditTradeDetails.protection",
          "trade.payLeg.type",
          "trade.receiveLeg.type");

  private static final Predicate<String> CREDIT_TRADES_MATCHER =
      name -> name.startsWith("trade.creditTradeDetails.") && !CUSTOM_TRADES_FIELDS.contains(name);

  private static final Set<String> LOAN_NOTE_TRADES_FIELDS =
      Set.of("trade.loanNoteTradeDetails.reference", "trade.loanNoteTradeDetails.creditSpread");

  private static final Predicate<String> OPTION_TRADES_MATCHER =
      name ->
          name.startsWith("trade.optionTradeDetails.")
              || name.equals("trade.positionType")
              || name.equals("trade.info.settlementDate");

  /** Far pay leg is the far part of the receive leg in that leg's currency. */
  private static final Set<String> NEAR_PAY_LEG_FIELDS =
      Set.of("trade.receiveLeg.nearPayCurrency", "trade.receiveLeg.nearNotional");

  /** Far receive leg is the far part of the pay leg in that leg's currency. */
  private static final Set<String> NEAR_RECEIVE_LEG_FIELDS =
      Set.of("trade.payLeg.nearReceiveCurrency", "trade.payLeg.nearNotional");

  private static final Predicate<String> PAY_LEG_MATCHER =
      // and not in custom trades
      name ->
          name.startsWith("trade.payLeg.")
              && !CUSTOM_TRADES_FIELDS.contains(name)
              && !NEAR_PAY_LEG_FIELDS.contains(name)
              && !NEAR_RECEIVE_LEG_FIELDS.contains(name);

  private static final Predicate<String> RECEIVE_LEG_MATCHER =
      name ->
          name.startsWith("trade.receiveLeg.")
              && !CUSTOM_TRADES_FIELDS.contains(name)
              && !NEAR_RECEIVE_LEG_FIELDS.contains(name)
              && !NEAR_PAY_LEG_FIELDS.contains(name);

  private static final Predicate<String> ONBOARDING_INFO_MATCHER =
      name -> name.startsWith("onboardingDetails.");

  private static final Predicate<String> LAST_MODIFIED_MATCHER =
      name -> name.equals("modifiedBy") || name.equals("modifiedAt");

  private static final Predicate<String> DEFAULT_HIDDEN_FIELDS_MATCHER =
      name ->
          Set.of(
                  "onboardingDetails.xplainCheckVerified",
                  "onboardingDetails.marketCheckVerified",
                  "onboardingDetails.vendorCheckVerified",
                  "trade.payLeg.payReceive",
                  "trade.payLeg.paymentFrequency",
                  "trade.payLeg.paymentCompounding",
                  "trade.payLeg.paymentOffsetDays",
                  "trade.payLeg.fixingDateOffsetDays",
                  "trade.payLeg.inflationLag",
                  "trade.payLeg.isOffshore",
                  "trade.receiveLeg.payReceive",
                  "trade.receiveLeg.paymentFrequency",
                  "trade.receiveLeg.paymentCompounding",
                  "trade.receiveLeg.paymentOffsetDays",
                  "trade.receiveLeg.fixingDateOffsetDays",
                  "trade.receiveLeg.inflationLag",
                  "trade.receiveLeg.isOffshore",
                  "trade.info.tradeTime",
                  "trade.info.settlementDate",
                  "trade.optionTradeDetails.expiryTime",
                  "trade.optionTradeDetails.expiryZone",
                  "trade.optionTradeDetails.expiryDate",
                  "trade.optionTradeDetails.expiryDateConvention",
                  "trade.optionTradeDetails.premiumDate",
                  "trade.optionTradeDetails.premiumDateConvention",
                  "trade.optionTradeDetails.premiumValue",
                  "trade.optionTradeDetails.premiumCurrency",
                  "trade.optionTradeDetails.swaptionSettlementType",
                  "trade.creditTradeDetails.entityLongName",
                  "trade.creditTradeDetails.sector",
                  "trade.creditTradeDetails.creditIndexVersion",
                  "trade.creditTradeDetails.creditIndexSeries",
                  "trade.creditTradeDetails.creditIndexTranche",
                  "trade.customTradeDetails.additionalInfo",
                  "trade.businessDayConvention",
                  "trade.notionalScheduleInitialExchange",
                  "trade.notionalScheduleFinalExchange",
                  "trade.stubConvention",
                  "trade.firstRegularStartDate",
                  "trade.lastRegularEndDate",
                  "trade.rollConvention",
                  "trade.fxRate",
                  "allocationDetails.allocationNotional",
                  "allocationDetails.positionType",
                  "allocationDetails.counterParty",
                  "allocationDetails.counterPartyType",
                  "allocationDetails.presentValue",
                  "allocationDetails.tradeDate",
                  "allocationDetails.description",
                  "allocationDetails.csaDiscountingGroup",
                  "allocationDetails.optionPosition",
                  "allocationDetails.protection",
                  "externalIdentifier",
                  "customFieldValue")
              .contains(name);

  private static final Predicate<String> TRADE_DETAILS_GROUP_MATCHER =
      Predicate.not(
          name ->
              CUSTOM_TRADES_FIELDS.contains(name)
                  || CREDIT_TRADES_MATCHER.test(name)
                  || LOAN_NOTE_TRADES_FIELDS.contains(name)
                  || OPTION_TRADES_MATCHER.test(name)
                  || PAY_LEG_MATCHER.test(name)
                  || RECEIVE_LEG_MATCHER.test(name)
                  || ONBOARDING_INFO_MATCHER.test(name)
                  || LAST_MODIFIED_MATCHER.test(name)
                  || NEAR_PAY_LEG_FIELDS.contains(name)
                  || NEAR_RECEIVE_LEG_FIELDS.contains(name));

  public DefaultColumnsBuilder() {
    super(
        List.of(
            builder(TRADE_DETAILS_GROUP_MATCHER, "Trade Details"),
            builder(CUSTOM_TRADES_FIELDS::contains, "Custom Trades"),
            builder(CREDIT_TRADES_MATCHER, "Credit Trades"),
            builder(LOAN_NOTE_TRADES_FIELDS::contains, "Loan Note Trades"),
            builder(OPTION_TRADES_MATCHER, "Options"),
            builder(PAY_LEG_MATCHER, "Pay Leg"),
            builder(RECEIVE_LEG_MATCHER, "Receive Leg"),
            builder(ONBOARDING_INFO_MATCHER, "Onboarding Information"),
            builder(NEAR_PAY_LEG_FIELDS::contains, "Near Pay Leg"),
            builder(NEAR_RECEIVE_LEG_FIELDS::contains, "Near Receive Leg"),
            builder(LAST_MODIFIED_MATCHER, "Last Modified")));
  }

  private static ColumnDefinitionGroupBuilder builder(Predicate<String> matcher, String label) {
    return new ColumnDefinitionGroupBuilder(matcher, label, DEFAULT_HIDDEN_FIELDS_MATCHER);
  }
}
