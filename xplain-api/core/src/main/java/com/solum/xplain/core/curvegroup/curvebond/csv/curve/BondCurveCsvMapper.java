package com.solum.xplain.core.curvegroup.curvebond.csv.curve;

import static com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvFields.EXTRAPOLATOR_LEFT_FIELD;
import static com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvFields.EXTRAPOLATOR_RIGHT_FIELD;
import static com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvFields.INTERPOLATOR_FIELD;
import static com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvFields.NAME_FIELD;
import static com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvFields.NUMBER_OF_NODES_FIELDS;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveView;
import java.util.List;

public class BondCurveCsvMapper extends CsvMapper<BondCurveView> {

  private static final List<CsvColumn<BondCurveView>> COLUMNS =
      List.of(
          CsvColumn.text(BondCurveView.Fields.name, NAME_FIELD, BondCurveView::getName),
          CsvColumn.text(
              BondCurveView.Fields.interpolator,
              INTERPOLATOR_FIELD,
              BondCurveView::getInterpolator),
          CsvColumn.text(
              BondCurveView.Fields.extrapolatorLeft,
              EXTRAPOLATOR_LEFT_FIELD,
              BondCurveView::getExtrapolatorLeft),
          CsvColumn.text(
              BondCurveView.Fields.extrapolatorRight,
              EXTRAPOLATOR_RIGHT_FIELD,
              BondCurveView::getExtrapolatorRight),
          CsvColumn.integer(
                  BondCurveView.Fields.numberOfNodes,
                  NUMBER_OF_NODES_FIELDS,
                  BondCurveView::getNumberOfNodes)
              .optional());

  public BondCurveCsvMapper() {
    super(COLUMNS, null);
  }

  @Override
  public List<String> header() {
    ImmutableList.Builder<String> builder = ImmutableList.builder();
    builder.addAll(super.header());
    return builder.build();
  }

  @Override
  public CsvRow toCsvRow(BondCurveView object) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.addAll(super.toCsvFields(object));
    return new CsvRow(builder.build());
  }
}
