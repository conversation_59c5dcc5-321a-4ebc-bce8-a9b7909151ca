package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_PORTFOLIO;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_TRADE;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_TRADE;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.lock.XplainLock.TRADES_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.GroupRequest;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.ScrolledGroupedFiltered;
import com.solum.xplain.core.common.Sorted;
import com.solum.xplain.core.common.SortedFiltered;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.csv.ValidationResponse;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.portfolio.form.PortfolioItemSearchForm;
import com.solum.xplain.core.portfolio.form.TradeImportOptions;
import com.solum.xplain.core.portfolio.value.PortfolioItemWithKeyView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Validated
@RestController
@RequestMapping("/portfolio")
public class PortfolioTradesController {

  private final PortfolioControllerService service;
  private final PortfolioExportService exportService;

  public PortfolioTradesController(
      PortfolioControllerService service, PortfolioExportService exportService) {
    this.service = service;
    this.exportService = exportService;
  }

  @Operation(summary = "Archives (sets status to ARCHIVED) portfolio item")
  @PutMapping("/{id}/trades/{tradeEntityId}/{version}/archive")
  @CommonErrors
  @RequireLock(name = TRADES_LOCK_ID)
  @PreAuthorize(AUTHORITY_MODIFY_TRADE)
  public ResponseEntity<EntityId> archivePortfolioItem(
      @PathVariable("id") String portfolioId,
      @PathVariable("tradeEntityId") String tradeEntityId,
      @PathVariable("version") LocalDate version,
      @RequestBody @Valid ArchiveEntityForm archiveEntityForm) {
    return eitherErrorItemResponse(
        service.archivePortfolioItem(portfolioId, tradeEntityId, version, archiveEntityForm));
  }

  @Operation(summary = "Deletes (sets status to DELETED) portfolio item")
  @PutMapping("/{id}/trades/{tradeEntityId}/{version}/delete")
  @CommonErrors
  @RequireLock(name = TRADES_LOCK_ID)
  @PreAuthorize(AUTHORITY_MODIFY_TRADE)
  public ResponseEntity<EntityId> deletePortfolioItem(
      @PathVariable("id") String portfolioId,
      @PathVariable("tradeEntityId") String tradeEntityId,
      @PathVariable("version") LocalDate version) {
    return eitherErrorItemResponse(
        service.deletePortfolioItem(portfolioId, tradeEntityId, version));
  }

  @Operation(summary = "Get portfolio item future versions")
  @GetMapping("/{id}/trades/future-versions/search")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_TRADE)
  public DateList getFutureTradeVersions(
      @PathVariable("id") String portfolioId, @Valid PortfolioItemSearchForm form) {
    return service.futureVersions(portfolioId, form);
  }

  @Operation(summary = "Get portfolio items")
  @GetMapping("/{id}/trades")
  @Sorted
  @ScrolledGroupedFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRADE)
  public ResponseEntity<ScrollableEntry<PortfolioItemWithKeyView>> getItems(
      @PathVariable("id") String id,
      @RequestParam("stateDate") LocalDate stateDate,
      @RequestParam(required = false) boolean withArchived,
      TableFilter tableFilter,
      @SortDefault(
              sort = {
                VersionedTradeEntity.Fields.productType,
                PortfolioItem.Fields.externalPortfolioId,
                VersionedTradeEntity.Fields.externalTradeId
              })
          ScrollRequest scrollRequest,
      GroupRequest groupRequest) {
    return eitherErrorItemResponse(
        service.getItems(
            id,
            BitemporalDate.newOf(stateDate),
            withArchived,
            tableFilter,
            scrollRequest,
            groupRequest));
  }

  @Operation(summary = "Get single portfolio item")
  @GetMapping("/{id}/trades/{tradeEntityId}")
  @ScrolledFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRADE)
  public ResponseEntity<PortfolioItemWithKeyView> getItem(
      @PathVariable("id") String id,
      @PathVariable("tradeEntityId") String tradeEntityId,
      @RequestParam("stateDate") LocalDate stateDate) {
    return eitherErrorItemResponse(
        service.getItem(id, tradeEntityId, BitemporalDate.newOf(stateDate)));
  }

  @Operation(summary = "Get all portfolios trade items in csv format")
  @GetMapping("/trades-csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRADE)
  public ResponseEntity<ByteArrayResource> getAllItemsCsv(
      @SortDefault(
              sort = {
                VersionedTradeEntity.Fields.productType,
                PortfolioItem.Fields.externalPortfolioId,
                VersionedTradeEntity.Fields.externalTradeId
              })
          Sort sort,
      @RequestParam LocalDate stateDate) {
    return eitherErrorItemFileResponse(
        exportService.exportAllPortfolioItems(BitemporalDate.newOf(stateDate), sort));
  }

  @Operation(summary = "Get portfolio trade items in csv format")
  @GetMapping(value = "/{id}/trades-csv")
  @CommonErrors
  @SortedFiltered
  @PreAuthorize(AUTHORITY_VIEW_TRADE)
  public ResponseEntity<ByteArrayResource> getItemsCsv(
      @PathVariable("id") String id,
      @RequestParam LocalDate stateDate,
      @SortDefault(
              sort = {
                VersionedTradeEntity.Fields.productType,
                PortfolioItem.Fields.externalPortfolioId,
                VersionedTradeEntity.Fields.externalTradeId
              })
          Sort sort,
      TableFilter tableFilter) {
    return eitherErrorItemFileResponse(
        exportService.exportPortfolioItems(BitemporalDate.newOf(stateDate), id, sort, tableFilter));
  }

  @Operation(summary = "Upload trades csv file")
  @PostMapping(value = "/trades/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_PORTFOLIO)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadTrades(
      @RequestPart MultipartFile file, @Valid TradeImportOptions importOptions) throws IOException {
    return eitherErrorItemsResponse(service.uploadTrades(file, importOptions));
  }

  @Operation(summary = "Validate trades csv file")
  @PostMapping(value = "/trades/upload/validate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_PORTFOLIO)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<ValidationResponse> validateImportFile(
      @RequestPart MultipartFile file, @Valid TradeImportOptions importOptions) throws IOException {
    return eitherErrorItemsResponse(service.validateTradesImport(file, importOptions));
  }

  @Operation(summary = "Upload trades csv file")
  @PostMapping(value = "{id}/trades/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_TRADE)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadTradesForPortfolio(
      @RequestPart MultipartFile file,
      @PathVariable String id,
      @Valid TradeImportOptions importOptions)
      throws IOException {
    return eitherErrorItemsResponse(service.uploadTradesForPortfolio(id, file, importOptions));
  }

  @Operation(summary = "Validate trades csv file")
  @PostMapping(
      value = "{id}/trades/upload/validate",
      consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_TRADE)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<ValidationResponse> validateImportForPortfolio(
      @RequestPart MultipartFile file,
      @PathVariable String id,
      @Valid TradeImportOptions importOptions)
      throws IOException {
    return eitherErrorItemsResponse(service.validateUploadForPortfolio(id, file, importOptions));
  }

  @Operation(summary = "Get portfolio item versions")
  @GetMapping("/{id}/trades/{tradeEntityId}/versions")
  @ScrolledFiltered
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_TRADE)
  public ResponseEntity<List<PortfolioItemWithKeyView>> getVersions(
      @PathVariable("id") String id, @PathVariable("tradeEntityId") String tradeEntityId) {
    return eitherErrorItemResponse(service.getVersions(id, tradeEntityId));
  }
}
