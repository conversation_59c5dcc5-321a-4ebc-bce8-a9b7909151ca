package com.solum.xplain.core.curvegroup.curve;

import static com.solum.xplain.core.classifiers.CurveNodeTypes.FRA_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FIXING_DEPOSIT_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FUTURE_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IMM_FRA_NODE;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.TERM_DEPOSIT_NODE;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.common.versions.VersionedEntityMapper;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult;
import com.solum.xplain.core.curvegroup.curve.entity.CurveNode;
import com.solum.xplain.core.curvegroup.curve.value.CurveForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveView;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    uses = {ObjectIdMapper.class, AuditUserMapper.class},
    imports = CollectionUtils.class)
public interface CurveMapper extends VersionedEntityMapper<Curve> {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "curveGroupId", source = "groupId")
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(target = "nodes", expression = "java(fromNodeFormList(form.getName(), form.getNodes()))")
  Curve fromForm(CurveForm form, String groupId, @MappingTarget Curve curve);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "curveGroupId", ignore = true)
  @Mapping(target = "name", ignore = true)
  @Mapping(target = "curveType", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(
      target = "nodes",
      expression = "java(fromNodeFormList(curve.getName(), form.getNodes()))")
  Curve fromForm(CurveUpdateForm form, @MappingTarget Curve curve);

  @Mapping(target = "versionForm", ignore = true)
  CurveForm toForm(Curve curve);

  CurveNodeForm toNodeForm(CurveNode node);

  @Mapping(target = "period", expression = "java(resolvePeriod(curveName, nodeForm))")
  CurveNode fromNodeForm(String curveName, CurveNodeForm nodeForm);

  default List<CurveNode> fromNodeFormList(String curveName, List<CurveNodeForm> nodes) {
    if (nodes == null) {
      return List.of();
    }

    return nodes.stream().map(n -> fromNodeForm(curveName, n)).toList();
  }

  default String resolvePeriod(String curveName, CurveNodeForm nodeForm) {
    if (StringUtils.equalsAny(
        nodeForm.getType(),
        FRA_NODE,
        IMM_FRA_NODE,
        IBOR_FIXING_DEPOSIT_NODE,
        TERM_DEPOSIT_NODE,
        IBOR_FUTURE_NODE)) {
      return CurvesUtils.curveTenor(curveName).map(Tenor::toString).orElse(nodeForm.getPeriod());
    }
    return nodeForm.getTenor();
  }

  // This method should be unused but left as a safeguard to throw error.
  default CurveNode fromForm(CurveNodeForm form) {
    throw new UnsupportedOperationException("Mapping from form not supported");
  }

  @Mapping(target = "numberOfNodes", expression = "java(curve.getNodes().size())")
  @Mapping(target = "recordDate", source = "curve.recordDate")
  @Mapping(target = "calibrated", source = "curveCalibrationResult.calibrated")
  @Mapping(
      target = "calibrationDiscountCurrency",
      source = "curveCalibrationResult.discountCurrency")
  CurveView toView(Curve curve, CurveCalibrationResult curveCalibrationResult);
}
