package com.solum.xplain.core.curvegroup.curve.entity;

import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

import com.solum.xplain.core.common.value.CalculatedValueAtDate;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document
@FieldNameConstants
public class CurveCalibrationResult {

  private String curveId;

  @CreatedDate private LocalDateTime calibratedAt;

  private String marketDataGroupId;
  private String marketDataSource;
  private String calibrationCurrency;
  private CalculationStrippingType calibrationStrippingType;
  private LocalDate stateDate;
  private LocalDate curveDate;
  private LocalDate valuationDate;
  private InstrumentPriceRequirements priceRequirements;

  private String discountCurrency;
  private String inflationAdjustmentType;
  private List<Double> inflationSeasonalityAdjustment;

  private List<CalculatedValueAtDate> discountFactorPoints;
  private List<CalculatedValueAtDate> nodeValues;
  private List<ChartPoint> chartValues;
  private List<String> nodesUsedInCalibration;

  public boolean isCalibrated() {
    return isNotEmpty(discountFactorPoints)
        || isNotEmpty(nodeValues)
        || isNotEmpty(nodesUsedInCalibration);
  }

  public static CurveCalibrationResult emptyResult() {
    CurveCalibrationResult result = new CurveCalibrationResult();
    result.setDiscountFactorPoints(List.of());
    result.setNodeValues(List.of());
    result.setNodesUsedInCalibration(List.of());
    return result;
  }

  public List<CalculatedValueAtDate> getNodeValuesOrEmpty() {
    return nodeValues != null ? nodeValues : List.of();
  }

  public List<CalculatedValueAtDate> getDiscountFactorPointsOrEmpty() {
    return discountFactorPoints != null ? discountFactorPoints : List.of();
  }

  public List<String> getNodesUsedInCalibrationOrEmpty() {
    return nodesUsedInCalibration != null ? nodesUsedInCalibration : List.of();
  }
}
