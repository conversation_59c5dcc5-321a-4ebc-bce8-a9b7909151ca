package com.solum.xplain.core.spring.mongo;

import static com.solum.xplain.core.common.versions.VersionedDataAggregations.listVersionedNonDeleted;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;

import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.shared.spring.mongo.AggregationParameterResolver;
import com.solum.xplain.shared.spring.mongo.ConfigurableEntitySupport;
import com.solum.xplain.shared.utils.filter.TableFilter;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;

@Slf4j
@Configurable
public class XplainAggregationParameterResolver implements AggregationParameterResolver {
  @Resource ConversionService conversionService;

  {
    ConfigurableEntitySupport.configureBean(this);
  }

  public List<AggregationOperation> getParameterOperations(Object parameter, Class<?> typeToRead) {
    if (parameter instanceof BitemporalDate stateDate) {
      log.info("Adding BitemporalDate to query: {}", stateDate);
      return listVersionedNonDeleted(stateDate);
    } else if (parameter instanceof VersionedEntityFilter filter) {
      log.info("Adding VersionedEntityFilter to query: {}", filter);
      return List.of(match(filter.criteria()));
    } else if (parameter instanceof AggregationOperation operation) {
      log.info("Adding AggregationOperation to query: {}", operation);
      return List.of(operation);
    } else if (parameter instanceof TableFilter tableFilter) {
      log.info("Adding TableFilter to query: {}", tableFilter);
      return List.of(match(tableFilter.criteria(typeToRead, conversionService)));
    }
    return List.of();
  }
}
