package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public class ClientMetrics {

  @ConfigurableViewQuery(sortable = true)
  private Double presentValue;
}
