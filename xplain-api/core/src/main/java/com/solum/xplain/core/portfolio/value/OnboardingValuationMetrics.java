package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.value.Sens01Value.sensitivity01Value;

import com.solum.xplain.core.portfolio.CoreProductType;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class OnboardingValuationMetrics {

  private String externalTradeId;
  private CoreProductType productType;
  private Double presentValue;
  private Double accrued;
  private Double cleanPresentValue;
  private Double dv01;
  private Double br01;
  private Double inf01;
  private Double cs01;
  private Double infcsbr01;
  private Double deltaFwd;
  private Double deltaSpot;
  private Double fxSpot;
  private Double pv01;
  private Double pvDelta;
  private Double pvGamma;
  private Double pvTheta;
  private Double pvVega;
  private Double parRate;
  private Double impliedVol;

  public Double delta() {
    // TODO: add inLocalCcy param
    return sensitivity01Value(productType, dv01, br01, inf01, cs01);
  }
}
