package com.solum.xplain.core.portfolio.validation;

import static com.solum.xplain.extensions.enums.CallPutType.CALL;
import static com.solum.xplain.extensions.enums.CallPutType.PUT;

import com.solum.xplain.core.portfolio.form.FxOptionTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidCallPutValidator implements ConstraintValidator<ValidCallPut, FxOptionTradeForm> {

  public boolean isValid(FxOptionTradeForm form, ConstraintValidatorContext context) {
    // if notional on domestic currency is positive, callPutType must be CALL, else PUT
    if (form != null) {
      var callPut = form.getCallPutType();
      if (callPut != null
          && form.getDomesticCurrencyAmount() != null
          && form.getDomesticCurrencyAmount().getAmount() != null) {
        return form.getDomesticCurrencyAmount().getAmount().compareTo(0d) < 0
            ? callPut == PUT
            : callPut == CALL;
      }
    }
    return true;
  }
}
