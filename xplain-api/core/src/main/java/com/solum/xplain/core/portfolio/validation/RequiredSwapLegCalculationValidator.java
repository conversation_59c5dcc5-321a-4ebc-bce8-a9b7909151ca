package com.solum.xplain.core.portfolio.validation;

import static org.apache.commons.lang3.ObjectUtils.anyNull;

import com.solum.xplain.core.portfolio.form.SwapLegForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredSwapLegCalculationValidator
    implements ConstraintValidator<RequiredSwapLegCalculation, SwapLegForm> {
  private static final String NOT_NULL = "NotNull";

  public boolean isValid(SwapLegForm form, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    if (anyNull(form, form.getCalculationType())) {
      return true;
    }
    return switch (form.getCalculationType()) {
      case FIXED -> checkCalculationFixed(form, context);
      case IBOR -> checkCalculationIbor(form, context);
      case INFLATION -> checkCalculationInflation(form, context);
      case OVERNIGHT -> checkCalculationOvernight(form, context);
    };
  }

  private boolean checkCalculationOvernight(SwapLegForm form, ConstraintValidatorContext context) {
    var result = true;
    if (form.getCalculationOvernightDayCount() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationOvernightDayCount")
          .addConstraintViolation();
      result = false;
    }
    if (form.getCalculationOvernightIndex() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationOvernightIndex")
          .addConstraintViolation();
      result = false;
    }
    if (form.getCalculationOvernightSpreadInitialValue() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationOvernightSpreadInitialValue")
          .addConstraintViolation();
      result = false;
    }
    if (form.getCalculationOvernightAccrualMethod() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationOvernightAccrualMethod")
          .addConstraintViolation();
      result = false;
    }
    if (form.getCalculationOvernightRateCutOffDays() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationOvernightRateCutOffDays")
          .addConstraintViolation();
      result = false;
    }
    return result;
  }

  private boolean checkCalculationInflation(SwapLegForm form, ConstraintValidatorContext context) {
    var result = true;
    if (form.getCalculationInflationIndex() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationInflationIndex")
          .addConstraintViolation();
      result = false;
    }
    if (form.getCalculationInflationLag() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationInflationLag")
          .addConstraintViolation();
      result = false;
    }
    if (form.getIndexCalculationMethod() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("indexCalculationMethod")
          .addConstraintViolation();
      result = false;
    }

    return result;
  }

  private boolean checkCalculationIbor(SwapLegForm form, ConstraintValidatorContext context) {
    var result = true;

    if (form.getCalculationIborDayCount() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationIborDayCount")
          .addConstraintViolation();
      result = false;
    }
    if (form.getCalculationIborIndex() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationIborIndex")
          .addConstraintViolation();
      result = false;
    }
    if (form.getCalculationIborFixingDateOffsetDays() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationIborFixingDateOffsetDays")
          .addConstraintViolation();
      result = false;
    }
    if (form.getCalculationIborSpreadInitialValue() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationIborSpreadInitialValue")
          .addConstraintViolation();
      result = false;
    }
    return result;
  }

  private boolean checkCalculationFixed(SwapLegForm form, ConstraintValidatorContext context) {
    var result = true;
    if (form.getCalculationFixedRateInitialValue() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationFixedRateInitialValue")
          .addConstraintViolation();
      result = false;
    }
    if (form.getCalculationFixedDayCount() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationFixedDayCount")
          .addConstraintViolation();
      result = false;
    }
    if (form.getCalculationFixedAccrualMethod() == null) {
      context
          .buildConstraintViolationWithTemplate(NOT_NULL)
          .addPropertyNode("calculationFixedAccrualMethod")
          .addConstraintViolation();
      result = false;
    }
    return result;
  }
}
