package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.CoreProductType.CREDIT_INDEX;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.CreditIndexTradeForm;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class CreditIndexTradeView extends CreditIndexTradeForm implements TradeView {
  private String updatedBy;
  private LocalDateTime updatedAt;
  private String tradeId;
  private String calendar;

  public CreditIndexTradeView(VersionedTradeEntity item) {
    super(item);
    setCalendar(item.getTradeDetails().getCalendar());
    this.updateCommonView(item);
  }

  public static Either<ErrorItem, CreditIndexTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == CREDIT_INDEX) {
      var view = new CreditIndexTradeView(item);
      return Either.right(view);
    } else {
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Product type is not " + "expected " + item.getProductType()));
    }
  }
}
