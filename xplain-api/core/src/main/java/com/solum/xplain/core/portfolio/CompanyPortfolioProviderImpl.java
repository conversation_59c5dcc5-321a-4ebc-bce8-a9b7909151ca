package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.company.CompanyPortfolioProvider;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import java.util.List;
import java.util.Map;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@NullMarked
public class CompanyPortfolioProviderImpl implements CompanyPortfolioProvider {

  private final PortfolioRepository portfolioRepository;

  public CompanyPortfolioProviderImpl(PortfolioRepository portfolioRepository) {
    this.portfolioRepository = portfolioRepository;
  }

  @Override
  public Map<String, Integer> countAll(List<String> companyIds) {
    return portfolioRepository.portfoliosCountAll(companyIds);
  }
}
