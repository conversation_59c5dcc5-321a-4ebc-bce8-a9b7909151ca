package com.solum.xplain.core.curvegroup.volatility.csv.surface;

import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.CAPLET_VALUATION_MODEL;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.NAME_FIELD;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.SABR_BETA;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.SABR_MODE;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.SABR_SHIFT;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.SKEW_INPUT_TYPE;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.X_EXTRAPOLATOR_LEFT_FIELD;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.X_EXTRAPOLATOR_RIGHT_FIELD;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.X_INTERPOLATOR_FIELD;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.Y_EXTRAPOLATOR_LEFT_FIELD;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.Y_EXTRAPOLATOR_RIGHT_FIELD;
import static com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvLoader.Y_INTERPOLATOR_FIELD;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView;
import java.util.List;

public class VolatilitySurfaceCsvMapper extends CsvMapper<VolatilitySurfaceView> {

  private static final String SWAPTION_VOLATILIY_NODES_FIELD = "Swaption Volatility Nodes";
  private static final String CAPLET_VOLATILIY_NODES_FIELD = "Caplet Volatility Nodes";
  private static final String SWAP_CONVENTION_FIELD = "Swap Convention";

  private static final String SWAP_CONVENTION_FIELD_NAME = "swapConvention";

  private static final List<CsvColumn<VolatilitySurfaceView>> COLUMNS =
      ImmutableList.<CsvColumn<VolatilitySurfaceView>>builder()
          .add(
              CsvColumn.text(
                  VolatilitySurfaceView.Fields.name, NAME_FIELD, VolatilitySurfaceView::getName))
          .add(
              CsvColumn.text(
                      SWAP_CONVENTION_FIELD_NAME,
                      SWAP_CONVENTION_FIELD,
                      VolatilitySurfaceView::getSwapConvention)
                  .optional())
          .add(
              CsvColumn.text(
                  VolatilitySurfaceView.Fields.xInterpolator,
                  X_INTERPOLATOR_FIELD,
                  VolatilitySurfaceView::getXInterpolator))
          .add(
              CsvColumn.text(
                  VolatilitySurfaceView.Fields.xExtrapolatorLeft,
                  X_EXTRAPOLATOR_LEFT_FIELD,
                  VolatilitySurfaceView::getXExtrapolatorLeft))
          .add(
              CsvColumn.text(
                  VolatilitySurfaceView.Fields.yExtrapolatorRight,
                  X_EXTRAPOLATOR_RIGHT_FIELD,
                  VolatilitySurfaceView::getXExtrapolatorRight))
          .add(
              CsvColumn.text(
                  VolatilitySurfaceView.Fields.yInterpolator,
                  Y_INTERPOLATOR_FIELD,
                  VolatilitySurfaceView::getYInterpolator))
          .add(
              CsvColumn.text(
                  VolatilitySurfaceView.Fields.yExtrapolatorLeft,
                  Y_EXTRAPOLATOR_LEFT_FIELD,
                  VolatilitySurfaceView::getYExtrapolatorLeft))
          .add(
              CsvColumn.text(
                  VolatilitySurfaceView.Fields.yExtrapolatorRight,
                  Y_EXTRAPOLATOR_RIGHT_FIELD,
                  VolatilitySurfaceView::getYExtrapolatorRight))
          .add(
              CsvColumn.enumField(
                  VolatilitySurfaceView.Fields.skewType,
                  SKEW_INPUT_TYPE,
                  VolatilitySurfaceView::getSkewType))
          .add(
              CsvColumn.enumField(
                  VolatilitySurfaceView.Fields.capletValuationModel,
                  CAPLET_VALUATION_MODEL,
                  VolatilitySurfaceView::getCapletValuationModel))
          .add(
              CsvColumn.bool(
                  VolatilitySurfaceView.Fields.sabr, SABR_MODE, VolatilitySurfaceView::isSabr))
          .add(
              CsvColumn.decimal(
                  VolatilitySurfaceView.Fields.sabrBeta,
                  SABR_BETA,
                  VolatilitySurfaceView::getSabrBeta))
          .add(
              CsvColumn.decimal(
                  VolatilitySurfaceView.Fields.sabrShift,
                  SABR_SHIFT,
                  VolatilitySurfaceView::getSabrShift))
          .add(
              CsvColumn.integer(
                      VolatilitySurfaceView.Fields.numberOfSwaptionVolatilityNodes,
                      SWAPTION_VOLATILIY_NODES_FIELD,
                      VolatilitySurfaceView::getNumberOfSwaptionVolatilityNodes)
                  .optional())
          .add(
              CsvColumn.integer(
                      VolatilitySurfaceView.Fields.numberOfCapletVolatilityNodes,
                      CAPLET_VOLATILIY_NODES_FIELD,
                      VolatilitySurfaceView::getNumberOfCapletVolatilityNodes)
                  .optional())
          .build();

  public VolatilitySurfaceCsvMapper(List<String> selectedColumns) {
    super(COLUMNS, selectedColumns);
  }
}
