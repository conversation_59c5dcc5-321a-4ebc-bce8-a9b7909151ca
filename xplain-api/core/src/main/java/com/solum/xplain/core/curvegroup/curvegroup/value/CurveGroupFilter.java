package com.solum.xplain.core.curvegroup.curvegroup.value;

import lombok.Data;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.mongodb.core.query.Criteria;

@Data
@ParameterObject
public class CurveGroupFilter {
  private Boolean archived;

  public Criteria criteria() {
    Criteria criteria = new Criteria();
    criteria.and(CurveGroupView.Fields.archived).is(Boolean.TRUE.equals(archived));

    return criteria;
  }
}
