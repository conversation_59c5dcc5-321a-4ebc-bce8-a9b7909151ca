package com.solum.xplain.core.curveconfiguration.csv.configuration;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.readCsv;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateEmptyContent;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateHeaders;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.CONFIGURATION_NAME_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.INSTRUMENT_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.PRIMARY_PROVIDER_FIELD;
import static com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader.SECONDARY_PROVIDER_FIELD;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static org.springframework.beans.factory.config.ConfigurableBeanFactory.SCOPE_PROTOTYPE;

import com.google.common.collect.Iterables;
import com.opengamma.strata.collect.io.CsvFile;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.curveconfiguration.csv.instrument.ConfigurationInstrumentCsvForm;
import com.solum.xplain.core.curveconfiguration.csv.instrument.ConfigurationInstrumentProviderCsvForm;
import com.solum.xplain.core.curveconfiguration.csv.instrument.CurveConfigurationInstrumentCsvLoader;
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationForm;
import com.solum.xplain.core.curveconfiguration.value.MarketDataProviderForm;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.instrument.InstrumentType;
import com.solum.xplain.core.instrument.InstrumentTypeResolver;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.inject.Provider;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.StreamSupport;
import org.apache.commons.collections4.IteratorUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope(SCOPE_PROTOTYPE)
public class CurveConfigurationCsvLoader {

  protected static final String CURVE_GROUP_FIELD = "Curve Group";
  private static final String DUPLICATE_INSTRUMENTS =
      "Duplicate instrument types: %s provided " + "for %s curve " + "configuration";
  private static final List<String> CONFIGURATION_CSV_HEADERS =
      List.of(
          CONFIGURATION_NAME_FIELD,
          CURVE_GROUP_FIELD,
          INSTRUMENT_FIELD,
          PRIMARY_PROVIDER_FIELD,
          SECONDARY_PROVIDER_FIELD);

  private final Provider<CurveConfigurationInstrumentCsvLoader> loaderProvider;
  private final InstrumentTypeResolver instrumentTypeResolver;
  private final Map<String, String> curveGroupMap;

  public CurveConfigurationCsvLoader(
      Provider<CurveConfigurationInstrumentCsvLoader> loaderProvider,
      InstrumentTypeResolver instrumentTypeResolver,
      CurveGroupRepository curveGroupRepository) {
    this.loaderProvider = loaderProvider;
    this.instrumentTypeResolver = instrumentTypeResolver;
    this.curveGroupMap =
        curveGroupRepository.curveGroupList().stream()
            .collect(toMap(CurveGroupView::getName, CurveGroupView::getId));
  }

  public Either<List<ErrorItem>, CsvParserResult<CurveConfigurationForm>> parse(
      byte[] csvContent, ParsingMode parsingMode) {
    try {
      CsvFile csvFile = readCsv(csvContent);
      validateEmptyContent(csvFile);
      validateHeaders(csvFile, CONFIGURATION_CSV_HEADERS);

      CurveConfigurationInstrumentCsvLoader instrumentCsvLoader = loaderProvider.get();

      var errors = new ArrayList<ErrorItem>();
      var rowEithers =
          csvFile.rows().stream().map(row -> this.parseRow(row, instrumentCsvLoader)).toList();
      Eithers.filterLeft(rowEithers).forEach(errors::add);

      var formEithers = resolveForms(Eithers.filterRight(rowEithers));
      Eithers.filterLeft(formEithers).forEach(errors::add);

      List<CurveConfigurationForm> forms =
          IteratorUtils.toList(Eithers.filterRight(formEithers).iterator());
      if (parsingMode.failOnError() && !errors.isEmpty()) {
        return Either.left(errors);
      }

      if (forms.isEmpty()) {
        return Either.left(List.of(PARSING_ERROR.entity("No valid entries in file!")));
      }
      return Either.right(new CsvParserResult<>(forms, errors));

    } catch (Exception e) {
      return Either.left(List.of(new ErrorItem(Error.PARSING_ERROR, e.getMessage())));
    }
  }

  private Either<ErrorItem, CurveConfigurationCsvForm> parseRow(
      CsvRow row, CurveConfigurationInstrumentCsvLoader instrumentCsvLoader) {
    return Steps.begin(parseGroupId(row))
        .then(() -> instrumentCsvLoader.parseRow(row))
        .yield(CurveConfigurationCsvForm::newOf);
  }

  private Either<ErrorItem, String> parseGroupId(CsvRow row) {
    return Checked.now(() -> row.getValue(CURVE_GROUP_FIELD))
        .map(groupName -> validateValue(groupName, curveGroupMap.keySet()))
        .toEither()
        .map(curveGroupMap::get)
        .leftMap(err -> rowParsingError(row, CURVE_GROUP_FIELD, err));
  }

  private List<Either<ErrorItem, CurveConfigurationForm>> resolveForms(
      Iterable<CurveConfigurationCsvForm> forms) {
    return StreamSupport.stream(forms.spliterator(), false)
        .collect(groupingBy(CurveConfigurationCsvForm::configurationName))
        .entrySet()
        .stream()
        .map(value -> resolveForm(value.getKey(), value.getValue()))
        .toList();
  }

  private Either<ErrorItem, CurveConfigurationForm> resolveForm(
      String name, List<CurveConfigurationCsvForm> forms) {
    return Steps.begin(resolveCurveGroupId(name, forms))
        .then(
            () ->
                forms.stream()
                    .map(CurveConfigurationCsvForm::getInstrumentCsvForm)
                    .map(ConfigurationInstrumentCsvForm::getInstrumentForm)
                    .collect(collectingAndThen(toList(), l -> resolveProvidersMap(name, l))))
        .yield((curveGroupId, instruments) -> form(name, curveGroupId, instruments));
  }

  private CurveConfigurationForm form(
      String configurationName,
      String curveGroupId,
      Map<InstrumentType, MarketDataProviderForm> instruments) {
    var form = new CurveConfigurationForm();
    form.setName(configurationName);
    form.setCurveGroupId(curveGroupId);
    form.setInstruments(instruments);
    return form;
  }

  private Either<ErrorItem, String> resolveCurveGroupId(
      String name, List<CurveConfigurationCsvForm> forms) {
    var curveGroupNames =
        forms.stream().map(CurveConfigurationCsvForm::getCurveGroupId).distinct().toList();
    if (curveGroupNames.size() != 1) {
      return Either.left(
          Error.PARSING_ERROR.entity(
              String.format("Several curve groups defined for %s curve configuration!", name)));
    }
    return Either.right(curveGroupNames.get(0));
  }

  private Either<ErrorItem, Map<InstrumentType, MarketDataProviderForm>> resolveProvidersMap(
      String configurationName, List<ConfigurationInstrumentProviderCsvForm> forms) {
    var instrumentsMap = toInstrumentMap(forms);
    var duplicateInstrumentTypes =
        instrumentsMap.entrySet().stream()
            .filter(v -> v.getValue().size() > 1)
            .map(Map.Entry::getKey)
            .toList();
    if (!duplicateInstrumentTypes.isEmpty()) {
      return Either.left(
          Error.PARSING_ERROR.entity(
              String.format(DUPLICATE_INSTRUMENTS, duplicateInstrumentTypes, configurationName)));
    }
    var result =
        instrumentTypeResolver.values().stream()
            .collect(toMap(Function.identity(), i -> resolveProvider(instrumentsMap, i)));
    return Either.right(result);
  }

  private Map<InstrumentType, List<MarketDataProviderForm>> toInstrumentMap(
      List<ConfigurationInstrumentProviderCsvForm> forms) {
    return forms.stream()
        .collect(
            groupingBy(
                ConfigurationInstrumentProviderCsvForm::getInstrumentType,
                mapping(ConfigurationInstrumentProviderCsvForm::getProvider, toList())));
  }

  private MarketDataProviderForm resolveProvider(
      Map<InstrumentType, List<MarketDataProviderForm>> instrumentsMap,
      InstrumentType instrumentType) {
    var instrument = instrumentsMap.get(instrumentType);
    return instrument == null ? MarketDataProviderForm.emptyForm() : Iterables.getLast(instrument);
  }
}
