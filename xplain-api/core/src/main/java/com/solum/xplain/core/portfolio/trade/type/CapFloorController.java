package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.CapFloorTradeForm;
import com.solum.xplain.core.portfolio.value.CapFloorTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/cap-floor")
@AllArgsConstructor
public class CapFloorController
    implements BespokeTradeTypedController<CapFloorTradeForm, CapFloorTradeView> {

  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, CapFloorTradeView> toViewFunction(PortfolioItem e) {
    return CapFloorTradeView.of(e);
  }
}
