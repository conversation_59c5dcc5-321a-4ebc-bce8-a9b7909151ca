package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.portfolio.CoreProductTypeGroup.CREDIT;
import static com.solum.xplain.core.portfolio.CoreProductTypeGroup.FX;
import static com.solum.xplain.core.portfolio.CoreProductTypeGroup.RATES;

import com.solum.xplain.core.product.ProductType;
import org.springframework.lang.Nullable;

public enum CoreProductType implements ProductType {
  CAP_FLOOR(RATES, true, "CapFloor", "Cap/Floor"),
  CDS(CREDIT, true, null, "CDS"),
  CREDIT_INDEX(CREDIT, true, null, "Credit Index"),
  CREDIT_INDEX_TRANCHE(CREDIT, true, null, "Credit Index Tranche"),
  FRA(RATES, false, "FRA", "FRA"),
  FXFWD(FX, false, "FxForward", "FX Forward"),
  FXSWAP(FX, false, "FxSwap", "FX Swap"),
  FXCOLLAR(FX, true, "FxCollar", "FX Collar"),
  FXOPT(FX, true, "FxOption", "FX Option"),
  INFLATION(RATES, false, null, "Inflation Swap"),
  IRS(RATES, false, "InterestRateSwap", "IRS"),
  LOAN_NOTE(RATES, false, null, "Loan Note"),
  SWAPTION(RATES, true, "Swaption", "Swaption"),
  XCCY(RATES, false, "CrossCurrencySwap", "XCCY Swap");

  private final CoreProductTypeGroup group;
  private final boolean buySell;
  private final String xvaType;
  private final String label;

  CoreProductType(CoreProductTypeGroup group, boolean buySell, String xvaType, String label) {
    this.group = group;
    this.buySell = buySell;
    this.xvaType = xvaType;
    this.label = label;
  }

  @Nullable
  @Override
  public String xvaType() {
    return xvaType;
  }

  @Override
  public String label() {
    return label;
  }

  @Override
  public CoreProductTypeGroup getGroup() {
    return group;
  }

  @Override
  public boolean isBuySell() {
    return buySell;
  }
}
