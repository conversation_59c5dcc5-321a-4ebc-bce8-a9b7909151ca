package com.solum.xplain.core.curvegroup.curve.csv.curve;

import static com.solum.xplain.core.curvegroup.curve.csv.curve.CurveCsvLoader.CURVE_TYPE_FIELD;
import static com.solum.xplain.core.curvegroup.curve.csv.curve.CurveCsvLoader.EXTRAPOLATOR_LEFT_FIELD;
import static com.solum.xplain.core.curvegroup.curve.csv.curve.CurveCsvLoader.EXTRAPOLATOR_RIGHT_FIELD;
import static com.solum.xplain.core.curvegroup.curve.csv.curve.CurveCsvLoader.INTERPOLATION_TYPE;
import static com.solum.xplain.core.curvegroup.curve.csv.curve.CurveCsvLoader.INTERPOLATOR_FIELD;
import static com.solum.xplain.core.curvegroup.curve.csv.curve.CurveCsvLoader.MINIMUM_NODE_GAP_FIELD;
import static com.solum.xplain.core.curvegroup.curve.csv.curve.CurveCsvLoader.NAME_FIELD;
import static com.solum.xplain.core.curvegroup.curve.csv.curve.CurveCsvLoader.NODE_CLASH_ACTION_FIELD;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.curvegroup.curve.value.CurveView;
import java.util.List;

public class CurveCsvMapper extends CsvMapper<CurveView> {

  private static final String NUMBER_OF_NODES_FIELDS = "Number of Nodes";

  private static final List<CsvColumn<CurveView>> COLUMNS =
      List.of(
          CsvColumn.text(CurveView.Fields.name, NAME_FIELD, CurveView::getName),
          CsvColumn.enumField(
              CurveView.Fields.curveType, CURVE_TYPE_FIELD, CurveView::getCurveType),
          CsvColumn.text(
              CurveView.Fields.interpolator, INTERPOLATOR_FIELD, CurveView::getInterpolator),
          CsvColumn.text(
              CurveView.Fields.yInterpolationMethod,
              INTERPOLATION_TYPE,
              CurveView::getYInterpolationMethod),
          CsvColumn.text(
              CurveView.Fields.extrapolatorLeft,
              EXTRAPOLATOR_LEFT_FIELD,
              CurveView::getExtrapolatorLeft),
          CsvColumn.text(
              CurveView.Fields.extrapolatorRight,
              EXTRAPOLATOR_RIGHT_FIELD,
              CurveView::getExtrapolatorRight),
          CsvColumn.integer(
                  CurveView.Fields.numberOfNodes,
                  NUMBER_OF_NODES_FIELDS,
                  CurveView::getNumberOfNodes)
              .optional(),
          CsvColumn.text(CurveView.Fields.minGap, MINIMUM_NODE_GAP_FIELD, CurveView::getMinGap),
          CsvColumn.text(
              CurveView.Fields.clashAction, NODE_CLASH_ACTION_FIELD, CurveView::getClashAction));

  public CurveCsvMapper(List<String> selectedColumns) {
    super(COLUMNS, selectedColumns);
  }

  @Override
  public List<String> header() {
    ImmutableList.Builder<String> builder = ImmutableList.builder();
    builder.addAll(super.header());
    return builder.build();
  }

  @Override
  public CsvRow toCsvRow(CurveView object) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.addAll(super.toCsvFields(object));
    return new CsvRow(builder.build());
  }
}
