package com.solum.xplain.core.ccyexposure;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static io.atlassian.fugue.Either.right;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toUnmodifiableList;

import com.solum.xplain.core.ccyexposure.csv.CashflowCsvMapper;
import com.solum.xplain.core.ccyexposure.csv.CashflowImportService;
import com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvMapper;
import com.solum.xplain.core.ccyexposure.csv.CcyExposureImportService;
import com.solum.xplain.core.ccyexposure.entity.CcyExposure;
import com.solum.xplain.core.ccyexposure.repository.CcyExposureCashflowRepository;
import com.solum.xplain.core.ccyexposure.value.CashflowForm;
import com.solum.xplain.core.ccyexposure.value.CashflowSearchForm;
import com.solum.xplain.core.ccyexposure.value.CashflowView;
import com.solum.xplain.core.ccyexposure.value.CcyExposureCreateForm;
import com.solum.xplain.core.ccyexposure.value.CcyExposureUpdateForm;
import com.solum.xplain.core.ccyexposure.value.CcyExposureView;
import com.solum.xplain.core.ccyexposure.value.CcyExposureWithCashflows;
import com.solum.xplain.core.ccyexposure.value.CombinedCcyExposureView;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.extensions.EitherExtensions;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.experimental.ExtensionMethod;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
@ExtensionMethod(EitherExtensions.class)
public class CcyExposureControllerService {

  private final CcyExposureRepository repository;
  private final CcyExposureCashflowRepository cashflowRepository;
  private final CcyExposureImportService importService;
  private final CashflowImportService cashflowImportService;
  private final CcyExposureCashflowMapper cashflowMapper;

  public Either<ErrorItem, EntityId> create(CcyExposureCreateForm form) {
    return repository.insert(form);
  }

  public Either<ErrorItem, EntityId> update(String entityId, CcyExposureUpdateForm form) {
    return repository.update(entityId, form);
  }

  public Either<ErrorItem, EntityId> archive(String entityId) {
    return repository.archive(entityId);
  }

  public List<CcyExposureView> getCcyExposures(boolean withArchived) {
    return withArchived ? repository.findAllAsViews() : repository.findAllByArchivedIsFalse();
  }

  public CombinedCcyExposureView getCcyExposureCombinedViewById(
      String ccyExposureId, LocalDate stateDate) {
    return getCombinedCcyExposureView(ccyExposureId, stateDate);
  }

  public Either<ErrorItem, CcyExposureView> getCcyExposureById(String ccyExposureId) {
    return repository
        .findAllByIdAndArchivedIsFalse(ccyExposureId)
        .toEitherOrNotFound("Ccy Exposure not found");
  }

  public List<CashflowView> getCashflows(String ccyExposureId, LocalDate stateDate) {

    return cashflowRepository.getActiveItemsByCcyExposureId(ccyExposureId, stateDate).stream()
        .map(cashflowMapper::toView)
        .toList();
  }

  public DateList getFutureVersions(String entityId, LocalDate stateDate) {
    return cashflowRepository.futureVersionsByEntityId(entityId, stateDate);
  }

  public Either<ErrorItem, FileResponseEntity> getCcyExposureListCsv(LocalDate stateDate) {
    var csvFileName = nameWithTimeStamp("CcyExposureList", stateDate);
    var mapper = new CcyExposureCsvMapper();
    var views = repository.findAllByArchivedIsFalse();

    var rows = views.stream().map(mapper::toCsvRow).toList();
    var csvFile = new CsvOutputFile(mapper.header(), rows);
    return right(FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName));
  }

  public Either<ErrorItem, FileResponseEntity> getCashflowListCsv(LocalDate stateDate) {
    var csvFileName = nameWithTimeStamp("CashflowList", stateDate);
    var views = repository.findAllByArchivedIsFalse();

    var csvFile = getCashflowCsv(views, stateDate);
    return right(FileResponseEntity.csvFile(csvFile, csvFileName));
  }

  private ByteArrayResource getCashflowCsv(List<CcyExposureView> views, LocalDate stateDate) {
    var cashflowCsvMapper = new CashflowCsvMapper();
    Stream<CsvRow> rows =
        views.stream()
            .flatMap(
                exposure ->
                    cashflowRepository
                        .getActiveItemsByCcyExposureId(exposure.getId(), stateDate)
                        .stream()
                        .map(
                            cashflow ->
                                cashflowCsvMapper.toCsvRow(
                                    exposure.getName(), cashflowMapper.toView(cashflow))));
    return rows.collect(
            collectingAndThen(toList(), l -> new CsvOutputFile(cashflowCsvMapper.header(), l)))
        .writeToByteArray();
  }

  public Either<ErrorItem, FileResponseEntity> getCashflowListCsv(
      BitemporalDate stateDate, String ccyExposureId) {
    return repository
        .findAllByIdAndArchivedIsFalse(ccyExposureId)
        .toEitherOrNotFound("Ccy Exposure not found")
        .map(
            c ->
                FileResponseEntity.csvFile(
                    getCashflowCsv(List.of(c), stateDate.getActualDate()),
                    nameWithTimeStamp(c.getName(), stateDate)));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadCcyExposures(
      ImportOptions importOptions, byte[] bytes) {
    return importService.importCcyExposures(importOptions, bytes);
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadCashflows(
      ImportOptions importOptions, byte[] bytes) {
    return cashflowImportService.importForAll(importOptions, bytes);
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadCashflow(
      ImportOptions importOptions, byte[] bytes, String ccyExposureId, LocalDate versionDate) {
    return cashflowImportService.importForSingleExposure(
        ccyExposureId, versionDate, importOptions, bytes);
  }

  public List<CcyExposureWithCashflows> ccyExposuresWithCashflows(
      @NotEmpty String exposureCurrency,
      @NotNull LocalDate stateDate,
      @NotNull List<String> exposureIds) {
    return repository.ccyExposures(exposureCurrency, exposureIds).stream()
        .map(ccyExposure -> getCombinedCcyExposureView(ccyExposure, stateDate))
        .collect(toUnmodifiableList());
  }

  public CombinedCcyExposureView getCombinedCcyExposureView(
      CcyExposure ccyExposure, LocalDate stateDate) {
    return cashflowMapper.toCombinedView(
        ccyExposure,
        cashflowRepository.getActiveItemsByCcyExposureId(ccyExposure.getId(), stateDate).stream()
            .toList());
  }

  public CombinedCcyExposureView getCombinedCcyExposureView(
      String ccyExposureId, LocalDate stateDate) {
    return getCombinedCcyExposureView(
        repository.getById(ccyExposureId),
        stateDate); // TODO handle not found at API in JsonExceptionHandler
  }

  public List<CashflowView> getCashflowsVersions(String entityId) {
    return cashflowRepository.findAllVersions(entityId);
  }

  public DateList futureCashflowsVersions(String ccyExposureId, CashflowSearchForm form) {
    return cashflowRepository.futureVersions(ccyExposureId, form);
  }

  public Either<ErrorItem, EntityId> createCashflow(
      String ccyExposureId, @Valid CashflowForm form) {
    return cashflowRepository.create(form);
  }

  public Either<ErrorItem, EntityId> updateCashflow(
      String entityId, LocalDate version, @Valid CashflowForm form) {
    return cashflowRepository.update(entityId, version, form);
  }

  public Either<ErrorItem, EntityId> deleteCashflowVersion(
      String entityId, LocalDate version, ArchiveEntityForm form) {
    return cashflowRepository.archive(entityId, version, form);
  }
}
