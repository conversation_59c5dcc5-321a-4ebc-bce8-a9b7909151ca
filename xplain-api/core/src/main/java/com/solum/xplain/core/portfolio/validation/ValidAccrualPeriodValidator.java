package com.solum.xplain.core.portfolio.validation;

import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidAccrualPeriodValidator
    implements ConstraintValidator<ValidAccrualPeriod, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    if (form != null
        && allNotNull(form.getEndDate(), form.getStartDate())
        && !form.getEndDate().isAfter(form.getStartDate())) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate("ValidAccrualPeriod")
          .addPropertyNode("endDate")
          .addConstraintViolation();

      return false;
    }
    return true;
  }
}
