package com.solum.xplain.core.curvegroup.curvebond.csv.curve;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BondCurveCsvFields {

  static final String NAME_FIELD = "Name";
  static final String INTERPOLATOR_FIELD = "Interpolator";
  static final String EXTRAPOLATOR_LEFT_FIELD = "Extrapolator Left";
  static final String EXTRAPOLATOR_RIGHT_FIELD = "Extrapolator Right";
  static final String NUMBER_OF_NODES_FIELDS = "Number of Nodes";
}
