package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_PORTFOLIO;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_COMPANY_LEGAL_ENTITY;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_PORTFOLIO;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_VALUATION_DATA;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;
import static com.solum.xplain.core.common.versions.BitemporalDate.newOf;
import static com.solum.xplain.core.lock.XplainLock.TRADES_LOCK_ID;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.Sorted;
import com.solum.xplain.core.common.SortedFiltered;
import com.solum.xplain.core.common.csv.DuplicateAction;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm;
import com.solum.xplain.core.portfolio.form.PortfolioUpdateForm;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.portfolio.value.PortfolioCountedView;
import com.solum.xplain.core.portfolio.value.PortfolioFilter;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Validated
@RestController
@RequestMapping("/portfolio")
@AllArgsConstructor
public class PortfolioController {

  private final PortfolioControllerService service;
  private final PortfolioUploadService portfolioUploadService;
  private final PortfolioExportService exportService;

  @Operation(summary = "Get all portfolios")
  @ScrolledFiltered
  @Sorted
  @CommonErrors
  @GetMapping
  @PreAuthorize(AUTHORITY_VIEW_PORTFOLIO)
  public ScrollableEntry<PortfolioCountedView> getAll(
      @RequestParam LocalDate stateDate,
      @SortDefault(
              sort = {
                PortfolioView.Fields.companyName,
                PortfolioView.Fields.entityName,
                PortfolioCondensedView.Fields.externalPortfolioId
              })
          ScrollRequest scrollRequest,
      TableFilter tableFilter,
      PortfolioFilter portfolioFilter) {
    return service.getAll(scrollRequest, tableFilter, portfolioFilter, newOf(stateDate));
  }

  @Operation(summary = "Get all active portfolios")
  @CommonErrors
  @GetMapping("/active")
  @PreAuthorize(AUTHORITY_VIEW_PORTFOLIO)
  public List<PortfolioCondensedView> getAllActivePortfolios() {
    return service.getAllActivePortfolios();
  }

  @Operation(summary = "Get all portfolios csv list")
  @SortedFiltered
  @CommonErrors
  @GetMapping("/portfolios-csv")
  @PreAuthorize(AUTHORITY_VIEW_PORTFOLIO)
  public ResponseEntity<ByteArrayResource> exportPortfolios(
      @SortDefault(
              sort = {
                PortfolioView.Fields.companyName,
                PortfolioView.Fields.entityName,
                PortfolioCondensedView.Fields.externalPortfolioId
              })
          Sort sort,
      TableFilter tableFilter,
      PortfolioFilter portfolioFilter,
      @RequestParam LocalDate stateDate,
      @RequestParam(required = false) List<String> selectedColumns) {
    return eitherErrorItemFileResponse(
        exportService.exportPortfolios(
            sort, tableFilter, portfolioFilter, stateDate, selectedColumns));
  }

  @Operation(summary = "Get portfolios for company legal entity")
  @CommonErrors
  @GetMapping("/entities")
  @PreAuthorize(AUTHORITY_VIEW_COMPANY_LEGAL_ENTITY)
  public ResponseEntity<List<PortfolioCondensedView>> getAllForCompanyLegalEntity(
      @RequestParam("entityId") String entityId, PortfolioFilter portfolioFilter) {
    return ResponseEntity.ok(service.getPortfoliosForCompanyLegalEntity(entityId, portfolioFilter));
  }

  @Operation(summary = "Create new portfolio")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_PORTFOLIO)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<EntityId> newPortfolio(@Valid @RequestBody PortfolioCreateForm newForm) {
    return eitherErrorItemResponse(service.create(newForm));
  }

  @Operation(summary = "Update portfolio")
  @CommonErrors
  @PutMapping("/{id}")
  @PreAuthorize(AUTHORITY_MODIFY_PORTFOLIO)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<EntityId> editPortfolio(
      @PathVariable("id") String id, @Valid @RequestBody PortfolioUpdateForm edit) {
    return eitherErrorItemResponse(service.update(id, edit));
  }

  @Operation(summary = "Get portfolio data")
  @GetMapping("/{id}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_PORTFOLIO)
  public ResponseEntity<PortfolioCountedView> get(
      @RequestParam LocalDate stateDate, @PathVariable("id") String id) {
    return eitherErrorItemResponse(service.get(id, newOf(stateDate)));
  }

  @Operation(summary = "Get portfolio valuation data keys in CSV format")
  @GetMapping(value = "/{id}/valuation-data-keys/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA)
  public ResponseEntity<ByteArrayResource> getPortfolioTradeDataKeysCsv(
      @PathVariable("id") String portfolioId, @RequestParam LocalDate stateDate) {
    return eitherErrorItemFileResponse(
        exportService.exportPortfolioTradeDataKeys(portfolioId, newOf(stateDate)));
  }

  @Operation(summary = "Get all portfolios valuation data keys in CSV format")
  @GetMapping(value = "/valuation-data-keys/csv")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_VALUATION_DATA)
  public ResponseEntity<ByteArrayResource> getAllPortfolioTradeDataKeysCsv(
      @RequestParam LocalDate stateDate) {
    return eitherErrorItemFileResponse(
        exportService.exportAllPortfolioTradeDataKeys(newOf(stateDate)));
  }

  @Operation(summary = "Upload portfolio csv file")
  @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_PORTFOLIO)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<List<EntityId>> uploadPortfolios(
      @RequestPart MultipartFile file,
      @RequestParam(required = false, defaultValue = "STRICT") ParsingMode parsingMode,
      @RequestParam("duplicates") DuplicateAction action)
      throws IOException {
    return eitherErrorItemsResponse(
        portfolioUploadService.uploadPortfolios(parsingMode, action, file.getBytes()));
  }

  @Operation(summary = "Archive portfolio")
  @PutMapping("/{id}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_PORTFOLIO)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<EntityId> archivePortfolio(@PathVariable("id") String portfolioId) {
    return eitherErrorItemResponse(service.archivePortfolio(portfolioId));
  }
}
