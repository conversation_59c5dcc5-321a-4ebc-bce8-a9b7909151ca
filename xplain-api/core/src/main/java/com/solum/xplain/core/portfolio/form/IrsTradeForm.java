package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.portfolio.CoreProductType.IRS;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.RequiredNonFixedLeg;
import com.solum.xplain.core.portfolio.validation.RequiredNonInflationLeg;
import com.solum.xplain.core.portfolio.validation.RequiredSameCurrencies;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import io.atlassian.fugue.Either;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@RequiredSameCurrencies
@RequiredNonInflationLeg
@RequiredNonFixedLeg
public class IrsTradeForm extends SwapTradeForm implements ParsableToTradeValue {

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return createTradeDetails(toTradeInfo()).map(details -> defaultTradeValue(IRS, details));
  }
}
