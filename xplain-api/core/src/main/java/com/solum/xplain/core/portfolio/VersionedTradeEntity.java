package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails;
import com.solum.xplain.core.portfolio.trade.CustomTradeField;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import com.solum.xplain.core.portfolio.trade.OnboardingDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import jakarta.annotation.Nullable;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class VersionedTradeEntity extends DateRangeVersionedEntity {
  private String externalTradeId;
  private String description;
  private ProductType productType;
  private TradeDetails tradeDetails;
  private AllocationTradeDetails allocationTradeDetails;
  private ClientMetrics clientMetrics;
  private OnboardingDetails onboardingDetails;
  private List<ExternalIdentifier> externalIdentifiers;
  @Nullable private List<CustomTradeField> customFields;
}
