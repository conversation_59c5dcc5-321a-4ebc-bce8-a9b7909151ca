package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.common.versions.embedded.convert.EmbeddedVersionEntityToViewConverter;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import java.util.List;

public interface EnhanceableTradeConverterProvider {

  EmbeddedVersionEntityToViewConverter<TradeValue, PortfolioItemEntity, PortfolioItem>
      provideConverter(
          List<TradeValue> tradeValueList, List<PortfolioItemEntity> portfolioItemEntities);
}
