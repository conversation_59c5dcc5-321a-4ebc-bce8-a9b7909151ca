package com.solum.xplain.core.portfolio.value.item;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewIgnore;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import lombok.Data;

@Data
public class TradeInfoDetailsView {
  @ConfigurableViewQuery(sortable = true)
  private String counterParty;

  @ConfigurableViewQuery(sortable = true)
  private String counterPartyType;

  @ConfigurableViewQuery(sortable = true)
  private LocalDate tradeDate;

  @ConfigurableViewIgnore private LocalTime tradeTime;
  @ConfigurableViewIgnore private ZoneId zoneId;

  @ConfigurableViewQuery(sortable = true)
  private LocalDate settlementDate;

  @ConfigurableViewQuery(sortable = true)
  private String csaDiscountingGroup;

  @ConfigurableViewQuery(sortable = true)
  private String tradeCurrency;
}
