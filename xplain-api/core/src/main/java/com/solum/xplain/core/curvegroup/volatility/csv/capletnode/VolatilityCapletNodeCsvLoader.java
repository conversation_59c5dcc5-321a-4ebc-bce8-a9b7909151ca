package com.solum.xplain.core.curvegroup.volatility.csv.capletnode;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.getFieldValue;
import static io.atlassian.fugue.extensions.step.Steps.begin;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.csv.ItemKey;
import com.solum.xplain.core.common.csv.NodesCsvLoader;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeForm;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class VolatilityCapletNodeCsvLoader
    extends NodesCsvLoader<VolatilitySurface, CapletVolatilityNodeForm> {
  protected static final String CURVE_NAME = "Curve Name";
  protected static final String MATURITY_FIELD = "Maturity";
  protected static final String STRIKE_FIELD = "Strike";

  @Override
  protected List<String> getCsvFileHeaders() {
    return List.of(CURVE_NAME, MATURITY_FIELD, STRIKE_FIELD);
  }

  @Override
  protected String getCurveName(VolatilitySurface surface) {
    return surface.getName();
  }

  @Override
  protected ItemKey getNodeKey(CapletVolatilityNodeForm form) {
    return TenorStrikeKey.from(form);
  }

  @Override
  protected Either<ErrorItem, CapletVolatilityNodeForm> parse(@NonNull CsvRow row) {
    return begin(getFieldValue(row, STRIKE_FIELD, CsvLoaderUtils::parseBigDecimal))
        .then(() -> getPeriodFieldValue(row, MATURITY_FIELD))
        .yield(this::nodeForm);
  }

  private CapletVolatilityNodeForm nodeForm(BigDecimal strike, String tenor) {
    CapletVolatilityNodeForm f = new CapletVolatilityNodeForm();
    f.setStrike(strike);
    f.setTenor(tenor);
    return f;
  }
}
