package com.solum.xplain.core.portfolio.trade.type;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.lock.XplainLock.TRADES_LOCK_ID;

import com.solum.xplain.core.authentication.Authorities;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.IrsTradeForm;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.IrsTradeGroup;
import com.solum.xplain.core.portfolio.value.IrsTradeView;
import io.atlassian.fugue.Either;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.groups.Default;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/irs")
@AllArgsConstructor
public class IrsController implements BespokeTradeTypedController<IrsTradeForm, IrsTradeView> {
  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, IrsTradeView> toViewFunction(PortfolioItem e) {
    return IrsTradeView.of(e);
  }

  @Operation(summary = "Create trade")
  @CommonErrors
  @PostMapping
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_TRADE)
  @Override
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<EntityId> insert(
      @PathVariable("id") String portfolioId,
      @Validated(value = {IrsTradeGroup.class, BespokeTradeGroup.class, Default.class}) @RequestBody
          IrsTradeForm form) {
    return eitherErrorItemResponse(getService().insert(portfolioId, form));
  }

  @Operation(summary = "Update trade")
  @CommonErrors
  @Override
  @PutMapping("{tradeEntityId}/{version}")
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_TRADE)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<EntityId> update(
      @PathVariable("id") String portfolioId,
      @PathVariable("tradeEntityId") String tradeEntityId,
      @PathVariable("version") LocalDate version,
      @Validated(value = {IrsTradeGroup.class, BespokeTradeGroup.class, Default.class}) @RequestBody
          IrsTradeForm form) {

    return eitherErrorItemResponse(getService().update(portfolioId, version, form, tradeEntityId));
  }
}
