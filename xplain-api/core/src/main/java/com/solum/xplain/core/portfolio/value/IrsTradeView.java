package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.CoreProductType.IRS;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import io.atlassian.fugue.Either;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class IrsTradeView extends SwapTradeView {

  public static Either<ErrorItem, IrsTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == IRS) {
      var view = new IrsTradeView();
      view.withTradeDetails(item);
      return Either.right(view);

    } else {
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Product type is not " + "expected " + item.getProductType()));
    }
  }
}
