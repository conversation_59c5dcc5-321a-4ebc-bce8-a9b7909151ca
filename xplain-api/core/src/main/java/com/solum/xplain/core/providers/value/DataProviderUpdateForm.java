package com.solum.xplain.core.providers.value;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidCollectionStringSet;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import lombok.Data;

@Data
public class DataProviderUpdateForm {

  @NotEmpty private String name;

  @NotEmpty
  @ValidCollectionStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = "marketDataProviderType")
  private List<String> types;
}
