package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.portfolio.CoreProductType.FXFWD;
import static com.solum.xplain.core.portfolio.value.FxLongShort.LONG;
import static org.slf4j.LoggerFactory.getLogger;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableFxForwardDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.RequiredDifferentAmountSigns;
import com.solum.xplain.core.portfolio.validation.RequiredDifferentCurrencies;
import com.solum.xplain.core.portfolio.validation.RequiredDifferentFxLegIdentifiers;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.core.portfolio.value.FxLongShort;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@RequiredDifferentCurrencies
@RequiredDifferentAmountSigns
@RequiredDifferentFxLegIdentifiers
public class FxForwardTradeForm extends CommonFxTradeForm
    implements ParsableToTradeValue, SingleExchangeFxTradeForm {

  private static final Logger LOG = getLogger(FxForwardTradeForm.class);

  @NotNull(groups = ReferenceTradeGroup.class)
  @Null(groups = BespokeTradeGroup.class)
  @Schema(description = "Long/Short position. Only required for SecMaster trades.")
  private FxLongShort fxLongShort;

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return fxForwardTradeDetails(toTradeInfo()).map(details -> defaultTradeValue(FXFWD, details));
  }

  protected Either<ErrorItem, TradeDetails> fxForwardTradeDetails(TradeInfoDetails tradeInfo) {
    try {
      if (getFxRate() != null) {
        // security master
        var baseCurrency = Currency.of(getDomesticCurrencyAmount().getCurrency());
        var counterCurrency = Currency.of(getForeignCurrencyAmount().getCurrency());
        var payCcy = fxLongShort == LONG ? counterCurrency : baseCurrency;
        var recCcy = CurrencyPair.of(baseCurrency, counterCurrency).other(payCcy);
        return Either.right(
            ResolvableFxForwardDetails.builder()
                .businessDayConvention(getBusinessDayConvention())
                .paymentDate(getPaymentDate())
                .payCurrency(payCcy)
                .payLegExtIdentifier(StringUtils.upperCase(getPayLegExtIdentifier()))
                .receiveCurrency(recCcy)
                .receiveLegExtIdentifier(StringUtils.upperCase(getReceiveLegExtIdentifier()))
                .fxRate(getFxRate())
                .build()
                .toTradeDetails(tradeInfo));
      }
      return Steps.begin(payCurrencyAmount())
          .then(this::receiveCurrencyAmount)
          .yield(
              (pay, receive) ->
                  ResolvableFxForwardDetails.builder()
                      .businessDayConvention(getBusinessDayConvention())
                      .paymentDate(getPaymentDate())
                      .payCurrencyAmount(pay.getAmount())
                      .payCurrency(Currency.of(pay.getCurrency()))
                      .payLegExtIdentifier(StringUtils.upperCase(getPayLegExtIdentifier()))
                      .receiveCurrencyAmount(receive.getAmount())
                      .receiveCurrency(Currency.of(receive.getCurrency()))
                      .receiveLegExtIdentifier(StringUtils.upperCase(getReceiveLegExtIdentifier()))
                      .build()
                      .toTradeDetails(tradeInfo));
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return Either.left(new ErrorItem(Error.CONVERSION_ERROR, ex.getMessage()));
    }
  }
}
