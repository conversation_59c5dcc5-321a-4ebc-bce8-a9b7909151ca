package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.CompoundingMethodSupplier;
import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.DayCountsSupplier;
import com.solum.xplain.core.common.validation.IborIndicesSupplier;
import com.solum.xplain.core.common.validation.OvernightIndicesSupplier;
import com.solum.xplain.core.common.validation.PayReceiveSupplier;
import com.solum.xplain.core.common.validation.PriceIndicesSupplier;
import com.solum.xplain.core.common.validation.SwapLegFrequencySupplier;
import com.solum.xplain.core.common.validation.UpperCase;
import com.solum.xplain.core.common.validation.ValidPeriod;
import com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeLegDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.portfolio.validation.RequiredSwapLegCalculation;
import com.solum.xplain.core.portfolio.validation.SwaptionTradeIndicesSupplier;
import com.solum.xplain.core.portfolio.validation.ValidCompoundingMethod;
import com.solum.xplain.core.portfolio.validation.ValidIborLegAccrualFrequency;
import com.solum.xplain.core.portfolio.validation.ValidOffshoreSwapLeg;
import com.solum.xplain.core.portfolio.validation.groups.IrsTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.SwaptionTradeGroup;
import com.solum.xplain.core.portfolio.value.CalculationType;
import io.atlassian.fugue.Either;
import jakarta.validation.constraints.NegativeOrZero;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.BooleanUtils;

@Data
@RequiredSwapLegCalculation
@FieldNameConstants
@ValidOffshoreSwapLeg
@ValidCompoundingMethod
@ValidIborLegAccrualFrequency
public class SwapLegForm {

  @UpperCase @ValidIdentifier private String extLegIdentifier;

  @NotNull
  @ValidStringSet(PayReceiveSupplier.class)
  private String payReceive;

  @NotNull(groups = {IrsTradeGroup.class, SwaptionTradeGroup.class})
  @NotEmpty
  @ValidStringSet(SwapLegFrequencySupplier.class)
  private String accrualFrequency;

  @NotNull(groups = {IrsTradeGroup.class, SwaptionTradeGroup.class})
  @NotEmpty
  @ValidStringSet(SwapLegFrequencySupplier.class)
  private String paymentFrequency;

  @NotNull(groups = {IrsTradeGroup.class, SwaptionTradeGroup.class})
  private Integer paymentOffsetDays;

  @ValidStringSet(CompoundingMethodSupplier.class)
  private String compoundingMethod;

  @NotEmpty
  @ValidStringSet(CurrenciesSupplier.class)
  private String notionalCurrency;

  @NotNull
  @Positive(groups = {SwaptionTradeGroup.class, IrsTradeGroup.class})
  private Double notionalValue;

  @NotNull private CalculationType calculationType;

  @ValidStringSet(DayCountsSupplier.class)
  private String calculationFixedDayCount;

  private Double calculationFixedRateInitialValue;
  private String calculationFixedAccrualMethod;

  @ValidStringSet(DayCountsSupplier.class)
  private String calculationIborDayCount;

  @ValidStringSet(value = IborIndicesSupplier.class)
  @ValidStringSet(value = SwaptionTradeIndicesSupplier.class, groups = SwaptionTradeGroup.class)
  private String calculationIborIndex;

  @NegativeOrZero private Integer calculationIborFixingDateOffsetDays;
  private Double calculationIborSpreadInitialValue;

  @ValidStringSet(PriceIndicesSupplier.class)
  private String calculationInflationIndex;

  @ValidPeriod(style = PeriodStyle.YM)
  private String calculationInflationLag;

  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = "priceIndexCalculationMethod")
  private String indexCalculationMethod;

  @ValidStringSet(DayCountsSupplier.class)
  private String calculationOvernightDayCount;

  @ValidStringSet(OvernightIndicesSupplier.class)
  private String calculationOvernightIndex;

  private Boolean isOffshore;

  private Integer calculationOvernightRateCutOffDays;
  private String calculationOvernightAccrualMethod;
  private Double calculationOvernightSpreadInitialValue;

  public static SwapLegForm fromSwapLeg(TradeLegDetails legDetails) {
    var form = new SwapLegForm();
    form.setExtLegIdentifier(legDetails.getExtLegIdentifier());
    form.setPayReceive(legDetails.getPayReceive().getName());
    form.setAccrualFrequency(legDetails.getAccrualFrequency());
    form.setPaymentFrequency(legDetails.getPaymentFrequency());
    form.setPaymentOffsetDays(legDetails.getPaymentOffsetDays());
    form.setCompoundingMethod(legDetails.getPaymentCompounding());
    form.setNotionalCurrency(legDetails.getCurrency());
    form.setNotionalValue(legDetails.getNotional());
    form.setIsOffshore(BooleanUtils.isTrue(legDetails.getIsOffshore()));
    CalculationType type = legDetails.getType();
    if (type == CalculationType.FIXED) {
      convertFixed(legDetails, form);
    } else if (type == CalculationType.INFLATION) {
      convertInflation(legDetails, form);
    } else if (type == CalculationType.OVERNIGHT) {
      convertOvernight(legDetails, form);
    } else if (type == CalculationType.IBOR) {
      convertIbor(legDetails, form);
    }
    return form;
  }

  static void convertFixed(TradeLegDetails legDetails, SwapLegForm form) {
    form.setCalculationType(CalculationType.FIXED);
    form.setCalculationFixedDayCount(legDetails.getDayCount());
    form.setCalculationFixedRateInitialValue(legDetails.getInitialValue());
    form.setCalculationFixedAccrualMethod(legDetails.getAccrualMethod());
  }

  static void convertInflation(TradeLegDetails legDetails, SwapLegForm form) {
    form.setCalculationType(CalculationType.INFLATION);
    form.setCalculationInflationIndex(legDetails.getIndex());
    form.setCalculationInflationLag(legDetails.getInflationLag());
    form.setIndexCalculationMethod(legDetails.getIndexCalculationMethod());
  }

  static void convertOvernight(TradeLegDetails legDetails, SwapLegForm form) {
    form.setCalculationType(CalculationType.OVERNIGHT);
    form.setCalculationOvernightDayCount(legDetails.getDayCount());
    form.setCalculationOvernightIndex(legDetails.getIndex());
    form.setCalculationOvernightSpreadInitialValue(legDetails.getInitialValue());
    form.setCalculationOvernightAccrualMethod(legDetails.getAccrualMethod());
    form.setCalculationOvernightRateCutOffDays(legDetails.getOvernightRateCutOffDays());
  }

  static void convertIbor(TradeLegDetails legDetails, SwapLegForm form) {
    form.setCalculationType(CalculationType.IBOR);
    form.setCalculationIborDayCount(legDetails.getDayCount());
    form.setCalculationIborIndex(legDetails.getIndex());
    form.setCalculationIborFixingDateOffsetDays(legDetails.getFixingDateOffsetDays());
    form.setCalculationIborSpreadInitialValue(legDetails.getInitialValue());
  }

  public Either<ErrorItem, ResolvableTradeLegDetails> resolvableDetails() {
    try {
      return Either.right(SwapLegFormDecorator.newOf(this).resolvableLegDetails());
    } catch (Exception e) {
      return Either.left(new ErrorItem(Error.UNEXPECTED_TYPE, e.getMessage()));
    }
  }
}
