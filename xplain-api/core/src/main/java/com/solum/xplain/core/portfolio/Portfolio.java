package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.common.diff.Diffable;
import com.solum.xplain.core.common.diff.VersionDiffs;
import com.solum.xplain.core.common.team.WithTeams;
import com.solum.xplain.core.company.entity.CompanyLegalEntityReference;
import com.solum.xplain.core.company.entity.CompanyReference;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collation = "en", collection = Portfolio.PORTFOLIO_COLLECTION)
@ToString
@EqualsAndHashCode(exclude = {"createdAt", "lastModifiedAt"})
@FieldNameConstants
public class Portfolio implements WithTeams, Diffable<Portfolio> {
  public static final String PORTFOLIO_COLLECTION = "portfolio";

  @Id private String id;

  @CreatedBy private AuditUser createdBy;

  @LastModifiedBy private AuditUser lastModifiedBy;

  @CreatedDate private LocalDateTime createdAt;

  @LastModifiedDate private LocalDateTime lastModifiedAt;

  private LocalDateTime calculatedAt;
  private AuditUser calculatedBy;
  private LocalDate valuationDate;

  private Boolean allowAllTeams;
  private List<ObjectId> teamIds;

  private String description;

  private CompanyReference company;

  private CompanyLegalEntityReference entity;

  private String externalPortfolioId;

  private String name;
  private boolean archived;

  private List<AuditLog> auditLogs;

  public void addAuditLog(AuditLog log) {
    if (auditLogs == null) {
      auditLogs = new ArrayList<>();
    }
    auditLogs.add(log);
  }

  @Override
  public List<ObjectId> getTeamIds() {
    return teamIds;
  }

  public void setTeamIds(List<ObjectId> teamIds) {
    this.teamIds = teamIds;
  }

  @Override
  public Boolean getAllowAllTeams() {
    return allowAllTeams;
  }

  public void setAllowAllTeams(Boolean allowAllTeams) {
    this.allowAllTeams = allowAllTeams;
  }

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public AuditUser getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(AuditUser createdBy) {
    this.createdBy = createdBy;
  }

  public AuditUser getLastModifiedBy() {
    return lastModifiedBy;
  }

  public void setLastModifiedBy(AuditUser lastModifiedBy) {
    this.lastModifiedBy = lastModifiedBy;
  }

  public LocalDateTime getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(LocalDateTime createdAt) {
    this.createdAt = createdAt;
  }

  public LocalDateTime getLastModifiedAt() {
    return lastModifiedAt;
  }

  public void setLastModifiedAt(LocalDateTime lastModifiedAt) {
    this.lastModifiedAt = lastModifiedAt;
  }

  public LocalDateTime getCalculatedAt() {
    return calculatedAt;
  }

  public void setCalculatedAt(LocalDateTime calculatedAt) {
    this.calculatedAt = calculatedAt;
  }

  public AuditUser getCalculatedBy() {
    return calculatedBy;
  }

  public void setCalculatedBy(AuditUser calculatedBy) {
    this.calculatedBy = calculatedBy;
  }

  public LocalDate getValuationDate() {
    return valuationDate;
  }

  public void setValuationDate(LocalDate valuationDate) {
    this.valuationDate = valuationDate;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public boolean isArchived() {
    return archived;
  }

  public void setArchived(boolean archived) {
    this.archived = archived;
  }

  public CompanyLegalEntityReference getEntity() {
    return entity;
  }

  public void setEntity(CompanyLegalEntityReference entity) {
    this.entity = entity;
  }

  public CompanyReference getCompany() {
    return company;
  }

  public void setCompany(CompanyReference company) {
    this.company = company;
  }

  public String getExternalPortfolioId() {
    return externalPortfolioId;
  }

  public void setExternalPortfolioId(String externalPortfolioId) {
    this.externalPortfolioId = externalPortfolioId;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public List<AuditLog> getAuditLogs() {
    return auditLogs;
  }

  public void setAuditLogs(List<AuditLog> auditLogs) {
    this.auditLogs = auditLogs;
  }

  @Override
  public VersionDiffs diff(Portfolio obj) {
    return VersionDiffs.of(
        new DiffBuilder<>(this, obj, ToStringStyle.DEFAULT_STYLE)
            .append(Fields.name, this.name, obj.name)
            .append(Fields.archived, this.archived, obj.archived)
            .append(Fields.company, this.company, obj.company)
            .append(Fields.description, this.description, obj.description)
            .append(Fields.entity, this.entity, obj.entity)
            .append(Fields.externalPortfolioId, this.externalPortfolioId, obj.externalPortfolioId)
            .append(Fields.teamIds, this.teamIds, obj.teamIds)
            .build());
  }
}
