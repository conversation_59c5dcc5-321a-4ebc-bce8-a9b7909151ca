package com.solum.xplain.core.curvegroup.curve.dto;

import com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirementsForm;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor(staticName = "newOf")
public class GetCalibratedCurvesRequest {

  @NotNull private LocalDate stateDate;
  private Boolean withArchived = Boolean.FALSE;

  @NotNull private CalculationDiscountingType discountingType;
  @NotNull private CalculationStrippingType calibrationStrippingType;

  @NotEmpty private String marketDataGroupId;

  @NotNull private MarketDataSourceType marketDataSource;

  @NotNull private LocalDate curveDate;

  @NotNull private LocalDate valuationDate;
  private InstrumentPriceRequirementsForm priceRequirements;
}
