package com.solum.xplain.core.curvegroup.curvebond;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvImportService;
import com.solum.xplain.core.curvegroup.curvebond.csv.node.BondCurveNodeCsvImportService;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@AllArgsConstructor
@Service
public class BondCurveImportService {

  private final CurveGroupRepository curveGroupRepository;
  private final BondCurveCsvImportService curveCsvImportService;
  private final BondCurveNodeCsvImportService nodeCsvImportService;

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadCurves(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return groupEither(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(g -> curveCsvImportService.importCurves(groupId, importOptions, bytes));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadNodes(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return groupEither(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(g -> nodeCsvImportService.importForAll(groupId, importOptions, bytes));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadNodesForCurve(
      String groupId,
      String curveId,
      LocalDate versionDate,
      ImportOptions importOptions,
      byte[] bytes) {
    return groupEither(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(
            g ->
                nodeCsvImportService.importForCurve(
                    groupId, curveId, versionDate, importOptions, bytes));
  }

  private Either<ErrorItem, CurveGroupView> groupEither(String groupId) {
    return curveGroupRepository.getEither(groupId);
  }
}
