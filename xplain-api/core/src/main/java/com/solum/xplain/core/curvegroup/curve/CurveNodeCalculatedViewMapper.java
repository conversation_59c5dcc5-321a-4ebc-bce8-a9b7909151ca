package com.solum.xplain.core.curvegroup.curve;

import static com.opengamma.strata.market.curve.CurveNodeDateOrder.DEFAULT;
import static com.solum.xplain.core.classifiers.CurveNodeTypes.IBOR_FUTURE_NODE;
import static com.solum.xplain.core.curvegroup.utils.CalibrationValueUtils.calibrationValues;
import static com.solum.xplain.core.market.SemanticMarketDataKeyBuilder.semanticMarketDataKey;
import static com.solum.xplain.extensions.calendar.ValuationDateReferenceData.wrap;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.opengamma.strata.basics.ReferenceData;
import com.solum.xplain.core.common.value.CalculatedValueAtDate;
import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult;
import com.solum.xplain.core.curvegroup.curve.entity.CurveNode;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curve.value.IborFutureCurveNodeCalculatedView;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

@Mapper
public abstract class CurveNodeCalculatedViewMapper {
  @Autowired private ReferenceData referenceData;

  @Mapping(target = "quoteId", ignore = true)
  @Mapping(target = "dataSource", ignore = true)
  @Mapping(target = "ticker", ignore = true)
  @Mapping(target = "marketValue", ignore = true)
  @Mapping(target = "marketValueAsk", ignore = true)
  @Mapping(target = "marketValueMid", ignore = true)
  @Mapping(target = "marketValueBid", ignore = true)
  @Mapping(target = "calibrationPriceType", ignore = true)
  @Mapping(target = "rateValue", ignore = true)
  @Mapping(target = "discountFactor", ignore = true)
  @Mapping(target = "date", ignore = true)
  @Mapping(target = "clearingHouse", ignore = true)
  @Mapping(target = "key", ignore = true)
  @Mapping(target = "tenor", source = "node.period")
  public abstract CurveNodeCalculatedView toView(CurveNode node);

  @Mapping(target = "quoteId", ignore = true)
  @Mapping(target = "dataSource", ignore = true)
  @Mapping(target = "ticker", ignore = true)
  @Mapping(target = "marketValue", ignore = true)
  @Mapping(target = "marketValueAsk", ignore = true)
  @Mapping(target = "marketValueMid", ignore = true)
  @Mapping(target = "marketValueBid", ignore = true)
  @Mapping(target = "calibrationPriceType", ignore = true)
  @Mapping(target = "rateValue", ignore = true)
  @Mapping(target = "discountFactor", ignore = true)
  @Mapping(target = "date", ignore = true)
  @Mapping(target = "clearingHouse", ignore = true)
  @Mapping(target = "key", ignore = true)
  @Mapping(target = "tenor", source = "node.period")
  public abstract IborFutureCurveNodeCalculatedView toView(CurveNode node, LocalDate futureEndDate);

  List<CurveNodeCalculatedView> toNodeViews(
      @NonNull Curve curve,
      @NonNull Map<String, CalculationMarketValueFullView> quotes,
      @NonNull LocalDate valuationDate,
      @Nullable CurveCalibrationResult calibrationResults,
      ReferenceData refData) {
    var calibrationNodeValues =
        calibrationResults == null
            ? List.<CalculatedValueAtDate>of()
            : calibrationResults.getNodeValuesOrEmpty();
    var discountFactorPoints =
        calibrationResults == null
            ? List.<CalculatedValueAtDate>of()
            : calibrationResults.getDiscountFactorPointsOrEmpty();
    var calibratedNodes =
        calibrationResults == null
            ? List.<String>of()
            : calibrationResults.getNodesUsedInCalibrationOrEmpty();
    var isCurveDiscountCurve =
        Optional.ofNullable(calibrationResults).stream()
            .anyMatch(r -> isNotEmpty(r.getDiscountCurrency()));
    var calibrationPriceType =
        Optional.ofNullable(calibrationResults)
            .map(CurveCalibrationResult::getPriceRequirements)
            .map(r -> isCurveDiscountCurve ? r.getDscCurvesPriceType() : r.getCurvesPriceType());

    var nodeValues = calibrationValues(calibrationNodeValues);
    var factors = calibrationValues(discountFactorPoints);
    var isOffshore = curve.isOffshoreCurve();
    var clearingHouse = curve.clearingHouse();
    return curve.orderedNodes(valuationDate, refData).stream()
        .map(
            node -> {
              var date = node.date(isOffshore, clearingHouse, valuationDate, DEFAULT, refData);
              var view = toCalculatedView(node, isOffshore, clearingHouse, valuationDate, refData);
              view.setDate(date);
              view.setClearingHouse(clearingHouse);
              view.setKey(semanticMarketDataKey(node, clearingHouse));
              var quote = quotes.get(view.getKey());
              if (quote != null) {
                view.setQuoteId(quote.getId());
                view.setMarketValue(quote.getValue());
                view.setMarketValueAsk(quote.getAskValue());
                view.setMarketValueMid(quote.getMidValue());
                view.setMarketValueBid(quote.getBidValue());
                view.setDataSource(quote.getProvider());
                view.setTicker(quote.getTicker());
              }

              if (calibratedNodes.contains(node.getInstrument())) {
                calibrationPriceType.ifPresent(view::setCalibrationPriceType);
                ofNullable(nodeValues.get(date))
                    .map(CalculatedValueAtDate::getCalculatedValue)
                    .ifPresent(view::setRateValue);
                ofNullable(factors.get(date))
                    .map(CalculatedValueAtDate::getCalculatedValue)
                    .ifPresent(view::setDiscountFactor);
              }
              return view;
            })
        .toList();
  }

  private CurveNodeCalculatedView toCalculatedView(
      CurveNode curveNode,
      boolean isOffshoreCurve,
      ClearingHouse clearingHouse,
      LocalDate valuationDate,
      ReferenceData referenceData) {
    if (IBOR_FUTURE_NODE.equalsIgnoreCase(curveNode.getType())) {
      var futureEndDate =
          curveNode.iborFutureFutureEndDate(
              isOffshoreCurve, clearingHouse, valuationDate, DEFAULT, referenceData);
      return toView(curveNode, futureEndDate);
    }
    return toView(curveNode);
  }

  List<CurveNodeCalculatedView> toNodeViews(
      @NonNull Curve curve,
      @NonNull Map<String, CalculationMarketValueFullView> quotes,
      @NonNull LocalDate valuationDate,
      @Nullable CurveCalibrationResult calibrationResults) {
    var refData = wrap(this.referenceData, valuationDate);
    return toNodeViews(curve, quotes, valuationDate, calibrationResults, refData);
  }
}
