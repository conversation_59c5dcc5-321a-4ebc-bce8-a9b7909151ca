package com.solum.xplain.core.portfolio.trade;

import com.solum.xplain.core.portfolio.value.item.OptionTradeDetailsView;
import com.solum.xplain.core.portfolio.value.item.TradeDetailsView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface TradeMapper {
  @Mapping(target = "customTradeDetails", source = "customTradeDetails")
  TradeDetailsView tradeDetailsToTradeDetailsView(TradeDetails tradeDetails);

  @Mapping(target = "optionStyle", constant = "EUROPEAN")
  OptionTradeDetailsView toView(OptionTradeDetails details);
}
