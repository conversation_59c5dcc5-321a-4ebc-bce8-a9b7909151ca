package com.solum.xplain.core.portfolio;

import lombok.Data;

@Data(staticConstructor = "newOf")
public class PortfolioItemUniqueKey {
  private final String portfolioId;
  private final String externalTradeId;

  public static PortfolioItemUniqueKey newOf(PortfolioItem item) {
    return newOf(item.getPortfolioId().toHexString(), item.getExternalTradeId());
  }

  public String uniqueId() {
    return portfolioId + externalTradeId;
  }
}
