package com.solum.xplain.core.providers.csv;

import static com.solum.xplain.core.providers.csv.DataProviderCsvLoader.EXTERNAL_ID_FIELD;
import static com.solum.xplain.core.providers.csv.DataProviderCsvLoader.NAME_FIELD;
import static com.solum.xplain.core.providers.csv.DataProviderCsvLoader.TYPE_FIELD;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.providers.DataProvider;
import com.solum.xplain.core.providers.value.DataProviderView;
import java.util.List;

public class DataProviderCsvMapper extends CsvMapper<DataProviderView> {

  public DataProviderCsvMapper(List<String> selectedColumns) {
    super(
        List.of(
            CsvColumn.text(DataProvider.Fields.name, NAME_FIELD, DataProviderView::getName),
            CsvColumn.text(
                DataProvider.Fields.externalId, EXTERNAL_ID_FIELD, DataProviderView::getExternalId),
            CsvColumn.text(
                DataProvider.Fields.types, TYPE_FIELD, i -> String.join("|", i.getTypes()))),
        selectedColumns);
  }
}
