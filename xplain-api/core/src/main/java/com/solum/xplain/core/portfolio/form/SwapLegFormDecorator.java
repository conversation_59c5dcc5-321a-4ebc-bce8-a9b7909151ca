package com.solum.xplain.core.portfolio.form;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.product.common.PayReceive;
import com.opengamma.strata.product.swap.FixedAccrualMethod;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.solum.xplain.core.portfolio.builder.ResolvableFixedLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableIborLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableInflationLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableOvernightLeg;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeLegDetails;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;

@ToString
@EqualsAndHashCode
@AllArgsConstructor(staticName = "newOf")
public class SwapLegFormDecorator {
  private final SwapLegForm legForm;

  public ResolvableTradeLegDetails resolvableLegDetails() {
    return switch (legForm.getCalculationType()) {
      case FIXED -> fixedLeg();
      case OVERNIGHT -> overnightLeg();
      case IBOR -> iborLeg();
      case INFLATION -> inflationLeg();
    };
  }

  private ResolvableFixedLeg fixedLeg() {
    var fixedAccrualMethod =
        legForm.getCalculationFixedAccrualMethod() == null
            ? null
            : FixedAccrualMethod.of(legForm.getCalculationFixedAccrualMethod());

    var fixedAccrualFrequency = legForm.getAccrualFrequency() == null ? null : accrualFreq();

    var fixedPaymentFrequency = legForm.getPaymentFrequency() == null ? null : paymentFreq();

    return ResolvableFixedLeg.builder()
        .extLegIdentifier(legForm.getExtLegIdentifier())
        .dayCount(DayCount.of(legForm.getCalculationFixedDayCount()))
        .initialValue(legForm.getCalculationFixedRateInitialValue())
        .notional(legForm.getNotionalValue())
        .currency(notionalCurrency())
        .paymentCompounding(legForm.getCompoundingMethod())
        .accrualFrequency(fixedAccrualFrequency)
        .accrualMethod(fixedAccrualMethod)
        .payReceive(PayReceive.of(legForm.getPayReceive()))
        .paymentFrequency(fixedPaymentFrequency)
        .paymentOffsetDays(legForm.getPaymentOffsetDays())
        .build();
  }

  private ResolvableOvernightLeg overnightLeg() {
    var overnightAccrual =
        OvernightAccrualMethod.of(legForm.getCalculationOvernightAccrualMethod());
    return ResolvableOvernightLeg.builder()
        .extLegIdentifier(legForm.getExtLegIdentifier())
        .index(legForm.getCalculationOvernightIndex())
        .initialValue(legForm.getCalculationOvernightSpreadInitialValue())
        .dayCount(DayCount.of(legForm.getCalculationOvernightDayCount()))
        .notional(legForm.getNotionalValue())
        .currency(notionalCurrency())
        .paymentCompounding(legForm.getCompoundingMethod())
        .accrualFrequency(accrualFreq())
        .payReceive(PayReceive.of(legForm.getPayReceive()))
        .paymentFrequency(paymentFreq())
        .paymentOffsetDays(legForm.getPaymentOffsetDays())
        .rateCutOffDays(legForm.getCalculationOvernightRateCutOffDays())
        .isOffshore(BooleanUtils.isTrue(legForm.getIsOffshore()))
        .overnightAccrualMethod(overnightAccrual)
        .build();
  }

  private ResolvableIborLeg iborLeg() {

    var iborAccrualFrequency = legForm.getAccrualFrequency() == null ? null : accrualFreq();

    var iborPaymentFrequency = legForm.getPaymentFrequency() == null ? null : paymentFreq();
    return ResolvableIborLeg.builder()
        .extLegIdentifier(legForm.getExtLegIdentifier())
        .index(legForm.getCalculationIborIndex())
        .initialValue(legForm.getCalculationIborSpreadInitialValue())
        .dayCount(DayCount.of(legForm.getCalculationIborDayCount()))
        .fixingDateOffset(legForm.getCalculationIborFixingDateOffsetDays())
        .notional(legForm.getNotionalValue())
        .currency(notionalCurrency())
        .paymentCompounding(legForm.getCompoundingMethod())
        .accrualFrequency(iborAccrualFrequency)
        .payReceive(PayReceive.of(legForm.getPayReceive()))
        .paymentFrequency(iborPaymentFrequency)
        .paymentOffsetDays(legForm.getPaymentOffsetDays())
        .isOffshore(BooleanUtils.isTrue(legForm.getIsOffshore()))
        .build();
  }

  private ResolvableInflationLeg inflationLeg() {
    return ResolvableInflationLeg.builder()
        .extLegIdentifier(legForm.getExtLegIdentifier())
        .index(legForm.getCalculationInflationIndex())
        .inflationLag(legForm.getCalculationInflationLag())
        .indexCalculationMethod(legForm.getIndexCalculationMethod())
        .notional(legForm.getNotionalValue())
        .currency(notionalCurrency())
        .paymentCompounding(legForm.getCompoundingMethod())
        .accrualFrequency(accrualFreq())
        .payReceive(PayReceive.of(legForm.getPayReceive()))
        .paymentFrequency(paymentFreq())
        .paymentOffsetDays(legForm.getPaymentOffsetDays())
        .build();
  }

  private Currency notionalCurrency() {
    return Currency.of(legForm.getNotionalCurrency());
  }

  private Frequency accrualFreq() {
    return Frequency.parse(legForm.getAccrualFrequency());
  }

  private Frequency paymentFreq() {
    return Frequency.parse(legForm.getPaymentFrequency());
  }
}
