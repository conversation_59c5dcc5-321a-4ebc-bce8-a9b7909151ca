package com.solum.xplain.core.portfolio.trade;

import com.opengamma.strata.basics.StandardId;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.product.TradeInfo;
import com.solum.xplain.core.portfolio.value.CounterpartyType;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class TradeInfoDetails implements Serializable {
  private String counterParty;
  private CounterpartyType counterPartyType;
  private LocalDate tradeDate;
  private LocalTime tradeTime;
  private ZoneId zoneId;
  private LocalDate settlementDate;
  private String csaDiscountingGroup;
  private String tradeCurrency;

  public static TradeInfoDetails fromTradeInfo(TradeInfo info, Currency tradeCurrency) {
    var details = new TradeInfoDetails();
    info.getTradeDate().ifPresent(details::setTradeDate);
    info.getSettlementDate().ifPresent(details::setSettlementDate);
    info.getCounterparty().map(StandardId::getValue).ifPresent(details::setCounterParty);
    info.getTradeTime().ifPresent(details::setTradeTime);
    info.getZone().ifPresent(details::setZoneId);
    details.setTradeCurrency(tradeCurrency.getCode());
    return details;
  }
}
