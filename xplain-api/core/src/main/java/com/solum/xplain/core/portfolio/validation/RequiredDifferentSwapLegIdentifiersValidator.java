package com.solum.xplain.core.portfolio.validation;

import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredDifferentSwapLegIdentifiersValidator
    implements ConstraintValidator<RequiredDifferentSwapLegIdentifiers, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    if (form != null && allNotNull(form.getLeg1(), form.getLeg2())) {
      var leg1 = form.getLeg1();
      var leg2 = form.getLeg2();
      return leg1.getExtLegIdentifier() == null
          || leg2.getExtLegIdentifier() == null
          || !leg1.getExtLegIdentifier().equalsIgnoreCase(leg2.getExtLegIdentifier());
    }
    return true;
  }
}
