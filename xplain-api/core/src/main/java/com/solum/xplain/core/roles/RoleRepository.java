package com.solum.xplain.core.roles;

import static com.solum.xplain.core.common.AggregateFields.COUNT_FIELD;
import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.count;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.mongodb.client.result.DeleteResult;
import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.common.CountResult;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.roles.value.RoleForm;
import com.solum.xplain.core.roles.value.RoleListView;
import com.solum.xplain.core.roles.value.RoleView;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.function.UnaryOperator;
import lombok.AllArgsConstructor;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.stereotype.Repository;

@Repository
@AllArgsConstructor
public class RoleRepository {
  private final MongoOperations mongoOperations;
  private final RoleMapper roleMapper;
  private final ConversionService conversionService;
  private final AuditingHandler auditingHandler;

  public Either<ErrorItem, Role> get(String id) {
    Role role = mongoOperations.findById(id, Role.class);
    if (role == null) {
      return Either.left(Error.OBJECT_NOT_FOUND.entity("Role not found"));
    } else {
      return Either.right(role);
    }
  }

  public List<Role> activeRolesByExternalIds(List<String> externalIds) {
    var criteria =
        where(Role.Fields.archived).is(false).and(Role.Fields.externalId).in(externalIds);
    return mongoOperations.find(query(criteria), Role.class);
  }

  public Either<ErrorItem, EntityId> insert(RoleForm form) {
    Role role = roleMapper.from(form);
    mongoOperations.insert(role);
    return Either.right(EntityId.entityId(role.getId()));
  }

  public ScrollableEntry<RoleListView> list(
      ScrollRequest scrollRequest, TableFilter tableFilter, RoleFilter roleFilter) {
    List<AggregationOperation> baseOperations =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(roleFilter.criteria()),
                roleListViewProjection(),
                match(tableFilter.criteria(RoleListView.class, conversionService)))
            .build();
    TypedAggregation<Role> aggregation =
        newAggregation(
            Role.class,
            ImmutableList.<AggregationOperation>builder()
                .addAll(baseOperations)
                .add(count().as(COUNT_FIELD))
                .build());
    AggregationResults<CountResult> totalAggr =
        mongoOperations.aggregate(aggregation, CountResult.class);
    return ofNullable(totalAggr.getUniqueMappedResult())
        .map(CountResult::getCount)
        .map(
            t ->
                ScrollableEntry.of(
                    roleList(scrollRequest, baseOperations), scrollRequest, t.longValue()))
        .orElse(ScrollableEntry.empty());
  }

  private List<RoleListView> roleList(
      ScrollRequest scrollRequest, List<AggregationOperation> baseOperations) {
    ImmutableList.Builder<AggregationOperation> operations =
        ImmutableList.<AggregationOperation>builder().addAll(baseOperations);
    operations.addAll(new ScrollSortOperations(scrollRequest, RoleListView.Fields.id).build());
    TypedAggregation<Role> aggregation = newAggregation(Role.class, operations.build());
    return mongoOperations.aggregate(aggregation, RoleListView.class).getMappedResults();
  }

  private ProjectionOperation roleListViewProjection() {
    return project()
        .and(Role.Fields.id)
        .as(RoleListView.Fields.id)
        .and(Role.Fields.name)
        .as(RoleListView.Fields.name)
        .and(Role.Fields.externalId)
        .as(RoleListView.Fields.externalId)
        .and(Role.Fields.rolePermissions)
        .as(RoleListView.Fields.rolePermissions)
        .and(Role.Fields.archived)
        .as(RoleListView.Fields.archived)
        .and(Role.Fields.createdAt)
        .as(RoleListView.Fields.createdAt)
        .and(joinPaths(Role.Fields.createdBy, AuditUser.Fields.name))
        .as(RoleListView.Fields.creator)
        .and(Role.Fields.updatedAt)
        .as(RoleListView.Fields.updatedAt)
        .and(joinPaths(Role.Fields.lastModifiedBy, AuditUser.Fields.name))
        .as(RoleListView.Fields.updatedBy);
  }

  public Either<ErrorItem, RoleView> getView(String id) {
    return get(id).map(roleMapper::from);
  }

  public Either<ErrorItem, EntityId> update(String id, RoleForm edit) {
    return update(id, role -> roleMapper.from(edit, role));
  }

  private Either<ErrorItem, EntityId> update(String id, UnaryOperator<Role> f) {
    return get(id)
        .map(
            p -> {
              var newEntity = mongoOperations.findById(id, Role.class);
              newEntity = f.apply(newEntity);
              var diff = p.diff(newEntity);
              if (diff.numberOfDiffs() > 0) {
                newEntity.addAuditLog(auditingHandler.markCreated(AuditLog.of(diff)));
              }
              mongoOperations.save(newEntity);
              return entityId(newEntity.getId());
            });
  }

  public Either<ErrorItem, EntityId> setRoleArchived(String roleId, boolean archived) {
    return update(roleId, role -> role.archived(archived));
  }

  public Either<ErrorItem, EntityId> remove(String roleId) {
    DeleteResult result =
        mongoOperations.remove(query(where(Role.Fields.id).is(roleId)), Role.class);

    return result.getDeletedCount() > 0
        ? right(entityId(roleId))
        : left(OBJECT_NOT_FOUND.entity("Role not found"));
  }

  public boolean existsByExternalIdExcluding(String externalId, String excludeId) {
    var criteria = where(Role.Fields.archived).is(false).and(Role.Fields.externalId).is(externalId);

    if (isNotEmpty(excludeId)) {
      criteria.and(Role.Fields.id).ne(excludeId);
    }

    return mongoOperations.exists(query(criteria), Role.class);
  }
}
