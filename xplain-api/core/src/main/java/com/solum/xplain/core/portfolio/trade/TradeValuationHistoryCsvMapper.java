package com.solum.xplain.core.portfolio.trade;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import java.util.List;

public class TradeValuationHistoryCsvMapper extends CsvMapper<TradeValuationHistoryView> {

  private static final String EXTERNAL_TRADE_ID_COLUMN_NAME = "Trade ID";
  private static final String MARKET_DATA_GROUP_COLUMN_NAME = "Market Data Group";
  private static final String VALUATION_DATE_COLUMN_NAME = "Valuation Date";
  private static final String PRESENT_VALUE_COLUMN_NAME = "PV (Reporting Ccy)";
  private static final String PRICING_SOURCE_COLUMN_NAME = "Pricing Source";

  private static final List<CsvColumn<TradeValuationHistoryView>> COLUMNS =
      List.of(
          CsvColumn.text(
              TradeValuationHistoryView.Fields.externalTradeId,
              EXTERNAL_TRADE_ID_COLUMN_NAME,
              TradeValuationHistoryView::getExternalTradeId),
          CsvColumn.text(
              TradeValuationHistoryView.Fields.marketDataGroupName,
              MARKET_DATA_GROUP_COLUMN_NAME,
              TradeValuationHistoryView::getMarketDataGroupName),
          CsvColumn.date(
              TradeValuationHistoryView.Fields.valuationDate,
              VALUATION_DATE_COLUMN_NAME,
              TradeValuationHistoryView::getValuationDate),
          CsvColumn.bigDecimal(
              TradeValuationHistoryView.Fields.presentValueReportingCurrency,
              PRESENT_VALUE_COLUMN_NAME,
              TradeValuationHistoryView::getPresentValueReportingCurrency),
          CsvColumn.text(
              TradeValuationHistoryView.Fields.pricingSource,
              PRICING_SOURCE_COLUMN_NAME,
              TradeValuationHistoryView::getPricingSource));

  public TradeValuationHistoryCsvMapper() {
    super(COLUMNS, null);
  }
}
