package com.solum.xplain.core.portfolio.trade.type;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.lock.XplainLock.TRADES_LOCK_ID;

import com.solum.xplain.core.authentication.Authorities;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.SwaptionTradeForm;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.SwaptionTradeGroup;
import com.solum.xplain.core.portfolio.value.SwaptionTradeView;
import io.atlassian.fugue.Either;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.groups.Default;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/swaption")
@AllArgsConstructor
public class SwaptionController
    implements BespokeTradeTypedController<SwaptionTradeForm, SwaptionTradeView> {
  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, SwaptionTradeView> toViewFunction(PortfolioItem e) {
    return SwaptionTradeView.of(e);
  }

  @Operation(summary = "Create trade")
  @CommonErrors
  @Override
  @RequireLock(name = TRADES_LOCK_ID)
  @PostMapping()
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_TRADE)
  public ResponseEntity<EntityId> insert(
      @PathVariable("id") String id,
      @Validated(value = {SwaptionTradeGroup.class, BespokeTradeGroup.class, Default.class})
          @RequestBody
          SwaptionTradeForm newForm) {
    return eitherErrorItemResponse(service.insert(id, newForm));
  }

  @Operation(summary = "Update trade")
  @CommonErrors
  @Override
  @RequireLock(name = TRADES_LOCK_ID)
  @PutMapping("/{tradeEntityId}/{version}")
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_TRADE)
  public ResponseEntity<EntityId> update(
      @PathVariable("id") String id,
      @PathVariable("tradeEntityId") String tradeEntityId,
      @PathVariable("version") LocalDate version,
      @Validated(value = {SwaptionTradeGroup.class, BespokeTradeGroup.class, Default.class})
          @RequestBody
          SwaptionTradeForm edit) {
    return eitherErrorItemResponse(service.update(id, version, edit, tradeEntityId));
  }
}
