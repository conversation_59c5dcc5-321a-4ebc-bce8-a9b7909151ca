package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.FxForwardTradeForm;
import com.solum.xplain.core.portfolio.value.FxForwardTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/fxfwd")
@AllArgsConstructor
public class FxFwdController
    implements BespokeTradeTypedController<FxForwardTradeForm, FxForwardTradeView> {

  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, FxForwardTradeView> toViewFunction(PortfolioItem e) {
    return FxForwardTradeView.of(e);
  }
}
