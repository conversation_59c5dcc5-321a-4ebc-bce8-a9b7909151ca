package com.solum.xplain.core.curvegroup.curvebond.value;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_INTERPOLATOR_CLASSIFIER;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.value.HasVersionForm;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.curvegroup.curvebond.validation.UniqueBondCurveNodes;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
@UniqueBondCurveNodes
public class BondCurveUpdateForm implements HasVersionForm {

  @NotNull
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_INTERPOLATOR_CLASSIFIER)
  private String interpolator;

  @NotNull
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_EXTRAPOLATOR_CLASSIFIER)
  private String extrapolatorLeft;

  @NotNull
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_EXTRAPOLATOR_CLASSIFIER)
  private String extrapolatorRight;

  @Valid private List<BondCurveNodeForm> nodes;

  @Valid @NotNull private NewVersionFormV2 versionForm;
}
