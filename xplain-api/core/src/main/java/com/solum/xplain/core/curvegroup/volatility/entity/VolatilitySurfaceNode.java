package com.solum.xplain.core.curvegroup.volatility.entity;

import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.SWAPTION_ATM;
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.SWAPTION_SKEW;
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofVol;

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvegroup.volatility.classifier.SkewType;
import com.solum.xplain.core.market.validation.VolatilityValueKey;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.Locale;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class VolatilitySurfaceNode {

  private static final String ATM_VOLATILITY_MD_KEY_PATTERN = "%sV%s_ATM_%s";
  private static final String SKEW_VOLATILITY_MD_KEY_PATTERN = "%sV%s_%s_%s";

  private static final String ATM_VOLATILITY_MD_NAME_PATTERN = "%S SWO ATM %SV%S";
  private static final String SKEW_VOLATILITY_MD_NAME_PATTERN = "%S SWO %S %SV%S";

  private String expiry;
  private String tenor;

  public InstrumentDefinition atmInstrument(
      String name, String index, String ccy, String underlying) {
    return ofVol(
        name,
        ccy,
        SWAPTION_ATM,
        getAtmKey(index).getKey(),
        getAtmName(name),
        tenor,
        nodeInstrument(),
        underlying);
  }

  public InstrumentDefinition skewInstrument(
      String name, String index, String ccy, BigDecimal value, SkewType type, String underlying) {
    var skewKey = getSkewKey(index, value, type);
    var key = skewKey == null ? null : skewKey.getKey();
    var skewName = getSkewName(name, value, type);
    return ofVol(name, ccy, SWAPTION_SKEW, key, skewName, tenor, nodeInstrument(), underlying);
  }

  public VolatilityValueKey getAtmKey(String index) {
    return new VolatilityValueKey(
        String.format(ATM_VOLATILITY_MD_KEY_PATTERN, getExpiry(), getTenor(), index));
  }

  public String getAtmName(String name) {
    return String.format(ATM_VOLATILITY_MD_NAME_PATTERN, name, getExpiry(), getTenor());
  }

  public VolatilityValueKey getSkewKey(String index, BigDecimal skewValue, SkewType skewType) {
    return Optional.ofNullable(skewValue)
        .map(
            s ->
                String.format(
                    SKEW_VOLATILITY_MD_KEY_PATTERN,
                    getExpiry(),
                    getTenor(),
                    skewPart(s, skewType, marketDataSuffix(skewType, true)),
                    index))
        .map(VolatilityValueKey::new)
        .orElse(null);
  }

  public String getSkewName(String name, BigDecimal skewValue, SkewType skewType) {
    return Optional.ofNullable(skewValue)
        .map(
            s ->
                String.format(
                    SKEW_VOLATILITY_MD_NAME_PATTERN,
                    name,
                    skewPart(s, skewType, marketDataSuffix(skewType, false)),
                    getExpiry(),
                    getTenor()))
        .orElse(null);
  }

  private String nodeInstrument() {
    return String.format("%sx%s", getExpiry(), getTenor());
  }

  private String skewPart(BigDecimal skewValue, SkewType skewType, String suffix) {
    DecimalFormat df = new DecimalFormat("0", DecimalFormatSymbols.getInstance(Locale.ENGLISH));
    df.setMaximumFractionDigits(340);
    if (skewType == SkewType.STRIKE) {
      var percentage = df.format(skewValue.multiply(BigDecimal.valueOf(100)));
      return String.format("%S%S", percentage, suffix);
    }
    var basisPoints = df.format(skewValue.multiply(BigDecimal.valueOf(10000)));
    var sign = skewValue.compareTo(BigDecimal.ZERO) > 0 ? "+" : "";
    return String.format("%S%S%S", sign, basisPoints, suffix);
  }

  private String marketDataSuffix(SkewType type, boolean isKey) {
    if (type == SkewType.STRIKE) {
      return "%";
    } else if (type == SkewType.MONEYNESS && !isKey) {
      return "BP";
    }
    return "";
  }
}
