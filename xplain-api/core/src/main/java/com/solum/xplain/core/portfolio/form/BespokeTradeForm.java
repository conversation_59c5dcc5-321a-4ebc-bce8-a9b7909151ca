package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class BespokeTradeForm extends DefaultTradeForm {

  private LocalDate tradeDate;
  private LocalDate tradeSettlementDate;

  protected abstract String tradeCurrency();

  public void withTradeInfo(TradeDetails tradeDetails) {
    setTradeDate(tradeDetails.getInfo().getTradeDate());
    setTradeSettlementDate(tradeDetails.getInfo().getSettlementDate());
    setTradeCounterpartyType(tradeDetails.getInfo().getCounterPartyType());
    setTradeCounterparty(tradeDetails.getInfo().getCounterParty());
    setCsaDiscountingGroup(tradeDetails.getInfo().getCsaDiscountingGroup());
  }

  public TradeInfoDetails toTradeInfo() {
    TradeInfoDetails tradeInfoDetails = new TradeInfoDetails();
    tradeInfoDetails.setCounterParty(getTradeCounterparty());
    tradeInfoDetails.setCounterPartyType(getTradeCounterpartyType());
    tradeInfoDetails.setCsaDiscountingGroup(getCsaDiscountingGroup());
    tradeInfoDetails.setSettlementDate(tradeSettlementDate);
    tradeInfoDetails.setTradeDate(tradeDate);
    tradeInfoDetails.setTradeCurrency(tradeCurrency());
    return tradeInfoDetails;
  }
}
