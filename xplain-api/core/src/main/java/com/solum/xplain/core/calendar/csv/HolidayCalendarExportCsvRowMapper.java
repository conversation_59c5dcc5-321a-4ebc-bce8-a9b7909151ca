package com.solum.xplain.core.calendar.csv;

import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvMapper;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class HolidayCalendarExportCsvRowMapper extends CsvMapper<HolidayCalendarExportCsvRow> {

  static final String HOLIDAY_COLUMN = "Holiday";
  static final String WEEKEND_COLUMN = "Weekend";
  static final String WORKING_DAY_COLUMN = "Working Day";
  private static final List<CsvColumn<HolidayCalendarExportCsvRow>> COLUMNS =
      List.of(
          CsvColumn.date(
              HolidayCalendarExportCsvRow.Fields.holiday,
              HOLIDAY_COLUMN,
              HolidayCalendarExportCsvRow::getHoliday),
          CsvColumn.text(
              HolidayCalendarExportCsvRow.Fields.weekendName,
              WEEKEND_COLUMN,
              HolidayCalendarExportCsvRow::getWeekendName),
          CsvColumn.date(
              HolidayCalendarExportCsvRow.Fields.workingDay,
              WORKING_DAY_COLUMN,
              HolidayCalendarExportCsvRow::getWorkingDay));

  public HolidayCalendarExportCsvRowMapper() {
    super(COLUMNS, null);
  }
}
