package com.solum.xplain.core.portfolio.trade;

import java.io.Serializable;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

@Data
@FieldNameConstants
public class HedgeTradeDetails implements Serializable {
  // TODO (TECH DEBT): SXSD-8822 - find a better solution than custom fields with magic identifiers.
  public static final String IS_POTENTIAL_HEDGE_CUSTOM_FIELD_ID = "POTENTIAL_HEDGE";

  @Nullable private Boolean potentialHedge;
}
