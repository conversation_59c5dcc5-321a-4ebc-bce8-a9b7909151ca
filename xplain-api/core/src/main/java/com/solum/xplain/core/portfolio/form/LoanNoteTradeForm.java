package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.ROLL_CONVENTION_CLASSIFIER;
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.BOND_CURVE_NAMES_BY_CCY;
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.JP_LOAN_NOTE_DAY_COUNTS;
import static com.solum.xplain.core.classifiers.CoreClassifiersProvider.LOAN_NOTE_DAY_COUNTS;
import static com.solum.xplain.core.portfolio.CoreProductType.LOAN_NOTE;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.StubConventionSupplier;
import com.solum.xplain.core.common.validation.SwapLegFrequencySupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableLoanNoteDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.HasRegularDates;
import com.solum.xplain.core.portfolio.validation.LoanNoteGroupProvider;
import com.solum.xplain.core.portfolio.validation.ValidAccrualRegularDates;
import com.solum.xplain.core.portfolio.validation.ValidLoanNoteReference;
import com.solum.xplain.core.portfolio.validation.ValidPaymentPeriod;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.JpLoanNoteTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.core.portfolio.value.HasPaymentPeriod;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@ValidPaymentPeriod
@ValidLoanNoteReference
@ValidAccrualRegularDates
@EqualsAndHashCode(callSuper = true)
@GroupSequenceProvider(LoanNoteGroupProvider.class)
public class LoanNoteTradeForm extends BespokeTradeForm
    implements ParsableToTradeValue, HasPaymentPeriod, HasRegularDates {
  @NotNull private LocalDate startDate;

  @NotNull private LocalDate endDate;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = BOND_CURVE_NAMES_BY_CCY)
  private String currency;

  @NotEmpty private String reference;
  @NotNull private Double creditSpread;

  @NotNull(groups = BespokeTradeGroup.class)
  @Null(groups = ReferenceTradeGroup.class)
  @Positive(groups = BespokeTradeGroup.class)
  private Double notionalValue;

  @NotEmpty
  @Pattern(
      regexp = "NoAdjust",
      message = "{com.solum.xplain.core.portfolio.form.loanNote.businessDayConvention}")
  private String businessDayConvention;

  @NotNull @PositiveOrZero private Integer settlementOffsetDays;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = LOAN_NOTE_DAY_COUNTS)
  @ValidStringSet(
      groups = JpLoanNoteTradeGroup.class,
      value = ClassifierSupplier.class,
      supplierArgument = JP_LOAN_NOTE_DAY_COUNTS)
  private String dayCount;

  @NotEmpty
  @ValidStringSet(SwapLegFrequencySupplier.class)
  private String frequency;

  @NotNull @Positive private Double fixedRate;

  @ValidStringSet(StubConventionSupplier.class)
  private String stubConvention;

  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = ROLL_CONVENTION_CLASSIFIER)
  private String rollConvention;

  private LocalDate regularStartDate;
  private LocalDate regularEndDate;

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return Checked.now(this::toTradeDetails)
        .toEither()
        .leftMap(err -> Error.UNEXPECTED_TYPE.entity(err.getMessage()))
        .map(details -> defaultTradeValue(LOAN_NOTE, details));
  }

  private TradeDetails toTradeDetails() {
    return ResolvableLoanNoteDetails.builder()
        .startDate(startDate)
        .endDate(endDate)
        .currency(Currency.parse(currency))
        .notional(notionalValue)
        .curveReference(reference)
        .creditSpread(creditSpread)
        .paymentFrequency(frequency)
        .fixedRate(fixedRate)
        .businessDayConvention(businessDayConvention)
        .dayCount(dayCount)
        .settlementOffsetDays(settlementOffsetDays)
        .regularStartDate(regularStartDate)
        .regularEndDate(regularEndDate)
        .stubConvention(stubConvention)
        .rollConvention(rollConvention)
        .build()
        .toTradeDetails(toTradeInfo());
  }

  @Override
  protected String tradeCurrency() {
    return currency;
  }
}
