package com.solum.xplain.core.portfolio.trade;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.ExportFileNameUtils;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import io.atlassian.fugue.Either;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class TradeValuationHistoryControllerService {

  private final List<TradeValuationHistoryProvider> providers;
  private final PortfolioRepository portfolioRepository;
  private final AuthenticationContext authenticationContext;

  public Either<ErrorItem, List<TradeValuationHistoryView>> getTradeValuationHistory(
      String portfolioId, String tradeEntityId) {
    var user = authenticationContext.currentUser();
    return portfolioRepository
        .getUserPortfolioView(user, portfolioId)
        .map(portfolioView -> getTradeValuationHistoryViews(tradeEntityId, portfolioView).toList());
  }

  private Stream<TradeValuationHistoryView> getTradeValuationHistoryViews(
      String tradeEntityId, UserTeamEntity<PortfolioView> portfolioView) {
    return providers.stream()
        .flatMap(
            provider ->
                provider.getTradeValuationHistory(portfolioView.getView(), tradeEntityId).stream())
        .sorted(Comparator.comparing(TradeValuationHistoryView::getValuationDate).reversed());
  }

  public Either<ErrorItem, FileResponseEntity> getTradeValuationHistoryCsv(
      String portfolioId, String tradeEntityId) {
    var user = authenticationContext.currentUser();
    var mapper = new TradeValuationHistoryCsvMapper();

    return portfolioRepository
        .getUserPortfolioView(user, portfolioId)
        .map(
            portfolioView -> {
              var views = getTradeValuationHistoryViews(tradeEntityId, portfolioView).toList();
              var rows = views.stream().map(mapper::toCsvRow).toList();
              var externalTradeId =
                  views.stream()
                      .findAny()
                      .map(TradeValuationHistoryView::getExternalTradeId)
                      .orElse("");

              var csvFile = new CsvOutputFile(mapper.header(), rows);
              var csvFileName =
                  ExportFileNameUtils.nameWithTimeStamp(
                      String.format(
                          "%s_%s_PVCalculationResultsHistory",
                          portfolioView.getView().getExternalPortfolioId(), externalTradeId));
              return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
            });
  }
}
