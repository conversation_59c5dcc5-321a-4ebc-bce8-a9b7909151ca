package com.solum.xplain.core.curvegroup.volatility.value.surface;

import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.curvegroup.volatility.validation.UniqueVolatilitySurfaceName;
import com.solum.xplain.core.curvegroup.volatility.validation.VolatilitySurfaceNameSupplier;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@UniqueVolatilitySurfaceName
public class VolatilitySurfaceForm extends VolatilitySurfaceUpdateForm {

  @NotEmpty
  @ValidStringSet(VolatilitySurfaceNameSupplier.class)
  private String name;
}
