package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.CoreProductType.XCCY;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import io.atlassian.fugue.Either;
import lombok.Data;

@Data
public class XccyTradeView extends SwapTradeView {

  private String tradeCurrency;

  public static Either<ErrorItem, XccyTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == XCCY) {
      var view = new XccyTradeView();
      view.withTradeDetails(item);
      view.setTradeCurrency(item.getTradeDetails().getInfo().getTradeCurrency());
      return Either.right(view);

    } else {
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Product type is not " + "expected " + item.getProductType()));
    }
  }
}
