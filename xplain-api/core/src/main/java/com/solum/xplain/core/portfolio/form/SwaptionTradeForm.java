package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SWAPTION_SETTLEMENT_TYPE_CLASSIFIER;
import static com.solum.xplain.core.portfolio.CoreProductType.SWAPTION;

import com.solum.xplain.core.common.validation.BusinessDayConventionsSupplier;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.PositionTypeBuySupplier;
import com.solum.xplain.core.common.validation.PositionTypeSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.validation.ValidZone;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableSwapDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableSwaptionDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.ExpiryDateBeforeAccrualSchedule;
import com.solum.xplain.core.portfolio.validation.RequiredSameCurrencies;
import com.solum.xplain.core.portfolio.validation.RequiredSameIborIndexCurrency;
import com.solum.xplain.core.portfolio.validation.RequiredSameNotionals;
import com.solum.xplain.core.portfolio.validation.RequiredSettlementIfCash;
import com.solum.xplain.core.portfolio.validation.RequiredValidSwaptionLegs;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import com.solum.xplain.extensions.enums.PositionType;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@RequiredSettlementIfCash
@RequiredValidSwaptionLegs
@RequiredSameCurrencies
@RequiredSameNotionals
@RequiredSameIborIndexCurrency
@ExpiryDateBeforeAccrualSchedule
public class SwaptionTradeForm extends SwapTradeForm implements ParsableToTradeValue {

  @NotEmpty
  @ValidStringSet(PositionTypeSupplier.class)
  @ValidStringSet(value = PositionTypeBuySupplier.class, groups = ReferenceTradeGroup.class)
  private String position;

  @NotNull private LocalTime expiryTime;

  @NotEmpty @ValidZone private String expiryZone;

  @NotNull private LocalDate expiryDate;

  @ValidStringSet(BusinessDayConventionsSupplier.class)
  @NotEmpty
  private String expiryDateConvention;

  @NotNull private LocalDate premiumDate;

  @ValidStringSet(BusinessDayConventionsSupplier.class)
  @NotEmpty
  private String premiumDateConvention;

  @NotNull private Double premiumValue;

  @NotEmpty
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = SWAPTION_SETTLEMENT_TYPE_CLASSIFIER)
  private String swaptionSettlementType;

  private String exerciseNotice;

  private String underlyingSwapType;

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return createTradeDetails(toTradeInfo()).map(details -> defaultTradeValue(SWAPTION, details));
  }

  @Override
  protected Either<ErrorItem, TradeDetails> createTradeDetails(TradeInfoDetails infoDetails) {
    return Steps.begin(getLeg1().resolvableDetails())
        .then(getLeg2()::resolvableDetails)
        .yield(List::of)
        .map(this::resolvableSwapDetails)
        .map(ResolvableSwapDetails::toSwaptionDetailsBuilder)
        .map(this::resolvableSwaptionDetails)
        .map(d -> d.toTradeDetails(infoDetails));
  }

  private ResolvableSwaptionDetails resolvableSwaptionDetails(
      ResolvableSwaptionDetails.ResolvableSwaptionDetailsBuilder builder) {
    return builder
        .positionType(PositionType.valueOf(position))
        .expiryDate(expiryDate)
        .expiryDateConvention(expiryDateConvention)
        .expiryTime(expiryTime)
        .expiryZone(expiryZone)
        .premiumValue(premiumValue)
        .premiumDate(premiumDate)
        .premiumDateConvention(premiumDateConvention)
        .swaptionSettlement(swaptionSettlementType)
        .build();
  }
}
