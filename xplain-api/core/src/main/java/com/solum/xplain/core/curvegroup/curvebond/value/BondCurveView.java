package com.solum.xplain.core.curvegroup.curvebond.value;

import com.solum.xplain.core.common.versions.State;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class BondCurveView {

  // Versioned entity fields
  private String entityId;
  private LocalDate validFrom;
  private String comment;
  private LocalDateTime recordDate;
  private State state;
  private String modifiedBy;
  private LocalDateTime modifiedAt;

  // Versioned named entity fields
  private String name;

  // Bond curve fields
  private String interpolator;
  private String extrapolatorLeft;
  private String extrapolatorRight;

  // Curve fields - calculated
  private Integer numberOfNodes;
  private boolean calibrated;
}
