package com.solum.xplain.core.curvegroup.curvebond.entity;

import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@FieldNameConstants
@Data
public class BondCurveCalibrationResult {

  @Id private String id;
  private String curveId;

  @CreatedDate private LocalDateTime calibratedAt;

  private String marketDataGroupId;
  private String marketDataSource;
  private LocalDate stateDate;
  private LocalDate curveDate;
  private List<ChartPoint> chartPoints;
  private InstrumentPriceRequirements priceRequirements;
}
