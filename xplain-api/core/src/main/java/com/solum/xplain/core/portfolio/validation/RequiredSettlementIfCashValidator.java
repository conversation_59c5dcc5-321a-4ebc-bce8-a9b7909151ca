package com.solum.xplain.core.portfolio.validation;

import com.opengamma.strata.product.common.SettlementType;
import com.solum.xplain.core.portfolio.form.SwaptionTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredSettlementIfCashValidator
    implements ConstraintValidator<RequiredSettlementIfCash, SwaptionTradeForm> {

  public boolean isValid(SwaptionTradeForm form, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    if (form.getSwaptionSettlementType() != null
        && SettlementType.CASH.name().equals(form.getSwaptionSettlementType())) {
      boolean result = true;

      if (form.getTradeSettlementDate() == null) {
        context
            .buildConstraintViolationWithTemplate("NotNull")
            .addPropertyNode("tradeSettlementDate")
            .addConstraintViolation();
        result = false;
      }
      return result;
    } else {
      return true;
    }
  }
}
