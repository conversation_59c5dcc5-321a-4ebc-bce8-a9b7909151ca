package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.CreditIndexTrancheTradeForm;
import com.solum.xplain.core.portfolio.value.CreditIndexTrancheTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/credit-index-tranche")
@AllArgsConstructor
public class CreditIndexTrancheController
    implements BespokeTradeTypedController<
        CreditIndexTrancheTradeForm, CreditIndexTrancheTradeView> {
  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, CreditIndexTrancheTradeView> toViewFunction(PortfolioItem e) {
    return CreditIndexTrancheTradeView.of(e);
  }
}
