package com.solum.xplain.core.curvegroup.curve.value;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_DISCOUNT_FACTOR_INTERP_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_INTERPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_NODE_CLASH_ACTION_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DEFAULT_CURVE_VALUE_TYPE_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DISCOUNT_FACTOR_CURVE_INTERPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.INFLATION_CURVE_VALUE_TYPE_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.MIN_NODE_GAP_CLASSIFIER;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.value.HasVersionForm;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.curvegroup.curve.validation.CurveTypesSupplier;
import com.solum.xplain.core.curvegroup.curve.validation.RequireTnNode;
import com.solum.xplain.core.curvegroup.curve.validation.UniqueCurveNodes;
import com.solum.xplain.core.curvegroup.curve.validation.ValidCurveNodesConventions;
import com.solum.xplain.core.curvegroup.curve.validation.ValidCurveNodesTypes;
import com.solum.xplain.core.curvegroup.curve.validation.curvegroups.CurveFormDiscountFactorGroup;
import com.solum.xplain.core.curvegroup.curve.validation.curvegroups.CurveFormGenericInterpolatorGroup;
import com.solum.xplain.core.curvegroup.curve.validation.curvegroups.CurveFormGroupProvider;
import com.solum.xplain.core.curvegroup.curve.validation.curvegroups.CurveFormIndexGroup;
import com.solum.xplain.core.curvegroup.curve.validation.curvegroups.CurveFormInflationGroup;
import com.solum.xplain.core.curvegroup.curve.validation.curvegroups.CurveFormLogLinearDiscountFactorGroup;
import com.solum.xplain.core.curvegroup.curve.validation.curvegroups.CurveFormNonDiscountFactorGroup;
import com.solum.xplain.core.curvegroup.curve.validation.curvegroups.CurveFormXccyGroup;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@GroupSequenceProvider(CurveFormGroupProvider.class)
@RequireTnNode
@UniqueCurveNodes
@ValidCurveNodesTypes
@ValidCurveNodesConventions
public class CurveUpdateForm implements HasVersionForm {

  @NotNull
  @ValidStringSet(CurveTypesSupplier.class)
  private String curveType;

  @NotNull
  @JsonProperty("yInterpolationMethod")
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = INFLATION_CURVE_VALUE_TYPE_CLASSIFIER,
      groups = {CurveFormInflationGroup.class})
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = DEFAULT_CURVE_VALUE_TYPE_CLASSIFIER,
      groups = {CurveFormXccyGroup.class, CurveFormIndexGroup.class})
  private String yInterpolationMethod;

  @NotNull
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = DISCOUNT_FACTOR_CURVE_INTERPOLATOR_CLASSIFIER,
      groups = {CurveFormDiscountFactorGroup.class})
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_INTERPOLATOR_CLASSIFIER,
      groups = {CurveFormNonDiscountFactorGroup.class})
  private String interpolator;

  @NotNull
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_EXTRAPOLATOR_CLASSIFIER,
      groups = {CurveFormGenericInterpolatorGroup.class})
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_DISCOUNT_FACTOR_INTERP_EXTRAPOLATOR_CLASSIFIER,
      groups = {CurveFormLogLinearDiscountFactorGroup.class})
  private String extrapolatorLeft;

  @NotNull
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_EXTRAPOLATOR_CLASSIFIER,
      groups = {CurveFormGenericInterpolatorGroup.class})
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_DISCOUNT_FACTOR_INTERP_EXTRAPOLATOR_CLASSIFIER,
      groups = {CurveFormLogLinearDiscountFactorGroup.class})
  private String extrapolatorRight;

  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = MIN_NODE_GAP_CLASSIFIER)
  @Null(groups = {CurveFormInflationGroup.class, CurveFormXccyGroup.class})
  private String minGap;

  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = CURVE_NODE_CLASH_ACTION_CLASSIFIER)
  @Null(groups = {CurveFormInflationGroup.class, CurveFormXccyGroup.class})
  private String clashAction;

  @Valid private List<CurveNodeForm> nodes;

  @Valid @NotNull private NewVersionFormV2 versionForm;
}
