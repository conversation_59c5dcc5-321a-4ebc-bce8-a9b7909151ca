package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class RequiredPayReceiveValidator
    implements ConstraintValidator<RequiredPayReceive, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }
    var leg1 = form.getLeg1();
    var leg2 = form.getLeg2();
    if (hasPayReceive(leg1) && hasPayReceive(leg2) && hasSamePayReceive(leg1, leg2)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate("RequiredPayReceive")
          .addPropertyNode("leg2")
          .addConstraintViolation();
      return false;
    }
    return true;
  }

  private boolean hasPayReceive(SwapLegForm form) {
    return form != null && form.getPayReceive() != null;
  }

  private boolean hasSamePayReceive(SwapLegForm f1, SwapLegForm f2) {
    return StringUtils.equals(f1.getPayReceive(), f2.getPayReceive());
  }
}
