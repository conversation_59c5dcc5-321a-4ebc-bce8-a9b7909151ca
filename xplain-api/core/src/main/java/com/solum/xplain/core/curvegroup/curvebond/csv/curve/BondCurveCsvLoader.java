package com.solum.xplain.core.curvegroup.curvebond.csv.curve;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_EXTRAPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURVE_INTERPOLATOR_CLASSIFIER;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations.lookupKeyByName;
import static com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvFields.EXTRAPOLATOR_LEFT_FIELD;
import static com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvFields.EXTRAPOLATOR_RIGHT_FIELD;
import static com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvFields.INTERPOLATOR_FIELD;
import static com.solum.xplain.core.curvegroup.curvebond.csv.curve.BondCurveCsvFields.NAME_FIELD;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveForm;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.function.Function;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class BondCurveCsvLoader extends GenericCsvLoader<BondCurveForm, String> {

  private static final ClassifierSupplier BOND_CURVE_SUPPLIER =
      new ClassifierSupplier("bondCurveNodeConventions");
  private static final ClassifierSupplier CURVE_INTERPOLATOR_SUPPLIER =
      new ClassifierSupplier(CURVE_INTERPOLATOR_CLASSIFIER);
  private static final ClassifierSupplier CURVE_EXTRAPOLATOR_SUPPLIER =
      new ClassifierSupplier(CURVE_EXTRAPOLATOR_CLASSIFIER);

  @Override
  protected List<String> getFileHeaders() {
    return List.of(
        NAME_FIELD, INTERPOLATOR_FIELD, EXTRAPOLATOR_LEFT_FIELD, EXTRAPOLATOR_RIGHT_FIELD);
  }

  @Override
  protected Either<ErrorItem, BondCurveForm> parseLine(@NonNull CsvRow row) {
    try {
      String nameStr = row.getValue(NAME_FIELD);
      String interpolatorStr = row.getValue(INTERPOLATOR_FIELD);
      String extrapolatorLeftStr = row.getValue(EXTRAPOLATOR_LEFT_FIELD);
      String extrapolatorRightStr = row.getValue(EXTRAPOLATOR_RIGHT_FIELD);

      BondCurveForm curve = new BondCurveForm();
      curve.setName(validateValue(nameStr, BOND_CURVE_SUPPLIER));
      curve.setVersionForm(NewVersionFormV2.newDefault());
      curve.setInterpolator(validateValue(interpolatorStr, CURVE_INTERPOLATOR_SUPPLIER));
      curve.setExtrapolatorLeft(validateValue(extrapolatorLeftStr, CURVE_EXTRAPOLATOR_SUPPLIER));
      curve.setExtrapolatorRight(validateValue(extrapolatorRightStr, CURVE_EXTRAPOLATOR_SUPPLIER));
      return Either.right(curve);
    } catch (RuntimeException e) {
      return Either.left(rowParsingError(row, e));
    }
  }

  @Override
  protected CsvParserResultBuilder<BondCurveForm, String> createResult(ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        f -> lookupKeyByName(f.getName()).orElse(f.getName()),
        Function.identity(),
        parsingMode.failOnError());
  }
}
