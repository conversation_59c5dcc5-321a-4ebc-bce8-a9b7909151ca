package com.solum.xplain.core.curvemarket;

import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceTypeResolver;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.mdvalue.MarketDataValueService;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketData;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueView;
import io.atlassian.fugue.Either;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.lang.NonNull;

/**
 * Extension of {@link MarketDataExtractionParams} which holds an {@link
 * InstrumentOverrideCurveDateDetails} that can be passed to {@link MarketDataValueService} to
 * extract market data across two different dates, according to the market data instrument type.
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class InstrumentTypeDateMarketDataExtractionParams extends MarketDataExtractionParams {
  private final InstrumentOverrideCurveDateDetails curveDateDetails;

  public static InstrumentTypeDateMarketDataExtractionParams mdParamsWithCurves(
      CurveConfigMarketStateKey stateKey,
      List<String> discountCurves,
      InstrumentOverrideCurveDateDetails curveDateDetails) {
    return new InstrumentTypeDateMarketDataExtractionParams(
        stateKey,
        mdParamsWithCurvesResolver(stateKey.getPriceRequirements(), discountCurves),
        curveDateDetails);
  }

  public InstrumentTypeDateMarketDataExtractionParams(
      CurveConfigMarketStateKey stateKey,
      InstrumentPriceTypeResolver priceTypeResolver,
      @NonNull InstrumentOverrideCurveDateDetails curveDateDetails) {
    super(stateKey, priceTypeResolver);
    this.curveDateDetails = curveDateDetails;
  }

  @Override
  public Either<ErrorItem, Map<String, ResolvedMarketData>> resolvedValuesByKey(
      MarketDataValueService marketDataValueService) {
    Either<ErrorItem, Map<String, ResolvedMarketDataValueView>> results =
        marketDataValueService.getResolvedValuesByKey(
            getMarketDataGroupId(),
            getStateDate(),
            getCurveDateDetails(),
            Arrays.asList(CoreAssetGroup.values()));

    return results.map(r -> (Map<String, ResolvedMarketData>) (Map<String, ?>) r);
  }
}
