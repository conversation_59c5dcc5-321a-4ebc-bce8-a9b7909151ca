package com.solum.xplain.core.curvegroup.curvebond;

import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.common.versions.VersionedEntityMapper;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurveNode;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveUpdateForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveView;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;
import org.springframework.lang.NonNull;

@Mapper(
    uses = {ObjectIdMapper.class, AuditUserMapper.class},
    imports = {CollectionUtils.class})
public interface BondCurveMapper extends VersionedEntityMapper<BondCurve> {

  BondCurveMapper INSTANCE = Mappers.getMapper(BondCurveMapper.class);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "curveGroupId", source = "groupId")
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  BondCurve fromForm(BondCurveForm form, String groupId, @MappingTarget BondCurve curve);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "curveGroupId", ignore = true)
  @Mapping(target = "name", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  BondCurve fromForm(BondCurveUpdateForm form, @MappingTarget BondCurve curve);

  BondCurveNode fromNodeForm(BondCurveNodeForm nodeForm);

  @Mapping(target = "numberOfNodes", expression = "java(curve.getNodes().size())")
  BondCurveView toView(BondCurve curve, boolean calibrated);

  @Mapping(target = "versionForm", ignore = true)
  BondCurveForm toForm(BondCurve curve);

  BondCurveNodeForm toNodeForm(BondCurveNode bondCurveNode);

  @Mapping(target = "quoteId", ignore = true)
  @Mapping(target = "dataSource", ignore = true)
  @Mapping(target = "ticker", ignore = true)
  @Mapping(target = "marketValue", ignore = true)
  @Mapping(target = "marketValueAsk", ignore = true)
  @Mapping(target = "marketValueMid", ignore = true)
  @Mapping(target = "marketValueBid", ignore = true)
  BondCurveNodeCalculatedView toView(
      BondCurveNode node, String key, InstrumentPriceType calibrationPriceType);

  default List<BondCurveNodeCalculatedView> toNodeViews(
      @NonNull BondCurve curve,
      @NonNull Map<String, CalculationMarketValueFullView> quotes,
      @NonNull InstrumentPriceType calibrationPriceType) {
    return curve.getNodes().stream()
        .map(
            node -> {
              var view = toView(node, node.mdk(), calibrationPriceType);
              var quote = quotes.get(node.mdk());
              if (quote != null) {
                view.setQuoteId(quote.getId());
                view.setMarketValue(quote.getValue());
                view.setMarketValueAsk(quote.getAskValue());
                view.setMarketValueMid(quote.getMidValue());
                view.setMarketValueBid(quote.getBidValue());
                view.setDataSource(quote.getProvider());
                view.setTicker(quote.getTicker());
              }
              return view;
            })
        .toList();
  }
}
