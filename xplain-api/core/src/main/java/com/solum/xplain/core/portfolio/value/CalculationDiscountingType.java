package com.solum.xplain.core.portfolio.value;

import com.opengamma.strata.basics.currency.Currency;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum CalculationDiscountingType {
  LOCAL_CURRENCY(null, false, "Local Ccy"),
  DISCOUNT_USD(Currency.USD, true, "USD"),
  DISCOUNT_EUR(Currency.EUR, true, "EUR"),
  DISCOUNT_GBP(Currency.GBP, true, "GBP"),
  DISCOUNT_AUD(Currency.AUD, true, "AUD"),
  DISCOUNT_CAD(Currency.CAD, true, "CAD"),
  DISCOUNT_CHF(Currency.CHF, true, "CHF"),
  DISCOUNT_JPY(Currency.JPY, true, "JPY"),
  DISCOUNT_NZD(Currency.NZD, true, "NZD");

  private final Currency currency;
  private final boolean xvaApplicable;
  private final String label;
}
