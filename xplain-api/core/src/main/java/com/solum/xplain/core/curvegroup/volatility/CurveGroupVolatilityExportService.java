package com.solum.xplain.core.curvegroup.volatility;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.getPrefix;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.getVolatilitiesPrefix;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.zipNameWithTimeStamp;
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.csv.FilesResponseBuilder;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvegroup.volatility.csv.capletnode.VolatilityCapletNodeCsvMapper;
import com.solum.xplain.core.curvegroup.volatility.csv.node.VolatilityNodeCsvMapper;
import com.solum.xplain.core.curvegroup.volatility.csv.skew.VolatilitySkewCsvMapper;
import com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvMapper;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.InstrumentMarketKeyDefinitionExportService;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
public class CurveGroupVolatilityExportService {

  private static final String SWAPTION_VOLS = "SwaptionATMVols";
  private static final String CAP_FLOOR_VOLS = "CapFloorStrikeVols";
  private static final String SWAPTION_SKEWS = "SwaptionSkews";

  private final CurveGroupRepository curveGroupRepository;
  private final CurveGroupVolatilityRepository repository;
  private final MarketDataQuotesSupport marketDataQuotesSupport;
  private final InstrumentMarketKeyDefinitionExportService definitionExportService;

  public CurveGroupVolatilityExportService(
      CurveGroupRepository curveGroupRepository,
      CurveGroupVolatilityRepository repository,
      MarketDataQuotesSupport marketDataQuotesSupport,
      InstrumentMarketKeyDefinitionExportService definitionExportService) {
    this.curveGroupRepository = curveGroupRepository;
    this.repository = repository;
    this.marketDataQuotesSupport = marketDataQuotesSupport;
    this.definitionExportService = definitionExportService;
  }

  public Either<ErrorItem, FileResponseEntity> getAllSurfacesCsvBytes(
      String groupId, LocalDate stateDate, TableFilter tableFilter, List<String> selectedColumns) {
    var mapper = new VolatilitySurfaceCsvMapper(selectedColumns);
    var csvFileName = nameWithTimeStamp("IRVolSurfaceList", stateDate);
    return groupEither(groupId)
        .map(
            u -> {
              var rows =
                  repository.getSurfaceViews(groupId, stateDate, active(), tableFilter).stream()
                      .map(mapper::toCsvRow)
                      .toList();
              var csvFile = new CsvOutputFile(mapper.header(), rows);
              return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
            });
  }

  public Either<ErrorItem, FileResponseEntity> getAllSurfacesNodesCsvBytes(
      String groupId,
      BitemporalDate stateDate,
      CurveConfigMarketStateForm stateForm,
      TableFilter tableFilter,
      List<String> selectedColumns) {
    return groupEither(groupId)
        .map(
            g -> {
              var surfaces =
                  repository.getSurfaceViews(
                      groupId,
                      stateDate,
                      active(),
                      tableFilter,
                      Sort.by(VolatilitySurfaceView.Fields.name));
              var csvFile = getNodesCsv(stateForm, selectedColumns, surfaces);
              var prefix = getPrefix(SWAPTION_VOLS, surfaces, VolatilitySurfaceView::getName);
              var csvFileName = nameWithTimeStamp(prefix, stateDate);
              return FileResponseEntity.csvFile(csvFile, csvFileName);
            });
  }

  public Either<ErrorItem, FileResponseEntity> getSurfaceNodesCsvBytes(
      String groupId,
      String surfaceId,
      LocalDate version,
      CurveConfigMarketStateForm stateForm,
      List<String> selectedColumns) {
    return groupEither(groupId)
        .flatMap(
            g ->
                repository
                    .getActiveSurfaceView(groupId, surfaceId, version)
                    .map(
                        s -> {
                          var csvFile = getNodesCsv(stateForm, selectedColumns, List.of(s));
                          var csvFileName =
                              nameWithTimeStamp(
                                  s.getName(), SWAPTION_VOLS, stateForm.getStateDate());
                          return FileResponseEntity.csvFile(csvFile, csvFileName);
                        }));
  }

  private ByteArrayResource getNodesCsv(
      CurveConfigMarketStateForm stateForm,
      List<String> selectedColumns,
      List<VolatilitySurfaceView> views) {
    VolatilityNodeCsvMapper mapper = new VolatilityNodeCsvMapper(selectedColumns);
    var quotes = marketDataQuotesSupport.getQuotes(stateForm);
    return views.stream()
        .map(
            s ->
                repository
                    .getSurfaceNodesValuesViews(
                        s.getCurveGroupId(), s.getEntityId(), s.getValidFrom(), quotes)
                    .getList()
                    .stream()
                    .sorted()
                    .map(node -> mapper.toCsvRow(s.getName(), node))
                    .toList())
        .flatMap(Collection::stream)
        .collect(collectingAndThen(toList(), l -> new CsvOutputFile(mapper.header(), l)))
        .writeToByteArray();
  }

  public Either<ErrorItem, FileResponseEntity> getAllSurfacesCapletNodesCsvBytes(
      String groupId,
      LocalDate stateDate,
      CurveConfigMarketStateForm stateForm,
      TableFilter tableFilter,
      List<String> selectedColumns) {
    return groupEither(groupId)
        .map(
            g -> {
              var surfaces = repository.getSurfaceViews(groupId, stateDate, active(), tableFilter);
              var csvFile = getCapletNodesCsv(stateForm, selectedColumns, surfaces);
              var prefix = getPrefix(CAP_FLOOR_VOLS, surfaces, VolatilitySurfaceView::getName);
              var csvFileName = nameWithTimeStamp(prefix, stateDate);
              return FileResponseEntity.csvFile(csvFile, csvFileName);
            });
  }

  public Either<ErrorItem, FileResponseEntity> getSurfaceCapletNodesCsvBytes(
      String groupId,
      String surfaceId,
      LocalDate version,
      CurveConfigMarketStateForm stateForm,
      List<String> selectedColumns) {
    return groupEither(groupId)
        .flatMap(
            g ->
                repository
                    .getActiveSurfaceView(groupId, surfaceId, version)
                    .map(
                        s -> {
                          var csvFile = getCapletNodesCsv(stateForm, selectedColumns, List.of(s));
                          var csvFileName =
                              nameWithTimeStamp(
                                  s.getName(), CAP_FLOOR_VOLS, stateForm.getStateDate());
                          return FileResponseEntity.csvFile(csvFile, csvFileName);
                        }));
  }

  private ByteArrayResource getCapletNodesCsv(
      CurveConfigMarketStateForm stateForm,
      List<String> selectedColumns,
      List<VolatilitySurfaceView> views) {
    VolatilityCapletNodeCsvMapper mapper = new VolatilityCapletNodeCsvMapper(selectedColumns);
    var quotes = marketDataQuotesSupport.getQuotes(stateForm);
    return views.stream()
        .map(
            s ->
                repository
                    .getSurfaceCapletNodesValuesViews(
                        s.getCurveGroupId(), s.getEntityId(), s.getValidFrom(), quotes)
                    .getList()
                    .stream()
                    .map(node -> mapper.toCsvRow(s.getName(), node))
                    .toList())
        .flatMap(Collection::stream)
        .collect(collectingAndThen(toList(), l -> new CsvOutputFile(mapper.header(), l)))
        .writeToByteArray();
  }

  public Either<ErrorItem, FileResponseEntity> getAllSurfacesSkewsCsvBytes(
      String groupId, LocalDate stateDate, List<String> selectedColumns) {
    return groupEither(groupId)
        .map(
            g -> {
              var surfaces =
                  repository.getSurfaceViews(groupId, stateDate, active(), emptyTableFilter());
              var csvFile = getSurfaceSkewsCsv(groupId, selectedColumns, surfaces);
              var prefix = getPrefix(SWAPTION_SKEWS, surfaces, VolatilitySurfaceView::getName);
              var csvFileName = nameWithTimeStamp(prefix, stateDate);
              return FileResponseEntity.csvFile(csvFile, csvFileName);
            });
  }

  public Either<ErrorItem, FileResponseEntity> getSurfaceSkewsCsvBytes(
      String groupId,
      String surfaceId,
      LocalDate version,
      LocalDate stateDate,
      List<String> selectedColumns) {
    return groupEither(groupId)
        .flatMap(g -> repository.getActiveSurfaceView(groupId, surfaceId, version))
        .map(
            s -> {
              var csvFile = getSurfaceSkewsCsv(groupId, selectedColumns, List.of(s));
              var csvFileName = nameWithTimeStamp(s.getName(), SWAPTION_SKEWS, stateDate);
              return FileResponseEntity.csvFile(csvFile, csvFileName);
            });
  }

  private ByteArrayResource getSurfaceSkewsCsv(
      String groupId, List<String> selectedColumns, List<VolatilitySurfaceView> views) {
    VolatilitySkewCsvMapper mapper = new VolatilitySkewCsvMapper(selectedColumns);
    return views.stream()
        .map(
            c ->
                repository.getSurfaceSkewsViews(groupId, c.getEntityId(), c.getValidFrom()).stream()
                    .map(node -> mapper.toCsvRow(c.getName(), node))
                    .toList())
        .flatMap(Collection::stream)
        .collect(collectingAndThen(toList(), l -> new CsvOutputFile(mapper.header(), l)))
        .writeToByteArray();
  }

  public Either<ErrorItem, FileResponseEntity> getSurfacesNodesMdkDefinitions(
      String groupId, LocalDate stateDate, List<String> surfaceIds, String curveConfigurationId) {
    return definitionsCsv(
        groupId,
        stateDate,
        surfaceIds,
        curveConfigurationId,
        SWAPTION_VOLS,
        VolatilitySurface::surfaceInstruments);
  }

  public Either<ErrorItem, FileResponseEntity> getSurfacesCapletMdkDefinitions(
      String groupId, LocalDate stateDate, List<String> surfaceIds, String curveConfigurationId) {
    return definitionsCsv(
        groupId,
        stateDate,
        surfaceIds,
        curveConfigurationId,
        CAP_FLOOR_VOLS,
        VolatilitySurface::capletNodeInstruments);
  }

  public Either<ErrorItem, FileResponseEntity> getSurfacesSkewsMdkDefinitions(
      String groupId, LocalDate stateDate, List<String> surfaceIds, String curveConfigurationId) {
    return definitionsCsv(
        groupId,
        stateDate,
        surfaceIds,
        curveConfigurationId,
        SWAPTION_SKEWS,
        VolatilitySurface::skewInstruments);
  }

  private Either<ErrorItem, FileResponseEntity> definitionsCsv(
      String groupId,
      LocalDate stateDate,
      List<String> surfaceIds,
      String curveConfigurationId,
      String filePrefix,
      Function<VolatilitySurface, List<InstrumentDefinition>> instrumentExtractor) {
    return groupEither(groupId)
        .map(c -> repository.getActiveSurfaces(groupId, new BitemporalDate(stateDate)))
        .map(ss -> VolatilitySurfacesUtils.filterSurfaces(ss, surfaceIds))
        .flatMap(
            surfaces ->
                definitionExportService.instrumentMdkDefinitions(
                    filePrefix,
                    surfaces,
                    instrumentExtractor,
                    VolatilitySurface::getName,
                    stateDate,
                    curveConfigurationId));
  }

  public Either<ErrorItem, FileResponseEntity> getSurfacesMdkDefinitions(
      String groupId, LocalDate stateDate, List<String> surfaceIds, String curveConfigurationId) {
    var fileName = zipNameWithTimeStamp("All_Vols_Skews_MDKDefinitions", stateDate);
    return groupEither(groupId)
        .map(c -> repository.getActiveSurfaces(groupId, new BitemporalDate(stateDate)))
        .map(ss -> VolatilitySurfacesUtils.filterSurfaces(ss, surfaceIds))
        .flatMap(
            surfaces ->
                FilesResponseBuilder.newResponse(fileName)
                    .addAll(
                        definitionExportService.instrumentMdkDefinitionFiles(
                            surfaces,
                            VolatilitySurface::surfaceInstruments,
                            stateDate,
                            getVolatilitiesPrefix(SWAPTION_VOLS, surfaces),
                            curveConfigurationId))
                    .addAll(
                        definitionExportService.instrumentMdkDefinitionFiles(
                            surfaces,
                            VolatilitySurface::skewInstruments,
                            stateDate,
                            getVolatilitiesPrefix(SWAPTION_SKEWS, surfaces),
                            curveConfigurationId))
                    .addAll(
                        definitionExportService.instrumentMdkDefinitionFiles(
                            surfaces,
                            VolatilitySurface::capletNodeInstruments,
                            stateDate,
                            getVolatilitiesPrefix(CAP_FLOOR_VOLS, surfaces),
                            curveConfigurationId))
                    .build());
  }

  private Either<ErrorItem, CurveGroupView> groupEither(String id) {
    return curveGroupRepository.getEither(id);
  }
}
