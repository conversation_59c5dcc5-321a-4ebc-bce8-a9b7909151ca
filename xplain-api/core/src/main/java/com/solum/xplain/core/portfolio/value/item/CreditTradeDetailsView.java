package com.solum.xplain.core.portfolio.value.item;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import com.solum.xplain.extensions.enums.CreditDocClause;
import com.solum.xplain.extensions.enums.CreditSector;
import com.solum.xplain.extensions.enums.CreditSeniority;
import com.solum.xplain.extensions.enums.PositionType;
import java.time.LocalDate;
import lombok.Data;

@Data
public class CreditTradeDetailsView {

  @ConfigurableViewQuery(sortable = true)
  private String reference;

  private String entityLongName;
  // Common Credit fields
  private CreditSector sector;

  @ConfigurableViewQuery(sortable = true)
  private PositionType protection;

  @ConfigurableViewQuery(sortable = true)
  private CreditSeniority seniority;

  @ConfigurableViewQuery(sortable = true)
  private CreditDocClause docClause;

  // Credit Index fields
  private Integer creditIndexVersion;
  private Integer creditIndexSeries;
  // Credit Index tranche fields
  private String creditIndexTranche;

  @ConfigurableViewQuery(sortable = true)
  private Double upfront;

  @ConfigurableViewQuery(sortable = true)
  private LocalDate upfrontDate;
}
