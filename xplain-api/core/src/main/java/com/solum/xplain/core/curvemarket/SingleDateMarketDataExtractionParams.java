package com.solum.xplain.core.curvemarket;

import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceTypeResolver;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.mdvalue.MarketDataValueService;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketData;
import com.solum.xplain.core.mdvalue.value.ResolvedMarketDataValueView;
import io.atlassian.fugue.Either;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.EqualsAndHashCode;
import org.springframework.lang.NonNull;

@EqualsAndHashCode(callSuper = true)
public class SingleDateMarketDataExtractionParams extends MarketDataExtractionParams {

  public static SingleDateMarketDataExtractionParams mdParamsWithCurves(
      CurveConfigMarketStateKey stateKey, List<String> discountCurves) {
    return new SingleDateMarketDataExtractionParams(
        stateKey, mdParamsWithCurvesResolver(stateKey.getPriceRequirements(), discountCurves));
  }

  public static SingleDateMarketDataExtractionParams mdParamsWithoutCurves(
      CurveConfigMarketStateKey stateKey) {
    return new SingleDateMarketDataExtractionParams(
        stateKey, mdParamsWithoutCurvesResolver(stateKey.getPriceRequirements()));
  }

  protected SingleDateMarketDataExtractionParams(
      @NonNull CurveConfigMarketStateKey stateKey,
      @NonNull InstrumentPriceTypeResolver priceTypeResolver) {
    super(stateKey, priceTypeResolver);
  }

  @Override
  public Either<ErrorItem, Map<String, ResolvedMarketData>> resolvedValuesByKey(
      MarketDataValueService marketDataValueService) {

    Either<ErrorItem, Map<String, ResolvedMarketDataValueView>> results =
        marketDataValueService.getResolvedValuesByKey(
            getMarketDataGroupId(),
            getStateDate(),
            getCurveDate(),
            Arrays.asList(CoreAssetGroup.values()));

    return results.map(r -> (Map<String, ResolvedMarketData>) (Map<String, ?>) r);
  }
}
