package com.solum.xplain.core.curvegroup.volatility.value.surface;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.curvegroup.conventions.VolatilitySurfaceSwapConvention;
import com.solum.xplain.core.curvegroup.volatility.classifier.CapletValuationModel;
import com.solum.xplain.core.curvegroup.volatility.classifier.SwaptionValuationModel;
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class VolatilitySurfaceView implements VolatilitySurfaceSwapConvention {
  // Versioned entity fields
  private String entityId;
  private LocalDate validFrom;
  private String comment;
  private LocalDateTime recordDate;
  private State state;
  private String modifiedBy;
  private LocalDateTime modifiedAt;

  // Versioned named entity fields
  private String name;

  // Surface fields
  private String curveGroupId;
  private VolatilitySurfaceType skewType;
  private boolean sabr;
  private Double sabrBeta;
  private Double sabrShift;

  @JsonProperty("xInterpolator")
  private String xInterpolator;

  @JsonProperty("xExtrapolatorLeft")
  private String xExtrapolatorLeft;

  @JsonProperty("xExtrapolatorRight")
  private String xExtrapolatorRight;

  @JsonProperty("yInterpolator")
  private String yInterpolator;

  @JsonProperty("yExtrapolatorLeft")
  private String yExtrapolatorLeft;

  @JsonProperty("yExtrapolatorRight")
  private String yExtrapolatorRight;

  private CapletValuationModel capletValuationModel;

  // Surface fields - calculated
  private Integer numberOfSwaptionVolatilityNodes;
  private Integer numberOfCapletVolatilityNodes;
  private Integer numberOfSkewNodes;

  public String getValuationModel() {
    return SwaptionValuationModel.NORMAL.name();
  }
}
