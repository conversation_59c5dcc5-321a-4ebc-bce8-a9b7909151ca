package com.solum.xplain.core.portfolio.csv.mapper;

import static com.opengamma.strata.basics.date.BusinessDayConventions.FOLLOWING;
import static com.solum.xplain.core.classifiers.CreditTranches.formatTrancheLabel;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_END_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_START_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_ACCRUAL_FREQUENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_CORP_TICKER;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_CURRENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_DOC_CLAUSE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_ENTITY_LONG_NAME;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_FIXED_RATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_INDEX_SERIES;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_INDEX_TRANCHE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_INDEX_VERSION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_REFERENCE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_SECTOR;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_SENIORITY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_POSITION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_AMOUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_DATE_ADJ_CONVENTION;
import static com.solum.xplain.core.portfolio.csv.DayCountCsvUtils.toExportLabel;
import static com.solum.xplain.extensions.enums.BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.date.BusinessDayConvention;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DayCounts;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.solum.xplain.core.classifiers.CdsIndex;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

@Component
public final class CommonCreditCsvMapper {
  public static final BusinessDayConvention CDS_PAYMENT_DATE_CONVENTION = FOLLOWING;
  public static final BusinessDayAdjustmentType CDS_PAYMENT_DATE_ADJUSTMENT_TYPE =
      ACCRUAL_AND_PAYMENT;
  public static final StubConvention CDS_STUB_CONVENTION = StubConvention.SMART_INITIAL;
  public static final DayCount CDS_DAY_COUNT = DayCounts.ACT_360;

  public Iterable<CsvField> toCsvFields(CoreProductType type, TradeDetails trade) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    var creditTradeDetails = trade.getCreditTradeDetails();
    builder.add(new CsvField(TRADE_POSITION, trade.getPositionType()));
    builder.add(new CsvField(TRADE_CREDIT_DOC_CLAUSE, creditTradeDetails.getDocClause()));
    builder.add(new CsvField(TRADE_CREDIT_SECTOR, creditTradeDetails.getSector()));
    builder.add(new CsvField(TRADE_CREDIT_SENIORITY, creditTradeDetails.getSeniority()));
    builder.add(
        new CsvField(
            TRADE_CREDIT_CORP_TICKER,
            creditTradeDetails.getCorpTicker() == null
                ? null
                : creditTradeDetails.getCorpTicker().toUpperCase()));
    builder.add(new CsvField(TRADE_CREDIT_REFERENCE, creditTradeDetails.getReference()));
    builder.add(
        new CsvField(
            TRADE_CREDIT_ENTITY_LONG_NAME,
            formatLongName(type, creditTradeDetails.getEntityLongName())));
    builder.add(new CsvField(TRADE_CREDIT_INDEX_SERIES, creditTradeDetails.getCreditIndexSeries()));
    builder.add(
        new CsvField(TRADE_CREDIT_INDEX_VERSION, creditTradeDetails.getCreditIndexVersion()));
    builder.add(
        new CsvField(
            TRADE_CREDIT_INDEX_TRANCHE,
            formatTrancheLabel(creditTradeDetails.getCreditIndexTranche())));
    builder.add(new CsvField(TRADE_CREDIT_DAY_COUNT, toExportLabel(CDS_DAY_COUNT.toString())));
    builder.add(new CsvField(TRADE_ACCRUAL_SCHEDULE_START_DATE, trade.getStartDate()));
    builder.add(new CsvField(TRADE_ACCRUAL_SCHEDULE_END_DATE, trade.getEndDate()));
    builder.add(new CsvField(TRADE_PREMIUM_DATE, creditTradeDetails.getUpfrontDate()));
    builder.add(new CsvField(TRADE_PREMIUM_AMOUNT, creditTradeDetails.getUpfront()));
    builder.add(
        new CsvField(TRADE_PREMIUM_DATE_ADJ_CONVENTION, creditTradeDetails.getUpfrontConvention()));
    builder.add(new CsvField(TRADE_BUSINESS_DAY_CONVENTION, CDS_PAYMENT_DATE_CONVENTION.getName()));
    builder.add(
        new CsvField(TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE, CDS_PAYMENT_DATE_ADJUSTMENT_TYPE.name()));
    builder.add(new CsvField(TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION, CDS_STUB_CONVENTION));

    trade
        .tradePositionLeg()
        .ifPresent(
            leg -> {
              builder.add(new CsvField(TRADE_CREDIT_CURRENCY, leg.getCurrency()));
              builder.add(new CsvField(TRADE_CREDIT_NOTIONAL, leg.getNotional()));
              builder.add(new CsvField(TRADE_CREDIT_FIXED_RATE, leg.getInitialValue()));
              builder.add(new CsvField(TRADE_CREDIT_ACCRUAL_FREQUENCY, leg.getPaymentFrequency()));
            });

    return builder.build();
  }

  private String formatLongName(CoreProductType type, @Nullable String entityLongName) {
    return switch (type) {
      case CDS -> entityLongName;
      case CREDIT_INDEX, CREDIT_INDEX_TRANCHE -> CdsIndex.labelValueOf(entityLongName);
      default -> throw new IllegalArgumentException(String.format("Unhandled trade type %s", type));
    };
  }
}
