package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CUSTOM_FIELD_PREFIX;

import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.trade.CustomTradeField;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CustomTradeFieldsCsvMapper {
  public static final CustomTradeFieldsCsvMapper MAPPER = new CustomTradeFieldsCsvMapper();

  public List<CsvField> mapCustomFields(List<CustomTradeField> customTradeFields) {
    if (customTradeFields == null) {
      return List.of();
    }
    return customTradeFields.stream().map(this::toCsvField).toList();
  }

  public List<String> formatToHeaders(List<String> customFieldNames) {
    return customFieldNames.stream().map(this::toSourceHeader).toList();
  }

  private CsvField toCsvField(CustomTradeField customField) {
    var header = toSourceHeader(customField.getExternalFieldId());
    return new CsvField(header, customField.getValue());
  }

  private String toSourceHeader(String header) {
    return TRADE_CUSTOM_FIELD_PREFIX + header;
  }
}
