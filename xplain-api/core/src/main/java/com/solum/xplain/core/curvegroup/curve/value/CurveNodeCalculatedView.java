package com.solum.xplain.core.curvegroup.curve.value;

import com.solum.xplain.core.curvegroup.conventions.ClearingHouse;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CurveNodeCalculatedView {
  private String type;
  private String convention;
  private String period;
  private String serialFuture;
  private String fraSettlement;
  private ClearingHouse clearingHouse;

  private String quoteId;
  private String dataSource;
  private String ticker;
  private BigDecimal marketValue;
  private BigDecimal marketValueAsk;
  private BigDecimal marketValueMid;
  private BigDecimal marketValueBid;
  private Double rateValue;
  private Double discountFactor;
  private InstrumentPriceType calibrationPriceType;
  private LocalDate date;

  private String key;
  private String tenor;
  private String instrument;
}
