package com.solum.xplain.core.curvegroup.curvegroup;

import com.solum.xplain.core.curvegroup.curvegroup.value.CurveCounts;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupCountedView;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import org.mapstruct.Mapper;

@Mapper
public interface CurveGroupMapper {

  CurveGroupCountedView toCountedView(CurveGroupView view, CurveCounts curveCounts);
}
