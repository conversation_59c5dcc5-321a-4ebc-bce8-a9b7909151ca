package com.solum.xplain.core.curvegroup.curvebond.csv.node;

import static com.solum.xplain.core.curvegroup.curvebond.csv.node.BondCurveNodeCsvFields.COUPON;
import static com.solum.xplain.core.curvegroup.curvebond.csv.node.BondCurveNodeCsvFields.CURVE_NAME_FIELD;
import static com.solum.xplain.core.curvegroup.curvebond.csv.node.BondCurveNodeCsvFields.CUSIP;
import static com.solum.xplain.core.curvegroup.curvebond.csv.node.BondCurveNodeCsvFields.MATURITY_DATE;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvColumn;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvMapper;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeCalculatedView;
import java.util.List;

public class BondCurveNodeCsvMapper extends CsvMapper<BondCurveNodeCalculatedView> {

  private static final List<CsvColumn<BondCurveNodeCalculatedView>> COLUMNS =
      List.of(
          CsvColumn.date(
              BondCurveNodeCalculatedView.Fields.maturityDate,
              MATURITY_DATE,
              BondCurveNodeCalculatedView::getMaturityDate),
          CsvColumn.text(
              BondCurveNodeCalculatedView.Fields.cusip,
              CUSIP,
              BondCurveNodeCalculatedView::getCusip),
          CsvColumn.decimal(
              BondCurveNodeCalculatedView.Fields.coupon,
              COUPON,
              BondCurveNodeCalculatedView::getCoupon));

  public BondCurveNodeCsvMapper() {
    super(COLUMNS, null);
  }

  @Override
  public List<String> header() {
    ImmutableList.Builder<String> builder = ImmutableList.builder();
    builder.add(CURVE_NAME_FIELD);
    builder.addAll(super.header());
    return builder.build();
  }

  public CsvRow toCsvRow(String curveName, BondCurveNodeCalculatedView node) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(CURVE_NAME_FIELD, curveName));
    builder.addAll(super.toCsvFields(node));
    return new CsvRow(builder.build());
  }
}
