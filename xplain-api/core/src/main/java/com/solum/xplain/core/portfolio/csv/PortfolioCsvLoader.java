package com.solum.xplain.core.portfolio.csv;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.getFieldValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseIdentifier;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.value.AllowedTeamsForm;
import com.solum.xplain.core.company.CompanyTeamValidationService;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm;
import com.solum.xplain.core.portfolio.value.PortfolioUniqueKey;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import io.atlassian.fugue.extensions.step.Steps;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode
@AllArgsConstructor(staticName = "newOf")
public class PortfolioCsvLoader extends GenericCsvLoader<PortfolioCreateForm, PortfolioUniqueKey> {
  static final String TEAM_NAMES = "Team names";
  static final String ALLOW_ALL_TEAMS = "Allow all teams";
  static final String COMPANY_EXTERNAL_ID = "Company ID";
  static final String ENTITY_EXTERNAL_ID = "Entity ID";
  static final String EXTERNAL_ID = "Portfolio ID";

  static final String PORTFOLIO_NAME = "Portfolio Name";
  static final String DESCRIPTION = "Description";
  private static final String INVALID_TEAMS = "Not all teams are allowed for portfolio!";
  private final Map<String, String> companiesMap;
  private final Map<String, CompanyLegalEntityLookup> legalEntities;
  private final Map<String, String> teamsMap;
  private final Set<String> userTeamIds;
  private final CompanyTeamValidationService companyTeamValidationService;

  @Override
  protected CsvParserResultBuilder<PortfolioCreateForm, PortfolioUniqueKey> createResult(
      ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        PortfolioUniqueKey::fromForm,
        PortfolioUniqueKey::externalPortfolioId,
        parsingMode.failOnError());
  }

  @Override
  protected List<String> getFileHeaders() {
    return List.of(
        TEAM_NAMES,
        ALLOW_ALL_TEAMS,
        COMPANY_EXTERNAL_ID,
        ENTITY_EXTERNAL_ID,
        EXTERNAL_ID,
        DESCRIPTION);
  }

  @Override
  protected Either<ErrorItem, PortfolioCreateForm> parseLine(CsvRow row) {
    return Steps.begin(getFieldValue(row, COMPANY_EXTERNAL_ID, this::parseCompanyId))
        .then(cId -> getFieldValue(row, ENTITY_EXTERNAL_ID, id -> parseLegalEntityId(id, cId)))
        .then((companyId, entityId) -> parseTeams(companyId, entityId, row))
        .then(() -> parseExternalPortfolioId(row))
        .yield((cid, eId, teams, externalId) -> toForm(cid, eId, teams, externalId, row));
  }

  private PortfolioCreateForm toForm(
      String companyId,
      String entityId,
      AllowedTeamsForm teamsForm,
      String externalPortfolioId,
      CsvRow csvRow) {
    PortfolioCreateForm form = new PortfolioCreateForm();
    form.setCompanyId(companyId);
    form.setExternalPortfolioId(externalPortfolioId);
    form.setEntityId(entityId);
    form.setAllowedTeamsForm(teamsForm);

    csvRow.findValue(DESCRIPTION).ifPresent(form::setDescription);

    var portfolioName = csvRow.findValue(PORTFOLIO_NAME).orElse(externalPortfolioId);
    form.setName(portfolioName);

    return form;
  }

  private String parseCompanyId(String value) {
    String companyExternalId = validateValue(value, companiesMap::keySet);
    return companiesMap.get(companyExternalId);
  }

  private String parseLegalEntityId(String value, String companyId) {
    var entityLookup = legalEntities.getOrDefault(companyId, CompanyLegalEntityLookup.empty());
    String entityExternalId = validateValue(value, entityLookup::validExternalIds);
    return entityLookup.entityId(entityExternalId);
  }

  private Either<ErrorItem, AllowedTeamsForm> parseTeams(
      String companyId, String entityId, CsvRow row) {
    boolean allowAllTeams =
        row.findValue(ALLOW_ALL_TEAMS).map(CsvLoaderUtils::parseBoolean).orElse(false);
    if (allowAllTeams) {
      return Either.right(new AllowedTeamsForm(true, null));
    }
    return CsvLoaderUtils.getFieldValue(row, TEAM_NAMES, this::parseTeamNames)
        .map(ids -> new AllowedTeamsForm(false, ids))
        .flatMap(form -> validateTeams(companyId, entityId, form));
  }

  private List<String> parseTeamNames(String namesString) {
    return Arrays.stream(namesString.split("\\|"))
        .map(name -> validateValue(name, teamsMap::keySet))
        .map(teamsMap::get)
        .filter(userTeamIds::contains)
        .toList();
  }

  private Either<ErrorItem, AllowedTeamsForm> validateTeams(
      String companyId, String entityId, AllowedTeamsForm teamsForm) {
    boolean validTeams =
        companyTeamValidationService.validCompanyEntityTeams(entityId, companyId, teamsForm);

    return Eithers.cond(validTeams, PARSING_ERROR.entity(INVALID_TEAMS), teamsForm);
  }

  private Either<ErrorItem, String> parseExternalPortfolioId(CsvRow row) {
    return Checked.now(() -> parseIdentifier(row, EXTERNAL_ID))
        .toEither()
        .leftMap(e -> rowParsingError(row, e));
  }
}
