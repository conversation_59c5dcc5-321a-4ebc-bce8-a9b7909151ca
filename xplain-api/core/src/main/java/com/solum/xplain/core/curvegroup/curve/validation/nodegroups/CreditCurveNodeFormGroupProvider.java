package com.solum.xplain.core.curvegroup.curve.validation.nodegroups;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType;
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveNodeForm;
import java.util.List;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class CreditCurveNodeFormGroupProvider
    implements DefaultGroupSequenceProvider<CreditCurveNodeForm> {

  @Override
  public List<Class<?>> getValidationGroups(CreditCurveNodeForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(CreditCurveNodeForm.class);
    if (form != null && form.getType() != null) {
      if (form.getType().equals(CreditCurveNodeType.CDS)) {
        builder.add(CreditCurveNodeFormCdsGroup.class);
      } else {
        builder.add(CreditCurveNodeFormGenericCreditGroup.class);
      }
    }
    return builder.build();
  }
}
