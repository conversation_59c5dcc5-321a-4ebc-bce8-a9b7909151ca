package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_FXOPTION_CALL_PUT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_POSITION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REF_SEC_FX_RATE;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvMapper;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class FxOptionCsvMapper implements ProductCsvMapper {

  private final FxFwdCsvMapper fxFwdCsvMapper;

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.FXOPT);
  }

  @Override
  public Iterable<CsvField> toCsvFields(TradeDetails tradeDetails) {
    var optionDetails = tradeDetails.getOptionTradeDetails();
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(TRADE_REF_SEC_FX_RATE, optionDetails.getStrike()));
    builder.addAll(fxFwdCsvMapper.toCsvFields(tradeDetails));

    builder.add(new CsvField(TRADE_POSITION, tradeDetails.getPositionType()));
    builder.addAll(OptionDetailsCsvMapper.toCsvFields(optionDetails));
    builder.add(new CsvField(TRADE_FXOPTION_CALL_PUT, optionDetails.getCallPutType()));
    return builder.build();
  }
}
