package com.solum.xplain.core.portfolio.builder;

import static com.solum.xplain.core.portfolio.builder.validation.TradeBuilderValidatorUtils.validateTradeCurrency;
import static com.solum.xplain.core.portfolio.calendars.TradeCalendarUtils.getSwapTradeCalendar;

import com.solum.xplain.core.portfolio.trade.OptionTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeDefaultUtils;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.extensions.enums.BusinessDayAdjustmentType;
import com.solum.xplain.extensions.enums.PositionType;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalTime;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.ToString;

@Builder
@ToString
@EqualsAndHashCode
public class ResolvableSwaptionDetails implements ResolvableTradeDetails {
  @NonNull private final PositionType positionType;
  @NonNull private final LocalDate startDate;
  @NonNull private final LocalDate endDate;
  @NonNull private final ResolvableTradeLegDetails payLeg;
  @NonNull private final ResolvableTradeLegDetails receiveLeg;
  @NonNull private final String businessDayConvention;
  @NonNull private final BusinessDayAdjustmentType businessDayAdjustmentType;

  private final String stubConvention;
  private final String rollConvention;
  private final LocalDate firstRegularStartDate;
  private final LocalDate lastRegularEndDate;
  private final boolean notionalScheduleInitialExchange;
  private final boolean notionalScheduleFinalExchange;

  @NonNull private final LocalDate expiryDate;
  @NonNull private final String expiryDateConvention;
  @NonNull private final LocalTime expiryTime;
  @NonNull private final String expiryZone;
  @NonNull private final Double premiumValue;
  @NonNull private final LocalDate premiumDate;
  @NonNull private final String premiumDateConvention;
  @NotNull private final String swaptionSettlement;

  @Override
  public TradeDetails toTradeDetails(TradeInfoDetails tradeInfo) {
    validateTradeCurrency(
        tradeInfo.getTradeCurrency(),
        payLeg.getCurrency().getCode(),
        receiveLeg.getCurrency().getCode());

    var payLegDetails = payLeg.toTradeLegDetails();
    var receiveLegDetails = receiveLeg.toTradeLegDetails();
    TradeDetails tradeDetails = TradeDetails.newOf(tradeInfo);
    tradeDetails.setPositionType(positionType);
    tradeDetails.setStartDate(startDate);
    tradeDetails.setEndDate(endDate);
    tradeDetails.setPayLeg(payLegDetails);
    tradeDetails.setReceiveLeg(receiveLegDetails);
    tradeDetails.setStubConvention(TradeDefaultUtils.parseStubOrDefault(stubConvention).getName());
    tradeDetails.setRollConvention(TradeDefaultUtils.parseRollOrDefault(rollConvention).getName());
    tradeDetails.setCalendar(getSwapTradeCalendar(payLegDetails, receiveLegDetails));
    tradeDetails.setBusinessDayConvention(businessDayConvention);
    tradeDetails.setBusinessDayAdjustmentType(businessDayAdjustmentType);
    tradeDetails.setFirstRegularStartDate(firstRegularStartDate);
    tradeDetails.setLastRegularEndDate(lastRegularEndDate);
    tradeDetails.setNotionalScheduleInitialExchange(notionalScheduleInitialExchange);
    tradeDetails.setNotionalScheduleFinalExchange(notionalScheduleFinalExchange);
    tradeDetails.setOptionTradeDetails(optionDetails());
    return tradeDetails;
  }

  private OptionTradeDetails optionDetails() {
    OptionTradeDetails optionDetails = new OptionTradeDetails();
    optionDetails.setExpiryDate(expiryDate);
    optionDetails.setExpiryTime(expiryTime);
    optionDetails.setExpiryZone(expiryZone);
    optionDetails.setPremiumValue(premiumValue);
    optionDetails.setPremiumDate(premiumDate);
    optionDetails.setPremiumDateConvention(premiumDateConvention);
    optionDetails.setSwaptionSettlementType(swaptionSettlement);
    optionDetails.setPremiumCurrency(payLeg.getCurrency().toString());
    optionDetails.setExpiryDateConvention(expiryDateConvention);
    return optionDetails;
  }
}
