package com.solum.xplain.core.curvegroup.conventions.index;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.collect.named.Named;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import java.util.List;
import lombok.Data;

@Data
public abstract class IndexCurveConvention implements ConventionalCurveConvention {
  private final String name;
  private final FloatingRateIndex index;
  private final List<Named> nodeConventions;

  @Override
  public String getKey() {
    return name;
  }

  @Override
  public Currency getCurrency() {
    return index.getCurrency();
  }
}
