package com.solum.xplain.core.curvegroup.curvebond.value;

import com.solum.xplain.core.curvegroup.curvebond.validation.ValidBondCurveName;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ValidBondCurveName
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BondCurveForm extends BondCurveUpdateForm {

  @NotEmpty private String name;
}
