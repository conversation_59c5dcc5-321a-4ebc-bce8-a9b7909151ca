package com.solum.xplain.core.portfolio.validation;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.portfolio.form.CapFloorTradeForm;
import com.solum.xplain.core.portfolio.validation.groups.WithPremiumTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.WithoutPremiumTradeGroup;
import java.util.List;
import java.util.Objects;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

public class CapFloorTradeGroupProvider implements DefaultGroupSequenceProvider<CapFloorTradeForm> {
  @Override
  public List<Class<?>> getValidationGroups(CapFloorTradeForm form) {
    ImmutableList.Builder<Class<?>> builder = ImmutableList.builder();
    builder.add(CapFloorTradeForm.class);
    if (form == null) {
      return builder.build();
    }

    if (Objects.isNull(form.getPremiumValue())) {
      builder.add(WithoutPremiumTradeGroup.class);
    } else {
      builder.add(WithPremiumTradeGroup.class);
    }

    return builder.build();
  }
}
