package com.solum.xplain.core.portfolio.trade;

import com.opengamma.strata.basics.schedule.RollConvention;
import com.opengamma.strata.basics.schedule.RollConventions;
import com.opengamma.strata.basics.schedule.StubConvention;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TradeDefaultUtils {

  public static RollConvention parseRollOrDefault(String rollConvention) {
    return rollConvention == null ? RollConventions.NONE : RollConvention.of(rollConvention);
  }

  public static StubConvention parseStubOrDefault(String stubConvention) {
    return stubConvention == null
        ? StubConvention.SMART_INITIAL
        : StubConvention.of(stubConvention);
  }
}
