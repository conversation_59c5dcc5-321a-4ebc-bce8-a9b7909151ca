package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository;
import com.solum.xplain.core.company.repository.CompanyRepository;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@NullMarked
public class PortfolioTeamFilterProvider {
  private final CompanyRepository companyRepository;
  private final CompanyLegalEntityRepository entityRepository;
  private final AuthenticationContext authenticationContext;

  public PortfolioTeamFilter provideFilter(XplainPrincipal user) {
    var excludedCompanies =
        companyRepository
            .streamExcludedCompanyReferencesForUser(user)
            .map(EntityReference::getEntityId)
            .toList();
    var excludedEntities =
        entityRepository
            .streamExcludedLegalEntityReferencesForUser(user, excludedCompanies)
            .map(EntityReference::getEntityId)
            .toList();

    return PortfolioTeamFilter.portfolioTeamFilter(user, excludedCompanies, excludedEntities);
  }

  public PortfolioTeamFilter provideFilter() {
    return provideFilter(authenticationContext.currentUser());
  }
}
