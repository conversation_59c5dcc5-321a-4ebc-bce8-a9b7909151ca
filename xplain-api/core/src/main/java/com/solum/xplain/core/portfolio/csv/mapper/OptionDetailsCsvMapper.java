package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.common.csv.CsvFieldUtils.nullSafeToString;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_EXPIRY_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_EXPIRY_DATE_ADJ_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_EXPIRY_TIME;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_EXPIRY_ZONE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_AMOUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_CURRENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_DATE_ADJ_CONVENTION;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.trade.OptionTradeDetails;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class OptionDetailsCsvMapper {
  public static Iterable<CsvField> toCsvFields(OptionTradeDetails optionDetails) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(TRADE_PREMIUM_AMOUNT, optionDetails.getPremiumValue()));
    builder.add(new CsvField(TRADE_PREMIUM_DATE, optionDetails.getPremiumDate()));
    builder.add(
        new CsvField(TRADE_PREMIUM_DATE_ADJ_CONVENTION, optionDetails.getPremiumDateConvention()));
    builder.add(new CsvField(TRADE_PREMIUM_CURRENCY, optionDetails.getPremiumCurrency()));
    builder.add(new CsvField(TRADE_EXPIRY_DATE, optionDetails.getExpiryDate()));
    builder.add(
        new CsvField(TRADE_EXPIRY_DATE_ADJ_CONVENTION, optionDetails.getExpiryDateConvention()));
    builder.add(new CsvField(TRADE_EXPIRY_TIME, nullSafeToString(optionDetails.getExpiryTime())));
    builder.add(new CsvField(TRADE_EXPIRY_ZONE, optionDetails.getExpiryZone()));
    return builder.build();
  }
}
