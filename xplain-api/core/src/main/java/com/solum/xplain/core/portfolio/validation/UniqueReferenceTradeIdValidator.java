package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.portfolio.ReferenceTradesProvider;
import com.solum.xplain.core.portfolio.form.DefaultTradeForm;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import java.util.Optional;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class UniqueReferenceTradeIdValidator
    implements ConstraintValidator<UniqueReferenceTradeId, DefaultTradeForm> {

  private final ReferenceTradesProvider referenceTradesProvider;
  private final RequestPathVariablesSupport requestPathVariablesSupport;

  @Override
  public boolean isValid(DefaultTradeForm value, ConstraintValidatorContext context) {
    if (value != null && value.getVersionForm() != null) {
      final String internalId = requestPathVariablesSupport.getPathVariable("tradeEntityId");
      overrideMessage(context);

      return Steps.begin(Optional.ofNullable(value.getExternalTradeId()).filter(s -> !s.isEmpty()))
          .then(() -> Optional.of(value.stateDate()))
          .yield((extId, date) -> isUniqueTrade(date, extId, internalId))
          .orElse(true);
    }
    return true;
  }

  private boolean isUniqueTrade(LocalDate stateDate, String externalId, String internalId) {
    return !referenceTradesProvider.existsActiveReferenceTrade(externalId, stateDate, internalId);
  }

  private void overrideMessage(ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate("NotUnique")
        .addPropertyNode("externalTradeId")
        .addConstraintViolation();
  }
}
