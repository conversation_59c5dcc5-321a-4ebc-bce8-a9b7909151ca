package com.solum.xplain.core.curvegroup.volatility.value.caplet;

import com.solum.xplain.core.common.value.MatrixNode;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CapletVolatilityNodeView implements MatrixNode {

  private String tenor;
  private BigDecimal strike;
  private String key;

  @Override
  public String getColumn() {
    return strike.toString();
  }

  @Override
  public String getRow() {
    return getTenor();
  }
}
