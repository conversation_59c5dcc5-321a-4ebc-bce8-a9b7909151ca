package com.solum.xplain.core.portfolio.trade;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class OnboardingDetails {

  @ConfigurableViewQuery(sortable = true)
  private Double dealCost;

  @ConfigurableViewQuery(sortable = true)
  private Double accountingCost;

  @ConfigurableViewQuery(sortable = true)
  private LocalDate vendorOnboardingDate;

  @ConfigurableViewQuery(sortable = true)
  private Boolean xplainCostCheck;

  @ConfigurableViewQuery(sortable = true)
  private Boolean marketConfCheck;

  @ConfigurableViewQuery(sortable = true)
  private Boolean vendorCheck;

  private LocalDate xplainCheckVerified;
  private LocalDate marketCheckVerified;
  private LocalDate vendorCheckVerified;

  public static OnboardingDetails defaultOnboardingDetails() {
    var onboardingDetails = new OnboardingDetails();
    onboardingDetails.setXplainCostCheck(false);
    onboardingDetails.setMarketConfCheck(false);
    onboardingDetails.setVendorCheck(false);
    return onboardingDetails;
  }
}
