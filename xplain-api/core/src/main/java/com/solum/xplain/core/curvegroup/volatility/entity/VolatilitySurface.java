package com.solum.xplain.core.curvegroup.volatility.entity;

import static com.solum.xplain.core.common.CollectionUtils.nullSafeIsEqualCollection;
import static com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityMatrixValues.configurationFromList;
import static com.solum.xplain.core.utils.TenorUtils.toYearFraction;
import static io.atlassian.fugue.Either.right;
import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.emptyIfNull;
import static org.slf4j.LoggerFactory.getLogger;

import com.google.common.collect.Streams;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.RateIndex;
import com.opengamma.strata.collect.array.DoubleArray;
import com.opengamma.strata.collect.array.DoubleMatrix;
import com.opengamma.strata.data.MarketData;
import com.opengamma.strata.market.ValueType;
import com.opengamma.strata.market.curve.interpolator.CurveExtrapolator;
import com.opengamma.strata.market.curve.interpolator.CurveInterpolator;
import com.opengamma.strata.market.surface.interpolator.GridSurfaceInterpolator;
import com.opengamma.strata.pricer.capfloor.IborCapletFloorletVolatilitiesName;
import com.opengamma.strata.pricer.capfloor.IborCapletFloorletVolatilityDefinition;
import com.opengamma.strata.pricer.capfloor.SurfaceIborCapletFloorletVolatilityBootstrapDefinition;
import com.opengamma.strata.pricer.option.RawOptionData;
import com.opengamma.strata.product.swap.type.FixedFloatSwapConvention;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupEntry;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.conventions.VolatilitySurfaceSwapConvention;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.curvegroup.volatility.VolatilityMapper;
import com.solum.xplain.core.curvegroup.volatility.classifier.CapletValuationModel;
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityMatrixValues;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeValueView;
import com.solum.xplain.core.curvemarket.node.NodeInstrumentWrapper;
import com.solum.xplain.core.curvemarket.node.ValidNodesFilter;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.mapping.MarketDataUtils;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = VolatilitySurface.VOLATILITY_SURFACE_COLLECTION)
@FieldNameConstants
public class VolatilitySurface extends VersionedNamedEntity
    implements VolatilitySurfaceSwapConvention, CurveGroupEntry {
  public static final String VOLATILITY_SURFACE_COLLECTION = "volatilitySurface";

  private static final Logger LOG = getLogger(VolatilitySurface.class);

  private String curveGroupId;

  private String xInterpolator;

  private String xExtrapolatorLeft;

  private String xExtrapolatorRight;

  private String yInterpolator;

  private String yExtrapolatorLeft;

  private String yExtrapolatorRight;

  private boolean sabr;

  private Double sabrBeta;

  private Double sabrShift;

  private VolatilitySurfaceType skewType;

  private CapletValuationModel capletValuationModel;

  private List<VolatilitySurfaceNode> nodes = new ArrayList<>();

  private List<CapletVolatilityNode> capletVolatilities = new ArrayList<>();

  private List<VolatilitySurfaceSkew> skewNodes = new ArrayList<>();

  public static VolatilitySurface newOf() {
    VolatilitySurface c = new VolatilitySurface();
    c.setEntityId(ObjectId.get().toString());
    c.setRecordDate(LocalDateTime.now());
    c.setState(State.ACTIVE);
    return c;
  }

  public RateIndex applicableRateIndex() {
    return swapConvention().getFloatingLeg().getIndex();
  }

  public boolean isOvernight() {
    return applicableRateIndex() instanceof OvernightIndex;
  }

  public void orderNodes() {
    var orderedNodes =
        nodes.stream()
            .sorted(
                comparing(VolatilitySurfaceNode::getExpiry)
                    .thenComparing((VolatilitySurfaceNode n) -> toYearFraction(n.getTenor())))
            .toList();
    setNodes(orderedNodes);
  }

  public void orderCapletNodes() {
    var orderedNodes =
        capletVolatilities.stream()
            .sorted(
                comparing((CapletVolatilityNode n) -> toYearFraction(n.getTenor()))
                    .thenComparing(CapletVolatilityNode::getStrike))
            .toList();
    setCapletVolatilities(orderedNodes);
  }

  public Either<ErrorItem, IborCapletFloorletVolatilityDefinition> capletDefinition() {
    try {
      if (isOvernight()) {
        return Either.left(
            new ErrorItem(Error.VALIDATION_ERROR, "Overnight Cap floor not supported"));
      }
      var iborIndex = (IborIndex) applicableRateIndex();
      var surfaceInterpolator = gridSurfaceInterpolator();
      var definition =
          SurfaceIborCapletFloorletVolatilityBootstrapDefinition.of(
              IborCapletFloorletVolatilitiesName.of(getName()),
              iborIndex,
              iborIndex.getDayCount(),
              surfaceInterpolator);
      return right(definition);
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return Either.left(new ErrorItem(Error.CALIBRATION_ERROR, ex.getMessage()));
    }
  }

  private GridSurfaceInterpolator gridSurfaceInterpolator() {
    return GridSurfaceInterpolator.of(
        CurveInterpolator.of(xInterpolator),
        CurveExtrapolator.of(xExtrapolatorLeft),
        CurveExtrapolator.of(xExtrapolatorRight),
        CurveInterpolator.of(yInterpolator),
        CurveExtrapolator.of(yExtrapolatorLeft),
        CurveExtrapolator.of(yExtrapolatorRight));
  }

  public Either<ErrorItem, RawOptionData> rawOptionData(
      MarketData marketData, ValidNodesFilter filter) {
    try {
      var filteredNodes = filteredCapletNodes(marketData, filter);
      if (filteredNodes.size() < 2) {
        throw new IllegalArgumentException("Surface must have at least 2 valid caplet " + "nodes");
      }
      CapletVolatilityMatrixValues matrixValues =
          configurationFromList(filteredNodes, getValidFrom());

      return right(
          RawOptionData.of(
              matrixValues.getRows().stream().map(v -> Tenor.parse(v).getPeriod()).toList(),
              DoubleArray.copyOf(
                  matrixValues.getColumns().stream()
                      .mapToDouble(v -> new BigDecimal(v).doubleValue())
                      .toArray()),
              ValueType.STRIKE,
              DoubleMatrix.ofUnsafe(matrixValues.matrix()),
              valueTypeByModel()));
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return Either.left(
          new ErrorItem(
              Error.CALIBRATION_ERROR,
              "Error creating caplet value matrix for surface "
                  + getName()
                  + ": "
                  + ex.getMessage()));
    }
  }

  private ValueType valueTypeByModel() {
    if (capletValuationModel == null) {
      return ValueType.NORMAL_VOLATILITY;
    } else {
      return switch (capletValuationModel) {
        case BLACK -> ValueType.BLACK_VOLATILITY;
        case NORMAL -> ValueType.NORMAL_VOLATILITY;
      };
    }
  }

  public FixedFloatSwapConvention swapConvention() {
    return FixedFloatSwapConvention.of(getSwapConvention());
  }

  public String index() {
    return swapConvention().getFloatingLeg().getIndex().getName();
  }

  public String ccy() {
    return swapConvention().getFloatingLeg().getIndex().getCurrency().getCode();
  }

  public String underlying() {
    var indexName = applicableRateIndex().getName();
    return ConventionalCurveConfigurations.lookupByIndex(indexName)
        .map(CurveConvention::getName)
        .orElseThrow(
            () ->
                new IllegalArgumentException(
                    "Curve configuration lookup does not exist for index: " + indexName));
  }

  @Override
  public List<InstrumentDefinition> allInstruments() {
    return Streams.concat(
            surfaceInstruments().stream(),
            skewInstruments().stream(),
            capletNodeInstruments().stream())
        .toList();
  }

  public List<InstrumentDefinition> surfaceInstruments() {
    return nodes.stream()
        .map(v -> v.atmInstrument(getName(), index(), ccy(), underlying()))
        .toList();
  }

  public List<InstrumentDefinition> skewInstruments() {
    return emptyIfNull(skewNodes).stream()
        .map(
            v ->
                v.allInstruments(
                    getName(), index(), ccy(), skewType.getSkewType(), underlying(), nodes))
        .flatMap(Collection::stream)
        .toList();
  }

  public List<InstrumentDefinition> capletNodeInstruments() {
    return capletVolatilities.stream()
        .map(v -> v.instrument(getName(), index(), ccy(), underlying()))
        .toList();
  }

  private List<CapletVolatilityNodeValueView> filteredCapletNodes(
      MarketData marketData, ValidNodesFilter filter) {
    return capletVolatilities.stream()
        .map(
            node ->
                NodeInstrumentWrapper.of(
                    getCapletNodeView(marketData, node),
                    node.instrument(getName(), index(), ccy(), underlying())))
        .collect(Collectors.collectingAndThen(toList(), filter::filterNodes));
  }

  private CapletVolatilityNodeValueView getCapletNodeView(
      MarketData marketData, CapletVolatilityNode node) {
    var marketId = MarketDataUtils.quoteId(node.getKey(index()).getKey());
    var marketDataValue = marketData.findValue(marketId).orElse(null);
    return VolatilityMapper.MAPPER_INSTANCE.toCapletNodeValueView(node, marketDataValue);
  }

  public List<VolatilitySurfaceNode> filteredNodes(ValidNodesFilter filter) {
    return nodes.stream()
        .map(
            node ->
                NodeInstrumentWrapper.of(
                    node, node.atmInstrument(getName(), index(), ccy(), underlying())))
        .collect(Collectors.collectingAndThen(toList(), filter::filterNodes));
  }

  public Map<BigDecimal, List<VolatilitySurfaceNode>> filteredSkewNodes(
      ValidNodesFilter nodesFilter) {
    return emptyIfNull(skewNodes).stream()
        .collect(
            Collectors.toMap(
                skew -> skew.getSkewValue().stripTrailingZeros(),
                skew ->
                    skew.filteredNodes(
                        getName(),
                        nodesFilter,
                        index(),
                        ccy(),
                        skewType.getSkewType(),
                        underlying(),
                        nodes)));
  }

  @Override
  public boolean valueEquals(Object object) {
    VolatilitySurface entity = (VolatilitySurface) object;
    return super.valueEquals(entity)
        && Objects.equals(this.curveGroupId, entity.curveGroupId)
        && Objects.equals(this.xInterpolator, entity.xInterpolator)
        && Objects.equals(this.xExtrapolatorLeft, entity.xExtrapolatorLeft)
        && Objects.equals(this.xExtrapolatorRight, entity.xExtrapolatorRight)
        && Objects.equals(this.yInterpolator, entity.yInterpolator)
        && Objects.equals(this.yExtrapolatorLeft, entity.yExtrapolatorLeft)
        && Objects.equals(this.yExtrapolatorRight, entity.yExtrapolatorRight)
        && Objects.equals(this.sabr, entity.sabr)
        && Objects.equals(this.sabrBeta, entity.sabrBeta)
        && Objects.equals(this.sabrShift, entity.sabrShift)
        && Objects.equals(this.skewType, entity.skewType)
        && Objects.equals(this.capletValuationModel, entity.capletValuationModel)
        && nullSafeIsEqualCollection(this.nodes, entity.nodes)
        && nullSafeIsEqualCollection(this.capletVolatilities, entity.capletVolatilities)
        && nullSafeIsEqualCollection(this.skewNodes, entity.skewNodes);
  }
}
