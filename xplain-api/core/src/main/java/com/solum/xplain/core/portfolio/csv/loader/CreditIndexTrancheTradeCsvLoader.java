package com.solum.xplain.core.portfolio.csv.loader;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_INDEX_TRANCHE;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.classifiers.CreditTranches;
import com.solum.xplain.core.common.creditindex.ValidCreditIndexTrancheValidator;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.builder.ResolvableCreditIndexDetails.ResolvableCreditIndexDetailsBuilder;
import com.solum.xplain.core.product.ProductType;
import java.util.List;
import java.util.function.Function;
import org.springframework.stereotype.Component;

@Component
public class CreditIndexTrancheTradeCsvLoader extends CreditIndexTradeCsvLoader {

  public CreditIndexTrancheTradeCsvLoader(CommonCreditTradeCsvLoader commonLoader) {
    super(commonLoader);
  }

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.CREDIT_INDEX_TRANCHE);
  }

  @Override
  protected ResolvableCreditIndexDetailsBuilder parseRow(CsvRow row, boolean refSecTrade) {
    return super.parseRow(row, refSecTrade).creditIndexTranche(tranche(row));
  }

  private String tranche(CsvRow row) {
    var tranche = CreditTranches.parseFromLabel(row.getValue(TRADE_CREDIT_INDEX_TRANCHE));
    var index = creditIndexLongName(row);
    return ValidCreditIndexTrancheValidator.validTranche(tranche, index)
        .leftMap(ErrorItem::getDescription)
        .fold(
            error -> validateValue(tranche, TRADE_CREDIT_INDEX_TRANCHE, error, false),
            Function.identity());
  }
}
