package com.solum.xplain.core.curvegroup.curve.validation;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.opengamma.strata.collect.named.Named;
import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveView;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class ValidCurveNodeConventionValidator extends BaseValidCurveNodesValidator
    implements ConstraintValidator<ValidCurveNodesConventions, CurveUpdateForm> {

  public ValidCurveNodeConventionValidator(
      RequestPathVariablesSupport requestPathVariablesSupport,
      CurveGroupCurveRepository repository) {
    super(requestPathVariablesSupport, repository);
  }

  @Override
  public boolean isValid(CurveUpdateForm form, ConstraintValidatorContext context) {
    return isCurveNodesValid(form, context);
  }

  @Override
  protected boolean isNodeFormValid(CurveView curve, CurveNodeForm form) {
    if (isNotEmpty(form.getConvention()) && isNotEmpty(form.getType())) {
      return getValidConventions(curve, form.getType()).contains(form.getConvention());
    }
    return true;
  }

  @Override
  protected void addViolation(ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate("NotValid")
        .addPropertyNode("convention")
        .addConstraintViolation();
  }

  private List<String> getValidConventions(CurveView curve, String nodeType) {
    return ConventionalCurveConfigurations.lookupByName(curve.getName(), curve.getCurveType())
        .map(c -> c.nodeTypeConventions(nodeType))
        .stream()
        .flatMap(Collection::stream)
        .map(Named::getName)
        .toList();
  }
}
