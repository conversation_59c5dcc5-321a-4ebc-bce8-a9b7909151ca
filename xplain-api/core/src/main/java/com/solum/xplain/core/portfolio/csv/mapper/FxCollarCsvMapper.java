package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_OTHER_OPTION_COUNTER_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REF_SEC_OTHER_OPTION_STRIKE;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvMapper;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class FxCollarCsvMapper implements ProductCsvMapper {

  private final FxOptionCsvMapper fxOptCsvMapper;

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.FXCOLLAR);
  }

  @Override
  public Iterable<CsvField> toCsvFields(TradeDetails tradeDetails) {
    var optionDetails = tradeDetails.getOptionTradeDetails();
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    var notional = optionDetails.getOtherOptionCounterNotional();
    if (notional != null) {
      notional = Math.abs(notional);
    }
    builder.add(new CsvField(TRADE_OTHER_OPTION_COUNTER_NOTIONAL, notional));
    builder.add(
        new CsvField(TRADE_REF_SEC_OTHER_OPTION_STRIKE, optionDetails.getOtherOptionStrike()));
    builder.addAll(fxOptCsvMapper.toCsvFields(tradeDetails));
    return builder.build();
  }
}
