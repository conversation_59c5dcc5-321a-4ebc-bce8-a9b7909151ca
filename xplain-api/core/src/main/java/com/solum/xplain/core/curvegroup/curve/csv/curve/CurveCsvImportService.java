package com.solum.xplain.core.curvegroup.curve.csv.curve;

import static com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations.lookupKeyByName;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static java.lang.String.format;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.BaseVersionedImportService;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curve.value.CurveForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveSearch;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurveCsvImportService extends BaseVersionedImportService<CurveForm, String, Curve> {

  private final CurveGroupCurveRepository curveRepository;
  private final CurveCsvLoader csvLoader;

  public CurveCsvImportService(
      AuditEntryService auditEntryService,
      CurveGroupCurveRepository curveRepository,
      CurveCsvLoader csvLoader) {
    super(auditEntryService);
    this.curveRepository = curveRepository;
    this.csvLoader = csvLoader;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importCurves(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return csvLoader
        .parse(bytes, importOptions.parsingMode())
        .map(parsed -> importCurves(groupId, parsed, importOptions))
        .fold(
            err -> toErrorReturn(importOptions.getDuplicateAction(), err),
            importResult -> toReturn(importOptions.getDuplicateAction(), importResult));
  }

  private ImportResult importCurves(
      String groupId, CsvParserResult<CurveForm> parserResult, ImportOptions importOptions) {
    var forms = parserResult.getParsedLines();
    var existingItems = fetchCurves(groupId, importOptions.getStateDate());
    var sameKeyDuplicateErrors = verifyKeyDuplicates(existingItems, forms);
    if (!sameKeyDuplicateErrors.isEmpty()) {
      return new ImportResult(sameKeyDuplicateErrors, parserResult.getWarnings());
    }
    var importItems = buildImportItems(existingItems, forms);
    var importLogs = importItems(groupId, importOptions, importItems);
    return new ImportResult(importLogs, parserResult.getWarnings());
  }

  private List<LogItem> verifyKeyDuplicates(List<Curve> existingCurves, List<CurveForm> newCurves) {
    return newCurves.stream()
        .flatMap(
            n ->
                lookupKeyByName(n.getName()).stream()
                    .flatMap(
                        nk ->
                            existingCurves.stream()
                                .map(VersionedNamedEntity::getName)
                                .filter(e -> lookupKeyByName(e).stream().anyMatch(nk::equals))
                                .filter(e -> !n.getName().equals(e))
                                .map(
                                    e ->
                                        new ErrorItem(
                                            PARSING_ERROR,
                                            format(
                                                "Curve %s already exists. Existing curve %s, importing curve %s",
                                                nk, e, n.getName())))))
        .map(LogItem.class::cast)
        .toList();
  }

  private ImportItems<CurveForm, String, Curve> buildImportItems(
      List<Curve> existingItems, List<CurveForm> forms) {
    return ImportItems.<CurveForm, String, Curve>builder()
        .existingActiveItems(existingItems)
        .existingItemToKeyFn(Curve::getName)
        .importItems(forms)
        .importItemToKeyFn(CurveForm::getName)
        .build();
  }

  private List<Curve> fetchCurves(String groupId, LocalDate stateDate) {
    return curveRepository.getActiveCurves(groupId, BitemporalDate.newOf(stateDate));
  }

  @Override
  public String getCollection() {
    return Curve.CURVE_COLLECTION;
  }

  @Override
  public String getObjectName() {
    return "IR + Inflation curves";
  }

  @Override
  protected String getIdentifier(String key) {
    return key;
  }

  @Override
  protected Either<ErrorItem, EntityId> insert(String groupId, CurveForm f) {
    return curveRepository.createCurve(groupId, f);
  }

  @Override
  protected Either<ErrorItem, EntityId> update(Curve e, CurveForm f) {
    return curveRepository.updateCurve(e.getCurveGroupId(), e.getEntityId(), e.getValidFrom(), f);
  }

  @Override
  protected Either<ErrorItem, EntityId> archive(Curve e, ArchiveEntityForm f) {
    return curveRepository.archiveCurve(e.getCurveGroupId(), e.getEntityId(), e.getValidFrom(), f);
  }

  @Override
  protected boolean hasFutureVersions(String groupId, String key, LocalDate stateDate) {
    var f = new CurveSearch(key, stateDate);
    return curveRepository.getFutureVersions(groupId, f).notEmpty();
  }
}
