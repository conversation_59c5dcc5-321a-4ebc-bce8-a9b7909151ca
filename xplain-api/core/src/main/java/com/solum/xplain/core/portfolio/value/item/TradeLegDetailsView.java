package com.solum.xplain.core.portfolio.value.item;

import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TradeLegDetailsView {
  @ConfigurableViewField(enumClassifier = "payReceive")
  private PayReceive payReceive;

  @ConfigurableViewQuery(sortable = true)
  private CalculationType type;

  @Schema(
      description =
          "For swaps, the amount that will be paid or received at the start of the period in the same currency as the notional.")
  @ConfigurableViewQuery(sortable = true)
  private Double nearNotional;

  @ConfigurableViewQuery(sortable = true)
  private Double notional;

  @ConfigurableViewQuery(sortable = true)
  private String currency;

  @ConfigurableViewQuery(sortable = true)
  private String accrualFrequency;

  @ConfigurableViewQuery(sortable = true)
  private String paymentFrequency;

  @ConfigurableViewQuery(sortable = true)
  private String paymentCompounding;

  @ConfigurableViewQuery(sortable = true)
  private Integer paymentOffsetDays;

  @ConfigurableViewQuery(sortable = true)
  private String index;

  @ConfigurableViewQuery(sortable = true)
  private Integer fixingDateOffsetDays;

  @ConfigurableViewQuery(sortable = true)
  private String dayCount;

  @ConfigurableViewQuery(sortable = true)
  private Double initialValue;

  @ConfigurableViewQuery(sortable = true)
  private String inflationLag;

  @ConfigurableViewQuery(sortable = true)
  private Boolean isOffshore;
}
