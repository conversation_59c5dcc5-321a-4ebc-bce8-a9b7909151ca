package com.solum.xplain.core.portfolio.convention;

import static com.solum.xplain.extensions.product.ExtendedFxSwapConventions.lookupSpotOffset;
import static com.solum.xplain.extensions.spotdate.FxSpotDateCalculator.spotDateFromBaseDate;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.core.authentication.Authorities;
import com.solum.xplain.core.common.CommonErrors;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.time.LocalDate;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/portfolio/convention")
@RequiredArgsConstructor
public class TradeConventionController {
  private final ReferenceData referenceData;

  @Operation(summary = "Get default FXOPT payment date from expiry date")
  @GetMapping("/fx-opt/default-payment-date")
  @CommonErrors
  @PreAuthorize(Authorities.AUTHORITY_VIEW_PORTFOLIO)
  public LocalDate defaultFxOptPaymentDate(@Valid FxOptDefaultPaymentDateCalcForm calcForm) {
    var domesticCurrency = Currency.of(calcForm.getDomesticCcy());
    var foreignCurrency = Currency.of(calcForm.getForeignCcy());
    var ccyPair = CurrencyPair.of(domesticCurrency, foreignCurrency).toConventional();
    var spotOffset = lookupSpotOffset(ccyPair);
    return spotDateFromBaseDate(calcForm.getExpiryDate(), ccyPair, spotOffset, referenceData);
  }
}
