package com.solum.xplain.core.curvegroup.curve;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.getPrefix;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.curvegroup.curve.CurvesUtils.filterCurves;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curve.csv.curve.CurveCsvMapper;
import com.solum.xplain.core.curvegroup.curve.csv.node.CurveNodeCsvMapper;
import com.solum.xplain.core.curvegroup.curve.dto.CalibratedCurvesOptions;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curve.value.CurveView;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateQuotes;
import com.solum.xplain.core.curvemarket.InstrumentMarketKeyDefinitionExportService;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.Collection;
import java.util.List;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Service
public class CurveGroupCurveExportService {

  private static final String IR_CURVES_FILE_PREFIX = "IRCurveList";
  private static final String CURVE_NODES = "CurveNodes";
  private static final String IR_CURVE_NODES = "IRCurveNodes";

  private final CurveGroupRepository curveGroupRepository;
  private final CurveGroupCurveRepository curveRepository;
  private final MarketDataQuotesSupport marketDataQuotesSupport;
  private final InstrumentMarketKeyDefinitionExportService definitionExportService;
  private final CurveNodeCsvMapper nodeCsvMapper;

  public CurveGroupCurveExportService(
      CurveGroupRepository curveGroupRepository,
      CurveGroupCurveRepository curveRepository,
      MarketDataQuotesSupport marketDataQuotesSupport,
      InstrumentMarketKeyDefinitionExportService definitionExportService,
      CurveNodeCsvMapper nodeCsvMapper) {
    this.curveGroupRepository = curveGroupRepository;
    this.curveRepository = curveRepository;
    this.marketDataQuotesSupport = marketDataQuotesSupport;
    this.definitionExportService = definitionExportService;
    this.nodeCsvMapper = nodeCsvMapper;
  }

  public Either<ErrorItem, FileResponseEntity> getCurvesCsvBytes(
      String groupId, BitemporalDate stateDate, List<String> selectedColumns) {
    var csvFileName = nameWithTimeStamp(IR_CURVES_FILE_PREFIX, stateDate.getActualDate());
    return groupEither(groupId)
        .map(g -> curveRepository.getCurveViews(groupId, stateDate, active()).stream())
        .map(
            views -> {
              var mapper = new CurveCsvMapper(selectedColumns);
              var rows = views.map(mapper::toCsvRow).toList();
              var csvFile = new CsvOutputFile(mapper.header(), rows);
              return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
            });
  }

  public Either<ErrorItem, FileResponseEntity> getCurveNodesCsvBytes(
      String groupId,
      String curveId,
      BitemporalDate stateDate,
      CurveConfigMarketStateForm stateForm) {
    var curve = curveRepository.getActiveCurveView(groupId, curveId, stateDate);
    return groupEither(groupId)
        .flatMap(
            group ->
                curve.map(
                    c -> {
                      var csvFile = getCurveNodesCsv(group, stateForm, List.of(c));
                      var csvFileName =
                          nameWithTimeStamp(c.getName(), CURVE_NODES, stateForm.getStateDate());
                      return FileResponseEntity.csvFile(csvFile, csvFileName);
                    }));
  }

  public Either<ErrorItem, FileResponseEntity> getCurvesNodesCsvBytes(
      String groupId, BitemporalDate version, CurveConfigMarketStateForm stateForm) {
    return groupEither(groupId)
        .map(
            group -> {
              var curves = curveRepository.getCurveViews(groupId, version, active());
              var prefix = getPrefix(IR_CURVE_NODES, curves, CurveView::getName, CURVE_NODES);
              var csvFileName = nameWithTimeStamp(prefix, stateForm.getStateDate());
              var csvFile = getCurveNodesCsv(group, stateForm, curves);
              return FileResponseEntity.csvFile(csvFile, csvFileName);
            });
  }

  public Either<ErrorItem, FileResponseEntity> getCurvesMDKDefinitionsCsvBytes(
      String groupId,
      String curveConfigurationId,
      List<String> curveIds,
      BitemporalDate stateDate) {
    return groupEither(groupId)
        .map(g -> curveRepository.getActiveCurves(groupId, stateDate))
        .map(curves -> filterCurves(curves, curveIds))
        .flatMap(
            curves -> {
              var prefix = getPrefix(IR_CURVE_NODES, curves, Curve::getName, CURVE_NODES);
              return definitionExportService.instrumentMdkDefinitions(
                  curves,
                  prefix,
                  Curve::allInstruments,
                  stateDate.getActualDate(),
                  curveConfigurationId);
            });
  }

  private ByteArrayResource getCurveNodesCsv(
      CurveGroupView group, CurveConfigMarketStateForm stateForm, List<CurveView> views) {
    var quotes = marketDataQuotesSupport.getFullQuotes(stateForm);
    var marketStateQuotes = new CurveConfigMarketStateQuotes(stateForm, quotes);
    return views.stream()
        .map(
            c ->
                curveRepository
                    .getCurveNodes(
                        group,
                        c.getEntityId(),
                        BitemporalDate.newOf(c.getValidFrom()),
                        marketStateQuotes,
                        new CalibratedCurvesOptions(
                            c.getValidFrom(),
                            group.getCalibrationCurrency(),
                            group.getCalibrationStrippingType()))
                    .stream()
                    .map(node -> nodeCsvMapper.toCsvRow(c.getName(), node))
                    .toList())
        .flatMap(Collection::stream)
        .collect(collectingAndThen(toList(), l -> new CsvOutputFile(nodeCsvMapper.header(), l)))
        .writeToByteArray();
  }

  private Either<ErrorItem, CurveGroupView> groupEither(String groupId) {
    return curveGroupRepository.getEither(groupId);
  }
}
