package com.solum.xplain.core.curvegroup.ratefx.value;

import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.curvegroup.ratefx.validation.ValidFxRateCurrencies;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
@ValidFxRateCurrencies
public class CurveGroupFxRatesNodeForm {
  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "currency")
  private String domesticCurrency;

  @NotEmpty
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "currency")
  private String foreignCurrency;
}
