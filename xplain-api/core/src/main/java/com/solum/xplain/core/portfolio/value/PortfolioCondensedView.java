package com.solum.xplain.core.portfolio.value;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
@EqualsAndHashCode
public class PortfolioCondensedView implements Serializable {
  private String id;
  private String externalPortfolioId;
  private String companyId;
  private String externalCompanyId;
  private String entityId;
  private String externalEntityId;

  public static PortfolioCondensedView newOf(
      String portfolioId,
      String externalPortfolioId,
      String companyId,
      String externalCompanyId,
      String entityId,
      String externalEntityId) {
    var view = new PortfolioCondensedView();
    view.setId(portfolioId);
    view.setExternalPortfolioId(externalPortfolioId);
    view.setCompanyId(companyId);
    view.setExternalCompanyId(externalCompanyId);
    view.setEntityId(entityId);
    view.setExternalEntityId(externalEntityId);
    return view;
  }
}
