package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.common.validation.NotZero;
import com.solum.xplain.core.portfolio.validation.RequiredSameBaseCurrencyNotionals;
import com.solum.xplain.core.portfolio.validation.RequiredValidNearFarDateNotionalSigns;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@RequiredValidNearFarDateNotionalSigns
@RequiredSameBaseCurrencyNotionals
public class FxSwapNotionalsForm {

  @NotNull
  @Schema(description = "Notional in base currency for Near Date.")
  @NotZero
  private Double nearDateBaseNotional;

  @NotNull
  @Schema(description = "Notional in counter currency for Near Date.")
  @NotZero
  private Double nearDateCounterNotional;

  @NotNull
  @Schema(description = "Notional in base currency for Far Date.")
  @NotZero
  private Double farDateBaseNotional;

  @NotNull
  @Schema(description = "Notional in counter currency for Far Date.")
  @NotZero
  private Double farDateCounterNotional;
}
