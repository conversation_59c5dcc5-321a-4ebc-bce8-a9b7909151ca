package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDate;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.lang.Nullable;

public abstract class TradeIdValidator {
  private final RequestPathVariablesSupport requestPathVariablesSupport;
  private final String idPath;
  private final String portfolioPath;

  protected TradeIdValidator(
      RequestPathVariablesSupport requestPathVariablesSupport,
      String idPath,
      String portfolioPath) {
    this.requestPathVariablesSupport = requestPathVariablesSupport;
    this.idPath = idPath;
    this.portfolioPath = portfolioPath;
  }

  public boolean isValid(
      @Nullable String externalId,
      @Nullable LocalDate stateDate,
      ConstraintValidatorContext context) {
    if (StringUtils.isNotEmpty(externalId)) {
      final String internalId = requestPathVariablesSupport.getPathVariable(idPath);
      final String portfolioId = requestPathVariablesSupport.getPathVariable(portfolioPath);
      overrideMessage(context);

      return Steps.begin(Optional.ofNullable(portfolioId).filter(ObjectId::isValid))
          .then(() -> Optional.of(externalId).filter(s -> !s.isEmpty()))
          .then(() -> Optional.ofNullable(stateDate))
          .yield((pId, extId, date) -> isUniqueTrade(date, pId, extId, internalId))
          .orElse(true);
    }
    return true;
  }

  protected abstract boolean isUniqueTrade(
      LocalDate stateDate, String portfolioId, String externalId, String internalId);

  private void overrideMessage(ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
        .addPropertyNode("externalTradeId")
        .addConstraintViolation();
  }
}
