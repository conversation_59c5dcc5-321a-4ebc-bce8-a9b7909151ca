package com.solum.xplain.core.curvegroup.curvebond;

import static com.solum.xplain.core.common.CollectionUtils.chunked;
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.ChartPoint;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupEntryCountSupport;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurveCalibrationResult;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveSearch;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveUpdateForm;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveView;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryCount;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueFullView;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
public class BondCurveRepository extends GenericUniqueVersionedEntityRepository<BondCurve> {

  private final MongoOperations mongoOperations;
  private final BondCurveMapper mapper;
  private final CurveGroupEntryCountSupport countSupport;

  public BondCurveRepository(
      MongoOperations mongoOperations,
      BondCurveMapper mapper,
      CurveGroupEntryCountSupport countSupport) {
    super(mongoOperations, mapper);
    this.mongoOperations = mongoOperations;
    this.mapper = mapper;
    this.countSupport = countSupport;
  }

  public static Criteria uniqueEntityCriteria(String curveGroupId, String curveName) {
    return groupIdCriteria(curveGroupId).and(VersionedNamedEntity.Fields.name).is(curveName);
  }

  private static Criteria groupIdCriteria(String curveGroupId) {
    return where(BondCurve.Fields.curveGroupId).is(curveGroupId);
  }

  @Override
  protected Criteria uniqueEntityCriteria(BondCurve entity) {
    return uniqueEntityCriteria(entity.getCurveGroupId(), entity.getName());
  }

  @Override
  protected BondCurve beforeStoring(BondCurve entity) {
    return entity.orderNodes();
  }

  @Override
  protected Sort defaultEntitiesSort() {
    return Sort.by(VersionedNamedEntity.Fields.name);
  }

  public Either<ErrorItem, EntityId> createCurve(String groupId, BondCurveForm curveForm) {
    var entity = mapper.fromForm(curveForm, groupId, BondCurve.newOf());
    return insert(entity, curveForm.getVersionForm()).map(this::removeCurveCalibrationResults);
  }

  public Either<ErrorItem, EntityId> updateCurve(
      String groupId, String curveId, LocalDate version, BondCurveUpdateForm f) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entityExact(entityId, version))
        .map(e -> update(e, f.getVersionForm(), c -> mapper.fromForm(f, c)))
        .map(this::removeCurveCalibrationResults);
  }

  public Either<ErrorItem, EntityId> archiveCurve(
      String groupId, String curveId, LocalDate version, ArchiveEntityForm f) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entityExact(entityId, version))
        .map(entity -> archive(entity, f))
        .map(this::removeCurveCalibrationResults);
  }

  public Either<ErrorItem, EntityId> deleteCurve(
      String groupId, String curveId, LocalDate version) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entityExact(entityId, version))
        .flatMap(this::delete)
        .map(this::removeCurveCalibrationResults);
  }

  public Either<ErrorItem, BondCurveView> getActiveCurveView(
      String groupId, String curveId, BitemporalDate stateDate) {
    return getActiveCurve(groupId, curveId, stateDate).map(c -> mapper.toView(c, false));
  }

  public List<BondCurveView> getCurveViews(
      String groupId, CurveConfigMarketStateForm stateForm, VersionedEntityFilter filter) {
    var criteria = groupIdCriteria(groupId);
    return entities(stateForm.bitemporalDate(), filter, criteria).stream()
        .map(c -> toView(c, stateForm))
        .toList();
  }

  public List<BondCurveView> getCurveViewsForExport(String groupId, BitemporalDate stateDate) {
    return entities(stateDate, active(), groupIdCriteria(groupId)).stream()
        .map(c -> mapper.toView(c, false))
        .toList();
  }

  public List<BondCurveView> getCurveVersionViews(String groupId, String curveId) {
    return curveInGroup(groupId, curveId)
        .map(this::entityVersions)
        .map(versions -> versions.stream().map(c -> mapper.toView(c, false)).toList())
        .getOrElse(List.of());
  }

  public DateList getFutureVersions(String groupId, BondCurveSearch search) {
    var searchCriteria = uniqueEntityCriteria(groupId, search.getName());
    return futureVersionsByCriteria(searchCriteria, search.getStateDate());
  }

  public List<CurveGroupEntryCount> activeEntriesCount(CurveGroupEntryFilter filter) {
    return countSupport.activeEntriesCount(filter, BondCurve.class);
  }

  public List<BondCurve> getActiveCurves(String groupId, BitemporalDate stateDate) {
    return entities(stateDate, active(), groupIdCriteria(groupId));
  }

  public Either<ErrorItem, BondCurve> getActiveCurve(
      String groupId, String curveId, BitemporalDate stateDate) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entity(entityId, stateDate, active()));
  }

  public List<BondCurveNodeCalculatedView> getCurveNodes(
      String groupId,
      String curveId,
      BitemporalDate version,
      Map<String, CalculationMarketValueFullView> quotes,
      InstrumentPriceRequirements priceRequirements) {
    var priceType = priceRequirements.getCurvesPriceType();
    return getActiveCurve(groupId, curveId, version)
        .map(c -> mapper.toNodeViews(c, quotes, priceType))
        .getOrElse(List.of());
  }

  public List<ChartPoint> getCurveChartPoints(
      String groupId, CurveConfigMarketStateForm stateForm, String curveId) {
    return getActiveCurve(groupId, curveId, stateForm.bitemporalDate())
        .toOptional()
        .flatMap(curve -> calibrationResult(curve, stateForm))
        .map(BondCurveCalibrationResult::getChartPoints)
        .orElse(List.of());
  }

  public void updateCalibrationResults(BondCurveCalibrationResult result) {
    mongoOperations.insert(result);
  }

  public void clearGroupCalibrationResults(String groupId) {
    var idsStream =
        mongoOperations.query(BondCurve.class).matching(query(groupIdCriteria(groupId))).stream()
            .map(BondCurve::getEntityId);
    chunked(idsStream, 1000).forEach(this::removeCurveCalibrationResults);
  }

  private void removeCurveCalibrationResults(List<String> ids) {
    mongoOperations.remove(
        query(where(BondCurveCalibrationResult.Fields.curveId).in(ids)),
        BondCurveCalibrationResult.class);
  }

  private EntityId removeCurveCalibrationResults(EntityId curveId) {
    mongoOperations.remove(
        query(where(BondCurveCalibrationResult.Fields.curveId).is(curveId.getId())),
        BondCurveCalibrationResult.class);
    return curveId;
  }

  private BondCurveView toView(BondCurve curve, CurveConfigMarketStateForm stateForm) {
    var calibrationResult = calibrationResult(curve, stateForm);
    return mapper.toView(curve, calibrationResult.isPresent());
  }

  private Optional<BondCurveCalibrationResult> calibrationResult(
      BondCurve curve, CurveConfigMarketStateForm stateForm) {
    return mongoOperations
        .query(BondCurveCalibrationResult.class)
        .matching(
            query(
                where(BondCurveCalibrationResult.Fields.curveId)
                    .is(curve.getEntityId())
                    .and(BondCurveCalibrationResult.Fields.stateDate)
                    .is(stateForm.getStateDate())
                    .and(BondCurveCalibrationResult.Fields.curveDate)
                    .is(stateForm.getCurveDate())
                    .and(BondCurveCalibrationResult.Fields.marketDataGroupId)
                    .is(stateForm.getMarketDataGroupId())
                    .and(BondCurveCalibrationResult.Fields.marketDataSource)
                    .is(stateForm.getMarketDataSource())
                    .and(BondCurveCalibrationResult.Fields.priceRequirements)
                    .is(stateForm.priceRequirements())))
        .stream()
        .findAny();
  }

  private Either<ErrorItem, String> curveInGroup(String groupId, String curveId) {
    var criteria = groupIdCriteria(groupId).and(VersionedEntity.Fields.entityId).is(curveId);
    var curveInGroup = mongoOperations.exists(query(criteria), BondCurve.class);
    return Eithers.cond(curveInGroup, OBJECT_NOT_FOUND.entity("Bond curve not found"), curveId);
  }
}
