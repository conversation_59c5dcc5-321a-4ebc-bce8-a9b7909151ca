package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import java.util.Map;
import java.util.function.BiFunction;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public class PortfolioItemToViewConverter
    implements BiFunction<PortfolioItemEntity, TradeValue, PortfolioItem> {

  private final Map<String, PortfolioCondensedView> portfolios;
  private final PortfolioMapper portfolioMapper;

  @Override
  public PortfolioItem apply(PortfolioItemEntity entity, TradeValue value) {
    return portfolioMapper.generateRead(entity, value, portfolioView(entity.getPortfolioId()));
  }

  private synchronized PortfolioCondensedView portfolioView(String portfolioId) {
    return portfolios.get(portfolioId);
  }
}
