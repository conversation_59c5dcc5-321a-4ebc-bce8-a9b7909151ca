package com.solum.xplain.core.portfolio.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = RequiredSameDayCountValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface RequiredSameDayCount {

  String message() default
      "{com.solum.xplain.api.portfolio.validation.RequiredValidSameDayCount" + ".message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
