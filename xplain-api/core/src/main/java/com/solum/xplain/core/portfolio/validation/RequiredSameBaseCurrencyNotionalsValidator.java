package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.FxSwapNotionalsForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.ObjectUtils;

/** Validates that the near and far base currency notionals are the same magnitude. */
public class RequiredSameBaseCurrencyNotionalsValidator
    implements ConstraintValidator<RequiredSameBaseCurrencyNotionals, FxSwapNotionalsForm> {

  public boolean isValid(FxSwapNotionalsForm form, ConstraintValidatorContext context) {
    if (form == null
        || ObjectUtils.anyNull(form.getNearDateBaseNotional(), form.getFarDateBaseNotional())) {
      return true;
    }

    var baseNear = form.getNearDateBaseNotional();
    var baseFar = form.getFarDateBaseNotional();

    return Math.abs(baseNear) == Math.abs(baseFar);
  }
}
