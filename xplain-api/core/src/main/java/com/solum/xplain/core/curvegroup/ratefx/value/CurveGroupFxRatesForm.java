package com.solum.xplain.core.curvegroup.ratefx.value;

import com.solum.xplain.core.common.validation.UniqueValues;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class CurveGroupFxRatesForm {

  @Valid @UniqueValues private List<CurveGroupFxRatesNodeForm> nodes;

  @Valid @NotNull private NewVersionFormV2 versionForm;
}
