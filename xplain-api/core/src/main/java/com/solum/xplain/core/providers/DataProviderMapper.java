package com.solum.xplain.core.providers;

import com.solum.xplain.core.providers.value.DataProviderCreateForm;
import com.solum.xplain.core.providers.value.DataProviderUpdateForm;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper
public interface DataProviderMapper {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "lastModifiedBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "archived", constant = "false")
  DataProvider toEntity(DataProviderCreateForm form);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "lastModifiedBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "externalId", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "archived", ignore = true)
  DataProvider toEntity(DataProviderUpdateForm form, @MappingTarget DataProvider entity);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "lastModifiedBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "externalId", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  DataProvider copy(DataProvider form, @MappingTarget DataProvider entity);

  DataProvider copy(DataProvider entity);

  @Mapping(target = "archived", constant = "true")
  DataProvider copyArchived(DataProvider entity);
}
