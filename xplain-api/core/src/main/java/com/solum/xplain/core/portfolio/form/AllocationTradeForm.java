package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.portfolio.trade.TradeValue.allocationTradeValue;

import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.trade.AllocationTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.ValidAllocationTrade;
import com.solum.xplain.core.portfolio.validation.ValidReferenceTradeId;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import com.solum.xplain.extensions.enums.PositionType;
import io.atlassian.fugue.Either;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ValidReferenceTradeId
@ValidAllocationTrade
public class AllocationTradeForm extends DefaultTradeForm implements ParsableToTradeValue {

  @NotEmpty @ValidIdentifier private String referenceTradeId;
  @NotNull @Positive private Double allocationNotional;
  private PositionType positionType;
  private LocalDate tradeDate;
  private String optionPosition;
  private String protection;

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    var allocationTradeDetails = new AllocationTradeDetails();
    allocationTradeDetails.setReferenceTradeId(referenceTradeId);
    allocationTradeDetails.setAllocationNotional(allocationNotional);
    allocationTradeDetails.setTradeDate(tradeDate);
    allocationTradeDetails.setDescription(getDescription());
    allocationTradeDetails.setCounterParty(getTradeCounterparty());
    allocationTradeDetails.setCounterPartyType(getTradeCounterpartyType());
    allocationTradeDetails.setPositionType(positionType);
    allocationTradeDetails.setClientMetrics(nullSafeClientMetrics());
    allocationTradeDetails.setCsaDiscountingGroup(getCsaDiscountingGroup());
    allocationTradeDetails.setOptionPosition(getOptionPosition());
    allocationTradeDetails.setProtection(getProtection());
    return Either.right(
        allocationTradeValue(
            allocationTradeDetails,
            getDescription(),
            nullSafeOnboardDetails(),
            nullSafeExternalIdentifiers(),
            nullSafeCustomFields()));
  }
}
