package com.solum.xplain.core.authentication;

import static java.util.Optional.empty;
import static java.util.Optional.ofNullable;
import static java.util.function.Predicate.not;
import static org.springframework.security.oauth2.server.resource.BearerTokenErrors.insufficientScope;

import com.solum.xplain.core.accesslog.AccessLogService;
import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.config.properties.DefaultsProperties;
import com.solum.xplain.core.permissions.UserPermissionService;
import com.solum.xplain.core.teams.TeamRepository;
import com.solum.xplain.core.teams.value.TeamNameView;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
@NullMarked
public class XplainPrincipalResolver {

  private final UserPermissionService userPermissionService;
  private final TeamRepository teamRepository;
  private final AccessLogService accessLogService;
  private final DefaultsProperties defaultsProperties;

  public XplainPrincipal resolvePrincipal(
      String id,
      @Nullable Object principalName,
      @Nullable String tokenClient,
      @Nullable List<String> tokenRoles,
      @Nullable List<String> tokenTeams,
      Collection<GrantedAuthority> tokenAuthorities,
      Map<String, Object> attributes,
      boolean isClientPrincipal) {
    var trustedPrincipal = isTrustedClientPrincipal(isClientPrincipal, tokenClient);
    // TODO: SXSD-9683 - we can remove this when we resolve whatever is happening. For now we are
    //  logging it to try to understand the cause
    if (!trustedPrincipal && tokenRoles == null) {
      log.warn("Null tokenRoles provided for principalName: {} id/sub: {}", principalName, id);
    } else if (!trustedPrincipal && tokenRoles.isEmpty()) {
      log.warn("Empty tokenRoles provided for principalName: {} id/sub: {}", principalName, id);
    }
    var permissions =
        ofNullable(tokenRoles)
            .filter(not(List::isEmpty))
            .or(
                () ->
                    trustedPrincipal ? Optional.of(List.of(defaultsProperties.getRole())) : empty())
            .map(userPermissionService::fromExternalRoles)
            .orElse(List.of());
    if (permissions.isEmpty()) {
      throw new OAuth2AuthenticationException(
          insufficientScope("No permissions assigned to the user", null));
    }

    var teams =
        ofNullable(tokenTeams)
            .filter(not(List::isEmpty))
            .or(
                () ->
                    trustedPrincipal ? Optional.of(List.of(defaultsProperties.getTeam())) : empty())
            .map(this::resolveTeams)
            .orElse(List.of());
    if (teams.isEmpty()) {
      throw new OAuth2AuthenticationException(
          insufficientScope("No teams assigned to the user", null));
    }

    var principal =
        new XplainPrincipal(
            id, principalName, teams, attributes, tokenAuthorities, permissions, trustedPrincipal);
    accessLogService.logPrincipalResolution(
        principal, tokenClient, tokenRoles, tokenTeams, isClientPrincipal);
    return principal;
  }

  private boolean isTrustedClientPrincipal(
      boolean isClientPrincipal, @Nullable String tokenClient) {
    return isClientPrincipal && defaultsProperties.isTrustedClient(tokenClient);
  }

  public List<ObjectId> resolveTeams(List<String> tokenTeams) {
    return teamRepository.teamNamesListByExternalIds(tokenTeams).stream()
        .map(TeamNameView::getId)
        .map(ObjectId::new)
        .toList();
  }
}
