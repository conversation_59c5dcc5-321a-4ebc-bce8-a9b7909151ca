package com.solum.xplain.core.curvegroup.ratefx;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesForm;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesNodeValueView;
import com.solum.xplain.core.curvegroup.ratefx.value.CurveGroupFxRatesView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class CurveGroupFxRatesService {

  private final CurveGroupRepository curveGroupRepository;
  private final CurveGroupFxRatesRepository repository;
  private final MarketDataQuotesSupport marketDataQuotesSupport;

  @Transactional
  public Either<ErrorItem, EntityId> create(String groupId, CurveGroupFxRatesForm form) {
    return curveGroupRepository
        .getGroup(groupId)
        .flatMap(g -> repository.createRates(groupId, form))
        .flatMap(entityId -> clearCalibrationResults(groupId));
  }

  @Transactional
  public Either<ErrorItem, EntityId> update(
      String groupId, LocalDate version, CurveGroupFxRatesForm form) {
    return curveGroupRepository
        .getGroup(groupId)
        .flatMap(g -> repository.updateRates(groupId, version, form))
        .flatMap(entityId -> clearCalibrationResults(groupId));
  }

  @Transactional
  public Either<ErrorItem, EntityId> delete(String groupId, LocalDate version) {
    return curveGroupRepository
        .getGroup(groupId)
        .flatMap(g -> repository.deleteRates(groupId, version))
        .flatMap(entityId -> clearCalibrationResults(groupId));
  }

  public Either<ErrorItem, CurveGroupFxRatesView> get(String groupId, BitemporalDate stateDate) {
    return curveGroupRepository
        .getGroup(groupId)
        .flatMap(g -> repository.getActiveRatesView(groupId, stateDate));
  }

  public Either<ErrorItem, List<CurveGroupFxRatesView>> getVersions(String groupId) {
    return curveGroupRepository
        .getGroup(groupId)
        .map(g -> repository.getRatesVersionViews(groupId));
  }

  public Either<ErrorItem, DateList> getFutureVersionsDates(String groupId, LocalDate stateDate) {
    return curveGroupRepository
        .getGroup(groupId)
        .map(g -> repository.getFutureVersions(groupId, stateDate));
  }

  public Either<ErrorItem, VersionedList<CurveGroupFxRatesNodeValueView>> getRatesValues(
      String groupId, CurveConfigMarketStateForm stateForm) {
    return curveGroupRepository
        .getGroup(groupId)
        .map(
            g ->
                repository.getRatesNodesValuesViews(
                    groupId,
                    stateForm.getStateDate(),
                    marketDataQuotesSupport.getFullQuotes(stateForm)));
  }

  private Either<ErrorItem, EntityId> clearCalibrationResults(String groupId) {
    return curveGroupRepository.clearCalibrationResults(groupId);
  }
}
