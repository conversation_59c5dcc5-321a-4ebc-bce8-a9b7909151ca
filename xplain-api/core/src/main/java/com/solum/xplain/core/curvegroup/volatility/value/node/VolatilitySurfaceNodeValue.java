package com.solum.xplain.core.curvegroup.volatility.value.node;

import static com.opengamma.strata.basics.date.DayCounts.ACT_365F;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.product.swap.type.FixedFloatSwapConvention;
import com.solum.xplain.core.curvegroup.ParsableTenor;
import com.solum.xplain.extensions.calendar.ValuationDateReferenceData;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class VolatilitySurfaceNodeValue implements ParsableTenor {

  private String expiry;
  private String tenor;
  private double value;

  public double parsedVolExpiry(
      LocalDate calibrationDate, FixedFloatSwapConvention convention, ReferenceData referenceData) {
    BusinessDayAdjustment bda = convention.getFloatingLeg().getStartDateBusinessDayAdjustment();
    LocalDate exerciseDate =
        bda.adjust(
            calibrationDate.plus(Tenor.parse(getExpiry()).normalized().getPeriod()),
            ValuationDateReferenceData.wrap(referenceData, calibrationDate));

    return ACT_365F.relativeYearFraction(calibrationDate, exerciseDate);
  }
}
