package com.solum.xplain.core.curvegroup.curve.csv;

import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.value.CombinedCurvePoint;
import java.util.ArrayList;
import java.util.List;
import lombok.experimental.Delegate;

public class CombinedCurvePointsCsvBuilder {

  private static final String CURVE_FIELD = "Curve Name";
  private static final String DATE_FIELD = "Date";
  private static final String ZERO_RATE = "Zero Rate";
  private static final String PRICE_INDEX = "Price Index";
  private static final String FWD_RATE = "Fwd Rate";
  private static final String DISCOUNT_FACTOR_FIELD = "Discount Factor";
  private static final String MARKET_RATE_FIELD = "Market Rate";

  private final List<CurvePoint> rows = new ArrayList<>();

  public CombinedCurvePointsCsvBuilder withPoints(
      List<CombinedCurvePoint> points, String curveName) {
    rows.addAll(points.stream().map(p -> new CurvePoint(curveName, p)).toList());
    return this;
  }

  public CsvOutputFile toCsv() {
    var headers =
        List.of(
            CURVE_FIELD,
            DATE_FIELD,
            ZERO_RATE,
            PRICE_INDEX,
            FWD_RATE,
            DISCOUNT_FACTOR_FIELD,
            MARKET_RATE_FIELD);
    return new CsvOutputFile(headers, rows.stream().map(this::toRow).toList());
  }

  private CsvRow toRow(CurvePoint point) {
    return new CsvRow(
        List.of(
            new CsvField(CURVE_FIELD, point.curveName),
            new CsvField(DATE_FIELD, point.date()),
            new CsvField(ZERO_RATE, point.zeroRate()),
            new CsvField(PRICE_INDEX, point.priceIndex()),
            new CsvField(FWD_RATE, point.forwardRate()),
            new CsvField(DISCOUNT_FACTOR_FIELD, point.discountFactor()),
            new CsvField(MARKET_RATE_FIELD, point.marketRate())));
  }

  private record CurvePoint(String curveName, @Delegate CombinedCurvePoint point) {}
}
