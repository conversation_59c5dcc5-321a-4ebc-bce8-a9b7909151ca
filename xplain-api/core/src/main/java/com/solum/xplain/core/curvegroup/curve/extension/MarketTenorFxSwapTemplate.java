package com.solum.xplain.core.curvegroup.curve.extension;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.BusinessDayConventions;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.MarketTenor;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.product.common.BuySell;
import com.opengamma.strata.product.fx.FxSwapTrade;
import com.opengamma.strata.product.fx.type.FxSwapConvention;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.utils.TenorUtils;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString
@EqualsAndHashCode
@AllArgsConstructor
public class MarketTenorFxSwapTemplate {

  private final MarketTenor tenor;
  private final FxSwapConvention convention;

  public static Either<ErrorItem, MarketTenorFxSwapTemplate> newOf(
      String period, String convention) {
    var fxSwapConvention = FxSwapConvention.of(convention);
    return TenorUtils.parseMarketTenor(period)
        .map(tenor -> new MarketTenorFxSwapTemplate(tenor, fxSwapConvention));
  }

  public FxSwapTrade createTrade(
      LocalDate tradeDate,
      BuySell buySell,
      double notional,
      double nearFxRate,
      double forwardPoints,
      ReferenceData refData) {
    return createTrade(tradeDate, tenor, buySell, notional, nearFxRate, forwardPoints, refData);
  }

  private LocalDate startDate(LocalDate tradeDate, MarketTenor marketTenor, ReferenceData refData) {
    if (tenor == MarketTenor.ON) {
      return tradeDate;
    } else if (tenor == MarketTenor.TN) {
      var calendar = convention.getSpotDateOffset().getCalendar();
      var bda = BusinessDayAdjustment.of(BusinessDayConventions.FOLLOWING, calendar);
      DaysAdjustment daysAdjustment = DaysAdjustment.ofBusinessDays(1, calendar, bda);
      return daysAdjustment.adjust(tradeDate, refData);
    } else {
      return convention.calculateSpotDateFromTradeDate(tradeDate, refData);
    }
  }

  private FxSwapTrade createTrade(
      LocalDate tradeDate,
      MarketTenor marketTenor,
      BuySell buySell,
      double notional,
      double nearFxRate,
      double farLegForwardPoints,
      ReferenceData refData) {
    Currency buySellCurrency = convention.getCurrencyPair().getBase();

    LocalDate startDate = startDate(tradeDate, marketTenor, refData);
    LocalDate endDate = startDate.plus(marketTenor.getTenor());

    return convention.toTrade(
        tradeDate,
        startDate,
        endDate,
        buySell,
        buySellCurrency,
        notional,
        nearFxRate,
        farLegForwardPoints);
  }

  public CurrencyPair currencyPair() {
    return convention.getCurrencyPair();
  }

  public FxSwapConvention convention() {
    return convention;
  }

  public MarketTenor marketTenor() {
    return tenor;
  }

  public Tenor tenor() {
    return tenor.getTenor();
  }
}
