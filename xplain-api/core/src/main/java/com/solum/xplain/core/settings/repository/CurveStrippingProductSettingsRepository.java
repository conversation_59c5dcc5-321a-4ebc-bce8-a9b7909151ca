package com.solum.xplain.core.settings.repository;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.settings.GenericVersionedSettingsRepository;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.settings.entity.CurveStrippingProductSettings;
import com.solum.xplain.core.settings.form.CurveStrippingProductForm;
import com.solum.xplain.core.settings.mappers.CurveStrippingProductSettingsMapper;
import com.solum.xplain.core.settings.value.CurveStrippingProductSettingsView;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.stereotype.Repository;

@Repository
public class CurveStrippingProductSettingsRepository
    extends GenericVersionedSettingsRepository<
        CurveStrippingProductSettings, CurveStrippingProductSettingsView> {

  private final CurveStrippingProductSettingsMapper mapper;

  public CurveStrippingProductSettingsRepository(
      MongoOperations mongoOperations, CurveStrippingProductSettingsMapper mapper) {
    super(mongoOperations, mapper, CurveStrippingProductSettings::empty);
    this.mapper = mapper;
  }

  public Either<ErrorItem, EntityId> saveProductSettings(
      LocalDate stateDate, CurveStrippingProductForm form) {
    return getExactSettings(stateDate)
        .flatMap(settings -> update(settings, form.getVersionForm(), s -> mapper.from(form, s)));
  }

  public Either<ErrorItem, EntityId> deleteProductSettingsVersion(LocalDate stateDate) {
    return getExactSettings(stateDate).flatMap(this::checkFirstVersion).flatMap(this::delete);
  }

  public CurveStrippingProductSettings getCurveStrippingProductSettings(BitemporalDate stateDate) {
    return activeEntityOrEmpty(stateDate);
  }
}
