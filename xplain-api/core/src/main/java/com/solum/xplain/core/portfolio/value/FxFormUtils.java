package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.portfolio.form.FxSwapTradeForm;
import com.solum.xplain.core.portfolio.form.SingleExchangeFxTradeForm;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class FxFormUtils {

  public static <T extends SingleExchangeFxTradeForm> T assignFxFields(
      TradeDetails details, T form, String baseCcy) {
    var receiveLeg = details.getReceiveLeg();
    var payLeg = details.getPayLeg();

    if (receiveLeg.getCurrency().equals(baseCcy)) {
      form.setDomesticCurrencyAmount(
          CurrencyAmountForm.of(receiveLeg.getCurrency(), receiveLeg.getNotional()));
      form.setForeignCurrencyAmount(
          CurrencyAmountForm.of(payLeg.getCurrency(), payLeg.getNotional()));
    } else {
      form.setForeignCurrencyAmount(
          CurrencyAmountForm.of(receiveLeg.getCurrency(), receiveLeg.getNotional()));
      form.setDomesticCurrencyAmount(
          CurrencyAmountForm.of(payLeg.getCurrency(), payLeg.getNotional()));
    }
    return form;
  }

  public static FxSwapTradeForm assignFxFields(
      TradeDetails details, FxSwapTradeForm form, String baseCcy) {
    form.setBaseCurrency(baseCcy);

    var receiveLeg = details.getReceiveLeg();
    var payLeg = details.getPayLeg();

    form.setFarDateFxRate(details.getFxRate());
    form.setNearDateFxRate(details.getNearDateFxRate());

    if (receiveLeg.getCurrency().equals(baseCcy)) {
      form.setCounterCurrency(payLeg.getCurrency());
    } else {
      form.setCounterCurrency(receiveLeg.getCurrency());
    }
    return form;
  }
}
