package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.CoreProductType.FRA;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.FraTradeForm;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.extensions.enums.PositionType;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class FraTradeView extends FraTradeForm implements TradeView {

  private String updatedBy;
  private LocalDateTime updatedAt;
  private String tradeId;
  private String calendar;

  public static Either<ErrorItem, FraTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == FRA) {
      var view = new FraTradeView();
      view.updateCommonView(item);
      view.withTradeInfo(item.getTradeDetails());
      view.withTradeDetails(item.getTradeDetails());
      return Either.right(view);
    } else {
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Product type is not " + "expected " + item.getProductType()));
    }
  }

  private void withTradeDetails(TradeDetails details) {
    PositionType positionType = details.getPositionType();
    if (positionType == null) {
      // Infer from IBOR leg direction (PAY = SELL, RECEIVE = BUY)
      positionType =
          details.getPayLeg().getType() == CalculationType.IBOR
              ? PositionType.SELL
              : PositionType.BUY;
    }
    this.setPosition(positionType.name());
    this.setStartDate(details.getStartDate());
    this.setEndDate(details.getEndDate());
    this.setBusinessDayConvention(details.getBusinessDayConvention());
    this.setCalendar(details.getCalendar());

    TradeLegDetails iborLeg =
        details.getPayLeg().getType() == CalculationType.IBOR
            ? details.getPayLeg()
            : details.getReceiveLeg();
    this.setNotionalCurrency(iborLeg.getCurrency());
    this.setNotionalValue(iborLeg.getNotional());
    this.setCalculationIborIndex(iborLeg.getIndex());
    this.setDayCount(iborLeg.getDayCount());
    this.setExtIborLegIdentifier(iborLeg.getExtLegIdentifier());

    TradeLegDetails fixedLeg =
        details.getPayLeg().getType() == CalculationType.FIXED
            ? details.getPayLeg()
            : details.getReceiveLeg();
    this.setCalculationFixedRateInitialValue(fixedLeg.getInitialValue());
    this.setExtFixedLegIdentifier(fixedLeg.getExtLegIdentifier());
  }
}
