package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.error.Error.UNEXPECTED_TYPE;
import static com.solum.xplain.core.portfolio.value.FxFormUtils.assignFxFields;
import static com.solum.xplain.core.portfolio.value.FxLongShort.LONG;
import static com.solum.xplain.core.portfolio.value.FxLongShort.SHORT;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.FxForwardTradeForm;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FxForwardTradeView extends FxForwardTradeForm implements TradeView {
  private String tradeId;
  private String updatedBy;
  private LocalDateTime updatedAt;
  private String calendar;

  public static Either<ErrorItem, FxForwardTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == CoreProductType.FXFWD) {
      var view = new FxForwardTradeView();
      view.withTradeInfo(item.getTradeDetails());
      view.updateCommonView(item);
      view.setPaymentDate(item.getTradeDetails().getEndDate());
      view.setBusinessDayConvention(item.getTradeDetails().getBusinessDayConvention());
      var baseCcy = item.getTradeDetails().getInfo().getTradeCurrency();
      assignFxFields(item.getTradeDetails(), view, baseCcy);
      view.setFxLongShort(
          baseCcy.equals(item.getTradeDetails().getPayLeg().getCurrency()) ? SHORT : LONG);
      view.setFxRate(item.getTradeDetails().getFxRate());
      view.setPayLegExtIdentifier(item.getTradeDetails().getPayLeg().getExtLegIdentifier());
      view.setReceiveLegExtIdentifier(item.getTradeDetails().getReceiveLeg().getExtLegIdentifier());
      view.setCalendar(item.getTradeDetails().getCalendar());
      return Either.right(view);
    } else {
      return Either.left(
          new ErrorItem(UNEXPECTED_TYPE, "Product type is not expected " + item.getProductType()));
    }
  }
}
