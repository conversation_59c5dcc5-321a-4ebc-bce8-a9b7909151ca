package com.solum.xplain.core.curvegroup.curve.validation;

import static java.util.stream.Collectors.groupingBy;

import com.solum.xplain.core.curvegroup.curve.entity.CurveNodeInstrument;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class UniqueCurveNodesValidator
    implements ConstraintValidator<UniqueCurveNodes, CurveUpdateForm> {

  private static final String DUPLICATE_ERROR_TEMPLATE = "Duplicate node periods: %s";
  private static final String DUPLICATE_DELIMITER = " and ";
  private static final String EMPTY_IDENTIFIER = "EMPTY";

  public boolean isValid(CurveUpdateForm obj, ConstraintValidatorContext context) {
    if (obj.getNodes() == null) {
      return true;
    }
    var groupedValues =
        obj.getNodes().stream()
            .collect(groupingBy(n -> n.identifier().orElse(EMPTY_IDENTIFIER)))
            .entrySet()
            .stream()
            .filter(v -> !EMPTY_IDENTIFIER.equals(v.getKey()))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    var hasDuplicates = groupedValues.entrySet().stream().anyMatch(e -> e.getValue().size() > 1);

    if (hasDuplicates) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(formatErrorMessage(groupedValues))
          .addPropertyNode("nodes")
          .addConstraintViolation();
    }

    return !hasDuplicates;
  }

  private String formatErrorMessage(Map<String, List<CurveNodeForm>> forms) {
    var duplicateNodesError =
        forms.values().stream()
            .filter(v -> v.size() > 1)
            .map(
                v ->
                    v.stream()
                        .map(CurveNodeInstrument::getInstrument)
                        .collect(Collectors.joining(DUPLICATE_DELIMITER)))
            .collect(Collectors.joining(", "));
    return String.format(DUPLICATE_ERROR_TEMPLATE, duplicateNodesError);
  }
}
