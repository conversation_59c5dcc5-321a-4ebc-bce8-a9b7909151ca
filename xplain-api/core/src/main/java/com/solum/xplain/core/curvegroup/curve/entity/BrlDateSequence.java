package com.solum.xplain.core.curvegroup.curve.entity;

import com.opengamma.strata.basics.date.DateSequence;
import com.opengamma.strata.collect.ArgChecker;
import java.time.LocalDate;
import java.time.YearMonth;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@EqualsAndHashCode
@ToString
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BrlDateSequence implements DateSequence {

  public static final BrlDateSequence QUARTERLY_BRL_IMM = new BrlDateSequence();

  private static final String NAME = "Quarterly-BRL-IMM";

  @Override
  public LocalDate next(LocalDate date) {
    return nth(date, 1);
  }

  @Override
  public LocalDate nextOrSame(LocalDate date) {
    return nthOrSame(date, 1);
  }

  @Override
  public LocalDate nth(LocalDate date, int sequenceNumber) {
    ArgChecker.notNegativeOrZero(sequenceNumber, "sequenceNumber");
    LocalDate base = date.withDayOfMonth(1);
    if (!base.isAfter(date)) {
      base = base.plusMonths(1);
    }
    return shift(base, sequenceNumber);
  }

  @Override
  public LocalDate nthOrSame(LocalDate date, int sequenceNumber) {
    ArgChecker.notNegativeOrZero(sequenceNumber, "sequenceNumber");
    LocalDate base = date.withDayOfMonth(1);
    if (base.isBefore(date)) {
      base = base.plusMonths(1);
    }
    return shift(base, sequenceNumber);
  }

  private LocalDate shift(LocalDate base, int sequenceNumber) {
    var date = base.plusMonths(sequenceNumber * 3L);
    int monthInQuarter = (date.getMonthValue() + 2) % 3; // 0/1/2
    int monthsUntilNextQuarter = 2 - monthInQuarter;
    return date.plusMonths(-monthsUntilNextQuarter).withDayOfMonth(1);
  }

  @Override
  public LocalDate dateMatching(YearMonth yearMonth) {
    return nextOrSame(yearMonth.atDay(1));
  }

  @Override
  public String getName() {
    return NAME;
  }
}
