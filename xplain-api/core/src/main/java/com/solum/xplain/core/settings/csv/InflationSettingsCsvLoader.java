package com.solum.xplain.core.settings.csv;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseBigDecimal;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseNonNegativeInteger;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValueInRange;
import static com.solum.xplain.core.settings.value.InflationSeasonalitySettingsType.AUTO;
import static com.solum.xplain.core.settings.value.InflationSeasonalitySettingsType.MANUAL;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.settings.InflationSettingsTypeSupplier;
import com.solum.xplain.core.settings.entity.CurveSeasonality;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class InflationSettingsCsvLoader
    extends GenericCsvLoader<CurveCalibrationSettings<CurveSeasonality>, String> {

  static final String PRICE_INDEX = "Price Index";
  static final String JAN = "January";
  static final String FEB = "February";
  static final String MAR = "March";
  static final String APR = "April";
  static final String MAY = "May";
  static final String JUN = "June";
  static final String JUL = "July";
  static final String AUG = "August";
  static final String SEP = "September";
  static final String OCT = "October";
  static final String NOV = "November";
  static final String DEC = "December";
  static final String PERIOD = "Observation Period";
  static final String TYPE = "Settings Type";

  public static final List<String> INFLATION_SEASONALITY_CSV_HEADERS =
      List.of(
          PRICE_INDEX, JAN, FEB, MAR, APR, MAY, JUN, JUL, AUG, SEP, OCT, NOV, DEC, PERIOD, TYPE);

  private static final List<String> MONTHS =
      List.of(JAN, FEB, MAR, APR, MAY, JUN, JUL, AUG, SEP, OCT, NOV, DEC);

  @Override
  protected CsvParserResultBuilder<CurveCalibrationSettings<CurveSeasonality>, String> createResult(
      ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        CurveCalibrationSettings::getCurveName, Function.identity(), parsingMode.failOnError());
  }

  @Override
  protected List<String> getFileHeaders() {
    return INFLATION_SEASONALITY_CSV_HEADERS;
  }

  @Override
  protected Either<ErrorItem, CurveCalibrationSettings<CurveSeasonality>> parseLine(
      @NonNull CsvRow row) {
    try {
      var priceIndex =
          validateValue(row.getValue(PRICE_INDEX), new ClassifierSupplier("inflationCurveIndices"));
      var seasonality = parseSeasonality(row);
      seasonality.setPriceIndex(priceIndex);
      return right(new CurveCalibrationSettings<>(priceIndex, seasonality));
    } catch (RuntimeException e) {
      return left(rowParsingError(row, e));
    }
  }

  private CurveSeasonality parseSeasonality(CsvRow row) {
    String type = validateValue(row.getValue(TYPE), new InflationSettingsTypeSupplier());
    if (type.equals(MANUAL.name())) {
      return parseManual(row);
    } else {
      return parseAuto(row);
    }
  }

  private CurveSeasonality parseAuto(CsvRow row) {
    var period = parseNonNegativeInteger(row, PERIOD);

    CurveSeasonality seasonality = new CurveSeasonality();
    seasonality.setSettingsType(AUTO);
    seasonality.setObservationPeriod(validateValueInRange(period, 2, 20));
    return seasonality;
  }

  private CurveSeasonality parseManual(CsvRow row) {
    var monthlyAdjustments =
        MONTHS.stream()
            .collect(
                (Supplier<HashMap<String, BigDecimal>>) HashMap::new,
                (map, month) -> map.put(month, parseBigDecimal(row.getValue(month))),
                HashMap::putAll);
    CurveSeasonality seasonality = new CurveSeasonality();
    seasonality.setSettingsType(MANUAL);
    seasonality.setJan(monthlyAdjustments.get(JAN));
    seasonality.setFeb(monthlyAdjustments.get(FEB));
    seasonality.setMar(monthlyAdjustments.get(MAR));
    seasonality.setApr(monthlyAdjustments.get(APR));
    seasonality.setMay(monthlyAdjustments.get(MAY));
    seasonality.setJun(monthlyAdjustments.get(JUN));
    seasonality.setJul(monthlyAdjustments.get(JUL));
    seasonality.setAug(monthlyAdjustments.get(AUG));
    seasonality.setSep(monthlyAdjustments.get(SEP));
    seasonality.setOct(monthlyAdjustments.get(OCT));
    seasonality.setNov(monthlyAdjustments.get(NOV));
    seasonality.setDec(monthlyAdjustments.get(DEC));
    return seasonality;
  }
}
