package com.solum.xplain.core.portfolio.builder;

import com.solum.xplain.core.portfolio.trade.CreditTradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.ToString;

@Builder
@ToString
@EqualsAndHashCode
public class ResolvableCreditIndexDetails implements ResolvableTradeDetails {

  // Credit Index
  private final String entityLongName;
  private final Integer creditIndexVersion;
  private final Integer creditIndexSeries;

  // Credit Index Tranche
  private final String creditIndexTranche;

  @NonNull private final CommonCreditTradeDetails commonCreditTradeDetails;

  @Override
  public TradeDetails toTradeDetails(TradeInfoDetails tradeInfo) {
    return commonCreditTradeDetails.toTradeDetails(creditTradeDetails(), tradeInfo);
  }

  private CreditTradeDetails creditTradeDetails() {
    var details = new CreditTradeDetails();
    details.setEntityLongName(entityLongName);
    details.setCreditIndexSeries(creditIndexSeries);
    details.setCreditIndexVersion(creditIndexVersion);
    details.setCreditIndexTranche(creditIndexTranche);
    return details;
  }
}
