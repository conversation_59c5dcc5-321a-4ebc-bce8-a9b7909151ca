package com.solum.xplain.core.customfield.repository.fragments;

import static org.springframework.data.domain.Sort.unsorted;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.diff.DiffAuditedRepositoryImpl;
import com.solum.xplain.core.customfield.CustomFieldName;
import com.solum.xplain.core.customfield.CustomFieldNameMapper;
import com.solum.xplain.core.customfield.value.CustomFieldNameCreateForm;
import com.solum.xplain.core.customfield.value.CustomFieldNameUpdateForm;
import com.solum.xplain.core.customfield.value.CustomFieldNameView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import java.util.List;
import java.util.stream.Stream;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.auditing.AuditingHandler;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.stereotype.Component;

@Component
public class CustomFieldNameQueriesImpl
    extends DiffAuditedRepositoryImpl<
        CustomFieldNameCreateForm, CustomFieldNameUpdateForm, CustomFieldName>
    implements CustomFieldNameQueries {

  private final ConversionService conversionService;

  public CustomFieldNameQueriesImpl(
      MongoOperations mongoOperations,
      CustomFieldNameMapper mapper,
      AuditingHandler auditingHandler,
      ConversionService conversionService) {
    super(mongoOperations, CustomFieldName.class, "Custom field name", mapper, auditingHandler);
    this.conversionService = conversionService;
  }

  @Override
  public List<CustomFieldNameView> list(TableFilter tableFilter, Sort sort, boolean archived) {
    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(where(CustomFieldName.Fields.archived).is(archived)))
            .add(
                project()
                    .and(CustomFieldName.Fields.id)
                    .as(CustomFieldNameView.Fields.id)
                    .and(CustomFieldName.Fields.name)
                    .as(CustomFieldNameView.Fields.name)
                    .and(CustomFieldName.Fields.externalId)
                    .as(CustomFieldNameView.Fields.externalId))
            .add(match(tableFilter.criteria(CustomFieldNameView.class, conversionService)));

    if (!sort.equals(unsorted())) {
      operations.add(sort(sort));
    } else {
      operations.add(sort(Sort.by(CustomFieldNameView.Fields.externalId)));
    }
    return mongoOperations
        .aggregate(
            newAggregation(CustomFieldName.class, operations.build()), CustomFieldNameView.class)
        .getMappedResults();
  }

  @Override
  public List<String> activeFieldExternalIds() {
    return activeFieldNames().map(CustomFieldNameView::getExternalId).toList();
  }

  @Override
  public Stream<CustomFieldNameView> activeFieldNames() {
    return list(TableFilter.emptyTableFilter(), unsorted(), false).stream();
  }

  @Override
  public boolean existsByExternalId(String externalSourceId) {
    return mongoOperations.exists(
        query(
            where(CustomFieldName.Fields.externalId)
                .is(externalSourceId)
                .and(CustomFieldName.Fields.archived)
                .is(false)),
        CustomFieldName.class);
  }

  @Override
  public EntityId entityId(CustomFieldName entity) {
    return EntityId.entityId(entity.getId());
  }
}
