package com.solum.xplain.core.curvegroup.curvegroup;

import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_MODIFY_CURVE_GROUP;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_CURVE_GROUP;
import static com.solum.xplain.core.authentication.Authorities.AUTHORITY_VIEW_MARKET_DATA_KEY;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemFileResponse;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;

import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.ScrolledFiltered;
import com.solum.xplain.core.common.Sorted;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupCountedView;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupFilter;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupForm;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.time.LocalDate;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.web.SortDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/curve-group")
public class CurveGroupController {

  private final CurveGroupControllerService service;

  public CurveGroupController(CurveGroupControllerService service) {
    this.service = service;
  }

  @ScrolledFiltered
  @CommonErrors
  @GetMapping
  @Sorted
  @PreAuthorize(AUTHORITY_VIEW_CURVE_GROUP)
  public ScrollableEntry<CurveGroupCountedView> getAll(
      @RequestParam LocalDate stateDate,
      @SortDefault(sort = CurveGroupView.Fields.name) ScrollRequest scrollRequest,
      TableFilter tableFilter,
      CurveGroupFilter groupFilter) {
    return service.getAll(stateDate, scrollRequest, tableFilter, groupFilter);
  }

  @Operation(summary = "Get Curve Group")
  @GetMapping("/{groupId}")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_CURVE_GROUP)
  public ResponseEntity<CurveGroupCountedView> get(
      @PathVariable("groupId") String id, @RequestParam LocalDate stateDate) {
    return eitherErrorItemResponse(service.get(id, stateDate));
  }

  @Operation(summary = "Create new curve group")
  @CommonErrors
  @PostMapping
  @PreAuthorize(AUTHORITY_MODIFY_CURVE_GROUP)
  public ResponseEntity<EntityId> newCurveGroup(@Valid @RequestBody CurveGroupForm newForm) {
    return eitherErrorItemResponse(service.create(newForm));
  }

  @Operation(summary = "Update curveGroup")
  @CommonErrors
  @PutMapping("/{groupId}")
  @PreAuthorize(AUTHORITY_MODIFY_CURVE_GROUP)
  public ResponseEntity<EntityId> editCurveGroup(
      @PathVariable("groupId") String id, @Valid @RequestBody CurveGroupForm edit) {
    return eitherErrorItemResponse(service.update(id, edit));
  }

  @Operation(summary = "Archive curve group")
  @PutMapping("/{groupId}/archive")
  @CommonErrors
  @PreAuthorize(AUTHORITY_MODIFY_CURVE_GROUP)
  public ResponseEntity<EntityId> archiveCurveGroup(@PathVariable("groupId") String groupId) {
    return eitherErrorItemResponse(service.archiveCurveGroup(groupId));
  }

  @Operation(summary = "Gets all Curve Group MDK definitions")
  @GetMapping("/{groupId}/mdk-definitions")
  @CommonErrors
  @PreAuthorize(AUTHORITY_VIEW_MARKET_DATA_KEY)
  public ResponseEntity<ByteArrayResource> getAllCurveGroupMarketDataKeyDefinitionsCsv(
      @PathVariable("groupId") String groupId,
      @RequestParam("stateDate") LocalDate stateDate,
      @RequestParam String configurationId) {
    var bitemporalDate = BitemporalDate.newOf(stateDate);
    return eitherErrorItemFileResponse(
        service.getAllCurveGroupMDKDefinitionsCsvBytes(groupId, bitemporalDate, configurationId));
  }
}
