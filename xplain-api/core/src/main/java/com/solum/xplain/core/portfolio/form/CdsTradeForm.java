package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.portfolio.CoreProductType.CDS;
import static org.slf4j.LoggerFactory.getLogger;

import com.solum.xplain.core.classifiers.ClassifiersControllerService;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableCdsDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableCdsDetails.ResolvableCdsDetailsBuilder;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.groups.CdsTradeGroup;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import com.solum.xplain.extensions.enums.CreditSeniority;
import io.atlassian.fugue.Either;
import jakarta.validation.GroupSequence;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.slf4j.Logger;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@GroupSequence(value = {CdsTradeForm.class, CdsTradeGroup.class})
public class CdsTradeForm extends CommonCreditTradeForm implements ParsableToTradeValue {

  private static final Logger LOG = getLogger(CdsTradeForm.class);

  @NotEmpty
  @ValidStringSet(
      value = ClassifierSupplier.class,
      supplierArgument = ClassifiersControllerService.CREDIT_SENIORITY_CLASSIFIER)
  private String seniority;

  private String entityLongName;

  private String corpTicker;

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return toTrade(toTradeInfo()).map(trade -> defaultTradeValue(CDS, trade));
  }

  protected Either<ErrorItem, TradeDetails> toTrade(TradeInfoDetails tradeInfo) {
    try {
      return Either.right(buildTrade(tradeInfo));
    } catch (RuntimeException ex) {
      LOG.debug(ex.getMessage(), ex);
      return Either.left(new ErrorItem(Error.CONVERSION_ERROR, ex.getMessage()));
    }
  }

  private TradeDetails buildTrade(TradeInfoDetails tradeInfo) {
    ResolvableCdsDetailsBuilder builder =
        ResolvableCdsDetails.builder()
            .entityLongName(entityLongName)
            .seniority(CreditSeniority.valueOf(seniority))
            .corpTicker(corpTicker)
            .commonCreditTradeDetails(commonDetails());

    return builder.build().toTradeDetails(tradeInfo);
  }
}
