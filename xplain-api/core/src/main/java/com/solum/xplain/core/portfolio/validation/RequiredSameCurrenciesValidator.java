package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class RequiredSameCurrenciesValidator
    implements ConstraintValidator<RequiredSameCurrencies, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }
    var leg1 = form.getLeg1();
    var leg2 = form.getLeg2();
    if (hasCurrency(leg1) && hasCurrency(leg2) && hasDifferentCurrencies(leg1, leg2)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode("leg2")
          .addPropertyNode("notionalCurrency")
          .addConstraintViolation();
      return false;
    }
    return true;
  }

  private boolean hasCurrency(SwapLegForm form) {
    return form != null && form.getNotionalCurrency() != null;
  }

  private boolean hasDifferentCurrencies(SwapLegForm f1, SwapLegForm f2) {
    return !StringUtils.equals(f1.getNotionalCurrency(), f2.getNotionalCurrency());
  }
}
