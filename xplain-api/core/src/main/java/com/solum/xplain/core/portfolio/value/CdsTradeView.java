package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.CoreProductType.CDS;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.CdsTradeForm;
import com.solum.xplain.core.portfolio.form.PaymentDateForm;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CdsTradeView extends CdsTradeForm implements TradeView {

  private String updatedBy;
  private LocalDateTime updatedAt;
  private String tradeId;
  private String calendar;

  public static Either<ErrorItem, CdsTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == CDS) {
      var view = new CdsTradeView();
      view.updateCommonView(item);
      view.withTradeInfo(item.getTradeDetails());
      view.setCalendar(item.getTradeDetails().getCalendar());
      view.setPosition(item.getTradeDetails().getPositionType().name());
      item.getTradeDetails()
          .tradePositionLeg()
          .ifPresent(
              leg -> {
                view.setDayCount(leg.getDayCount());
                view.setNotionalValue(leg.getNotional());
                view.setCurrency(leg.getCurrency());
                view.setFixedRate(leg.getInitialValue());
                view.setFrequency(leg.getPaymentFrequency());
              });

      var creditDetails = item.getTradeDetails().getCreditTradeDetails();
      view.setStartDate(item.getTradeDetails().getStartDate());
      view.setEndDate(item.getTradeDetails().getEndDate());
      view.setReference(creditDetails.getReference());
      view.setCorpTicker(creditDetails.getCorpTicker());
      view.setSeniority(creditDetails.getSeniority().name());
      view.setDocClause(creditDetails.getDocClause().name());
      view.setSector(creditDetails.getSector().name());
      view.setEntityLongName(creditDetails.getEntityLongName());
      Optional.ofNullable(creditDetails.getUpfront())
          .map(
              value ->
                  PaymentDateForm.of(
                      creditDetails.getUpfrontDate(), value, creditDetails.getUpfrontConvention()))
          .ifPresent(view::setUpfrontFee);
      return Either.right(view);

    } else {
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Product type is not " + "expected " + item.getProductType()));
    }
  }
}
