package com.solum.xplain.core.portfolio.trade.type;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.lock.XplainLock.TRADES_LOCK_ID;

import com.solum.xplain.core.authentication.Authorities;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import com.solum.xplain.core.portfolio.value.TradeView;
import io.atlassian.fugue.Either;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.groups.Default;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping("/portfolio/{id}/trades")
public interface BespokeTradeTypedController<T extends ParsableToTradeValue, R extends TradeView> {

  TradeTypeControllerService getService();

  Either<ErrorItem, R> toViewFunction(PortfolioItem e);

  @Operation(summary = "Create trade")
  @CommonErrors
  @PostMapping
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_TRADE)
  @RequireLock(name = TRADES_LOCK_ID)
  default ResponseEntity<EntityId> insert(
      @PathVariable("id") String portfolioId,
      @Validated(value = {BespokeTradeGroup.class, Default.class}) @RequestBody T form) {
    return eitherErrorItemResponse(getService().insert(portfolioId, form));
  }

  @Operation(summary = "Update trade")
  @CommonErrors
  @PutMapping("{tradeEntityId}/{version}")
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_TRADE)
  @RequireLock(name = TRADES_LOCK_ID)
  default ResponseEntity<EntityId> update(
      @PathVariable("id") String portfolioId,
      @PathVariable("tradeEntityId") String tradeEntityId,
      @PathVariable("version") LocalDate version,
      @Validated(value = {BespokeTradeGroup.class, Default.class}) @RequestBody T form) {

    return eitherErrorItemResponse(getService().update(portfolioId, version, form, tradeEntityId));
  }

  @Operation(summary = "Get trade")
  @GetMapping("/{tradeEntityId}")
  @CommonErrors
  @PreAuthorize(Authorities.AUTHORITY_VIEW_TRADE)
  default ResponseEntity<R> get(
      @PathVariable("id") String portfolioId,
      @PathVariable String tradeEntityId,
      @RequestParam LocalDate stateDate,
      @RequestParam(required = false) LocalDateTime recordDate) {
    var bitemporalDate = BitemporalDate.newOfNullable(stateDate, recordDate);
    return eitherErrorItemResponse(
        getService()
            .tradeView(portfolioId, tradeEntityId, bitemporalDate)
            .flatMap(this::toViewFunction));
  }
}
