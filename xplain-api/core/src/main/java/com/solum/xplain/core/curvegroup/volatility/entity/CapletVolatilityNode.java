package com.solum.xplain.core.curvegroup.volatility.entity;

import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.CAP_FLOOR_VOL;
import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofVol;

import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.market.validation.VolatilityValueKey;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.Locale;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CapletVolatilityNode {

  private static final String CAP_FLOOR_VOLATILITY_MD_KEY_PATTERN = "%s_%s_CF_%s";
  private static final String CAP_FLOOR_VOLATILITY_MD_NAME_PATTERN = "%S CF %S %S";

  private String tenor;
  private BigDecimal strike;

  public InstrumentDefinition instrument(
      String assetName, String index, String ccy, String underlying) {
    return ofVol(
        assetName,
        ccy,
        CAP_FLOOR_VOL,
        getKey(index).getKey(),
        getName(assetName),
        tenor,
        tenor,
        underlying);
  }

  public VolatilityValueKey getKey(String index) {
    return new VolatilityValueKey(
        String.format(CAP_FLOOR_VOLATILITY_MD_KEY_PATTERN, getTenor(), formatStrike(), index));
  }

  public String getName(String assetName) {
    return String.format(
        CAP_FLOOR_VOLATILITY_MD_NAME_PATTERN, assetName, formatStrike(), getTenor());
  }

  private String formatStrike() {
    if (getStrike() == null) {
      return null;
    }
    DecimalFormat df = new DecimalFormat("0", DecimalFormatSymbols.getInstance(Locale.ENGLISH));
    df.setMaximumFractionDigits(340);
    df.setMinimumFractionDigits(2);
    String strikeStr = df.format(getStrike().multiply(BigDecimal.valueOf(100)).doubleValue());
    return strikeStr + "%";
  }

  @EqualsAndHashCode.Include(replaces = "strike")
  private BigDecimal normalisedStrike() {
    return strike == null ? null : strike.stripTrailingZeros();
  }
}
