package com.solum.xplain.core.portfolio.form;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.NOT_REQUIRED;

import com.solum.xplain.core.common.validation.BusinessDayConventionsSupplier;
import com.solum.xplain.core.common.validation.UpperCase;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.portfolio.validation.RequiredDifferentFxLegIdentifiers;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.core.portfolio.value.CurrencyAmountForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@RequiredDifferentFxLegIdentifiers
public class CommonFxTradeForm extends BespokeTradeForm {

  @Schema(description = "Date of payment")
  @NotNull
  private LocalDate paymentDate;

  @NotEmpty
  @Schema(description = "Business day convention")
  @ValidStringSet(BusinessDayConventionsSupplier.class)
  private String businessDayConvention;

  @Schema(description = "Base currency amount")
  @Valid
  @NotNull
  private CurrencyAmountForm domesticCurrencyAmount;

  @Schema(description = "Counter currency amount")
  @Valid
  @NotNull
  private CurrencyAmountForm foreignCurrencyAmount;

  @NotNull(groups = ReferenceTradeGroup.class)
  @Null(groups = BespokeTradeGroup.class)
  @Positive
  @Schema(description = "Fx rate. Only required for SecMaster trades.", requiredMode = NOT_REQUIRED)
  private Double fxRate;

  @Schema(description = "Pay leg external identifier")
  @UpperCase
  @ValidIdentifier
  private String payLegExtIdentifier;

  @Schema(description = "Receive leg external identifier")
  @UpperCase
  @ValidIdentifier
  private String receiveLegExtIdentifier;

  @Override
  protected String tradeCurrency() {
    return domesticCurrencyAmount.getCurrency();
  }
}
