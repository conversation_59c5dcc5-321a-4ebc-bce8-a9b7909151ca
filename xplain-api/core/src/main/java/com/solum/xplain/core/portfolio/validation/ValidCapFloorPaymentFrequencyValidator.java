package com.solum.xplain.core.portfolio.validation;

import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import com.solum.xplain.core.portfolio.form.CapFloorTradeForm;
import com.solum.xplain.core.utils.FrequencyUtils;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Optional;
import org.apache.commons.lang3.ObjectUtils;

public class ValidCapFloorPaymentFrequencyValidator
    implements ConstraintValidator<ValidCapFloorPaymentFrequency, CapFloorTradeForm> {

  public static final String INVALID_PAYMENT_FREQUENCY =
      "Payment Frequency must come from IBOR index attributes";

  public static boolean validatePaymentFrequency(String paymentFrequency, String iborIndexTenor) {
    return paymentFrequency.equals(iborIndexTenor);
  }

  @Override
  public boolean isValid(CapFloorTradeForm form, ConstraintValidatorContext context) {
    if (ObjectUtils.anyNull(form, form.getCalculationIborIndex(), form.getPaymentFrequency())) {
      return true;
    }
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
        .addPropertyNode("paymentFrequency")
        .addConstraintViolation();
    return Steps.begin(parseFrequency(form.getPaymentFrequency()))
        .then(() -> parseIndexTenor(form.getCalculationIborIndex()))
        .yield(ValidCapFloorPaymentFrequencyValidator::validatePaymentFrequency)
        .orElse(true);
  }

  private Optional<String> parseFrequency(String frequencyStr) {
    return Checked.now(() -> FrequencyUtils.toStringNoPrefix(Frequency.parse(frequencyStr)))
        .toOptional();
  }

  private Optional<String> parseIndexTenor(String iborIndex) {
    return Checked.now(() -> IborIndex.of(iborIndex).getTenor().toString()).toOptional();
  }
}
