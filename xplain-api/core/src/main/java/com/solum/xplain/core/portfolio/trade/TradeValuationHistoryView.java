package com.solum.xplain.core.portfolio.trade;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class TradeValuationHistoryView {
  @Schema(description = "Calculation result ID", requiredMode = REQUIRED)
  private String calculationResultId;

  @Schema(description = "Portfolio ID", requiredMode = REQUIRED)
  private String portfolioId;

  @Schema(description = "Trade ID", requiredMode = REQUIRED)
  private String tradeEntityId;

  @Schema(description = "External trade ID", requiredMode = REQUIRED)
  private String externalTradeId;

  @Schema(description = "Valuation Date", requiredMode = REQUIRED)
  private LocalDate valuationDate;

  @Schema(description = "Market data group name", requiredMode = REQUIRED)
  private String marketDataGroupName;

  @Schema(description = "Reporting currency", requiredMode = REQUIRED)
  private String reportingCurrency;

  @Schema(description = "PV in reporting currency", requiredMode = REQUIRED)
  private BigDecimal presentValueReportingCurrency;

  private String exceptionManagementResultId;

  @Schema(description = "Price source", requiredMode = REQUIRED)
  private String pricingSource;

  @Schema(description = "Dashboard ID", requiredMode = REQUIRED)
  private String dashboardId;
}
