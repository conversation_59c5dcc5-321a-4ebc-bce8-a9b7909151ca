package com.solum.xplain.core.portfolio.validation;

import com.opengamma.strata.basics.schedule.StubConvention;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class ValidAccrualRegularDatesValidator
    implements ConstraintValidator<ValidAccrualRegularDates, HasRegularDates> {

  private static final String NOT_NULL_MESSAGE = "jakarta.validation.constraints.NotNull.message";
  private static final String NULL_MESSAGE = "jakarta.validation.constraints.Null.message";

  public static final String GREATER_THAN_START_MESSAGE =
      "com.solum.xplain.core.portfolio.validation.GreaterThanStartDate";
  public static final String LESS_THAN_END_DATE_MESSAGE =
      "com.solum.xplain.core.portfolio.validation.LessThanEndDate";
  public static final String GREATER_THAN_REGULAR_START_DATE_MESSAGE =
      "com.solum.xplain.core.portfolio.validation.GreaterThanRegularStartDate";

  private static final String REGULAR_START_DATE = "regularStartDate";
  private static final String REGULAR_END_DATE = "regularEndDate";

  @SuppressWarnings("java:S2589")
  public boolean isValid(HasRegularDates form, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    try {
      boolean result = true;
      if (StringUtils.equalsIgnoreCase(form.getStubConvention(), StubConvention.BOTH.toString())) {
        if (form.getRegularStartDate() == null) {
          context
              .buildConstraintViolationWithTemplate(toTemplate(NOT_NULL_MESSAGE))
              .addPropertyNode(REGULAR_START_DATE)
              .addConstraintViolation();
          result = false;
        } else if (form.getStartDate() != null
            && form.getStartDate().isAfter(form.getRegularStartDate())) {
          context
              .buildConstraintViolationWithTemplate(toTemplate(GREATER_THAN_START_MESSAGE))
              .addPropertyNode(REGULAR_START_DATE)
              .addConstraintViolation();
          result = false;
        }
        if (form.getRegularEndDate() == null) {
          context
              .buildConstraintViolationWithTemplate(toTemplate(NOT_NULL_MESSAGE))
              .addPropertyNode(REGULAR_END_DATE)
              .addConstraintViolation();
          result = false;
        } else if (form.getEndDate() != null
            && form.getRegularEndDate().isAfter(form.getEndDate())) {
          context
              .buildConstraintViolationWithTemplate(toTemplate(LESS_THAN_END_DATE_MESSAGE))
              .addPropertyNode(REGULAR_END_DATE)
              .addConstraintViolation();
          result = false;
        }
        if (form.getRegularStartDate() != null
            && form.getRegularEndDate() != null
            && !form.getRegularStartDate().isBefore(form.getRegularEndDate())) {
          context
              .buildConstraintViolationWithTemplate(
                  toTemplate(GREATER_THAN_REGULAR_START_DATE_MESSAGE))
              .addPropertyNode(REGULAR_END_DATE)
              .addConstraintViolation();
          result = false;
        }
      } else {
        if (form.getRegularStartDate() != null) {
          context
              .buildConstraintViolationWithTemplate(toTemplate(NULL_MESSAGE))
              .addPropertyNode(REGULAR_START_DATE)
              .addConstraintViolation();
          result = false;
        }
        if (form.getRegularEndDate() != null) {
          context
              .buildConstraintViolationWithTemplate(toTemplate(NULL_MESSAGE))
              .addPropertyNode(REGULAR_END_DATE)
              .addConstraintViolation();
          result = false;
        }
      }
      return result;
    } catch (IllegalArgumentException ex) {
      return true;
    }
  }

  private String toTemplate(String key) {
    return String.format("{%s}", key);
  }
}
