package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.getPrefix;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.portfolio.value.PortfolioFilter.activePortfolios;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.team.UserTeamEntity;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.ValuationDataKeyUtils;
import com.solum.xplain.core.ipv.data.IpvDataType;
import com.solum.xplain.core.ipv.data.csv.IpvDataCsvMapper;
import com.solum.xplain.core.ipv.data.value.IpvDataProviderValueView;
import com.solum.xplain.core.portfolio.csv.PortfolioCsvMapper;
import com.solum.xplain.core.portfolio.csv.PortfolioItemCsvMapper;
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.portfolio.value.PortfolioFilter;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import com.solum.xplain.core.teams.TeamRepository;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class PortfolioExportService {

  private final PortfolioTeamFilterProvider filterProvider;
  private final AuthenticationContext authenticationContext;
  private final PortfolioRepository portfolioRepository;
  private final PortfolioItemRepository itemRepository;
  private final PortfolioItemCsvMapper itemCsvMapper;
  private final TeamRepository teamRepository;

  public Either<ErrorItem, FileResponseEntity> exportPortfolios(
      Sort sort,
      TableFilter tableFilter,
      PortfolioFilter portfolioFilter,
      LocalDate stateDate,
      List<String> selectedColumns) {
    var csvFileName = nameWithTimeStamp("PortfolioList", stateDate);
    var filter = filterProvider.provideFilter();
    var file = exportPortfolios(filter, sort, tableFilter, portfolioFilter, selectedColumns);
    return Either.right(FileResponseEntity.csvFile(file.writeToByteArray(), csvFileName));
  }

  private CsvOutputFile exportPortfolios(
      PortfolioTeamFilter teamFilter,
      Sort sort,
      TableFilter tableFilter,
      PortfolioFilter portfolioFilter,
      List<String> selectedColumns) {
    var mapper = new PortfolioCsvMapper(selectedColumns, teamRepository.getTeamsById());

    var portfolioViewStream =
        portfolioRepository
            .portfolioViewStream(tableFilter, sort, portfolioFilter, teamFilter)
            .map(mapper::toCsvRow)
            .map(Either::<ErrorItem, CsvRow>right);
    return new CsvOutputFile(mapper.header(), portfolioViewStream);
  }

  public Either<ErrorItem, FileResponseEntity> exportAllPortfolioItems(
      BitemporalDate stateDate, Sort sort) {
    var portfolios = userPortfolios();
    return Either.right(exportPortfolioItems(portfolios, stateDate, sort));
  }

  private FileResponseEntity exportPortfolioItems(
      List<PortfolioView> portfolioList, BitemporalDate stateDate, Sort sort) {
    var portfolioIds = portfolioList.stream().map(PortfolioView::getId).collect(toSet());
    var items = itemRepository.portfoliosItemsStream(portfolioIds, stateDate, sort);
    var csvFile = itemCsvMapper.generatePortfolioItemCsv(items);
    var prefix = getPrefix("Trades", portfolioList, PortfolioCondensedView::getExternalPortfolioId);
    var csvFileName = nameWithTimeStamp(prefix, stateDate);

    return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
  }

  public Either<ErrorItem, FileResponseEntity> exportPortfolioItems(
      BitemporalDate stateDate, String id, Sort sort, TableFilter tableFilter) {
    var items = itemRepository.portfolioItemsStream(id, stateDate, sort, tableFilter);

    return getUserPortfolio(id)
        .map(UserTeamEntity::getView)
        .map(
            p -> {
              var csvFile = itemCsvMapper.generatePortfolioItemCsv(items);
              var csvFileName =
                  nameWithTimeStamp(
                      p.getExternalPortfolioId(), "Trades", stateDate.getActualDate());
              return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
            });
  }

  public Either<ErrorItem, FileResponseEntity> exportPortfolioTradeDataKeys(
      String id, BitemporalDate stateDate) {
    return getUserPortfolio(id)
        .map(UserTeamEntity::getView)
        .map(
            portfolio -> {
              var fileName =
                  nameWithTimeStamp(
                      portfolio.getExternalPortfolioId(),
                      "ValuationDataDefinitions",
                      stateDate.getActualDate());
              return FileResponseEntity.csvFile(file(portfolio, stateDate), fileName);
            });
  }

  private ByteArrayResource file(PortfolioView view, BitemporalDate stateDate) {
    var mapper = new IpvDataCsvMapper(IpvDataType.ALL);
    var rows =
        itemRepository
            .activePortfolioItemsStream(view.getId(), stateDate)
            .map(i -> toIpvDataValueView(view, i.getExternalTradeId()))
            .sorted(Comparator.comparing(IpvDataProviderValueView::getKey))
            .map(mapper::toCsvRow)
            .map(Either::<ErrorItem, CsvRow>right);
    return new CsvOutputFile(mapper.header(), rows).writeToByteArray();
  }

  public Either<ErrorItem, FileResponseEntity> exportAllPortfolioTradeDataKeys(
      BitemporalDate stateDate) {
    var mapper = new IpvDataCsvMapper(IpvDataType.ALL);
    var portfoliosMap =
        userPortfolios().stream().collect(toMap(PortfolioCondensedView::getId, v -> v));

    var rows =
        itemRepository
            .portfoliosItemsStream(portfoliosMap.keySet(), stateDate)
            .map(i -> toIpvDataValueView(portfoliosMap, i))
            .sorted(Comparator.comparing(IpvDataProviderValueView::getKey))
            .map(mapper::toCsvRow)
            .toList();
    var prefix =
        getPrefix(
            "ValuationDataDefinitions",
            new ArrayList<>(portfoliosMap.values()),
            PortfolioCondensedView::getExternalPortfolioId);
    var csvFileName = nameWithTimeStamp(prefix, stateDate);

    var csvFile = new CsvOutputFile(mapper.header(), rows);
    return Either.right(FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName));
  }

  private IpvDataProviderValueView toIpvDataValueView(
      Map<String, PortfolioView> portfolioViewMap, PortfolioItem item) {

    return toIpvDataValueView(portfolioViewMap.get(item.getPortfolioId().toHexString()), item);
  }

  private IpvDataProviderValueView toIpvDataValueView(
      PortfolioView view, PortfolioItem portfolioItem) {
    return toIpvDataValueView(view, portfolioItem.getExternalTradeId());
  }

  private IpvDataProviderValueView toIpvDataValueView(PortfolioView view, String externalTradeId) {
    var key =
        ValuationDataKeyUtils.toValuationDataKey(
            view.getExternalCompanyId(),
            view.getExternalEntityId(),
            view.getExternalPortfolioId(),
            externalTradeId);
    var v = new IpvDataProviderValueView();
    v.setKey(key);
    return v;
  }

  private Either<ErrorItem, UserTeamEntity<PortfolioView>> getUserPortfolio(String id) {
    return portfolioRepository.getUserPortfolioView(authenticationContext.currentUser(), id);
  }

  private List<PortfolioView> userPortfolios() {
    var filter = filterProvider.provideFilter();
    return portfolioRepository.portfolioViewList(activePortfolios(), filter);
  }
}
