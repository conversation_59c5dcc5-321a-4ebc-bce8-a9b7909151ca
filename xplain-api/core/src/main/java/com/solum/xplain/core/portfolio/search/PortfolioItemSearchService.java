package com.solum.xplain.core.portfolio.search;

import static com.solum.xplain.core.portfolio.value.PortfolioFilter.activePortfolios;

import com.solum.xplain.core.authentication.AuthenticationContext;
import com.solum.xplain.core.portfolio.PortfolioTeamFilterProvider;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.search.SearchRequest;
import com.solum.xplain.core.search.SearchResponse;
import com.solum.xplain.core.search.SearchTradeView;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class PortfolioItemSearchService {
  private final PortfolioItemSearchRepository repository;
  private final PortfolioRepository portfolioRepository;
  private final PortfolioTeamFilterProvider filterProvider;
  private final AuthenticationContext authenticationContext;

  public SearchResponse<SearchTradeView> search(SearchRequest request) {
    var portfolios = permissiblePortfolioIds();
    return repository.portfolioItems(portfolios, request);
  }

  private List<String> permissiblePortfolioIds() {
    var user = authenticationContext.currentUser();
    var teamFilter = filterProvider.provideFilter(user);
    return portfolioRepository.portfolioViews(activePortfolios(), teamFilter).stream()
        .map(PortfolioCondensedView::getId)
        .toList();
  }
}
