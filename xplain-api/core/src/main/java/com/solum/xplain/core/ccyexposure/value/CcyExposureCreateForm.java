package com.solum.xplain.core.ccyexposure.value;

import com.solum.xplain.core.ccyexposure.validation.UniqueCcyExposureName;
import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@UniqueCcyExposureName
public class CcyExposureCreateForm extends CcyExposureUpdateForm {

  @NotEmpty @ValidIdentifier private String name;
}
