package com.solum.xplain.core;

import static java.time.ZoneOffset.UTC;

import com.solum.xplain.core.config.properties.CalculationProperties;
import com.solum.xplain.core.config.properties.DefaultsProperties;
import com.solum.xplain.core.config.properties.IpvExceptionManagementProperties;
import com.solum.xplain.core.config.properties.OAuth2CustomProperties;
import com.solum.xplain.core.config.properties.PreviewFeatureProperties;
import com.solum.xplain.extensions.StrataExtensionsConfig;
import java.util.TimeZone;
import org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.TypeExcludeFilter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;

@Configuration
@ComponentScan(
    excludeFilters = {
      @Filter(type = FilterType.CUSTOM, classes = TypeExcludeFilter.class),
      @Filter(type = FilterType.CUSTOM, classes = AutoConfigurationExcludeFilter.class)
    })
@EnableConfigurationProperties({
  CalculationProperties.class,
  OAuth2CustomProperties.class,
  PreviewFeatureProperties.class,
  DefaultsProperties.class,
  IpvExceptionManagementProperties.class,
})
@EntityScan(basePackages = {"com.solum.xplain.core"})
@Import(StrataExtensionsConfig.class)
public class CoreConfig {
  static {
    if (!TimeZone.getDefault().hasSameRules(TimeZone.getTimeZone(UTC))) {
      throw new IllegalStateException(
          "The timezone, "
              + TimeZone.getDefault().getID()
              + ", is not UTC which persistence and the API require. Please set -Duser.timezone=UTC in JVM arguments.");
    }
  }
}
