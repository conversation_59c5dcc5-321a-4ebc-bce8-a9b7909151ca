package com.solum.xplain.core.portfolio;

import com.solum.xplain.core.common.versions.embedded.EmbeddedVersion;
import com.solum.xplain.core.common.versions.embedded.EmbeddedVersionEntity;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Data
@FieldNameConstants
@NoArgsConstructor
public class PortfolioItemEntity implements EmbeddedVersionEntity<TradeValue> {

  public static final String PORTFOLIO_ITEM_WRITE_COLLECTION = "portfolioItemEntity";

  @Id private String id;
  private String semanticId;
  private String portfolioId;
  private String externalTradeId;
  private LocalDateTime portfolioArchivedAt;
  private List<EmbeddedVersion<TradeValue>> versions = List.of();

  public PortfolioItemEntity(PortfolioItemUniqueKey key) {
    this.id = new ObjectId().toString();
    this.semanticId = key.uniqueId();
    this.portfolioId = key.getPortfolioId();
    this.externalTradeId = key.getExternalTradeId();
  }

  public PortfolioItemUniqueKey uniqueKey() {
    return PortfolioItemUniqueKey.newOf(portfolioId, externalTradeId);
  }
}
