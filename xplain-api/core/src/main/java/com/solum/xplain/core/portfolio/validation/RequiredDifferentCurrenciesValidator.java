package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.SingleExchangeFxTradeForm;
import com.solum.xplain.core.portfolio.value.CurrencyAmountForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class RequiredDifferentCurrenciesValidator
    implements ConstraintValidator<RequiredDifferentCurrencies, SingleExchangeFxTradeForm> {

  public boolean isValid(SingleExchangeFxTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }

    var foreign = form.getForeignCurrencyAmount();
    var domestic = form.getDomesticCurrencyAmount();

    if (hasCurrency(foreign) && hasCurrency(domestic) && sameCurrencies(foreign, domestic)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate("RequiredDifferentCurrencies")
          .addPropertyNode("foreignCurrencyAmount")
          .addPropertyNode("currency")
          .addConstraintViolation();

      return false;
    }

    return true;
  }

  public boolean hasCurrency(CurrencyAmountForm form) {
    return form != null && StringUtils.isNotEmpty(form.getCurrency());
  }

  public boolean sameCurrencies(CurrencyAmountForm f1, CurrencyAmountForm f2) {
    return StringUtils.equals(f1.getCurrency(), f2.getCurrency());
  }
}
