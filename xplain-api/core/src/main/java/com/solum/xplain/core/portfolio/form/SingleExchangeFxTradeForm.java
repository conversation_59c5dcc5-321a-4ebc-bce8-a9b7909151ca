package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.value.CurrencyAmountForm;
import io.atlassian.fugue.Either;

/**
 * Interface for FX trade form where only one exchange of cashflows occurs. Therefore, forms such as
 * {@link FxSwapTradeForm} are not included, since they involve two exchanges of cashflows.
 */
public interface SingleExchangeFxTradeForm {

  CurrencyAmountForm getDomesticCurrencyAmount();

  void setDomesticCurrencyAmount(CurrencyAmountForm domesticCurrencyAmount);

  CurrencyAmountForm getForeignCurrencyAmount();

  void setForeignCurrencyAmount(CurrencyAmountForm foreignCurrencyAmount);

  default Either<ErrorItem, CurrencyAmountForm> payCurrencyAmount() {
    if (getDomesticCurrencyAmount().getAmount() < 0) {
      return Either.right(getDomesticCurrencyAmount());
    } else if (getForeignCurrencyAmount().getAmount() < 0) {
      return Either.right(getForeignCurrencyAmount());
    } else {
      return Either.left(Error.CONVERSION_ERROR.entity("Pay amount must be negative!"));
    }
  }

  default Either<ErrorItem, CurrencyAmountForm> receiveCurrencyAmount() {
    if (getDomesticCurrencyAmount().getAmount() > 0) {
      return Either.right(getDomesticCurrencyAmount());
    } else if (getForeignCurrencyAmount().getAmount() > 0) {
      return Either.right(getForeignCurrencyAmount());
    } else {
      return Either.left(Error.CONVERSION_ERROR.entity("Receive amount must be positive!"));
    }
  }
}
