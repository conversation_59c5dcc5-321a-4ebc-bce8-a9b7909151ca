package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.AllocationTradeForm;
import com.solum.xplain.core.portfolio.form.ClientMetricsForm;
import com.solum.xplain.core.product.ProductType;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.function.Function;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AllocationTradeView extends AllocationTradeForm implements TradeView {

  private String updatedBy;
  private LocalDateTime updatedAt;
  private String tradeId;
  private String calendar;

  private String referenceTradeEntityId;
  private ProductType referenceTradeProductType;

  public static Either<ErrorItem, AllocationTradeView> of(
      VersionedTradeEntity item,
      Function<String, Optional<PortfolioItemRefDetailsView>> fetchReferenceTrade) {
    var allocationTradeDetails = item.getAllocationTradeDetails();
    if (allocationTradeDetails != null) {
      var view = new AllocationTradeView();
      view.updateCommonView(item);
      view.setClientMetrics(new ClientMetricsForm(allocationTradeDetails.getClientMetrics()));

      view.setReferenceTradeId(allocationTradeDetails.getReferenceTradeId());
      view.setAllocationNotional(allocationTradeDetails.getAllocationNotional());
      view.setPositionType(allocationTradeDetails.getPositionType());
      view.setTradeCounterparty(allocationTradeDetails.getCounterParty());
      view.setTradeCounterpartyType(allocationTradeDetails.getCounterPartyType());
      view.setTradeDate(allocationTradeDetails.getTradeDate());
      view.setDescription(allocationTradeDetails.getDescription());
      view.setCsaDiscountingGroup(allocationTradeDetails.getCsaDiscountingGroup());
      view.setOptionPosition(allocationTradeDetails.getOptionPosition());
      view.setProtection(allocationTradeDetails.getProtection());

      fetchReferenceTrade
          .apply(allocationTradeDetails.getReferenceTradeId())
          .map(PortfolioItemRefDetailsView::getEntityId)
          .ifPresent(view::setReferenceTradeEntityId);
      view.setReferenceTradeProductType(item.getProductType());

      return Either.right(view);
    } else {
      return Either.left(new ErrorItem(Error.UNEXPECTED_TYPE, "Trade is not allocation trade"));
    }
  }
}
