package com.solum.xplain.core.curvegroup.curve.csv.node;

import com.solum.xplain.core.common.csv.NodeKey;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeForm;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CurveNode<PERSON>ey implements NodeKey {

  private final String identifier;

  public static CurveNodeKey from(CurveNodeForm form) {
    var identifier =
        form.identifier()
            .orElseThrow(() -> new IllegalStateException("Node form must have identifier"));
    return new CurveNode<PERSON>ey(identifier);
  }
}
