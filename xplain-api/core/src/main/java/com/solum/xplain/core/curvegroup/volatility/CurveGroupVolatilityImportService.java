package com.solum.xplain.core.curvegroup.volatility;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvegroup.volatility.csv.capletnode.VolatilityCapletNodeCsvImportService;
import com.solum.xplain.core.curvegroup.volatility.csv.node.VolatilityNodeCsvImportService;
import com.solum.xplain.core.curvegroup.volatility.csv.skew.VolatilitySkewCsvImportService;
import com.solum.xplain.core.curvegroup.volatility.csv.surface.VolatilitySurfaceCsvImportService;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurveGroupVolatilityImportService {

  private final CurveGroupRepository curveGroupRepository;
  private final VolatilitySurfaceCsvImportService surfaceCsvImportService;
  private final VolatilityNodeCsvImportService nodeCsvImportService;
  private final VolatilityCapletNodeCsvImportService capletNodeCsvImportService;
  private final VolatilitySkewCsvImportService skewCsvImportService;

  public CurveGroupVolatilityImportService(
      CurveGroupRepository curveGroupRepository,
      VolatilitySurfaceCsvImportService surfaceCsvImportService,
      VolatilityNodeCsvImportService nodeCsvImportService,
      VolatilityCapletNodeCsvImportService capletNodeCsvImportService,
      VolatilitySkewCsvImportService skewCsvImportService) {
    this.curveGroupRepository = curveGroupRepository;
    this.surfaceCsvImportService = surfaceCsvImportService;
    this.nodeCsvImportService = nodeCsvImportService;
    this.capletNodeCsvImportService = capletNodeCsvImportService;
    this.skewCsvImportService = skewCsvImportService;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadSurfaces(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return groupEither(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(g -> surfaceCsvImportService.importSurfaces(groupId, importOptions, bytes));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadNodes(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return groupEither(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(g -> nodeCsvImportService.importForAll(groupId, importOptions, bytes));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadNodesForSurface(
      String groupId,
      String surfaceId,
      LocalDate versionDate,
      ImportOptions importOptions,
      byte[] bytes) {
    return groupEither(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(
            g ->
                nodeCsvImportService.importForSurface(
                    groupId, surfaceId, versionDate, importOptions, bytes));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadCapletNodes(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return groupEither(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(g -> capletNodeCsvImportService.importForAll(groupId, importOptions, bytes));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadCapletNodesForSurface(
      String groupId,
      String surfaceId,
      LocalDate versionDate,
      ImportOptions importOptions,
      byte[] bytes) {
    return groupEither(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(
            g ->
                capletNodeCsvImportService.importForSurface(
                    groupId, surfaceId, versionDate, importOptions, bytes));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadSkews(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return groupEither(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(g -> skewCsvImportService.importForAll(groupId, importOptions, bytes));
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> uploadSkewsForSurface(
      String groupId,
      String surfaceId,
      LocalDate versionDate,
      ImportOptions importOptions,
      byte[] bytes) {
    return groupEither(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(
            g ->
                skewCsvImportService.importForSurface(
                    groupId, surfaceId, versionDate, importOptions, bytes));
  }

  private Either<ErrorItem, CurveGroupView> groupEither(String groupId) {
    return curveGroupRepository.getEither(groupId);
  }
}
