package com.solum.xplain.core.curvemarket;

import static com.solum.xplain.core.common.CollectionUtils.distinctByKeyFilter;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.getPrefix;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;
import static com.solum.xplain.core.common.csv.ExportFileNameUtils.zipNameWithTimeStamp;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toList;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.csv.FilesResponseBuilder;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository;
import com.solum.xplain.core.curvegroup.HasInstruments;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Pair;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Service
public class InstrumentMarketKeyDefinitionExportService {

  private static final String DEFINITIONS_PATTERN = "%s_MDKDefinitions";
  private static final String PROVIDERS_PATTERN = "%s_MDKProviders";
  private static final String ZIP_PATTERN = "%s_MDK";

  private final CurveConfigurationRepository curveConfigurationRepository;

  public InstrumentMarketKeyDefinitionExportService(
      CurveConfigurationRepository curveConfigurationRepository) {
    this.curveConfigurationRepository = curveConfigurationRepository;
  }

  public Either<ErrorItem, FileResponseEntity> allInstrumentMdkDefinitions(
      List<InstrumentDefinition> definitions, LocalDate versionDate, String curveConfigurationId) {
    var prefix = "All";
    return fileResponseEntity(definitions, versionDate, prefix, curveConfigurationId);
  }

  public <T extends HasInstruments> Either<ErrorItem, FileResponseEntity> instrumentMdkDefinitions(
      String collectionName,
      List<T> entities,
      Function<T, List<InstrumentDefinition>> instrumentExtractor,
      Function<T, String> entityNameExtractor,
      LocalDate versionDate,
      String curveConfigurationId) {
    var prefix = getPrefix(collectionName, entities, entityNameExtractor);
    return instrumentMdkDefinitions(
        entities, prefix, instrumentExtractor, versionDate, curveConfigurationId);
  }

  public <T extends HasInstruments> Either<ErrorItem, FileResponseEntity> instrumentMdkDefinitions(
      List<T> entities,
      String prefix,
      Function<T, List<InstrumentDefinition>> instrumentExtractor,
      LocalDate versionDate,
      String curveConfigurationId) {
    var definitions = instrumentDefinitions(entities, instrumentExtractor);
    return fileResponseEntity(definitions, versionDate, prefix, curveConfigurationId);
  }

  public <T extends HasInstruments>
      List<Pair<String, ByteArrayResource>> instrumentMdkDefinitionFiles(
          List<T> entities,
          Function<T, List<InstrumentDefinition>> instrumentExtractor,
          LocalDate versionDate,
          String prefix,
          String curveConfigurationId) {
    var definitions = instrumentDefinitions(entities, instrumentExtractor);
    return responseBuilder(definitions, versionDate, prefix, curveConfigurationId);
  }

  private Either<ErrorItem, FileResponseEntity> fileResponseEntity(
      List<InstrumentDefinition> definitions,
      LocalDate versionDate,
      String prefix,
      String curveConfigurationId) {
    return responseBuilder(definitions, versionDate, prefix, curveConfigurationId).stream()
        .collect(
            collectingAndThen(toList(), pairs -> zipResponseEntity(pairs, prefix, versionDate)));
  }

  private List<Pair<String, ByteArrayResource>> responseBuilder(
      List<InstrumentDefinition> definitions,
      LocalDate versionDate,
      String prefix,
      String curveConfigurationId) {
    ImmutableList.Builder<Pair<String, ByteArrayResource>> builder = ImmutableList.builder();
    if (StringUtils.isNotEmpty(curveConfigurationId)) {
      builder.add(providerFile(definitions, versionDate, prefix, curveConfigurationId));
    }
    builder.add(definitionFile(definitions, prefix, versionDate));
    return builder.build();
  }

  private <T extends HasInstruments> List<InstrumentDefinition> instrumentDefinitions(
      List<T> entities, Function<T, List<InstrumentDefinition>> instrumentExtractor) {
    return ofNullable(entities).stream()
        .flatMap(Collection::stream)
        .map(instrumentExtractor)
        .flatMap(Collection::stream)
        .toList();
  }

  private Either<ErrorItem, FileResponseEntity> zipResponseEntity(
      List<Pair<String, ByteArrayResource>> files, String prefix, LocalDate versionDate) {
    var filePrefix = String.format(ZIP_PATTERN, prefix);
    return FilesResponseBuilder.newResponse(zipNameWithTimeStamp(filePrefix, versionDate))
        .addAll(files)
        .build();
  }

  private Pair<String, ByteArrayResource> providerFile(
      List<InstrumentDefinition> definitions,
      LocalDate versionDate,
      String prefix,
      String curveConfigurationId) {
    var filePrefix = String.format(PROVIDERS_PATTERN, prefix);
    return Pair.pair(
        nameWithTimeStamp(filePrefix, versionDate),
        toConfigurationsCsvFile(definitions, versionDate, curveConfigurationId));
  }

  private Pair<String, ByteArrayResource> definitionFile(
      List<InstrumentDefinition> definitions, String prefix, LocalDate versionDate) {
    var filePrefix = String.format(DEFINITIONS_PATTERN, prefix);
    return Pair.pair(nameWithTimeStamp(filePrefix, versionDate), toDefinitionsCsvFile(definitions));
  }

  public ByteArrayResource toDefinitionsCsvFile(List<InstrumentDefinition> definitions) {
    return definitions.stream()
        .filter(distinctByKeyFilter(InstrumentDefinition::getKey))
        .map(CurveMarketKeyCsvMapper::toCsvRow)
        .collect(Collectors.collectingAndThen(toList(), CurveMarketKeyCsvMapper::csvOutputFile))
        .writeToByteArray();
  }

  public ByteArrayResource toConfigurationsCsvFile(
      List<InstrumentDefinition> definitions, LocalDate version, String curveConfigurationId) {
    var resolver =
        curveConfigurationRepository
            .curveConfigInstrResolver(new BitemporalDate(version), curveConfigurationId)
            .getOrElse(CurveConfigurationInstrumentResolver.empty());
    return definitions.stream()
        .filter(distinctByKeyFilter(InstrumentDefinition::getKey))
        .flatMap(
            d ->
                resolver
                    .resolveProvider(d)
                    .map(p -> CurveMarketConfigurationCsvMapper.toCsvRows(d.getKey(), p))
                    .stream())
        .flatMap(Collection::stream)
        .collect(
            Collectors.collectingAndThen(
                toList(), CurveMarketConfigurationCsvMapper::csvOutputFile))
        .writeToByteArray();
  }
}
