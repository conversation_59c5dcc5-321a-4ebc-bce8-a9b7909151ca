package com.solum.xplain.core.curveconfiguration;

import static com.google.common.collect.Streams.concat;
import static java.util.Comparator.comparing;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toUnmodifiableSet;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.common.team.EntityTeamFilter;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.core.company.repository.CompanyValuationSettingsRepository;
import com.solum.xplain.core.company.value.ValuationSettingsView;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
public class CurveConfigurationValuationsResolver {

  private final CurveConfigurationRepository curveConfigurationRepository;
  private final CompanyValuationSettingsRepository valuationSettingsRepository;
  private final CompanyRepository companyRepository;

  public CurveConfigurationValuationsResolver(
      CurveConfigurationRepository curveConfigurationRepository,
      CompanyValuationSettingsRepository valuationSettingsRepository,
      CompanyRepository companyRepository) {
    this.curveConfigurationRepository = curveConfigurationRepository;
    this.valuationSettingsRepository = valuationSettingsRepository;
    this.companyRepository = companyRepository;
  }

  public Map<String, List<CurveConfigurationInstrumentResolver>> curveConfigResolversByMarketData(
      BitemporalDate stateDate) {
    var resolvers = curveConfigurationRepository.curveConfigInstrResolvers(stateDate);
    return curveConfigsIdsByMarkedData(stateDate).entrySet().stream()
        .collect(
            toMap(
                Map.Entry::getKey,
                e ->
                    e.getValue().stream()
                        .flatMap(v -> resolvers.stream().filter(x -> x.getId().equals(v)))
                        .sorted(comparing(CurveConfigurationInstrumentResolver::getId))
                        .toList()));
  }

  public Map<String, Set<String>> curveConfigsIdsByMarkedData(BitemporalDate stateDate) {
    return companyRepository.companyList(EntityTeamFilter.emptyFilter()).stream()
        .flatMap(
            v ->
                valuationSettingsRepository
                    .getCompanyEntitySettingsResolver(v.getId(), stateDate)
                    .allUniqueSettings()
                    .stream())
        .filter(v -> isNotEmpty(v.getMarketDataGroupId()))
        .collect(
            toMap(
                ValuationSettingsView::getMarketDataGroupId,
                v ->
                    Stream.of(v.getCurveConfigurationId(), v.getNonFxCurveConfigurationId())
                        .filter(StringUtils::isNotEmpty)
                        .collect(toUnmodifiableSet()),
                (a, b) -> concat(a.stream(), b.stream()).collect(toUnmodifiableSet())));
  }
}
