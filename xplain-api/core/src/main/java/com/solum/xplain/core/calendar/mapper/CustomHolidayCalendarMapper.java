package com.solum.xplain.core.calendar.mapper;

import com.opengamma.strata.basics.date.ImmutableHolidayCalendar;
import com.solum.xplain.core.calendar.entity.CustomHolidayCalendar;
import com.solum.xplain.core.calendar.form.CustomHolidayCalendarForm;
import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.common.versions.VersionedEntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueMappingStrategy;

@Mapper(
    uses = {ObjectIdMapper.class, AuditUserMapper.class},
    nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface CustomHolidayCalendarMapper extends VersionedEntityMapper<CustomHolidayCalendar> {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  CustomHolidayCalendar fromForm(
      CustomHolidayCalendarForm form, @MappingTarget CustomHolidayCalendar calendar);

  default ImmutableHolidayCalendar toImmutableHolidayCalendar(
      CustomHolidayCalendar customHolidayCalendar) {
    return ImmutableHolidayCalendar.of(
        customHolidayCalendar.getHolidayCalendarId(),
        customHolidayCalendar.getHolidays(),
        customHolidayCalendar.getWeekendDays(),
        customHolidayCalendar.getWorkingDays());
  }
}
