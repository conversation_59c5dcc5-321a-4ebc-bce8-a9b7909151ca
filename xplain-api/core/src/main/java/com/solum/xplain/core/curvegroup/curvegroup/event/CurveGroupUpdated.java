package com.solum.xplain.core.curvegroup.curvegroup.event;

import com.solum.xplain.core.common.EntityEvent;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupForm;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class CurveGroupUpdated extends EntityEvent {
  private final CurveGroupForm form;

  public CurveGroupUpdated(String id, CurveGroupForm form) {
    super(id);
    this.form = form;
  }
}
