package com.solum.xplain.core.curvegroup.curvebond.csv.node;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseDate;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseOptionalDouble;
import static com.solum.xplain.core.curvegroup.curvebond.csv.node.BondCurveNodeCsvFields.COUPON;
import static com.solum.xplain.core.curvegroup.curvebond.csv.node.BondCurveNodeCsvFields.CURVE_NAME_FIELD;
import static com.solum.xplain.core.curvegroup.curvebond.csv.node.BondCurveNodeCsvFields.CUSIP;
import static com.solum.xplain.core.curvegroup.curvebond.csv.node.BondCurveNodeCsvFields.MATURITY_DATE;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static io.atlassian.fugue.Either.left;
import static io.atlassian.fugue.Either.right;
import static java.lang.String.format;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.common.csv.ItemKey;
import com.solum.xplain.core.common.csv.NodesCsvLoader;
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve;
import com.solum.xplain.core.curvegroup.curvebond.value.BondCurveNodeForm;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class BondCurveNodesCsvLoader extends NodesCsvLoader<BondCurve, BondCurveNodeForm> {

  @Override
  protected List<String> getCsvFileHeaders() {
    return List.of(CURVE_NAME_FIELD, MATURITY_DATE, CUSIP, COUPON);
  }

  @Override
  protected String getCurveName(BondCurve curve) {
    return curve.getName();
  }

  @Override
  protected Either<ErrorItem, BondCurveNodeForm> parse(@NonNull CsvRow row) {
    try {
      return right(
          new BondCurveNodeForm(
              row.getValue(CUSIP),
              parseDate(row.getValue(MATURITY_DATE)),
              parseOptionalDouble(row, COUPON).orElse(null)));
    } catch (RuntimeException e) {
      return left(
          new ErrorItem(
              PARSING_ERROR,
              format("Error parsing line %d: %s", row.lineNumber(), e.getMessage())));
    }
  }

  @Override
  protected ItemKey getNodeKey(BondCurveNodeForm form) {
    return BondCurveNodeKey.from(form);
  }
}
