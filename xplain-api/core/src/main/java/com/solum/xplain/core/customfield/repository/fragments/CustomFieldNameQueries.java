package com.solum.xplain.core.customfield.repository.fragments;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.diff.DiffAuditedRepository;
import com.solum.xplain.core.customfield.CustomFieldName;
import com.solum.xplain.core.customfield.value.CustomFieldNameCreateForm;
import com.solum.xplain.core.customfield.value.CustomFieldNameUpdateForm;
import com.solum.xplain.core.customfield.value.CustomFieldNameView;
import com.solum.xplain.shared.utils.filter.TableFilter;
import java.util.List;
import java.util.stream.Stream;
import org.springframework.data.domain.Sort;

public interface CustomFieldNameQueries
    extends DiffAuditedRepository<
        CustomFieldNameCreateForm, CustomFieldNameUpdateForm, CustomFieldName> {

  List<CustomFieldNameView> list(TableFilter tableFilter, Sort sort, boolean archived);

  List<String> activeFieldExternalIds();

  Stream<CustomFieldNameView> activeFieldNames();

  boolean existsByExternalId(String externalSourceId);

  EntityId entityId(CustomFieldName entity);
}
