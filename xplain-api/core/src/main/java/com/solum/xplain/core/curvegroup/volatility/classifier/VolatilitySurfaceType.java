package com.solum.xplain.core.curvegroup.volatility.classifier;

import com.opengamma.strata.market.ValueType;

public enum VolatilitySurfaceType {
  ATM_ONLY(null, null, "ATM Only"),
  STRIKE(SkewType.STRIKE, ValueType.STRIKE, "Absolute Strike"),
  MONEYNESS(SkewType.MONEYNESS, ValueType.SIMPLE_MONEYNESS, "Moneyness");

  private final SkewType skewType;
  private final ValueType valueType;
  private final String label;

  VolatilitySurfaceType(SkewType skewType, ValueType valueType, String label) {
    this.skewType = skewType;
    this.valueType = valueType;
    this.label = label;
  }

  public SkewType getSkewType() {
    return skewType;
  }

  public ValueType getValueType() {
    return valueType;
  }

  public String getLabel() {
    return label;
  }
}
