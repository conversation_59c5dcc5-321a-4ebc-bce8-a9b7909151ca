package com.solum.xplain.core.portfolio.csv.loader;

import static com.solum.xplain.core.common.creditindex.CreditIndexCsvUtils.defaultIndexValue;
import static com.solum.xplain.core.error.Error.PARSING_ERROR;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_ENTITY_LONG_NAME;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_INDEX_SERIES;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_INDEX_VERSION;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.classifiers.CdsIndex;
import com.solum.xplain.core.common.creditindex.CreditIndexCsvUtils;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.builder.ResolvableCreditIndexDetails;
import com.solum.xplain.core.portfolio.builder.ResolvableTradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvLoader;
import com.solum.xplain.extensions.enums.CreditSector;
import io.atlassian.fugue.Either;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class CreditIndexTradeCsvLoader implements ProductCsvLoader {

  private final CommonCreditTradeCsvLoader commonLoader;

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.CREDIT_INDEX);
  }

  @Override
  public Either<ErrorItem, ResolvableTradeDetails> parse(CsvRow row, boolean refSecTrade) {
    try {
      return Either.right(parseRow(row, refSecTrade).build());
    } catch (RuntimeException ex) {
      return Either.left(
          PARSING_ERROR.entity(
              String.format(
                  "Error at line number %s. Error: %s", row.lineNumber(), ex.getMessage())));
    }
  }

  @Override
  public Currency parseTradeCcy(CsvRow row) {
    return commonLoader.tradeCcy(row);
  }

  protected ResolvableCreditIndexDetails.ResolvableCreditIndexDetailsBuilder parseRow(
      CsvRow row, boolean refSecTrade) {
    var index = creditIndexLongName(row);
    var sector = defaultIndexValue(index, CdsIndex::getSector, CreditSector.UNDEFINED);
    var docClause = defaultIndexValue(index, CdsIndex::getDocClause, null);
    return ResolvableCreditIndexDetails.builder()
        .entityLongName(index)
        .creditIndexVersion(version(row))
        .creditIndexSeries(series(row))
        .commonCreditTradeDetails(commonLoader.parse(row, sector, () -> docClause, refSecTrade));
  }

  @Nullable
  private static Integer version(CsvRow row) {
    return row.findValue(TRADE_CREDIT_INDEX_VERSION)
        .map(Integer::parseInt)
        .map(CsvLoaderUtils::validatePositiveValue)
        .orElse(null);
  }

  @Nullable
  private static Integer series(CsvRow row) {
    return row.findValue(TRADE_CREDIT_INDEX_SERIES)
        .map(Integer::parseInt)
        .map(CsvLoaderUtils::validatePositiveValue)
        .orElse(null);
  }

  @Nullable
  protected static String creditIndexLongName(CsvRow row) {
    return CreditIndexCsvUtils.parseCreditIndex(row, TRADE_CREDIT_ENTITY_LONG_NAME);
  }
}
