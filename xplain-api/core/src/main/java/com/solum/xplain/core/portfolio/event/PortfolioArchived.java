package com.solum.xplain.core.portfolio.event;

import com.solum.xplain.core.company.entity.CompanyReference;
import java.util.Collection;
import java.util.List;
import org.jspecify.annotations.NullMarked;

@NullMarked
public record PortfolioArchived(Collection<String> companyIds, Collection<String> portfolioIds)
    implements CompanyPortfoliosModified {
  public PortfolioArchived(String companyId, String portfolioId) {
    this(List.of(companyId), List.of(portfolioId));
  }

  public PortfolioArchived(CompanyReference reference, String portfolioId) {
    this(reference.getEntityId(), portfolioId);
  }

  public static PortfolioArchived newOf(CompanyReference reference, String portfolioId) {
    return new PortfolioArchived(reference, portfolioId);
  }
}
