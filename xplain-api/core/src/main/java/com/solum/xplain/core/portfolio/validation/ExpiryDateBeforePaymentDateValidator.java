package com.solum.xplain.core.portfolio.validation;

import static org.apache.commons.lang3.ObjectUtils.anyNull;

import com.solum.xplain.core.portfolio.form.FxOptionTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ExpiryDateBeforePaymentDateValidator
    implements ConstraintValidator<ExpiryDateBeforePaymentDate, FxOptionTradeForm> {

  public boolean isValid(FxOptionTradeForm form, ConstraintValidatorContext context) {
    return form == null
        || anyNull(form.getPaymentDate(), form.getExpiryDate())
        || !form.getPaymentDate().isBefore(form.getExpiryDate());
  }
}
