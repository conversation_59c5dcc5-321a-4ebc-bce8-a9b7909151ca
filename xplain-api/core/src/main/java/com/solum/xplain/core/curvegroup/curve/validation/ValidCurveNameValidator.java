package com.solum.xplain.core.curvegroup.curve.validation;

import static com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations.lookupKeyByName;
import static com.solum.xplain.core.curvegroup.curve.CurveGroupCurveRepository.uniqueEntityCriteria;
import static org.apache.commons.lang3.ObjectUtils.anyNull;
import static org.apache.commons.lang3.StringUtils.equalsIgnoreCase;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.common.validation.UniqueEntitySupport;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curve.value.CurveForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.Collection;
import org.springframework.stereotype.Component;

@Component
public class ValidCurveNameValidator implements ConstraintValidator<ValidCurveName, CurveForm> {
  private static final String INVALID_NAME_FOR_TYPE_ERROR =
      "Invalid curve name : %s for curve type: %s";
  private static final String NOT_UNIQUE_NAME_ERROR = "Curve %s already exists at %s date";

  private final RequestPathVariablesSupport requestPathSupport;
  private final UniqueEntitySupport uniqueSupport;

  public ValidCurveNameValidator(
      RequestPathVariablesSupport requestPathSupport, UniqueEntitySupport uniqueSupport) {
    this.requestPathSupport = requestPathSupport;
    this.uniqueSupport = uniqueSupport;
  }

  public static boolean isNameValidForType(String name, String type) {
    return CurveType.toOptionalValue(type)
        .map(ConventionalCurveConfigurations::configurationsByType)
        .stream()
        .flatMap(Collection::stream)
        .anyMatch(obj -> equalsIgnoreCase(name, obj.getName()));
  }

  public static String invalidNameForTypeErrorMessage(String name, String type) {
    return String.format(INVALID_NAME_FOR_TYPE_ERROR, name, type);
  }

  @Override
  public boolean isValid(CurveForm value, ConstraintValidatorContext context) {
    if (value == null) {
      return true;
    }
    return isNameValid(value, context) && isNameUnique(value, context);
  }

  private boolean isNameValid(CurveForm value, ConstraintValidatorContext context) {
    var curveType = value.getCurveType();
    var name = value.getName();
    if (anyNull(curveType, name)) {
      return true;
    }

    if (!isNameValidForType(name, curveType)) {
      addError(context, invalidNameForTypeErrorMessage(name, curveType));
      return false;
    }
    return true;
  }

  private boolean isNameUnique(CurveForm form, ConstraintValidatorContext context) {
    String groupId = requestPathSupport.getPathVariable("groupId");
    var name = form.getName();
    var validFrom = form.getVersionForm().getValidFrom();

    if (!isUnique(groupId, validFrom, name)) {
      addError(context, notUniqueCurveErrorMessage(name, validFrom));
      return false;
    }
    return true;
  }

  private String notUniqueCurveErrorMessage(String name, LocalDate valuationDate) {
    return String.format(NOT_UNIQUE_NAME_ERROR, lookupKeyByName(name).orElse(name), valuationDate);
  }

  private boolean isUnique(
      @NotNull String groupId, @NotNull LocalDate validFrom, @NotNull String name) {
    return lookupKeyByName(name).map(ConventionalCurveConfigurations::lookupAllByKey).stream()
        .flatMap(Collection::stream)
        .map(CurveConvention::getName)
        .noneMatch(
            n ->
                uniqueSupport.existsByCriteria(
                    validFrom, uniqueEntityCriteria(groupId, n), Curve.class));
  }

  private void addError(ConstraintValidatorContext context, String message) {
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate(message)
        .addPropertyNode("name")
        .addConstraintViolation();
  }
}
