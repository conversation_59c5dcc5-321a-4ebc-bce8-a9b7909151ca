package com.solum.xplain.core.portfolio;

import static java.util.Optional.ofNullable;

import com.solum.xplain.core.common.versions.VersionedDataAggregations;
import com.solum.xplain.core.company.entity.CompanyLegalEntityReference;
import com.solum.xplain.core.company.entity.CompanyReference;
import com.solum.xplain.core.ipv.ValuationDataKeyUtils;
import com.solum.xplain.core.portfolio.form.PortfolioCreateForm;
import com.solum.xplain.core.portfolio.form.PortfolioUpdateForm;
import com.solum.xplain.core.portfolio.trade.TradeMapper;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.core.portfolio.value.PortfolioCountedView;
import com.solum.xplain.core.portfolio.value.PortfolioItemWithKeyView;
import com.solum.xplain.core.portfolio.value.PortfolioView;
import com.solum.xplain.core.teams.TeamMapper;
import com.solum.xplain.core.teams.value.TeamNameView;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.jspecify.annotations.Nullable;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(
    uses = {TradeMapper.class, TeamMapper.class},
    imports = {LocalDateTime.class, VersionedDataAggregations.class})
public interface PortfolioMapper {
  @Mapping(target = "modifiedBy", source = "item.modifiedBy.name")
  @Mapping(target = "tradeInfoExternalIdentifiers", source = "item.externalIdentifiers")
  @Mapping(target = "tradeInfoCustomFields", source = "item.customFields")
  @Mapping(target = "key", source = "item.valuationDataKey")
  PortfolioItemWithKeyView toKeyView(PortfolioItem item);

  @Mapping(target = "teamNames", source = "view.teamIds", qualifiedByName = "teamNamesLookup")
  PortfolioCountedView toCountedView(
      PortfolioView view,
      @Nullable Integer numberOfTrades,
      @Context Map<String, ? extends TeamNameView> teamNamesLookup);

  default PortfolioItem generateRead(
      PortfolioItemEntity entity, TradeValue value, PortfolioCondensedView portfolioView) {
    var view = new PortfolioItem();
    view.setPortfolioId(new ObjectId(entity.getPortfolioId()));
    if (portfolioView == null) {
      throw new IllegalStateException(
          String.format(
              "Could not load associated portfolio [%s] for external trade id [%s]. This is not a stable state, all trade must be associated with a portfolio.",
              entity.getPortfolioId(), entity.getExternalTradeId()));
    }

    view.setExternalPortfolioId(portfolioView.getExternalPortfolioId());
    view.setExternalCompanyId(portfolioView.getExternalCompanyId());
    view.setExternalEntityId(portfolioView.getExternalEntityId());
    var valuationDataKey =
        ValuationDataKeyUtils.toValuationDataKey(
            portfolioView.getExternalCompanyId(),
            portfolioView.getExternalEntityId(),
            portfolioView.getExternalPortfolioId(),
            entity.getExternalTradeId());
    view.setValuationDataKey(valuationDataKey);

    view.setPortfolioArchivedAt(entity.getPortfolioArchivedAt());
    view.setExternalTradeId(entity.getExternalTradeId());
    view.setDescription(value.getDescription());
    view.setClientMetrics(value.getClientMetrics());
    view.setProductType(value.getProductType());
    view.setTradeDetails(value.getTradeDetails());
    view.setAllocationTradeDetails(value.getAllocationTradeDetails());
    view.setOnboardingDetails(value.getOnboardingDetails());
    view.setExternalIdentifiers(value.getExternalIdentifiers());
    view.setCustomFields(value.getCustomFields());
    return view;
  }

  @Mapping(target = "name", source = "form.name")
  @Mapping(target = "description", source = "form.description")
  @Mapping(target = "teamIds", source = "form", qualifiedByName = "teamIds")
  @Mapping(target = "allowAllTeams", source = "form.allowedTeamsForm.allowAll")
  Portfolio updatedPortfolio(Portfolio originalPortfolio, PortfolioUpdateForm form);

  @Named("teamIds")
  default List<ObjectId> teamIds(PortfolioUpdateForm form) {
    return ofNullable(form.getAllowedTeamsForm().getTeamIds()).stream()
        .flatMap(List::stream)
        .map(ObjectId::new)
        .toList();
  }

  @Mapping(target = "calculatedAt", ignore = true)
  @Mapping(target = "calculatedBy", ignore = true)
  @Mapping(target = "archived", ignore = true)
  @Mapping(target = "valuationDate", ignore = true)
  @Mapping(target = "auditLogs", ignore = true)
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "lastModifiedAt", ignore = true)
  @Mapping(target = "lastModifiedBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "name", source = "form.name")
  @Mapping(target = "description", source = "form.description")
  @Mapping(target = "teamIds", source = "form", qualifiedByName = "teamIds")
  @Mapping(target = "allowAllTeams", source = "form.allowedTeamsForm.allowAll")
  @Mapping(target = "externalPortfolioId", source = "form.externalPortfolioId")
  @Mapping(target = "entity", source = "legalEntityReference")
  @Mapping(target = "company", source = "companyReference")
  Portfolio createPortfolio(
      PortfolioCreateForm form,
      CompanyReference companyReference,
      CompanyLegalEntityReference legalEntityReference);

  Portfolio shallowClone(Portfolio originalPortfolio);
}
