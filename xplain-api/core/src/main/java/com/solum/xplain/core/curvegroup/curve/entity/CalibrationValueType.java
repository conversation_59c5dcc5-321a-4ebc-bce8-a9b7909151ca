package com.solum.xplain.core.curvegroup.curve.entity;

import com.opengamma.strata.market.ValueType;

public enum CalibrationValueType {
  PRICE_INDEX("Price Index"),
  ZERO_COUPON_RATE("ZC Rate"),
  FORWARD_RATE("FWD Rate");

  private final String label;

  CalibrationValueType(String label) {
    this.label = label;
  }

  public String getLabel() {
    return label;
  }

  public String labelNoSpaces() {
    return label.replaceAll("\\s", "");
  }

  public static CalibrationValueType fromOGType(ValueType type) {
    if (ValueType.ZERO_RATE.equals(type)) {
      return ZERO_COUPON_RATE;
    } else if (ValueType.PRICE_INDEX.equals(type)) {
      return PRICE_INDEX;
    } else if (ValueType.FORWARD_RATE.equals(type)) {
      return FORWARD_RATE;
    } else {
      throw new IllegalStateException("Unexpected curve value type: " + type);
    }
  }
}
