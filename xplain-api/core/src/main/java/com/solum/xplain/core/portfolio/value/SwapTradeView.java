package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.form.SwapLegForm.fromSwapLeg;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class SwapTradeView extends SwapTradeForm implements TradeView {

  private String updatedBy;
  private LocalDateTime updatedAt;
  private String tradeId;
  private String calendar;

  void withTradeDetails(VersionedTradeEntity item) {
    var leg1 = fromSwapLeg(item.getTradeDetails().getPayLeg());
    var leg2 = fromSwapLeg(item.getTradeDetails().getReceiveLeg());
    this.updateCommonView(item);
    this.withTradeInfo(item.getTradeDetails());
    this.setEndDate(item.getTradeDetails().getEndDate());
    this.setStartDate(item.getTradeDetails().getStartDate());
    this.setStubConvention(item.getTradeDetails().getStubConvention());
    this.setRegularStartDate(item.getTradeDetails().getFirstRegularStartDate());
    this.setRegularEndDate(item.getTradeDetails().getLastRegularEndDate());
    this.setRollConvention(item.getTradeDetails().getRollConvention());
    this.setBusinessDayConvention(item.getTradeDetails().getBusinessDayConvention());
    this.setBusinessDayAdjustmentType(
        ofNullable(item.getTradeDetails().getBusinessDayAdjustmentType())
            .map(Enum::name)
            .orElse(null));
    this.setFinalExchange(isTrue(item.getTradeDetails().getNotionalScheduleFinalExchange()));
    this.setInitialExchange(isTrue(item.getTradeDetails().getNotionalScheduleInitialExchange()));
    this.setLeg1(leg1);
    this.setLeg2(leg2);
    this.setCalendar(item.getTradeDetails().getCalendar());
  }
}
