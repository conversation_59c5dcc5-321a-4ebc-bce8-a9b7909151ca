package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.portfolio.CoreProductType.FXCOLLAR;
import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.NOT_REQUIRED;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableFxCollarDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.ValidOtherOptionCounterNotional;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.extensions.enums.PositionType;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.extensions.step.Steps;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Positive;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Slf4j
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@ValidOtherOptionCounterNotional
public class FxCollarTradeForm extends FxOptionTradeForm {

  @Null(groups = ReferenceTradeGroup.class)
  @NotNull(groups = BespokeTradeGroup.class)
  @Schema(description = "Other option counter notional. (Null for reference trades)")
  private Double otherOptionCounterNotional;

  @NotNull(groups = ReferenceTradeGroup.class)
  @Null(groups = BespokeTradeGroup.class)
  @Positive
  @Schema(
      description = "Other option Fx rate. Only required for SecMaster trades.",
      requiredMode = NOT_REQUIRED)
  private Double otherOptionFxRate;

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return fxCollarTradeDetails(toTradeInfo()).map(details -> defaultTradeValue(FXCOLLAR, details));
  }

  private Either<ErrorItem, TradeDetails> fxCollarTradeDetails(TradeInfoDetails tradeInfo) {
    try {
      var builder =
          ResolvableFxCollarDetails.builder()
              .businessDayConvention(getBusinessDayConvention())
              .paymentDate(getPaymentDate())
              .positionType(PositionType.valueOf(getPosition()))
              .expiryDate(getExpiryDate())
              .expiryTime(getExpiryTime())
              .expiryZone(getExpiryZone())
              .premiumDate(getPremiumDate())
              .payLegExtIdentifier(StringUtils.upperCase(getPayLegExtIdentifier()))
              .receiveLegExtIdentifier(StringUtils.upperCase(getReceiveLegExtIdentifier()))
              .premiumCurrency(Currency.of(getPremiumValue().getCurrency()))
              .premiumValue(getPremiumValue().getAmount())
              .premiumDateConvention(getPremiumDateConvention())
              .otherOptionCounterNotional(otherOptionCounterNotional);
      if (getFxRate() != null || otherOptionFxRate != null) {
        // security master
        var baseCurrency = Currency.of(getDomesticCurrencyAmount().getCurrency());
        var counterCurrency = Currency.of(getForeignCurrencyAmount().getCurrency());
        var payCcy =
            getCallPutType() == null
                ? counterCurrency
                : switch (getCallPutType()) {
                  case PUT -> baseCurrency;
                  case CALL -> counterCurrency;
                };
        var recCcy = CurrencyPair.of(baseCurrency, counterCurrency).other(payCcy);
        return Either.right(
            builder
                .callPutType(getCallPutType())
                .strike(getFxRate())
                .otherOptionStrike(otherOptionFxRate)
                .payCurrency(payCcy)
                .receiveCurrency(recCcy)
                .build()
                .toTradeDetails(tradeInfo));
      }
      // bespoke trade
      return Steps.begin(payCurrencyAmount())
          .then(this::receiveCurrencyAmount)
          .yield(
              (pay, receive) ->
                  builder
                      .receiveCurrency(Currency.of(receive.getCurrency()))
                      .receiveCurrencyAmount(receive.getAmount())
                      .payCurrency(Currency.of(pay.getCurrency()))
                      .payCurrencyAmount(pay.getAmount())
                      .callPutType(getCallPutType())
                      .build()
                      .toTradeDetails(tradeInfo));
    } catch (RuntimeException ex) {
      log.debug(ex.getMessage(), ex);
      return Either.left(new ErrorItem(Error.CONVERSION_ERROR, ex.getMessage()));
    }
  }
}
