package com.solum.xplain.core.curvegroup.curve.value;

import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

/**
 * CurveNodeCalculatedView specifically for {@link
 * com.solum.xplain.core.curvegroup.curve.extension.IborFutureCurveNode}'s, which holds an
 * additional date that represents the delivery date (IMM date) plus the ibor index tenor.
 */
@Data
@FieldNameConstants
public class IborFutureCurveNodeCalculatedView extends CurveNodeCalculatedView {
  private LocalDate futureEndDate;
}
