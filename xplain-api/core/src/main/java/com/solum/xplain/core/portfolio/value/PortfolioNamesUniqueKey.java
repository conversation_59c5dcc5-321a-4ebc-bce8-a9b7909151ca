package com.solum.xplain.core.portfolio.value;

import lombok.Data;

@Data(staticConstructor = "newOf")
public class PortfolioNamesUniqueKey {
  private final String portfolioExternalId;
  private final String companyExternalId;
  private final String entityExternalId;

  public static PortfolioNamesUniqueKey fromView(PortfolioCondensedView view) {
    return PortfolioNamesUniqueKey.newOf(
        view.getExternalPortfolioId(), view.getExternalCompanyId(), view.getExternalEntityId());
  }
}
