package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.CoreProductType.SWAPTION;
import static com.solum.xplain.core.portfolio.form.SwapLegForm.fromSwapLeg;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.SwaptionTradeForm;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SwaptionTradeView extends SwaptionTradeForm implements TradeView {

  private String updatedBy;
  private LocalDateTime updatedAt;
  private String tradeId;
  private String calendar;

  public static Either<ErrorItem, SwaptionTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == SWAPTION) {
      var view = new SwaptionTradeView();
      var leg1 = fromSwapLeg(item.getTradeDetails().getPayLeg());
      var leg2 = fromSwapLeg(item.getTradeDetails().getReceiveLeg());
      view.withTradeInfo(item.getTradeDetails());
      view.setEndDate(item.getTradeDetails().getEndDate());
      view.setStartDate(item.getTradeDetails().getStartDate());
      view.setStubConvention(item.getTradeDetails().getStubConvention());
      view.setRegularStartDate(item.getTradeDetails().getFirstRegularStartDate());
      view.setRegularEndDate(item.getTradeDetails().getLastRegularEndDate());
      view.setRollConvention(item.getTradeDetails().getRollConvention());
      view.setBusinessDayConvention(item.getTradeDetails().getBusinessDayConvention());
      view.setBusinessDayAdjustmentType(
          item.getTradeDetails().getBusinessDayAdjustmentType().name());
      view.setFinalExchange(item.getTradeDetails().getNotionalScheduleFinalExchange());
      view.setInitialExchange(item.getTradeDetails().getNotionalScheduleInitialExchange());
      view.setLeg1(leg1);
      view.setLeg2(leg2);
      view.updateCommonView(item);
      view.withTradeDetails(item.getTradeDetails());
      view.setCalendar(item.getTradeDetails().getCalendar());
      return Either.right(view);

    } else {
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Product type is not " + "expected " + item.getProductType()));
    }
  }

  private void withTradeDetails(TradeDetails source) {
    this.setPosition(source.getPositionType().name());
    this.setExpiryDate(source.getOptionTradeDetails().getExpiryDate());
    this.setExpiryDateConvention(source.getOptionTradeDetails().getExpiryDateConvention());
    this.setExpiryTime(source.getOptionTradeDetails().getExpiryTime());
    this.setExpiryZone(source.getOptionTradeDetails().getExpiryZone());
    this.setPremiumDate(source.getOptionTradeDetails().getPremiumDate());
    this.setPremiumValue(source.getOptionTradeDetails().getPremiumValue());
    this.setPremiumDateConvention(source.getOptionTradeDetails().getPremiumDateConvention());
    this.setSwaptionSettlementType(source.getOptionTradeDetails().getSwaptionSettlementType());
  }
}
