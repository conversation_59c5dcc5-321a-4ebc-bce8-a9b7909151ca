package com.solum.xplain.core.curvegroup.curve.entity;

import static java.util.Optional.ofNullable;

import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.Index;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.index.IndexCurveConvention;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import java.util.Optional;

public interface CurveProjectionIndex {
  String getName();

  CurveType getCurveType();

  default String getIndices() {
    return ConventionalCurveConfigurations.lookupByName(getName(), getCurveType())
        .filter(IndexCurveConvention.class::isInstance)
        .map(IndexCurveConvention.class::cast)
        .map(IndexCurveConvention::getIndex)
        .map(Index::getName)
        .orElse(null);
  }

  default Optional<FloatingRateIndex> index() {
    try {
      return ofNullable(getIndices()).flatMap(FloatingRateIndex::tryParse);
    } catch (IllegalArgumentException ex) {
      return Optional.empty();
    }
  }
}
