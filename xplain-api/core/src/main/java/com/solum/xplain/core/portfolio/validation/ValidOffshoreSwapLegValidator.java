package com.solum.xplain.core.portfolio.validation;

import static com.solum.xplain.core.portfolio.validation.OffshoreIndexValidator.validateIborOffshoreIndex;
import static com.solum.xplain.core.portfolio.validation.OffshoreIndexValidator.validateOvernightOffshoreIndex;
import static org.apache.commons.lang3.BooleanUtils.isTrue;
import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.form.SwapLegForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;

public class ValidOffshoreSwapLegValidator
    implements ConstraintValidator<ValidOffshoreSwapLeg, SwapLegForm> {

  @Override
  public boolean isValid(SwapLegForm value, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    if (allNotNull(value.getIsOffshore(), value.getCalculationType())
        && isTrue(value.getIsOffshore())) {
      var error = validateLeg(value);
      error.ifPresent(
          errorItem ->
              context
                  .buildConstraintViolationWithTemplate(errorItem.getDescription())
                  .addPropertyNode(SwapLegForm.Fields.isOffshore)
                  .addConstraintViolation());
      return error.isEmpty();
    }
    return true;
  }

  private Optional<ErrorItem> validateLeg(SwapLegForm legForm) {
    return switch (legForm.getCalculationType()) {
      case IBOR -> validateIbor(legForm.getCalculationIborIndex());
      case OVERNIGHT -> validateOvernight(legForm.getCalculationOvernightIndex());
      default -> Optional.empty();
    };
  }

  private Optional<ErrorItem> validateOvernight(@Nullable String index) {
    if (StringUtils.isEmpty(index)) {
      return Optional.empty();
    }
    return validateOvernightOffshoreIndex(index);
  }

  private Optional<ErrorItem> validateIbor(@Nullable String index) {
    if (StringUtils.isEmpty(index)) {
      return Optional.empty();
    }
    return validateIborOffshoreIndex(index);
  }
}
