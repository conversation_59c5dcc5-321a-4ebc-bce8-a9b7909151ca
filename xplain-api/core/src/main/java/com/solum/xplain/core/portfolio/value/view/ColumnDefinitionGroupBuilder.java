package com.solum.xplain.core.portfolio.value.view;

import com.solum.xplain.core.viewconfig.provider.AbstractColumnDefinitionGroupBuilder;
import com.solum.xplain.core.viewconfig.value.FieldDefinitionView;
import com.solum.xplain.core.viewconfig.value.FieldType;
import jakarta.annotation.Nullable;
import java.util.function.Predicate;

public class ColumnDefinitionGroupBuilder extends AbstractColumnDefinitionGroupBuilder {
  private static final int DEFAULT_PRECISION = 2;

  public ColumnDefinitionGroupBuilder(
      Predicate<String> matcher, String label, Predicate<String> hidden) {
    super(matcher, label, hidden);
  }

  /**
   * Simple implementation to determine the default precision for a field.
   *
   * <p>Numeric fields have a default precision of 5 decimal places, but should generally be
   * overridden to have a default value of 2 decimal places. Numeric fields such as Par Rate should
   * be displayed as real numbers to be in line with other parts of Xplain, and therefore keep the 5
   * decimal places
   *
   * @param field the field to return precision for.
   * @return precision in decimal places, or null if a non-numeric field
   */
  @Override
  protected @Nullable Integer precision(FieldDefinitionView field) {
    if (field.type() == FieldType.NUMBER) {
      return DEFAULT_PRECISION;
    }
    return null;
  }
}
