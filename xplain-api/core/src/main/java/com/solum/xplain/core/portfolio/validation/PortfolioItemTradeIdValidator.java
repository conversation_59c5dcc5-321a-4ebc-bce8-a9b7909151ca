package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.portfolio.form.DefaultTradeForm;
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.time.LocalDate;

public class PortfolioItemTradeIdValidator extends TradeIdValidator
    implements ConstraintValidator<UniqueTradeId, DefaultTradeForm> {
  private static final String TRADE_ID_PATH = "tradeEntityId";
  private static final String PORTFOLIO_ID_PATH = "id";

  private final PortfolioItemRepository portfolioItemRepository;

  public PortfolioItemTradeIdValidator(
      RequestPathVariablesSupport requestPathVariablesSupport,
      PortfolioItemRepository portfolioItemRepository) {
    super(requestPathVariablesSupport, TRADE_ID_PATH, PORTFOLIO_ID_PATH);
    this.portfolioItemRepository = portfolioItemRepository;
  }

  @Override
  public boolean isValid(DefaultTradeForm value, ConstraintValidatorContext context) {
    if (value != null && value.getVersionForm() != null) {
      return isValid(value.getExternalTradeId(), value.stateDate(), context);
    }
    return true;
  }

  @Override
  protected boolean isUniqueTrade(
      LocalDate stateDate, String portfolioId, String externalId, String internalId) {
    return !portfolioItemRepository.hasPortfolioItemByExternalTradeId(
        BitemporalDate.newOf(stateDate), portfolioId, externalId, internalId);
  }
}
