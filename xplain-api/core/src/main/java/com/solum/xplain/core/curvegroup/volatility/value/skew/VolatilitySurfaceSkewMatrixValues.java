package com.solum.xplain.core.curvegroup.volatility.value.skew;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.value.VolatilityValueMatrix;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeValueView;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

public class VolatilitySurfaceSkewMatrixValues
    extends VolatilityValueMatrix<VolatilityNodeValueView> {

  private final BigDecimal skewValue;

  private VolatilitySurfaceSkewMatrixValues(
      BigDecimal skewValue, List<VolatilityNodeValueView> nodes, LocalDate versionDate) {
    super(nodes, versionDate);
    this.skewValue = skewValue;
  }

  public static VolatilitySurfaceSkewMatrixValues configurationFromList(
      BigDecimal skewValue, VersionedList<VolatilityNodeValueView> nodes) {
    return new VolatilitySurfaceSkewMatrixValues(
        skewValue, nodes.getList(), nodes.getVersionDate());
  }

  @Override
  protected Comparator<String> rowComparator() {
    return Comparator.comparing(Tenor::parse);
  }

  @Override
  protected Comparator<String> columnComparator() {
    return Comparator.comparing(Tenor::parse);
  }

  public BigDecimal getSkewValue() {
    return skewValue;
  }
}
