package com.solum.xplain.core.portfolio.csv.loader;

import static com.solum.xplain.core.classifiers.ClassifiersControllerService.DOC_CLAUSE_CLASSIFIER_NAME;
import static com.solum.xplain.core.classifiers.Constants.CREDIT_FREQUENCIES;
import static com.solum.xplain.core.classifiers.PermissibleConventions.CREDIT_CURRENCIES;
import static com.solum.xplain.core.classifiers.type.SupportedBusinessDayConvention.FOLLOWING;
import static com.solum.xplain.core.classifiers.type.SupportedDayCount.ACT_360;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseBusinessDayAdjustment;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseBusinessDayConvention;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.parseDayCount;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validatePositiveValue;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_ACCRUAL_FREQUENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_CURRENCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_DOC_CLAUSE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_FIXED_RATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_CREDIT_REFERENCE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_POSITION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_AMOUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_PREMIUM_DATE_ADJ_CONVENTION;
import static com.solum.xplain.core.portfolio.csv.loader.TradeCsvLoaderUtils.parseBusinessDayConvention;
import static com.solum.xplain.core.portfolio.csv.loader.TradeCsvLoaderUtils.parseEndDate;
import static com.solum.xplain.core.portfolio.csv.loader.TradeCsvLoaderUtils.parseStartDate;
import static com.solum.xplain.extensions.enums.BusinessDayAdjustmentType.ACCRUAL_AND_PAYMENT;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.collect.io.CsvRow;
import com.opengamma.strata.loader.LoaderUtils;
import com.solum.xplain.core.common.csv.CsvLoaderUtils;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.portfolio.builder.CommonCreditTradeDetails;
import com.solum.xplain.extensions.enums.CreditDocClause;
import com.solum.xplain.extensions.enums.CreditSector;
import com.solum.xplain.extensions.enums.PositionType;
import java.time.LocalDate;
import java.util.List;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class CommonCreditTradeCsvLoader {

  private static final String INVALID_CREDIT_REFERENCE = "Invalid credit reference";
  private static final ClassifierSupplier DOC_CLAUSE_SUPPLIER =
      new ClassifierSupplier(DOC_CLAUSE_CLASSIFIER_NAME);
  private static final double DEFAULT_FIXED_RATE = 0.01d;

  private static CreditDocClause docClause(
      CsvRow row, Supplier<CreditDocClause> docClauseSupplier) {
    return row.findValue(TRADE_CREDIT_DOC_CLAUSE)
        .map(str -> validateValue(str, TRADE_CREDIT_DOC_CLAUSE, DOC_CLAUSE_SUPPLIER))
        .map(CreditDocClause::valueOf)
        .orElseGet(docClauseSupplier);
  }

  public CommonCreditTradeDetails parse(
      CsvRow row,
      @NonNull CreditSector defaultSector,
      Supplier<CreditDocClause> docClauseSupplier,
      boolean refSecTrade) {
    validateBusinessDayConvention(row);
    validateBusinessDayAdjustmentType(row);
    validateStubConvention(row);
    validateDayCount(row);
    var startDate = parseStartDate(row);
    CommonCreditTradeDetails.CommonCreditTradeDetailsBuilder builder =
        CommonCreditTradeDetails.builder()
            .reference(reference(row))
            .sector(CreditSectorCsvLoader.sector(row, defaultSector))
            .docClause(docClause(row, docClauseSupplier))
            .currency(currency(row))
            .startDate(startDate)
            .position(position(row))
            .endDate(parseEndDate(row, startDate))
            .paymentFrequency(frequency(row))
            .fixedRate(fixedRate(row));

    if (!refSecTrade) {
      builder.notional(notional(row));
    }

    row.findValue(TRADE_PREMIUM_AMOUNT)
        .map(LoaderUtils::parseDouble)
        .ifPresent(
            upfront ->
                builder
                    .upfrontFee(upfront)
                    .upfrontDate(upfrontDate(row))
                    .upfrontConvention(
                        parseBusinessDayConvention(row, TRADE_PREMIUM_DATE_ADJ_CONVENTION)
                            .getName()));

    return builder.build();
  }

  public Currency tradeCcy(CsvRow row) {
    return CsvLoaderUtils.parseTradeCurrency(row).orElseGet(() -> currency(row));
  }

  private PositionType position(CsvRow row) {
    return row.getValue(
        TRADE_POSITION, p -> PositionType.fromBuySell(CsvLoaderUtils.parseBuySell(p)));
  }

  private Currency currency(CsvRow row) {
    var ccyStr = row.getValue(TRADE_CREDIT_CURRENCY);
    validateValue(ccyStr, () -> CREDIT_CURRENCIES);
    return Currency.of(ccyStr);
  }

  private double notional(CsvRow row) {
    Double notional = row.getValue(TRADE_CREDIT_NOTIONAL, LoaderUtils::parseDouble);
    return validatePositiveValue(notional, TRADE_CREDIT_NOTIONAL);
  }

  private LocalDate upfrontDate(CsvRow row) {
    return row.getValue(TRADE_PREMIUM_DATE, CsvLoaderUtils::parseDate);
  }

  private Frequency frequency(CsvRow row) {
    return row.findValue(TRADE_CREDIT_ACCRUAL_FREQUENCY)
        .map(
            freq ->
                CsvLoaderUtils.parseFrequency(
                    freq, TRADE_CREDIT_ACCRUAL_FREQUENCY, CREDIT_FREQUENCIES))
        .orElse(Frequency.P3M);
  }

  private double fixedRate(CsvRow row) {
    return row.findValue(TRADE_CREDIT_FIXED_RATE)
        .map(LoaderUtils::parseDouble)
        .orElse(DEFAULT_FIXED_RATE);
  }

  private String reference(CsvRow row) {
    return CsvLoaderUtils.parseIdentifier(
        row, TRADE_CREDIT_REFERENCE, () -> INVALID_CREDIT_REFERENCE);
  }

  private void validateStubConvention(CsvRow row) {
    row.findValue(TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION)
        .ifPresent(
            c ->
                validateValue(
                    StubConvention.of(c),
                    TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION,
                    () -> List.of(StubConvention.SMART_INITIAL)));
  }

  private void validateDayCount(CsvRow row) {
    row.findValue(TRADE_CREDIT_DAY_COUNT)
        .ifPresent(p -> parseDayCount(p, TRADE_CREDIT_DAY_COUNT, List.of(ACT_360)));
  }

  private void validateBusinessDayConvention(CsvRow row) {
    row.findValue(TRADE_BUSINESS_DAY_CONVENTION)
        .ifPresent(
            p -> parseBusinessDayConvention(p, TRADE_BUSINESS_DAY_CONVENTION, List.of(FOLLOWING)));
  }

  private void validateBusinessDayAdjustmentType(CsvRow row) {
    row.findValue(TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE)
        .ifPresent(
            p ->
                parseBusinessDayAdjustment(
                    p, TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE, List.of(ACCRUAL_AND_PAYMENT)));
  }
}
