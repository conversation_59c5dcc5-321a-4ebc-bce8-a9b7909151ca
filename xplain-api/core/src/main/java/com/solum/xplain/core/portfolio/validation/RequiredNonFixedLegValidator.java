package com.solum.xplain.core.portfolio.validation;

import static com.solum.xplain.core.portfolio.value.CalculationType.FIXED;
import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.stream.Stream;

public class RequiredNonFixedLegValidator
    implements ConstraintValidator<RequiredNonFixedLeg, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    if (allNotNull(form.getLeg1(), form.getLeg2())
        && Stream.of(form.getLeg1(), form.getLeg2())
            .map(SwapLegForm::getCalculationType)
            .allMatch(v -> v == FIXED)) {

      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode("leg1")
          .addPropertyNode("calculationType")
          .addConstraintViolation();
      return false;
    }
    return true;
  }
}
