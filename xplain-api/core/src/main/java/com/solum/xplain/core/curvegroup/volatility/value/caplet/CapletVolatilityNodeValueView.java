package com.solum.xplain.core.curvegroup.volatility.value.caplet;

import com.solum.xplain.core.common.value.MatrixValueNode;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CapletVolatilityNodeValueView implements MatrixValueNode {

  private String tenor;
  private BigDecimal strike;
  private Double value;

  @Override
  public String getColumn() {
    return strike.toString();
  }

  @Override
  public String getRow() {
    return getTenor();
  }
}
