package com.solum.xplain.core.curvegroup.curvebond.entity;

import static com.solum.xplain.core.common.CollectionUtils.nullSafeIsEqualCollection;
import static com.solum.xplain.core.curvegroup.conventions.bond.BondCurveConventions.findBondCurveByName;
import static java.util.Comparator.comparing;
import static java.util.Optional.ofNullable;

import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupEntry;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = BondCurve.BOND_CURVE_COLLECTION)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
@Getter
@Setter
public class BondCurve extends VersionedNamedEntity implements CurveGroupEntry {

  public static final String BOND_CURVE_COLLECTION = "bondCurve";
  private String curveGroupId;
  private String interpolator;
  private String extrapolatorLeft;
  private String extrapolatorRight;

  private List<BondCurveNode> nodes = new ArrayList<>();

  public static BondCurve newOf() {
    var curve = new BondCurve();
    curve.setEntityId(ObjectId.get().toString());
    curve.setState(State.ACTIVE);
    curve.setRecordDate(LocalDateTime.now());
    return curve;
  }

  public BondCurve orderNodes() {
    nodes =
        ofNullable(nodes).stream()
            .flatMap(Collection::stream)
            .sorted(comparing(BondCurveNode::getMaturityDate))
            .toList();
    return this;
  }

  @Override
  public List<InstrumentDefinition> allInstruments() {
    return findBondCurveByName(getName()).stream()
        .flatMap(
            c -> ofNullable(nodes).stream().flatMap(Collection::stream).map(n -> n.instrument(c)))
        .toList();
  }

  @Override
  public boolean valueEquals(Object object) {
    BondCurve entity = (BondCurve) object;
    return super.valueEquals(entity)
        && Objects.equals(this.curveGroupId, entity.curveGroupId)
        && Objects.equals(this.extrapolatorLeft, entity.extrapolatorLeft)
        && Objects.equals(this.extrapolatorRight, entity.extrapolatorRight)
        && Objects.equals(this.interpolator, entity.interpolator)
        && nullSafeIsEqualCollection(this.nodes, entity.nodes);
  }
}
