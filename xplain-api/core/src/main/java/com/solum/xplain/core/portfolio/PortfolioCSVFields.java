package com.solum.xplain.core.portfolio;

import static com.solum.xplain.core.common.csv.CsvLoaderUtils.addField;

import com.google.common.collect.ImmutableList;
import java.util.List;

public class PortfolioCSVFields {
  public static final String LEG_1 = "Leg1";
  public static final String LEG_2 = "Leg2";

  public static final String CAP_FLOOR = "Cap / Floor";
  public static final String COUNTERPARTY = "Counterparty";
  public static final String COUNTERPARTY_TYPE = "Counterparty Type";
  public static final String TRADE_DATE = "Trade Date";
  public static final String UNDERLYING = "Underlying";
  public static final String CURRENCIES = "Currencies";
  public static final String BUY_SELL = "Buy/Sell";
  public static final String DISCOUNT_CCY = "Discount Ccy";
  public static final String VALUATION_STATUS = "Valuation Status";
  public static final String VALUATION_ERROR = "Valuation Error";

  public static final String PAY_CURRENCY = "Pay Ccy";
  public static final String PAY_NOTIONAL = "Pay Notional";
  public static final String PAY_MARGIN = "Pay Rate/Margin";
  public static final String PAY_INDEX = "Pay Index";
  public static final String PAY_FREQUENCY = "Pay Freq";
  public static final String PAY_DAY_COUNT = "Pay Daycount";
  public static final String PAY_IDENTIFIER = "Pay Identifier";

  public static final String RECEIVE_CURRENCY = "Rec Ccy";
  public static final String RECEIVE_NOTIONAL = "Rec Notional";
  public static final String RECEIVE_MARGIN = "Rec Rate/Margin";
  public static final String RECEIVE_INDEX = "Rec Index";
  public static final String RECEIVE_FREQUENCY = "Rec Freq";
  public static final String RECEIVE_DAY_COUNT = "Rec Daycount";
  public static final String RECEIVE_IDENTIFIER = "Rec Identifier";

  // Leg Fields
  public static final String LEG_IDENTIFIER = "Identifier";
  public static final String PAY_RECEIVE = "PayReceive";
  public static final String LEG_TYPE = "Type";
  public static final String OFFSHORE = "Offshore";
  public static final String ACRUAL_SCHEDULE_FREQUENCY = "AccrualFreq";
  public static final String PAYMENT_SCHEDULE_FREQUENCY = "PaymentFreq";
  public static final String PAYMENT_SCHEDULE_DATE_OFFSET = "PaymentDaysOffset";
  public static final String PAYMENT_SCHEDULE_COMPOUNDING_METHOD = "CompoundingMethod";
  public static final String CALCULATION_FIXED_DAY_COUNT = "Fixed.DayCount";
  public static final String CALCULATION_FIXED_RATE_VALUE = "Fixed.Rate";
  public static final String CALCULATION_FIXED_ACCRUAL_METHOD = "Fixed.AccrualMethod";
  public static final String CALCULATION_IBOR_DAY_COUNT = "Ibor.DayCount";
  public static final String CALCULATION_IBOR_FIXING_OFFSET = "Ibor.FixingDaysOffset";
  public static final String CALCULATION_IBOR_SPREAD_VALUE = "Ibor.Margin";
  public static final String CALCULATION_IBOR_INDEX = "Ibor.Index";
  public static final String CALCULATION_INFLATION_INDEX = "Inflation.Index";
  public static final String CALCULATION_INFLATION_LAG = "Inflation.IndexLag";
  public static final String CALCULATION_INFLATION_CALCULATION_METHOD =
      "Inflation.IndexCalculationMethod";
  public static final String CALCULATION_OVERNIGHT_DAY_COUNT = "Overnight.DayCount";
  public static final String CALCULATION_OVERNIGHT_INDEX = "Overnight.Index";
  public static final String CALCULATION_OVERNIGHT_SPREAD_VALUE = "Overnight.Margin";
  public static final String CALCULATION_OVERNIGHT_RATE_CUTOFF_DAYS = "Overnight.RateCutOffDays";
  public static final String CALCULATION_OVERNIGHT_ACCRUAL_METHOD = "Overnight.AccrualMethod";
  public static final String LEG_CURRENCY = "Ccy";
  public static final String LEG_NOTIONAL = "Notional";
  public static final String LEG_NEAR_NOTIONAL = "NearNotional";

  // Portfolio item fields
  public static final String PORTFOLIO_EXTERNAL_ID = "Portfolio ID";
  public static final String COMPANY_EXTERNAL_ID = "Company ID";
  public static final String LEGAL_ENTITY_EXTERNAL_ID = "Entity ID";
  public static final String TRADE_TRADE_ID = "Trade ID";
  public static final String TRADE_TYPE = "Trade Type";
  public static final String TRADE_COUNTERPARTY = "Cpty";
  public static final String TRADE_COUNTERPARTY_TYPE = "Cpty Type";
  public static final String TRADE_CSA_DISCOUNTING = "CSA Discount Ccy";
  public static final String TRADE_DESCRIPTION = "Description";
  public static final String TRADE_PAYMENT_DATE = "Payment Date";
  public static final String TRADE_NEAR_LEG_PAYMENT_DATE = "Near Leg Payment Date";
  public static final String TRADE_BUSINESS_DAY_CONVENTION = "Business Day Convention";
  public static final String TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE = "Business Day Adjustment";
  public static final String TRADE_ROLL_CONVETIONT = "Roll";
  public static final String TRADE_POSITION = "BuySell";
  public static final String TRADE_EXPIRY_DATE = "Expiry Date";
  public static final String TRADE_EXPIRY_TIME = "Expiry Time";
  public static final String TRADE_EXPIRY_ZONE = "Expiry Zone";
  public static final String TRADE_EXPIRY_DATE_ADJ_CONVENTION = "Expiry Date Convention";
  public static final String TRADE_PREMIUM_DATE = "Premium Date";
  public static final String TRADE_PREMIUM_DATE_ADJ_CONVENTION = "Premium Date Convention";
  public static final String TRADE_PREMIUM_CURRENCY = "Premium Ccy";
  public static final String TRADE_PREMIUM_AMOUNT = "Premium Amount";
  public static final String TRADE_OTHER_OPTION_COUNTER_NOTIONAL = "Other Option Counter Notional";
  public static final String TRADE_ACCRUAL_SCHEDULE_START_DATE = "Start Date";
  public static final String TRADE_ACCRUAL_SCHEDULE_END_DATE = "End Date";
  public static final String TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION = "Stub";
  public static final String TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE = "Regular Start Date";
  public static final String TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE = "Regular End Date";
  public static final String TRADE_NOTIONAL_SCHEDULE_INITIAL_EXCHANGE = "Initial Exchange";
  public static final String TRADE_NOTIONAL_SCHEDULE_FINAL_EXCHANGE = "Final Exchange";
  public static final String TRADE_CLIENT_METRICS_PV = "Client PV";
  public static final String TRADE_CAPFLOOR_TYPE = "CF Type";
  public static final String TRADE_CAPFLOOR_STRIKE = "CF Strike";
  public static final String TRADE_FXOPTION_CALL_PUT = "FXO Call / Put";
  public static final String TRADE_SWAPTION_SETTLEMENT_DATE = "SWO Settlement Date";
  public static final String TRADE_SWAPTION_SETTLEMENT_TYPE = "SWO Settlement Type";
  public static final String TRADE_CREDIT_DOC_CLAUSE = "Credit Doc Clause";
  public static final String TRADE_CREDIT_SECTOR = "Credit Sector";
  public static final String TRADE_CREDIT_PROTECTION = "Credit Protection";
  public static final String TRADE_CREDIT_DAY_COUNT = "Credit Day Count";
  public static final String TRADE_CREDIT_SENIORITY = "CDS Seniority";
  public static final String TRADE_CREDIT_CORP_TICKER = "CDS Corp Ticker";
  public static final String TRADE_CREDIT_REFERENCE = "Credit Reference";
  public static final String TRADE_CREDIT_ENTITY_LONG_NAME = "Credit Long Name";
  public static final String TRADE_CREDIT_INDEX_SERIES = "Credit Index Series";
  public static final String TRADE_CREDIT_INDEX_VERSION = "Credit Index Version";
  public static final String TRADE_CREDIT_INDEX_TRANCHE = "Credit Index Tranche";

  public static final String TRADE_CREDIT_CURRENCY = "Credit Ccy";
  public static final String TRADE_CREDIT_NOTIONAL = "Credit Notional";
  public static final String TRADE_CREDIT_FIXED_RATE = "Credit Fixed Rate";
  public static final String TRADE_CREDIT_ACCRUAL_FREQUENCY = "Credit Freq";

  public static final String TRADE_ONBOARDING_VENDOR_DATE = "NAV Effective Date";
  public static final String TRADE_ONBOARDING_DEAL_COST = "Deal Cost";
  public static final String TRADE_ONBOARDING_ACCOUNTING_COST = "Accounting Cost";
  public static final String TRADE_ONBOARDING_XPLAIN_CHECK = "Xplain Conformity";
  public static final String TRADE_ONBOARDING_MARKET_CHECK = "Market Conformity";
  public static final String TRADE_ONBOARDING_VENDOR_CHECK = "Vendor Conformity";

  // Generic Trade
  public static final String GENERIC_ASSET_TYPE = "Asset Type";
  public static final String GENERIC_SUB_ASSET_TYPE = "Sub-Asset Type";
  public static final String GENERIC_ADDITIONAL_INFO = "Additional Information";
  public static final String GENERIC_NOTIONAL = "Custom Product Notional";
  public static final String GENERIC_UNDERLYING = "Custom Product Underlying";
  public static final String GENERIC_OPTION_POSITION = "Custom Product Option Position";

  // Custom Rates Trade

  public static final String TRADE_RATES_PAYLEG_TYPE = "Custom Rates Pay Leg Type";
  public static final String TRADE_RATES_RECEIVELEG_TYPE = "Custom Rates Receive Leg Type";

  // Loan Note Trade
  public static final String TRADE_LOAN_CCY = "Loan Ccy";
  public static final String TRADE_LOAN_REFERENCE = "Loan Reference";
  public static final String TRADE_LOAN_CREDIT_SPREAD = "Loan Credit Spread";
  public static final String TRADE_LOAN_NOTIONAL = "Loan Notional";
  public static final String TRADE_LOAN_SETTLEMENT_DAYS_OFFSET = "Loan Settlement Days Offset";
  public static final String TRADE_LOAN_FIXED_RATE = "Loan Fixed Rate";
  public static final String TRADE_LOAN_FREQ = "Loan Freq";
  public static final String TRADE_LOAN_DAY_COUNT = "Loan Day Count";

  // Allocation trade fields
  public static final String TRADE_REFERENCE_TRADE_ID = "Ref Security";
  public static final String TRADE_ALLOCATION_NOTIONAL = "Allocation Notional";

  // Ref sec trade fields
  public static final String TRADE_REF_SEC_FX_RATE = "Fx Rate";
  public static final String TRADE_REF_SEC_NEAR_DATE_FX_RATE = "Near Date Fx Rate";
  public static final String TRADE_REF_SEC_OTHER_OPTION_STRIKE = "Other Option Strike";

  // External trade ids prefix
  public static final String EXTERNAL_TRADE_ID_PREFIX = PortfolioCSVFields.TRADE_TRADE_ID + ".";

  public static final String TRADE_CUSTOM_FIELD_PREFIX = "Custom Field.";

  // Calculation result fields
  public static final String LOCAL_CCY = "Trade Ccy";
  public static final String RESULT_PV_LOCAL = "PV (Trade Ccy)";
  public static final String RESULT_CLEAN_PV_LOCAL = "Clean PV (Trade Ccy)";
  public static final String RESULT_ACCRUED_LOCAL = "Accrued (Trade Ccy)";
  public static final String RESULT_PV_PAY_LEG_LOCAL = "PV Pay Leg (Leg Ccy)";
  public static final String RESULT_ACCRUED_PAY_LEG_LOCAL = "Accrued Pay Leg (Leg Ccy)";
  public static final String RESULT_ACCRUED_DAYS_PAY_LEG = "Accrued Days Pay Leg";
  public static final String RESULT_PV_RECEIVE_LEG_LOCAL = "PV Rec Leg (Leg Ccy)";
  public static final String RESULT_ACCRUED_REC_LEG_LOCAL = "Accrued Rec Leg (Leg Ccy)";
  public static final String RESULT_ACCRUED_DAYS_REC_LEG = "Accrued Days Rec Leg";
  public static final String RESULT_DV01_LOCAL = "Total DV01 (Trade Ccy)";
  public static final String RESULT_PV01_LOCAL = "PV01 (Trade Ccy)";
  public static final String RESULT_INFCSBR01_LOCAL = "INF01 / CS01 / BR01 (Trade Ccy)";
  public static final String RESULT_GAMMA_LOCAL = "Gamma (Trade Ccy)";
  public static final String RESULT_THETA_LOCAL = "Theta (Trade Ccy)";
  public static final String RESULT_VEGA_LOCAL = "Vega (Trade Ccy)";
  public static final String RESULT_T0_CF_PAY_LOCAL = "Pay T0 CFS (Leg Ccy)";
  public static final String RESULT_T0_CF_REC_LOCAL = "Rec T0 CFS (Leg Ccy)";
  public static final String RESULT_T0_CF_NET_LOCAL = "T0 Net CFS (Trade Ccy)";
  public static final String RESULT_DELTA_FWD_LOCAL = "Delta Forward (Trade Ccy)";
  public static final String RESULT_DELTA_SPOT_LOCAL = "Delta Spot (Trade Ccy)";

  public static final String RESULT_PV_REP = "PV (Reporting Ccy)";
  public static final String RESULT_ACCRUED_REP = "Accrued (Reporting Ccy)";
  public static final String RESULT_CLEAN_PV_REP = "Clean PV (Reporting Ccy)";
  public static final String RESULT_DV01_REP = "Total DV01 (Reporting Ccy)";
  public static final String RESULT_PV01_REP = "PV01 (Reporting Ccy)";
  public static final String RESULT_INFCSBR01_REP = "INF01 / CS01 / BR01 (Reporting Ccy)";
  public static final String RESULT_GAMMA_REP = "Gamma (Reporting Ccy)";
  public static final String RESULT_THETA_REP = "Theta (Reporting Ccy)";
  public static final String RESULT_VEGA_REP = "Vega (Reporting Ccy)";
  public static final String RESULT_T0_CF_NET_REP = "T0 Net CFS (Reporting Ccy)";
  public static final String RESULT_DELTA_FWD_REP = "Delta Forward (Reporting Ccy)";
  public static final String RESULT_DELTA_SPOT_REP = "Delta Spot (Reporting Ccy)";

  public static final String RESULT_PAR_RATE = "Par Rate";
  public static final String RESULT_IMPLIED_VOL = "Implied Vol";

  // Inflation
  public static final String RESULT_INFL_INITIAL_INDEX = "Initial Index";
  public static final String RESULT_INFL_FINAL_INDEX = "Final Index";

  // Fx Option
  public static final String RESULT_STRIKE = "Strike";
  public static final String RESULT_FX_SPOT = "FX Spot";

  // Loan
  public static final String RESULT_GVT_YTM = "YTM (Gvt)";
  public static final String RESULT_BOND_YTM = "YTM (Bond)";
  public static final String RESULT_MODIFIED_DURATION = "Modified Duration";
  public static final String RESULT_ACCRUED_PERCENT = "Accrued (Price)";
  public static final String RESULT_PV_PERCENT = "Dirty Price";
  public static final String RESULT_CLEAN_PV_PERCENT = "Clean Price";
  public static final String RESULT_DURATION = "Duration";
  public static final String RESULT_LOAN_CREDIT_SPREAD = "Credit Spread";
  public static final String RESULT_LOAN_FIXED_RATE = "Bond Coupon";

  // Comparison fields
  public static final String COMPARE_PV_1 = "Dirty PV (1)";
  public static final String COMPARE_PV_2 = "Dirty PV (2)";
  public static final String COMPARE_DV01_1 = "Greeks 01 (1)";
  public static final String COMPARE_DV01_2 = "Greeks 01 (2)";
  public static final String COMPARE_PV01_1 = "PV01 (1)";
  public static final String COMPARE_PV2_PV1 = "PV (2)-PV (1)";
  public static final String COMPARE_PV2_PV1_ABS = "ABS(PV (2)-PV (1))";
  public static final String COMPARE_PV2_PV1_DIV_PV1 = "(PV (2)-PV (1))/PV (1)";
  public static final String COMPARE_PV2_PV1_DIV_PV1_ABS = "ABS((PV (2)-PV (1))/PV (1))";
  public static final String COMPARE_PV2_PV1_DIV_DV01 = "(PV (2)-PV (1))/Greeks 01 (1)";
  public static final String COMPARE_PV2_PV1_DIV_DV01_ABS = "ABS((PV (2)-PV (1))/Greeks 01 (1))";

  // Client PV comparison fields
  public static final String COMPARE_CLIENT_PV = "Client PV";
  public static final String COMPARE_CLIENT_DIRTY_PV = "Dirty PV";
  public static final String COMPARE_CLEAN_PV = "Clean PV";
  public static final String COMPARE_PV_CLIENT_PV = "PV - Client PV";
  public static final String COMPARE_PV_CLIENT_PV_ABS = "Abs(PV - Client PV)";
  public static final String TRADE_CURRENCY = "Trade Currency";

  public static final List<String> CAHS_FLOW_FIELDS =
      List.of(
          "discountCcy",
          "currency",
          "paymentDate",
          "forecastPay",
          "forecastRec",
          "forecastNet",
          "discountedPay",
          "discountedRec",
          "discountedNet",
          "discountFactor");
  public static final List<String> PARTY_EXPOSURE_FIELDS =
      List.of("time", "date", "epe", "ene", "pfe", "ee");
  private static final List<String> LEG_FIELDS =
      List.of(
          PAY_RECEIVE,
          LEG_TYPE,
          LEG_IDENTIFIER,
          ACRUAL_SCHEDULE_FREQUENCY,
          PAYMENT_SCHEDULE_FREQUENCY,
          PAYMENT_SCHEDULE_DATE_OFFSET,
          PAYMENT_SCHEDULE_COMPOUNDING_METHOD,
          LEG_CURRENCY,
          LEG_NEAR_NOTIONAL,
          LEG_NOTIONAL,
          OFFSHORE,
          CALCULATION_FIXED_DAY_COUNT,
          CALCULATION_FIXED_RATE_VALUE,
          CALCULATION_FIXED_ACCRUAL_METHOD,
          CALCULATION_IBOR_DAY_COUNT,
          CALCULATION_IBOR_INDEX,
          CALCULATION_IBOR_FIXING_OFFSET,
          CALCULATION_IBOR_SPREAD_VALUE,
          CALCULATION_INFLATION_INDEX,
          CALCULATION_INFLATION_LAG,
          CALCULATION_INFLATION_CALCULATION_METHOD,
          CALCULATION_OVERNIGHT_DAY_COUNT,
          CALCULATION_OVERNIGHT_INDEX,
          CALCULATION_OVERNIGHT_SPREAD_VALUE,
          CALCULATION_OVERNIGHT_ACCRUAL_METHOD,
          CALCULATION_OVERNIGHT_RATE_CUTOFF_DAYS);

  private static final List<String> ONBOARDING_FIELDS =
      List.of(
          TRADE_ONBOARDING_VENDOR_DATE,
          TRADE_ONBOARDING_DEAL_COST,
          TRADE_ONBOARDING_ACCOUNTING_COST,
          TRADE_ONBOARDING_XPLAIN_CHECK,
          TRADE_ONBOARDING_MARKET_CHECK,
          TRADE_ONBOARDING_VENDOR_CHECK);

  private static final List<String> CALCULATION_LEG_FIELDS =
      List.of(
          LEG_CURRENCY, LEG_NOTIONAL, "Rate/Margin", "Index", "Freq", "Daycount", LEG_IDENTIFIER);

  public static final List<String> CALCULATION_RESULTS_FIELDS =
      ImmutableList.<String>builder()
          .add(
              COMPANY_EXTERNAL_ID,
              LEGAL_ENTITY_EXTERNAL_ID,
              PORTFOLIO_EXTERNAL_ID,
              TRADE_TRADE_ID,
              TRADE_TYPE,
              COUNTERPARTY,
              COUNTERPARTY_TYPE,
              TRADE_CSA_DISCOUNTING,
              TRADE_DATE,
              UNDERLYING,
              CAP_FLOOR,
              TRADE_FXOPTION_CALL_PUT,
              BUY_SELL,
              TRADE_DESCRIPTION,
              TRADE_EXPIRY_DATE,
              TRADE_ACCRUAL_SCHEDULE_START_DATE,
              TRADE_ACCRUAL_SCHEDULE_END_DATE,
              TRADE_CLIENT_METRICS_PV,
              DISCOUNT_CCY,
              VALUATION_STATUS,
              VALUATION_ERROR,
              LOCAL_CCY,
              RESULT_PV_LOCAL,
              RESULT_ACCRUED_LOCAL,
              RESULT_CLEAN_PV_LOCAL,
              RESULT_PV_PAY_LEG_LOCAL,
              RESULT_ACCRUED_DAYS_PAY_LEG,
              RESULT_ACCRUED_PAY_LEG_LOCAL,
              RESULT_PV_RECEIVE_LEG_LOCAL,
              RESULT_ACCRUED_DAYS_REC_LEG,
              RESULT_ACCRUED_REC_LEG_LOCAL,
              RESULT_PV01_LOCAL,
              RESULT_DV01_LOCAL,
              RESULT_INFCSBR01_LOCAL,
              RESULT_GAMMA_LOCAL,
              RESULT_THETA_LOCAL,
              RESULT_VEGA_LOCAL,
              RESULT_T0_CF_PAY_LOCAL,
              RESULT_T0_CF_REC_LOCAL,
              RESULT_T0_CF_NET_LOCAL,
              RESULT_DELTA_FWD_LOCAL,
              RESULT_DELTA_SPOT_LOCAL,
              RESULT_PV_REP,
              RESULT_ACCRUED_REP,
              RESULT_CLEAN_PV_REP,
              RESULT_PV01_REP,
              RESULT_DV01_REP,
              RESULT_INFCSBR01_REP,
              RESULT_GAMMA_REP,
              RESULT_THETA_REP,
              RESULT_VEGA_REP,
              RESULT_T0_CF_NET_REP,
              RESULT_DELTA_FWD_REP,
              RESULT_DELTA_SPOT_REP,
              RESULT_STRIKE,
              RESULT_FX_SPOT,
              RESULT_PAR_RATE,
              RESULT_IMPLIED_VOL,
              RESULT_INFL_INITIAL_INDEX,
              RESULT_INFL_FINAL_INDEX,
              RESULT_LOAN_CREDIT_SPREAD,
              RESULT_LOAN_FIXED_RATE,
              RESULT_ACCRUED_PERCENT,
              RESULT_PV_PERCENT,
              RESULT_CLEAN_PV_PERCENT,
              RESULT_BOND_YTM,
              RESULT_GVT_YTM,
              RESULT_MODIFIED_DURATION,
              RESULT_DURATION)
          .addAll(CALCULATION_LEG_FIELDS.stream().map(s -> "Pay " + s).toList())
          .addAll(CALCULATION_LEG_FIELDS.stream().map(s -> "Rec " + s).toList())
          .build();

  public static List<String> buildRefSecHeader(List<String> additionalHeaders) {
    return ImmutableList.<String>builder()
        .addAll(tradeItemHeader((additionalHeaders)))
        .add(TRADE_REF_SEC_FX_RATE)
        .add(TRADE_REF_SEC_NEAR_DATE_FX_RATE)
        .add(TRADE_REF_SEC_OTHER_OPTION_STRIKE)
        .build();
  }

  public static List<String> buildAllocationTradeHeader(
      List<String> customIdHeaders, List<String> customFields) {
    return ImmutableList.<String>builder()
        .add(COMPANY_EXTERNAL_ID, LEGAL_ENTITY_EXTERNAL_ID, PORTFOLIO_EXTERNAL_ID, TRADE_TRADE_ID)
        .addAll(customIdHeaders)
        .add(
            TRADE_REFERENCE_TRADE_ID,
            TRADE_ALLOCATION_NOTIONAL,
            TRADE_POSITION,
            TRADE_DATE,
            TRADE_COUNTERPARTY,
            TRADE_COUNTERPARTY_TYPE,
            TRADE_CSA_DISCOUNTING,
            TRADE_CLIENT_METRICS_PV,
            TRADE_DESCRIPTION)
        .addAll(ONBOARDING_FIELDS)
        .addAll(customFields)
        .build();
  }

  public static List<String> buildBespokeTradeHeader(
      List<String> customIdHeaders, List<String> customFields) {
    return ImmutableList.<String>builder()
        .add(COMPANY_EXTERNAL_ID, LEGAL_ENTITY_EXTERNAL_ID, PORTFOLIO_EXTERNAL_ID)
        .addAll(tradeItemHeader(customIdHeaders))
        .add(TRADE_REFERENCE_TRADE_ID, TRADE_ALLOCATION_NOTIONAL)
        .addAll(ONBOARDING_FIELDS)
        .addAll(customFields)
        .build();
  }

  public static List<String> tradeItemHeader(List<String> customIdHeaders) {
    return ImmutableList.<String>builder()
        .add(TRADE_TYPE, TRADE_TRADE_ID)
        .addAll(customIdHeaders)
        .add(
            TRADE_BUSINESS_DAY_CONVENTION,
            TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE,
            TRADE_CURRENCY,
            TRADE_ACCRUAL_SCHEDULE_START_DATE,
            TRADE_ACCRUAL_SCHEDULE_END_DATE,
            TRADE_DATE,
            TRADE_NEAR_LEG_PAYMENT_DATE,
            TRADE_PAYMENT_DATE,
            TRADE_EXPIRY_DATE,
            TRADE_EXPIRY_TIME,
            TRADE_EXPIRY_ZONE,
            TRADE_EXPIRY_DATE_ADJ_CONVENTION,
            TRADE_CAPFLOOR_TYPE,
            TRADE_CAPFLOOR_STRIKE,
            TRADE_FXOPTION_CALL_PUT,
            TRADE_SWAPTION_SETTLEMENT_TYPE,
            TRADE_SWAPTION_SETTLEMENT_DATE,
            TRADE_PREMIUM_DATE_ADJ_CONVENTION,
            TRADE_PREMIUM_CURRENCY,
            TRADE_PREMIUM_AMOUNT,
            TRADE_OTHER_OPTION_COUNTER_NOTIONAL,
            TRADE_PREMIUM_DATE,
            TRADE_POSITION,
            TRADE_CREDIT_NOTIONAL,
            TRADE_CREDIT_CURRENCY,
            TRADE_CREDIT_ACCRUAL_FREQUENCY,
            TRADE_CREDIT_FIXED_RATE,
            TRADE_CREDIT_DOC_CLAUSE,
            TRADE_CREDIT_SECTOR,
            TRADE_CREDIT_PROTECTION,
            TRADE_CREDIT_DAY_COUNT,
            TRADE_CREDIT_SENIORITY,
            TRADE_CREDIT_CORP_TICKER,
            TRADE_CREDIT_REFERENCE,
            TRADE_CREDIT_ENTITY_LONG_NAME,
            TRADE_CREDIT_INDEX_SERIES,
            TRADE_CREDIT_INDEX_VERSION,
            TRADE_CREDIT_INDEX_TRANCHE,
            TRADE_LOAN_CCY,
            TRADE_LOAN_REFERENCE,
            TRADE_LOAN_CREDIT_SPREAD,
            TRADE_LOAN_NOTIONAL,
            TRADE_LOAN_FIXED_RATE,
            TRADE_LOAN_FREQ,
            TRADE_LOAN_DAY_COUNT,
            TRADE_LOAN_SETTLEMENT_DAYS_OFFSET,
            TRADE_COUNTERPARTY,
            TRADE_COUNTERPARTY_TYPE,
            TRADE_CSA_DISCOUNTING,
            TRADE_DESCRIPTION,
            TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION,
            TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE,
            TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE,
            TRADE_NOTIONAL_SCHEDULE_INITIAL_EXCHANGE,
            TRADE_NOTIONAL_SCHEDULE_FINAL_EXCHANGE,
            TRADE_ROLL_CONVETIONT,
            TRADE_CLIENT_METRICS_PV,
            TRADE_RATES_PAYLEG_TYPE,
            TRADE_RATES_RECEIVELEG_TYPE)
        .addAll(LEG_FIELDS.stream().map(l -> addField(LEG_1, l)).toList())
        .addAll(LEG_FIELDS.stream().map(l -> addField(LEG_2, l)).toList())
        .add(
            GENERIC_ASSET_TYPE,
            GENERIC_SUB_ASSET_TYPE,
            GENERIC_NOTIONAL,
            GENERIC_ADDITIONAL_INFO,
            GENERIC_UNDERLYING,
            GENERIC_OPTION_POSITION)
        .build();
  }

  private PortfolioCSVFields() {}
}
