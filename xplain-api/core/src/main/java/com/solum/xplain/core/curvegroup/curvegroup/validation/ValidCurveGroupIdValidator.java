package com.solum.xplain.core.curvegroup.curvegroup.validation;

import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class ValidCurveGroupIdValidator implements ConstraintValidator<ValidCurveGroupId, String> {

  private final CurveGroupRepository curveGroupRepository;

  public ValidCurveGroupIdValidator(CurveGroupRepository curveGroupRepository) {
    this.curveGroupRepository = curveGroupRepository;
  }

  @Override
  public boolean isValid(String id, ConstraintValidatorContext context) {
    if (StringUtils.isEmpty(id)) {
      return true;
    }
    return curveGroupRepository.getEither(id).isRight();
  }
}
