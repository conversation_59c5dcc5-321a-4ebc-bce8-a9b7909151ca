package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.FxCollarTradeForm;
import com.solum.xplain.core.portfolio.value.CurrencyAmountForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidOtherOptionCounterNotionalValidator
    implements ConstraintValidator<ValidOtherOptionCounterNotional, FxCollarTradeForm> {

  public boolean isValid(FxCollarTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }
    var otherOptionCounterNotional = form.getOtherOptionCounterNotional();
    var foreign = form.getForeignCurrencyAmount();
    if (hasAmount(otherOptionCounterNotional)
        && hasAmount(foreign)
        && hasSameSigns(otherOptionCounterNotional, foreign)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate("RequiredDifferentAmountSigns")
          .addPropertyNode("foreignCurrencyAmount")
          .addPropertyNode("amount")
          .addConstraintViolation();

      return false;
    }

    return true;
  }

  private boolean hasAmount(Double amount) {
    return amount != null && amount != 0d;
  }

  private boolean hasAmount(CurrencyAmountForm amountForm) {
    return amountForm != null && amountForm.getAmount() != null && amountForm.getAmount() != 0d;
  }

  private boolean hasSameSigns(Double f1, CurrencyAmountForm f2) {
    return Math.signum(f1) == Math.signum(f2.getAmount());
  }
}
