package com.solum.xplain.core.portfolio.validation;

import com.opengamma.strata.product.swap.CompoundingMethod;
import com.solum.xplain.core.portfolio.form.CapFloorTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidCapFloorCompoundingMethodValidator
    implements ConstraintValidator<ValidCapFloorCompoundingMethod, CapFloorTradeForm> {

  public static final String INVALID_COMPOUNDING_METHOD = "Compounding method must be None";

  @Override
  public boolean isValid(CapFloorTradeForm form, ConstraintValidatorContext context) {
    if (form == null
        || form.getCalculationIborIndex() == null
        || form.getCompoundingMethod() == null) {
      return true;
    }
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
        .addPropertyNode("compoundingMethod")
        .addConstraintViolation();
    return form.getCompoundingMethod().equals(CompoundingMethod.NONE.toString());
  }
}
