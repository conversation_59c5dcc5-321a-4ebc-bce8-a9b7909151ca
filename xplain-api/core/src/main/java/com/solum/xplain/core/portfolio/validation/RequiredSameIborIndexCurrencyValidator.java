package com.solum.xplain.core.portfolio.validation;

import com.opengamma.strata.basics.index.IborIndex;
import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredSameIborIndexCurrencyValidator
    implements ConstraintValidator<RequiredSameIborIndexCurrency, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }

    var leg1 = form.getLeg1();
    var leg2 = form.getLeg2();
    if (hasCurrency(leg1) && hasCurrency(leg2)) {
      context.disableDefaultConstraintViolation();
      return validateIndexCurrency(context, leg1, "leg1")
          && validateIndexCurrency(context, leg2, "leg2");
    }
    return true;
  }

  private boolean validateIndexCurrency(
      ConstraintValidatorContext context, SwapLegForm form, String leg) {
    String indexCurrency = extractIborIndexCurrency(form);
    if (indexCurrency != null && !indexCurrency.equals(form.getNotionalCurrency())) {
      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode(leg)
          .addPropertyNode("calculationIborIndex")
          .addConstraintViolation();
      return false;
    }
    return true;
  }

  private String extractIborIndexCurrency(SwapLegForm form) {
    if (form.getCalculationIborIndex() == null) {
      return null;
    }
    return IborIndex.of(form.getCalculationIborIndex()).getCurrency().toString();
  }

  private boolean hasCurrency(SwapLegForm form) {
    return form != null && form.getNotionalCurrency() != null;
  }
}
