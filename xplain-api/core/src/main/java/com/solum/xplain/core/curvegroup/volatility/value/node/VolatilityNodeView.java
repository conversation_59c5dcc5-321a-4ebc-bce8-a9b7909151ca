package com.solum.xplain.core.curvegroup.volatility.value.node;

import com.solum.xplain.core.common.value.MatrixNode;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class VolatilityNodeView implements MatrixNode {

  private String expiry;
  private String tenor;
  private String key;

  @Override
  public String getRow() {
    return getExpiry();
  }

  @Override
  public String getColumn() {
    return getTenor();
  }
}
