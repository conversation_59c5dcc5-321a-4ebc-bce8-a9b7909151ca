package com.solum.xplain.core.portfolio.search;

import static com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntityMongoOperations.latestItemsCriteria;
import static com.solum.xplain.core.search.SearchOptions.REGEX_OPTIONS;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollSortOperations;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.daterange.DateRangeVersionedEntity;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.trade.ExternalIdentifier;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.search.SearchRequest;
import com.solum.xplain.core.search.SearchResponse;
import com.solum.xplain.core.search.SearchTradeView;
import com.solum.xplain.core.utils.PathUtils;
import java.util.List;
import lombok.AllArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
@AllArgsConstructor
public class PortfolioItemSearchRepository {

  private final MongoOperations mongoOperations;

  public SearchResponse<SearchTradeView> portfolioItems(
      List<String> portfolioIds, SearchRequest searchRequest) {
    var scrollRequest = ScrollRequest.of(0, 10);
    var itemsCriteria =
        latestItemsCriteria(
                portfoliosCriteria(portfolioIds),
                BitemporalDate.newOf(searchRequest.getStateDate()),
                VersionedEntityFilter.active())
            .andOperator(searchCriteria(searchRequest));

    var operations =
        ImmutableList.<AggregationOperation>builder()
            .add(match(itemsCriteria))
            .add(projectSearchView());

    operations.addAll(
        new ScrollSortOperations(scrollRequest, DateRangeVersionedEntity.Fields.id).build());
    var items =
        mongoOperations
            .aggregateAndReturn(SearchTradeView.class)
            .by(newAggregation(PortfolioItem.class, operations.build()))
            .all()
            .getMappedResults();
    return new SearchResponse<>(ScrollableEntry.limitByPageSize(items, scrollRequest).getContent());
  }

  private Criteria portfoliosCriteria(List<String> portfolioIds) {
    var objectIds = CollectionUtils.convertCollectionTo(portfolioIds, ObjectId::new);
    return Criteria.where(PortfolioItem.Fields.portfolioId).in(objectIds);
  }

  private ProjectionOperation projectSearchView() {
    return project(
            SearchTradeView.Fields.portfolioId,
            SearchTradeView.Fields.externalPortfolioId,
            SearchTradeView.Fields.externalCompanyId,
            SearchTradeView.Fields.externalEntityId,
            SearchTradeView.Fields.externalTradeId,
            SearchTradeView.Fields.productType)
        .and(DateRangeVersionedEntity.Fields.entityId)
        .as(SearchTradeView.Fields.tradeId)
        .and(
            PathUtils.joinPaths(
                VersionedTradeEntity.Fields.tradeDetails,
                TradeDetails.Fields.info,
                TradeInfoDetails.Fields.tradeDate))
        .as(SearchTradeView.Fields.tradeDate)
        .and(VersionedTradeEntity.Fields.externalIdentifiers)
        .as(SearchTradeView.Fields.externalIdentifiers);
  }

  private Criteria searchCriteria(SearchRequest searchRequest) {
    var tradeDetailsField = VersionedTradeEntity.Fields.tradeDetails;
    var legIdentifier = TradeLegDetails.Fields.extLegIdentifier;
    var externalIdentifier =
        PathUtils.joinPaths(
            VersionedTradeEntity.Fields.externalIdentifiers, ExternalIdentifier.Fields.identifier);
    return new Criteria()
        .orOperator(
            Criteria.where(VersionedTradeEntity.Fields.externalTradeId)
                .regex(searchRequest.getQuery(), REGEX_OPTIONS),
            Criteria.where(
                    PathUtils.joinPaths(
                        tradeDetailsField, TradeDetails.Fields.payLeg, legIdentifier))
                .regex(searchRequest.getQuery(), REGEX_OPTIONS),
            Criteria.where(
                    PathUtils.joinPaths(
                        tradeDetailsField, TradeDetails.Fields.receiveLeg, legIdentifier))
                .regex(searchRequest.getQuery(), REGEX_OPTIONS),
            Criteria.where(externalIdentifier).regex(searchRequest.getQuery(), REGEX_OPTIONS));
  }
}
