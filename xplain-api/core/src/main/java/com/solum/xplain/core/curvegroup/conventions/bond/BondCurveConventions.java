package com.solum.xplain.core.curvegroup.conventions.bond;

import static com.opengamma.strata.basics.currency.Currency.EUR;
import static com.opengamma.strata.basics.currency.Currency.GBP;
import static com.opengamma.strata.basics.currency.Currency.JPY;
import static com.opengamma.strata.basics.currency.Currency.USD;
import static com.opengamma.strata.product.bond.FixedCouponBondYieldConvention.DE_BONDS;
import static com.opengamma.strata.product.bond.FixedCouponBondYieldConvention.GB_BUMP_DMO;
import static com.opengamma.strata.product.bond.FixedCouponBondYieldConvention.JP_SIMPLE;
import static com.opengamma.strata.product.bond.FixedCouponBondYieldConvention.US_STREET;
import static java.util.List.of;

import com.opengamma.strata.product.bond.FixedCouponBondYieldConvention;
import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import java.util.List;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class BondCurveConventions {

  private static final ImmutableBondCurveConvention UKGT =
      ImmutableBondCurveConvention.builder()
          .name("UKGT")
          .currency(GBP)
          .yieldConvention(GB_BUMP_DMO)
          .build();

  private static final ImmutableBondCurveConvention UST =
      ImmutableBondCurveConvention.builder()
          .name("UST")
          .currency(USD)
          .yieldConvention(US_STREET)
          .build();

  private static final ImmutableBondCurveConvention DEGT =
      ImmutableBondCurveConvention.builder()
          .name("DEGT")
          .currency(EUR)
          .yieldConvention(DE_BONDS)
          .build();

  private static final ImmutableBondCurveConvention JPGT =
      ImmutableBondCurveConvention.builder()
          .name("JPGT")
          .currency(JPY)
          .yieldConvention(JP_SIMPLE)
          .build();

  private static final List<ImmutableBondCurveConvention> BOND_CURVES = of(UKGT, UST, DEGT, JPGT);

  public static Optional<CurveConvention> findBondCurveByName(String name) {
    return BOND_CURVES.stream()
        .filter(c -> c.getName().equals(name))
        .findAny()
        .map(CurveConvention.class::cast);
  }

  public static Optional<FixedCouponBondYieldConvention> findBondCurveYieldConventionByName(
      String name) {
    return BOND_CURVES.stream()
        .filter(c -> c.getName().equals(name))
        .findAny()
        .map(ImmutableBondCurveConvention::getYieldConvention);
  }

  public static List<CurveConvention> getCurves() {
    return CollectionUtils.convertCollectionTo(BOND_CURVES, CurveConvention.class::cast);
  }
}
