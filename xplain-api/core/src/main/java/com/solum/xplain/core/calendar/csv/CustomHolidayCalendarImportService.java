package com.solum.xplain.core.calendar.csv;

import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.unexpectedDuplicateAction;
import static com.solum.xplain.core.common.csv.NewVersionFormV2Utils.fromImportOptions;
import static io.atlassian.fugue.Either.right;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.date.HolidayCalendar;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.calendar.HolidayCalendarController;
import com.solum.xplain.core.calendar.entity.CustomHolidayCalendar;
import com.solum.xplain.core.calendar.form.CustomHolidayCalendarForm;
import com.solum.xplain.core.calendar.repository.CustomHolidayCalendarRepository;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.*;
import com.solum.xplain.core.common.value.FutureVersionsAction;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import io.atlassian.fugue.Either;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Service;

@Service
public class CustomHolidayCalendarImportService extends LoggingImportService {
  private final CustomHolidayCalendarRepository repository;
  private final HolidayCalendarCsvLoader csvLoader;

  public CustomHolidayCalendarImportService(
      AuditEntryService auditEntryService,
      CustomHolidayCalendarRepository repository,
      HolidayCalendarCsvLoader csvLoader) {
    super(auditEntryService);
    this.repository = repository;
    this.csvLoader = csvLoader;
  }

  public Either<List<ErrorItem>, List<EntityId>> uploadCustomHolidayCalendar(
      String calendarId, ImportOptions importOptions, byte[] bytes) {
    if (importOptions.parsingMode() != ParsingMode.STRICT) {
      return Either.left(
          List.of(Error.PARSING_ERROR.entity("Parsing mode must be STRICT for custom calendars")));
    }
    var logItems =
        csvLoader
            .parse(bytes, ParsingMode.STRICT)
            .map(CsvParserResult::getParsedLines)
            .map(this::toForm)
            .fold(
                this::asLogItems,
                f -> importCalendar(HolidayCalendarId.of(calendarId), f, importOptions));
    return toReturn(importOptions.getDuplicateAction(), logItems);
  }

  private CustomHolidayCalendarForm toForm(List<HolidayCalendarImportCsvRow> rows) {
    CustomHolidayCalendarForm.CustomHolidayCalendarFormBuilder builder =
        CustomHolidayCalendarForm.builder();
    rows.forEach(
        row -> {
          Optional.ofNullable(row.getHoliday()).ifPresent(builder::holiday);
          Optional.ofNullable(row.getWorkingDay()).ifPresent(builder::workingDay);
          Optional.ofNullable(row.getWeekendDay()).ifPresent(builder::weekendDay);
        });
    return builder.build();
  }

  private List<LogItem> importCalendar(
      HolidayCalendarId id, CustomHolidayCalendarForm form, ImportOptions importOptions) {
    Optional<CustomHolidayCalendar> existingItem =
        repository.findActiveByCalendarId(id, importOptions.bitemporalDate());
    LocalDate defaultValidFrom =
        existingItem.map(VersionedEntity::getValidFrom).orElse(NewVersionFormV2.ROOT_DATE);
    form.setVersionForm(
        fromImportOptions(importOptions, defaultValidFrom, importOptions::getFutureVersionsAction));

    if (existingItem.isEmpty()
        && formMatchesStandardCalendar(id, form)
        && importOptions.getFutureVersionsAction() != FutureVersionsAction.DELETE) {
      return fakeImport(id, importOptions.getDuplicateAction());
    }

    return switch (importOptions.getDuplicateAction()) {
      case APPEND -> existingItem.isEmpty() ? List.of(create(id, form)) : List.of();
      case ERROR ->
          asLogItems(
              existingItem.map(c -> ImportErrorUtils.duplicateItem(c.getEntityId())).stream()
                  .toList());
      case REPLACE ->
          List.of(existingItem.map(e -> update(e, form)).orElseGet(() -> create(id, form)));
      case APPEND_DELETE, REPLACE_DELETE ->
          List.of(unexpectedDuplicateAction(importOptions.getDuplicateAction()));
    };
  }

  /**
   * Returns results as if an import had taken place but without making any database changes. This
   * is used if we know the standard calendar is identical to what we would have written.
   *
   * @param id the calendar id
   * @param duplicateAction the action to take on duplicates
   * @return list of log items as if an import had taken place
   */
  private List<LogItem> fakeImport(HolidayCalendarId id, @NotNull DuplicateAction duplicateAction) {
    return switch (duplicateAction) {
      case ERROR -> asLogItems(List.of(ImportErrorUtils.duplicateItem(id.toString())));
      case REPLACE -> List.of(createUpdateLogItem(id.toString(), right(entityId(id.toString()))));
      case APPEND -> List.of();
      case APPEND_DELETE, REPLACE_DELETE -> List.of(unexpectedDuplicateAction(duplicateAction));
    };
  }

  /**
   * Checks if a form containing holiday data has exactly the same holidays and working days as the
   * standard calendar over the period from {@value HolidayCalendarController#MIN_YEAR} to {@value
   * HolidayCalendarController#MAX_YEAR}.
   *
   * @param id the holiday calendar id to check
   * @param form the form containing holidays, working days and weekends
   * @return true if the two calendars are identical over the period, false otherwise
   */
  private boolean formMatchesStandardCalendar(
      HolidayCalendarId id, CustomHolidayCalendarForm form) {
    HolidayCalendar standardCalendar = id.resolve(ReferenceData.standard());
    boolean matching = true;
    for (LocalDate dateToCheck = LocalDate.ofYearDay(HolidayCalendarController.MIN_YEAR, 1);
        matching && dateToCheck.getYear() <= HolidayCalendarController.MAX_YEAR;
        dateToCheck = dateToCheck.plusDays(1)) {
      boolean isStandardHoliday = standardCalendar.isHoliday(dateToCheck);
      boolean isCustomHoliday =
          form.getHolidays().contains(dateToCheck)
              || (form.getWeekendDays().contains(dateToCheck.getDayOfWeek())
                  && !form.getWorkingDays().contains(dateToCheck));
      if (isStandardHoliday ^ isCustomHoliday) matching = false;
    }
    return matching;
  }

  private LogItem update(CustomHolidayCalendar existing, CustomHolidayCalendarForm form) {
    var entityId = repository.updateCalendar(existing, form);
    return createUpdateLogItem(existing.getEntityId(), right(entityId));
  }

  private LogItem create(HolidayCalendarId id, CustomHolidayCalendarForm form) {
    var entityId = repository.createCalendar(id, form);
    return createInsertLogItem(id.toString(), right(entityId));
  }

  @Override
  protected String getCollection() {
    return CustomHolidayCalendar.CUSTOM_HOLIDAY_CALENDAR_COLLECTION;
  }

  @Override
  protected String getObjectName() {
    return "Custom holiday calendar";
  }
}
