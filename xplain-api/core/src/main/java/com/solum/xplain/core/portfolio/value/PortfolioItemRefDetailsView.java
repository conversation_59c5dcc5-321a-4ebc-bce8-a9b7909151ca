package com.solum.xplain.core.portfolio.value;

import com.solum.xplain.core.portfolio.ClientMetrics;
import com.solum.xplain.core.product.ProductType;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class PortfolioItemRefDetailsView {
  private String entityId;
  private String externalTradeId;
  private ProductType productType;
  private String tradeCounterparty;
  private CounterpartyType tradeCounterpartyType;
  private ClientMetrics clientMetrics;
}
