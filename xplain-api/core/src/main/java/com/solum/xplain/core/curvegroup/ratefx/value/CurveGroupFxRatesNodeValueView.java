package com.solum.xplain.core.curvegroup.ratefx.value;

import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class CurveGroupFxRatesNodeValueView extends CurveGroupFxRatesNodeView {

  private BigDecimal value;
}
