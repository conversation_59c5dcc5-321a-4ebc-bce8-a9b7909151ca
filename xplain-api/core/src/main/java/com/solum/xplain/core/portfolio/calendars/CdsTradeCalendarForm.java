package com.solum.xplain.core.portfolio.calendars;

import com.solum.xplain.core.curvegroup.curvecredit.validation.ValidCreditCurrency;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springdoc.core.annotations.ParameterObject;

@ParameterObject
@ToString
@EqualsAndHashCode
@AllArgsConstructor
public class CdsTradeCalendarForm {

  @NotEmpty @ValidCreditCurrency private final String currency;

  public String getCurrency() {
    return currency;
  }
}
