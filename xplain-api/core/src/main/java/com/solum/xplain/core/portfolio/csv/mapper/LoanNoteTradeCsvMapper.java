package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_END_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_START_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_BUSINESS_DAY_CONVENTION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_LOAN_CCY;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_LOAN_CREDIT_SPREAD;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_LOAN_DAY_COUNT;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_LOAN_FIXED_RATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_LOAN_FREQ;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_LOAN_NOTIONAL;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_LOAN_REFERENCE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_LOAN_SETTLEMENT_DAYS_OFFSET;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_ROLL_CONVETIONT;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.csv.DayCountCsvUtils;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvMapper;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public final class LoanNoteTradeCsvMapper implements ProductCsvMapper {

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.LOAN_NOTE);
  }

  @Override
  public Iterable<CsvField> toCsvFields(TradeDetails trade) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    var loanTradeDetails = trade.getLoanNoteTradeDetails();
    var loanLeg = trade.getReceiveLeg();
    builder.add(new CsvField(TRADE_ACCRUAL_SCHEDULE_START_DATE, trade.getStartDate()));
    builder.add(new CsvField(TRADE_ACCRUAL_SCHEDULE_END_DATE, trade.getEndDate()));
    builder.add(
        new CsvField(TRADE_ACCRUAL_SCHEDULE_REGULAR_START_DATE, trade.getFirstRegularStartDate()));
    builder.add(
        new CsvField(TRADE_ACCRUAL_SCHEDULE_REGULAR_END_DATE, trade.getLastRegularEndDate()));
    builder.add(new CsvField(TRADE_LOAN_FIXED_RATE, loanTradeDetails.getFixedRate()));
    builder.add(
        new CsvField(
            TRADE_LOAN_SETTLEMENT_DAYS_OFFSET, loanTradeDetails.getSettlementOffsetDays()));
    builder.add(new CsvField(TRADE_LOAN_CCY, loanLeg.getCurrency()));
    builder.add(new CsvField(TRADE_LOAN_REFERENCE, loanTradeDetails.getReference()));
    builder.add(new CsvField(TRADE_LOAN_CREDIT_SPREAD, loanTradeDetails.getCreditSpread()));
    builder.add(new CsvField(TRADE_LOAN_NOTIONAL, loanLeg.getNotional()));
    builder.add(
        new CsvField(TRADE_LOAN_DAY_COUNT, DayCountCsvUtils.toExportLabel(loanLeg.getDayCount())));
    builder.add(new CsvField(TRADE_LOAN_FREQ, loanLeg.getPaymentFrequency()));

    builder.add(new CsvField(TRADE_ROLL_CONVETIONT, trade.getRollConvention()));
    builder.add(new CsvField(TRADE_ACCRUAL_SCHEDULE_STUB_CONVENTION, trade.getStubConvention()));
    builder.add(new CsvField(TRADE_BUSINESS_DAY_CONVENTION, trade.getBusinessDayConvention()));
    builder.add(
        new CsvField(TRADE_BUSINESS_DAY_ADJUSTMENT_TYPE, trade.getBusinessDayAdjustmentType()));

    return builder.build();
  }
}
