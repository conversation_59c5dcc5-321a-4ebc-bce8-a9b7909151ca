package com.solum.xplain.core.portfolio.trade.details;

import static com.solum.xplain.core.portfolio.trade.details.TradeDetailsUtils.tradeCcyOrPayLegPriorityNotional;

import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.details.ProductDetailsResolver;
import java.util.List;
import org.springframework.stereotype.Component;

@Component
public class CreditDetailsResolver implements ProductDetailsResolver {

  @Override
  public List<ProductType> productTypes() {
    return List.of(
        CoreProductType.CDS, CoreProductType.CREDIT_INDEX, CoreProductType.CREDIT_INDEX_TRANCHE);
  }

  @Override
  public String resolveUnderlying(ProductType productType, TradeDetails tradeDetails) {
    return tradeDetails
        .getCreditTradeDetails()
        .legalEntityStandardId(tradeDetails.tradeCurrency(), productType)
        .getValue();
  }

  @Override
  public double resolveNotional(TradeDetails tradeDetails) {
    return tradeCcyOrPayLegPriorityNotional(tradeDetails);
  }
}
