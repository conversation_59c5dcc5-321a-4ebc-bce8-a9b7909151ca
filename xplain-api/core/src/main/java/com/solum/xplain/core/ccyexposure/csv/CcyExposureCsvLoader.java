package com.solum.xplain.core.ccyexposure.csv;

import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CCY_EXPOSURE_CURRENCY_FIELD;
import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CCY_EXPOSURE_DESCRIPTION_FIELD;
import static com.solum.xplain.core.ccyexposure.csv.CcyExposureCsvFields.CCY_EXPOSURE_NAME_FIELD;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.CURRENCY_CLASSIFIER;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.rowParsingError;
import static com.solum.xplain.core.common.csv.CsvLoaderUtils.validateValue;

import com.opengamma.strata.collect.io.CsvRow;
import com.solum.xplain.core.ccyexposure.value.CcyExposureCreateForm;
import com.solum.xplain.core.common.csv.CsvParserResultBuilder;
import com.solum.xplain.core.common.csv.GenericCsvLoader;
import com.solum.xplain.core.common.csv.ParsingMode;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.function.Function;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class CcyExposureCsvLoader extends GenericCsvLoader<CcyExposureCreateForm, String> {

  private static final ClassifierSupplier CURRENCY_SUPPLIER =
      new ClassifierSupplier(CURRENCY_CLASSIFIER);

  @Override
  protected List<String> getFileHeaders() {
    return List.of(
        CCY_EXPOSURE_NAME_FIELD, CCY_EXPOSURE_CURRENCY_FIELD, CCY_EXPOSURE_DESCRIPTION_FIELD);
  }

  @Override
  protected Either<ErrorItem, CcyExposureCreateForm> parseLine(@NonNull CsvRow row) {
    try {
      CcyExposureCreateForm form = new CcyExposureCreateForm();
      String currency = row.getValue(CCY_EXPOSURE_CURRENCY_FIELD);

      form.setName(row.getValue(CCY_EXPOSURE_NAME_FIELD));
      form.setCurrency(validateValue(currency, CURRENCY_SUPPLIER));
      form.setDescription(row.getField(CCY_EXPOSURE_DESCRIPTION_FIELD));
      return Either.right(form);
    } catch (RuntimeException e) {
      return Either.left(rowParsingError(row, e));
    }
  }

  @Override
  protected CsvParserResultBuilder<CcyExposureCreateForm, String> createResult(
      ParsingMode parsingMode) {
    return new CsvParserResultBuilder<>(
        CcyExposureCreateForm::getName, Function.identity(), parsingMode.failOnError());
  }
}
