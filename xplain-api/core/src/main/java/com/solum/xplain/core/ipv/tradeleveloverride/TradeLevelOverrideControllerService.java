package com.solum.xplain.core.ipv.tradeleveloverride;

import static com.solum.xplain.core.common.csv.ExportFileNameUtils.nameWithTimeStamp;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.ScrollRequest;
import com.solum.xplain.core.common.ScrollableEntry;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.csv.FileResponseEntity;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.tradeleveloverride.csv.TradeLevelOverrideCsvMapper;
import com.solum.xplain.core.ipv.tradeleveloverride.mapping.TradeLevelOverrideMapper;
import com.solum.xplain.core.ipv.tradeleveloverride.repository.TradeLevelOverrideRepository;
import com.solum.xplain.core.ipv.tradeleveloverride.repository.TradeLevelOverrideWriteRepository;
import com.solum.xplain.core.ipv.tradeleveloverride.value.TradeLevelOverrideForm;
import com.solum.xplain.core.ipv.tradeleveloverride.value.TradeLevelOverrideSearchForm;
import com.solum.xplain.core.ipv.tradeleveloverride.value.TradeLevelOverrideUpdateForm;
import com.solum.xplain.core.ipv.tradeleveloverride.value.TradeLevelOverrideView;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslator;
import com.solum.xplain.core.viewconfig.filter.ViewQueryTranslatorFactory;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class TradeLevelOverrideControllerService {

  private final TradeLevelOverrideWriteRepository writeRepository;
  private final TradeLevelOverrideRepository readRepository;
  private final TradeLevelOverrideCsvMapper tradeLevelOverrideCsvMapper;
  private final TradeLevelOverrideMapper tradeLevelOverrideMapper;
  private final ViewQueryTranslatorFactory viewQueryTranslatorFactory;

  @Transactional
  public EntityId insert(TradeLevelOverrideForm form) {
    return writeRepository.insert(form);
  }

  public ScrollableEntry<TradeLevelOverrideView> getAll(
      ScrollRequest scrollRequest,
      TableFilter tableFilter,
      boolean withArchived,
      BitemporalDate stateDate) {
    ViewQueryTranslator viewQueryTranslator =
        viewQueryTranslatorFactory.getTranslator(TradeLevelOverrideView.class);
    return readRepository.getAll(
        stateDate,
        VersionedEntityFilter.of(withArchived),
        viewQueryTranslator.translate(tableFilter),
        viewQueryTranslator.translate(scrollRequest));
  }

  public List<TradeLevelOverride> activeTradeLevelOverrides(BitemporalDate stateDate) {
    return readRepository.activeTradeLevelOverrides(stateDate).toList();
  }

  public FileResponseEntity getTradeLevelOverrideCsv(
      Sort sort, TableFilter tableFilter, LocalDate stateDate) {

    var csvFileName = nameWithTimeStamp("DealExceptions", stateDate);

    var rows =
        readRepository
            .tradeLevelOverrides(sort, tableFilter, BitemporalDate.newOf(stateDate))
            .stream()
            .map(tradeLevelOverrideCsvMapper::toCsvRow)
            .map(Either::<ErrorItem, CsvRow>right);

    var csvFile = new CsvOutputFile(tradeLevelOverrideCsvMapper.header(), rows);

    return FileResponseEntity.csvFile(csvFile.writeToByteArray(), csvFileName);
  }

  @Transactional
  public Either<ErrorItem, EntityId> update(
      String entityId, LocalDate versionDate, TradeLevelOverrideUpdateForm form) {
    return writeRepository.update(entityId, versionDate, form);
  }

  @Transactional
  public Either<ErrorItem, EntityId> archive(
      String entityId, LocalDate versionDate, ArchiveEntityForm form) {
    return writeRepository.archive(entityId, versionDate, form);
  }

  @Transactional
  public Either<ErrorItem, EntityId> delete(String entityId, LocalDate versionDate) {
    return writeRepository.deleteItem(entityId, versionDate);
  }

  public DateList futureVersions(TradeLevelOverrideSearchForm searchForm) {
    return readRepository.futureVersions(searchForm);
  }
}
