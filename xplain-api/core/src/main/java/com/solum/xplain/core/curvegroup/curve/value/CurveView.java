package com.solum.xplain.core.curvegroup.curve.value;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.solum.xplain.core.common.versions.State;
import com.solum.xplain.core.curvegroup.curve.classifier.CurveType;
import com.solum.xplain.core.curvegroup.curve.entity.CurveProjectionIndex;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CurveView implements CurveProjectionIndex {
  // Versioned entity fields
  private String entityId;
  private LocalDate validFrom;
  private String comment;
  private LocalDateTime recordDate;
  private State state;
  private String modifiedBy;
  private LocalDateTime modifiedAt;

  // Versioned named entity fields
  private String name;

  // Curve fields
  private CurveType curveType;
  private boolean calibrated;
  private String calibrationDiscountCurrency;
  private String interpolator;
  private String extrapolatorLeft;
  private String extrapolatorRight;

  @JsonProperty("yInterpolationMethod")
  private String yInterpolationMethod;

  private String minGap;
  private String clashAction;

  // Curve fields - calculated
  private Integer numberOfNodes;
}
