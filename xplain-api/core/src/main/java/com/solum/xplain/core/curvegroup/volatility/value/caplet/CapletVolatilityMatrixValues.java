package com.solum.xplain.core.curvegroup.volatility.value.caplet;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.value.VolatilityValueMatrix;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

public class CapletVolatilityMatrixValues
    extends VolatilityValueMatrix<CapletVolatilityNodeValueView> {
  public CapletVolatilityMatrixValues(
      List<CapletVolatilityNodeValueView> content, LocalDate versionDate) {
    super(content, versionDate);
  }

  public static CapletVolatilityMatrixValues configurationFromList(
      List<CapletVolatilityNodeValueView> nodes, LocalDate versionDate) {
    return new CapletVolatilityMatrixValues(
        nodes.stream().filter(c -> c.getValue() != null).toList(), versionDate);
  }

  public static CapletVolatilityMatrixValues configurationFromList(
      VersionedList<CapletVolatilityNodeValueView> nodes) {
    return configurationFromList(nodes.getList(), nodes.getVersionDate());
  }

  @Override
  protected Comparator<String> rowComparator() {
    return Comparator.comparing(Tenor::parse);
  }

  @Override
  protected Comparator<String> columnComparator() {
    return Comparator.comparing(BigDecimal::new);
  }
}
