package com.solum.xplain.core.portfolio.validation;

import com.opengamma.strata.basics.index.IborIndex;
import com.solum.xplain.core.classifiers.Constants;
import java.util.Collection;
import java.util.function.Supplier;

public class CapFloorTradeIndicesSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return Constants.CAPFLOOR_INDICES.stream().map(IborIndex::getName).toList();
  }
}
