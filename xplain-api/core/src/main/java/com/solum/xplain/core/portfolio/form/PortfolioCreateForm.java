package com.solum.xplain.core.portfolio.form;

import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.portfolio.CompanyPortfolio;
import com.solum.xplain.core.portfolio.validation.UniquePortfolioExtId;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@UniquePortfolioExtId
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PortfolioCreateForm extends PortfolioUpdateForm implements CompanyPortfolio {

  @NotEmpty @ValidIdentifier private String externalPortfolioId;
}
