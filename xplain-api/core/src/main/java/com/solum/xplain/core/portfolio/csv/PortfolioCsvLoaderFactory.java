package com.solum.xplain.core.portfolio.csv;

import static java.util.stream.Collectors.groupingBy;

import com.solum.xplain.core.authentication.value.XplainPrincipal;
import com.solum.xplain.core.common.team.EntityTeamFilter;
import com.solum.xplain.core.company.CompanyTeamValidationService;
import com.solum.xplain.core.company.entity.Company;
import com.solum.xplain.core.company.entity.CompanyLegalEntity;
import com.solum.xplain.core.company.repository.CompanyLegalEntityRepository;
import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.core.teams.TeamRepository;
import com.solum.xplain.core.teams.value.TeamNameView;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.bson.types.ObjectId;
import org.springframework.stereotype.Component;

@Component
public class PortfolioCsvLoaderFactory {
  private final CompanyRepository companyRepository;
  private final CompanyLegalEntityRepository companyLegalEntityRepository;
  private final TeamRepository teamRepository;
  private final CompanyTeamValidationService companyTeamValidationService;

  public PortfolioCsvLoaderFactory(
      CompanyRepository companyRepository,
      CompanyLegalEntityRepository companyLegalEntityRepository,
      TeamRepository teamRepository,
      CompanyTeamValidationService companyTeamValidationService) {
    this.companyRepository = companyRepository;
    this.companyLegalEntityRepository = companyLegalEntityRepository;
    this.teamRepository = teamRepository;
    this.companyTeamValidationService = companyTeamValidationService;
  }

  public PortfolioCsvLoader getLoader(XplainPrincipal user) {
    var teamNames = teamNames();
    var companiesMap = companiesMap();
    var legalEntities = entitiesLookup();
    var userTeamIds = userTeams(user);

    return PortfolioCsvLoader.newOf(
        companiesMap, legalEntities, teamNames, userTeamIds, companyTeamValidationService);
  }

  private Map<String, String> teamNames() {
    return teamRepository.teamNamesList().stream()
        .collect(Collectors.toMap(TeamNameView::getName, TeamNameView::getId));
  }

  private Map<String, String> companiesMap() {
    return companyRepository.companyList(EntityTeamFilter.emptyFilter()).stream()
        .collect(Collectors.toMap(Company::getExternalCompanyId, Company::getId));
  }

  private Map<String, CompanyLegalEntityLookup> entitiesLookup() {
    return companyLegalEntityRepository.allLegalEntities().stream()
        .collect(groupingBy(CompanyLegalEntity::getCompanyId))
        .entrySet()
        .stream()
        .collect(
            Collectors.toMap(Map.Entry::getKey, v -> CompanyLegalEntityLookup.newOf(v.getValue())));
  }

  private Set<String> userTeams(XplainPrincipal user) {
    return user.getTeams().stream().map(ObjectId::toString).collect(Collectors.toSet());
  }
}
