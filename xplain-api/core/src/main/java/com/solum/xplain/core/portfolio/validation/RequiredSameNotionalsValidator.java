package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Objects;

public class RequiredSameNotionalsValidator
    implements ConstraintValidator<RequiredSameNotionals, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }

    var leg1 = form.getLeg1();
    var leg2 = form.getLeg2();

    if (hasNotional(leg1) && hasNotional(leg2) && hasDifferentNotionals(leg1, leg2)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode("leg2")
          .addPropertyNode("notionalValue")
          .addConstraintViolation();
      return false;
    }
    return true;
  }

  private boolean hasNotional(SwapLegForm form) {
    return form != null && form.getNotionalValue() != null;
  }

  private boolean hasDifferentNotionals(SwapLegForm f1, SwapLegForm f2) {
    return !Objects.equals(f1.getNotionalValue(), f2.getNotionalValue());
  }
}
