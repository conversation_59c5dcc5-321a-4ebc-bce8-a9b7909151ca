package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.Objects;

/** Validates that the day counts for each legs are the same. */
public class RequiredSameDayCountValidator
    implements ConstraintValidator<RequiredSameDayCount, SwapTradeForm> {

  /** Returns false when leg1 and leg2 have different day counts, true otherwise */
  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }

    var leg1 = form.getLeg1();
    var leg2 = form.getLeg2();

    if (hasDayCount(leg1) && hasDayCount(leg2) && hasDifferentDayCount(leg1, leg2)) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
          .addPropertyNode("leg2")
          .addPropertyNode(getDayCountNode(leg2))
          .addConstraintViolation();
      return false;
    }
    return true;
  }

  private boolean hasDayCount(SwapLegForm form) {
    return form != null && getDayCount(form) != null;
  }

  private boolean hasDifferentDayCount(SwapLegForm form1, SwapLegForm form2) {
    return !Objects.equals(getDayCount(form1), getDayCount(form2));
  }

  private String getDayCount(SwapLegForm form) {
    return form != null
        ? switch (form.getCalculationType()) {
          case FIXED -> form.getCalculationFixedDayCount();
          case IBOR -> form.getCalculationIborDayCount();
          case OVERNIGHT -> form.getCalculationOvernightDayCount();
          default -> null;
        }
        : null;
  }

  private String getDayCountNode(SwapLegForm form) {
    return switch (form.getCalculationType()) {
      case FIXED -> "calculationFixedDayCount";
      case IBOR -> "calculationIborDayCount";
      case OVERNIGHT -> "calculationOvernightDayCount";
      default -> null;
    };
  }
}
