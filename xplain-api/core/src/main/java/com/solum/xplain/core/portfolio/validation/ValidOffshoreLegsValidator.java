package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import com.solum.xplain.core.portfolio.value.CalculationType;
import com.solum.xplain.core.utils.PathUtils;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.stream.Stream;
import org.apache.commons.lang3.BooleanUtils;

public class ValidOffshoreLegsValidator
    implements ConstraintValidator<ValidOffshoreLegs, SwapTradeForm> {
  public static final String OFFSHORE_FIELDS_DIFFER_ERROR =
      "Legs with calculation type Ibor or Overnight must have the same offshore flag";

  @Override
  public boolean isValid(SwapTradeForm value, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    if (value.getLeg2() != null && value.getLeg1() != null && hasOnlyIborOrOvernight(value)) {
      var hasDifferentFlags =
          Stream.of(value.getLeg2(), value.getLeg1())
              .map(v -> BooleanUtils.isTrue(v.getIsOffshore()))
              .reduce(Boolean::logicalXor)
              .orElse(false);
      if (Boolean.TRUE.equals(hasDifferentFlags)) {
        context
            .buildConstraintViolationWithTemplate(OFFSHORE_FIELDS_DIFFER_ERROR)
            .addPropertyNode(
                PathUtils.joinPaths(SwapTradeForm.Fields.leg2, SwapLegForm.Fields.isOffshore))
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }

  private boolean hasOnlyIborOrOvernight(SwapTradeForm value) {
    return Stream.of(value.getLeg2(), value.getLeg1())
        .allMatch(v -> isIborOrOvernight(v.getCalculationType()));
  }

  private boolean isIborOrOvernight(CalculationType calculationType) {
    return calculationType == CalculationType.IBOR || calculationType == CalculationType.OVERNIGHT;
  }
}
