package com.solum.xplain.core.portfolio.builder;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.core.portfolio.value.CalculationType;

public interface ResolvableTradeLegDetails {
  TradeLegDetails toTradeLegDetails();

  String getExtLegIdentifier();

  PayReceive getPayReceive();

  Currency getCurrency();

  CalculationType getCalculationType();

  Double getNotional();

  DayCount getDayCount();
}
