package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_NEAR_LEG_PAYMENT_DATE;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_REF_SEC_NEAR_DATE_FX_RATE;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvMapper;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class FxSwapCsvMapper implements ProductCsvMapper {

  private final FxFwdCsvMapper fxFwdCsvMapper;

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.FXSWAP);
  }

  @Override
  public Iterable<CsvField> toCsvFields(TradeDetails tradeDetails) {
    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(TRADE_NEAR_LEG_PAYMENT_DATE, tradeDetails.getStartDate()));
    builder.add(new CsvField(TRADE_REF_SEC_NEAR_DATE_FX_RATE, tradeDetails.getNearDateFxRate()));
    builder.addAll(fxFwdCsvMapper.toCsvFields(tradeDetails));

    return builder.build();
  }
}
