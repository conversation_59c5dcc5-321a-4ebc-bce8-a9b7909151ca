package com.solum.xplain.core.portfolio.trade.type;

import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemResponse;
import static com.solum.xplain.core.lock.XplainLock.TRADES_LOCK_ID;

import com.solum.xplain.core.authentication.Authorities;
import com.solum.xplain.core.common.CommonErrors;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.lock.RequireLock;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.ReferenceTradesProvider;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.AllocationTradeForm;
import com.solum.xplain.core.portfolio.value.AllocationTradeView;
import io.atlassian.fugue.Either;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/allocated")
@AllArgsConstructor
public class AllocationTradeController {
  private final TradeTypeControllerService service;
  private final ReferenceTradesProvider referenceTradesProvider;

  @Operation(summary = "Create trade")
  @CommonErrors
  @PostMapping
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_TRADE)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<EntityId> insert(
      @PathVariable("id") String portfolioId, @Valid @RequestBody AllocationTradeForm form) {
    return eitherErrorItemResponse(getService().insert(portfolioId, form));
  }

  @Operation(summary = "Update trade")
  @CommonErrors
  @PutMapping("{tradeEntityId}/{version}")
  @PreAuthorize(Authorities.AUTHORITY_MODIFY_TRADE)
  @RequireLock(name = TRADES_LOCK_ID)
  public ResponseEntity<EntityId> update(
      @PathVariable("id") String portfolioId,
      @PathVariable("tradeEntityId") String tradeEntityId,
      @PathVariable("version") LocalDate version,
      @Valid @RequestBody AllocationTradeForm form) {

    return eitherErrorItemResponse(getService().update(portfolioId, version, form, tradeEntityId));
  }

  @Operation(summary = "Get trade")
  @GetMapping("/{tradeEntityId}")
  @CommonErrors
  @PreAuthorize(Authorities.AUTHORITY_VIEW_TRADE)
  public ResponseEntity<AllocationTradeView> get(
      @PathVariable("id") String portfolioId,
      @PathVariable("tradeEntityId") String tradeEntityId,
      @RequestParam("stateDate") LocalDate stateDate) {
    return eitherErrorItemResponse(
        getService()
            .tradeView(portfolioId, tradeEntityId, BitemporalDate.newOf(stateDate))
            .flatMap(i -> toViewFunction(i, stateDate)));
  }

  private Either<ErrorItem, AllocationTradeView> toViewFunction(
      PortfolioItem e, LocalDate stateDate) {
    return AllocationTradeView.of(
        e, refId -> referenceTradesProvider.fetchReferenceTrade(refId, stateDate));
  }
}
