package com.solum.xplain.core.ccyexposure.csv;

import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.ccyexposure.CcyExposureRepository;
import com.solum.xplain.core.ccyexposure.entity.CcyExposure;
import com.solum.xplain.core.ccyexposure.value.CcyExposureCreateForm;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.LoggingImportService;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.csv.BaseArchivableImportService;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Service
public class CcyExposureImportService
    extends BaseArchivableImportService<CcyExposureCreateForm, String, CcyExposure> {

  private final CcyExposureRepository repository;
  private final CcyExposureCsvLoader csvLoader;

  protected CcyExposureImportService(
      AuditEntryService auditEntryService,
      CcyExposureRepository repository,
      CcyExposureCsvLoader csvLoader) {
    super(auditEntryService);
    this.repository = repository;
    this.csvLoader = csvLoader;
  }

  @Transactional
  public Either<List<ErrorItem>, List<EntityId>> importCcyExposures(
      ImportOptions importOptions, byte[] bytes) {
    return csvLoader
        .parse(bytes, importOptions.parsingMode())
        .map(parsed -> importCcyExposures(parsed, importOptions))
        .fold(
            err -> toErrorReturn(importOptions.getDuplicateAction(), err),
            importResult -> toReturn(importOptions.getDuplicateAction(), importResult));
  }

  private LoggingImportService.ImportResult importCcyExposures(
      CsvParserResult<CcyExposureCreateForm> parserResult, ImportOptions importOptions) {
    var forms = parserResult.getParsedLines();
    var existingItems = repository.getActiveExposures();
    var importItems =
        ImportItems.<CcyExposureCreateForm, String, CcyExposure>builder()
            .existingActiveItems(existingItems)
            .existingItemToKeyFn(CcyExposure::getName)
            .importItems(forms)
            .importItemToKeyFn(CcyExposureCreateForm::getName)
            .build();

    var importLogs = importItems(importOptions, importItems);
    return new LoggingImportService.ImportResult(importLogs, parserResult.getWarnings());
  }

  @Override
  protected String getIdentifier(String key) {
    return key;
  }

  @Override
  protected Either<ErrorItem, EntityId> insert(CcyExposureCreateForm f) {
    return repository.insert(f);
  }

  @Override
  protected Either<ErrorItem, EntityId> update(CcyExposure e, CcyExposureCreateForm f) {
    return repository.update(e.getId(), f);
  }

  @Override
  protected Either<ErrorItem, EntityId> archive(CcyExposure e, ArchiveEntityForm f) {
    Assert.state(f == null, "Archive form not supported");
    return repository.archive(e.getId());
  }

  @Override
  protected String getCollection() {
    return "ccyExposure";
  }

  @Override
  protected String getObjectName() {
    return "Ccy Exposure";
  }
}
