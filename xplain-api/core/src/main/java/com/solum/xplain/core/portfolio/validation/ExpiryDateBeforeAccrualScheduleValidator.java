package com.solum.xplain.core.portfolio.validation;

import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.portfolio.form.SwaptionTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ExpiryDateBeforeAccrualScheduleValidator
    implements ConstraintValidator<ExpiryDateBeforeAccrualSchedule, SwaptionTradeForm> {

  public boolean isValid(SwaptionTradeForm form, ConstraintValidatorContext context) {
    if (form != null
        && allNotNull(form.getStartDate(), form.getExpiryDate())
        && form.getStartDate().isBefore(form.getExpiryDate())) {
      context.disableDefaultConstraintViolation();
      context
          .buildConstraintViolationWithTemplate("ExpiryDateBeforeAccrualSchedule")
          .addPropertyNode("expiryDate")
          .addConstraintViolation();

      return false;
    }
    return true;
  }
}
