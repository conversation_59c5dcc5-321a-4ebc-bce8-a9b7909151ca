package com.solum.xplain.core.portfolio.csv.mapper;

import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_POSITION;
import static com.solum.xplain.core.portfolio.PortfolioCSVFields.TRADE_SWAPTION_SETTLEMENT_TYPE;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.core.product.csv.ProductCsvMapper;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class SwaptionCsvMapper implements ProductCsvMapper {

  private final CommonSwapCsvMapper swapCsvMapper;

  @Override
  public List<ProductType> productTypes() {
    return List.of(CoreProductType.SWAPTION);
  }

  @Override
  public Iterable<CsvField> toCsvFields(TradeDetails details) {

    ImmutableList.Builder<CsvField> builder = ImmutableList.builder();
    builder.add(new CsvField(TRADE_POSITION, details.getPositionType()));
    builder.addAll(swapCsvMapper.toCsvFields(details));
    var optionDetails = details.getOptionTradeDetails();
    builder.addAll(OptionDetailsCsvMapper.toCsvFields(optionDetails));
    builder.add(
        new CsvField(TRADE_SWAPTION_SETTLEMENT_TYPE, optionDetails.getSwaptionSettlementType()));
    return builder.build();
  }
}
