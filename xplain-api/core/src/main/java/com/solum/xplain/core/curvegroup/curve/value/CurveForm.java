package com.solum.xplain.core.curvegroup.curve.value;

import com.solum.xplain.core.curvegroup.curve.validation.ValidCurveName;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ValidCurveName
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CurveForm extends CurveUpdateForm {

  @NotEmpty private String name;
}
