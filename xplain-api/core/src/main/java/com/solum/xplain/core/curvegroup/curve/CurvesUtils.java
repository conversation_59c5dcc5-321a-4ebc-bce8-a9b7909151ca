package com.solum.xplain.core.curvegroup.curve;

import static com.opengamma.strata.basics.date.MarketTenor.ON;
import static com.opengamma.strata.basics.date.MarketTenor.ofSpot;
import static com.opengamma.strata.basics.date.Tenor.TENOR_1D;
import static java.util.stream.Collectors.toUnmodifiableSet;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.MarketTenor;
import com.opengamma.strata.basics.date.Tenor;
import com.opengamma.strata.basics.index.FloatingRateIndex;
import com.opengamma.strata.basics.index.RateIndex;
import com.opengamma.strata.product.deposit.type.TermDepositConvention;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.index.IndexCurveConvention;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CurvesUtils {

  public static Set<Currency> collectOisDiscountCurrencies(List<Curve> curves) {
    return curves.stream().flatMap(c -> c.oisCurveCurrency().stream()).collect(toUnmodifiableSet());
  }

  public static Set<FloatingRateIndex> collectIndices(List<Curve> curves) {
    return curves.stream().map(Curve::index).flatMap(Optional::stream).collect(toUnmodifiableSet());
  }

  public static List<Curve> filterCurves(List<Curve> curves, List<String> curveIds) {
    return curves.stream().filter(c -> curveIds.contains(c.getEntityId())).toList();
  }

  public static Optional<MarketTenor> termDepositTenor(String curveName, String conventionStr) {
    var spotOffsetDays = TermDepositConvention.of(conventionStr).getSpotDateOffset().getDays();
    return curveTenor(curveName)
        .map(t -> t.equals(TENOR_1D) && spotOffsetDays == 0 ? ON : ofSpot(t));
  }

  public static Optional<Tenor> curveTenor(String curveName) {
    return ConventionalCurveConfigurations.lookupByName(curveName).stream()
        .filter(IndexCurveConvention.class::isInstance)
        .map(IndexCurveConvention.class::cast)
        .map(IndexCurveConvention::getIndex)
        .filter(RateIndex.class::isInstance)
        .map(RateIndex.class::cast)
        .map(RateIndex::getTenor)
        .map(Tenor::normalized)
        .findFirst();
  }

  public static boolean is3MCurve(String curveName) {
    return curveTenor(curveName).stream().anyMatch(t -> Objects.equals(t, Tenor.TENOR_3M));
  }
}
