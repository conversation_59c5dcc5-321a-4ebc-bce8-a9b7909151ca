package com.solum.xplain.core.calibrationapi;

import com.solum.xplain.core.curvegroup.curve.dto.CalibratedCurvesOptions;
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import java.util.List;
import org.springframework.lang.Nullable;

public interface CurveCalibrationResultsProvider {

  @Nullable
  CurveCalibrationResult resultEntity(
      String curveId,
      CalibratedCurvesOptions calibrationOptions,
      CurveConfigMarketStateForm stateForm);

  CurveCalibrationResult latestResult(String curveId);

  void clearCalibrationResults(List<String> curveIds);
}
