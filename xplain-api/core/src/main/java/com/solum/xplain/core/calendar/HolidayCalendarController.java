package com.solum.xplain.core.calendar;

import static com.solum.xplain.core.authentication.Authorities.*;
import static com.solum.xplain.core.classifiers.ClassifiersControllerService.SUPPORTED_CALENDARS_CLASSIFIER;
import static com.solum.xplain.core.common.EitherResultOrErrorResponseEntity.eitherErrorItemsResponse;

import com.solum.xplain.core.calendar.csv.CustomHolidayCalendarImportService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.FileUploadErrors;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.io.IOException;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/holiday-calendar")
@RequiredArgsConstructor
@Validated
public class HolidayCalendarController {

  public static final int MIN_YEAR = 2000;
  public static final int MAX_YEAR = 2099;
  private final HolidayCalendarControllerService service;
  private final CustomHolidayCalendarImportService importService;

  @GetMapping("/{calendarId}")
  @Operation(summary = "Gets a holiday calendar (Xplain or custom) for a range of years")
  @PreAuthorize(AUTHORITY_VIEW_CALENDAR)
  public HolidayCalendarView retrieveHolidaysCalendar(
      @PathVariable("calendarId")
          @ValidStringSet(
              value = ClassifierSupplier.class,
              supplierArgument = SUPPORTED_CALENDARS_CLASSIFIER)
          String calendarId,
      @RequestParam("yearFrom") @Min(MIN_YEAR) @Max(MAX_YEAR) Integer yearFrom,
      @RequestParam("yearTo") @Min(MIN_YEAR) @Max(MAX_YEAR) Integer yearTo) {
    return service.holidayCalendar(calendarId, yearFrom, yearTo);
  }

  @GetMapping("/{calendarId}/csv")
  @Operation(
      summary = "Gets a holiday calendar (Xplain or custom) for a range of years in CSV format")
  @PreAuthorize(AUTHORITY_VIEW_CALENDAR)
  public ResponseEntity<ByteArrayResource> retrieveHolidaysExport(
      @PathVariable("calendarId")
          @ValidStringSet(
              value = ClassifierSupplier.class,
              supplierArgument = SUPPORTED_CALENDARS_CLASSIFIER)
          String calendarId,
      @RequestParam("yearFrom") @Min(MIN_YEAR) @Max(MAX_YEAR) Integer yearFrom,
      @RequestParam("yearTo") @Min(MIN_YEAR) @Max(MAX_YEAR) Integer yearTo) {
    return service.holidaysExport(calendarId, yearFrom, yearTo).toResponse();
  }

  @PostMapping(value = "/{calendarId}/csv", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @Operation(
      summary =
          "Uploads a custom holiday calendar in CSV format. APPEND, REPLACE and ERROR are the only supported duplicate actions.")
  @FileUploadErrors
  @PreAuthorize(AUTHORITY_MODIFY_CALENDAR)
  public ResponseEntity<List<EntityId>> uploadCustomHolidayCalendar(
      @PathVariable("calendarId")
          @ValidStringSet(
              value = ClassifierSupplier.class,
              supplierArgument = SUPPORTED_CALENDARS_CLASSIFIER)
          String calendarId,
      @Valid ImportOptions importOptions,
      @RequestPart MultipartFile file)
      throws IOException {
    return eitherErrorItemsResponse(
        importService.uploadCustomHolidayCalendar(calendarId, importOptions, file.getBytes()));
  }
}
