package com.solum.xplain.core.curvegroup.volatility.value.caplet;

import static com.solum.xplain.core.common.validation.ValidPeriod.PeriodStyle.YMW;

import com.solum.xplain.core.common.validation.ValidPeriod;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class CapletVolatilityNodeForm {

  @NotNull private BigDecimal strike;

  @NotEmpty
  @ValidPeriod(style = YMW)
  private String tenor;
}
