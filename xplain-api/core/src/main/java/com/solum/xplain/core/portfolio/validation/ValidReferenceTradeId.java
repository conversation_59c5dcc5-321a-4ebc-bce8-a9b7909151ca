package com.solum.xplain.core.portfolio.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = ValidReferenceTradeIdValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface ValidReferenceTradeId {
  String message() default
      "{com.solum.xplain.api.portfolio.validation.ValidReferenceTradeId.message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
