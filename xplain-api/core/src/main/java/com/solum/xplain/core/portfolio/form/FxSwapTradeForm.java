package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.portfolio.CoreProductType.FXSWAP;
import static com.solum.xplain.core.portfolio.value.FxLongShort.LONG;
import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.NOT_REQUIRED;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.core.common.validation.BusinessDayConventionsSupplier;
import com.solum.xplain.core.common.validation.ClassifierSupplier;
import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.UpperCase;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.common.validation.identifier.ValidIdentifier;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.builder.ResolvableFxSwapDetails;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.RequiredDifferentFxSwapCurrencies;
import com.solum.xplain.core.portfolio.validation.RequiredDifferentFxSwapLegIdentifiers;
import com.solum.xplain.core.portfolio.validation.RequiredValidNearFarDateOrder;
import com.solum.xplain.core.portfolio.validation.groups.BespokeTradeGroup;
import com.solum.xplain.core.portfolio.validation.groups.ReferenceTradeGroup;
import com.solum.xplain.core.portfolio.value.FxLongShort;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import io.atlassian.fugue.Either;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@RequiredValidNearFarDateOrder
@RequiredDifferentFxSwapCurrencies
@RequiredDifferentFxSwapLegIdentifiers
@Slf4j
public class FxSwapTradeForm extends BespokeTradeForm implements ParsableToTradeValue {

  @Schema(description = "Far Leg Date of payment")
  @NotNull
  private LocalDate paymentDate;

  @Schema(description = "Near Leg Date of payment")
  @NotNull
  private LocalDate nearLegPaymentDate;

  @NotEmpty
  @Schema(description = "Business day convention")
  @ValidStringSet(BusinessDayConventionsSupplier.class)
  private String businessDayConvention;

  @NotEmpty
  @Schema(description = "Currency")
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "fxTradeCurrency")
  private String baseCurrency;

  @NotEmpty
  @Schema(description = "Currency")
  @ValidStringSet(value = ClassifierSupplier.class, supplierArgument = "fxTradeCurrency")
  private String counterCurrency;

  @Valid
  @Nullable
  @NotNull(groups = BespokeTradeGroup.class)
  @Null(groups = ReferenceTradeGroup.class)
  private FxSwapNotionalsForm notionalsForm;

  @NotNull(groups = ReferenceTradeGroup.class)
  @Null(groups = BespokeTradeGroup.class)
  @Schema(
      description = "Long/Short position (far date). Only required for SecMaster trades.",
      requiredMode = NOT_REQUIRED)
  private FxLongShort longShort;

  @Valid
  @Nullable
  @Null(groups = BespokeTradeGroup.class)
  @NotNull(groups = ReferenceTradeGroup.class)
  @Schema(description = "Only required for SecMaster trades.", requiredMode = NOT_REQUIRED)
  private Double farDateFxRate;

  @Valid
  @Nullable
  @Null(groups = BespokeTradeGroup.class)
  @NotNull(groups = ReferenceTradeGroup.class)
  @Schema(description = "Only required for SecMaster trades.", requiredMode = NOT_REQUIRED)
  private Double nearDateFxRate;

  @Schema(description = "Pay leg external identifier")
  @UpperCase
  @ValidIdentifier
  private String payLegExtIdentifier;

  @Schema(description = "Receive leg external identifier")
  @UpperCase
  @ValidIdentifier
  private String receiveLegExtIdentifier;

  @ValidStringSet(CurrenciesSupplier.class)
  private String tradeCurrency;

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return fxSwapTradeDetails(toTradeInfo()).map(details -> defaultTradeValue(FXSWAP, details));
  }

  @Override
  protected String tradeCurrency() {
    if (StringUtils.isNotEmpty(tradeCurrency)) {
      return tradeCurrency;
    }
    return baseCurrency;
  }

  protected Either<ErrorItem, TradeDetails> fxSwapTradeDetails(TradeInfoDetails tradeInfo) {
    try {
      // base is 'pay' when the far notional is negative
      var isBaseCurrencyReceiveCurrency =
          (notionalsForm != null ? notionalsForm.getFarDateBaseNotional() > 0 : longShort == LONG);

      var payCurrency = isBaseCurrencyReceiveCurrency ? counterCurrency : baseCurrency;
      var receiveCurrency = isBaseCurrencyReceiveCurrency ? baseCurrency : counterCurrency;

      var builder =
          ResolvableFxSwapDetails.builder()
              .settlementDate(nearLegPaymentDate)
              .maturityDate(paymentDate)
              .payCurrency(Currency.parse(payCurrency))
              .receiveCurrency(Currency.parse(receiveCurrency))
              .businessDayConvention(businessDayConvention)
              .payLegExtIdentifier(StringUtils.upperCase(payLegExtIdentifier))
              .receiveLegExtIdentifier(StringUtils.upperCase(receiveLegExtIdentifier));

      if (notionalsForm != null) {
        // bespoke trade
        var payNearDateNotional =
            isBaseCurrencyReceiveCurrency
                ? notionalsForm.getNearDateCounterNotional()
                : notionalsForm.getNearDateBaseNotional();

        var payFarDateNotional =
            isBaseCurrencyReceiveCurrency
                ? notionalsForm.getFarDateCounterNotional()
                : notionalsForm.getFarDateBaseNotional();
        var receiveNearDateNotional =
            isBaseCurrencyReceiveCurrency
                ? notionalsForm.getNearDateBaseNotional()
                : notionalsForm.getNearDateCounterNotional();
        var receiveFarDateNotional =
            isBaseCurrencyReceiveCurrency
                ? notionalsForm.getFarDateBaseNotional()
                : notionalsForm.getFarDateCounterNotional();
        builder =
            builder
                .nearLegPayCurrencyAmount(payNearDateNotional)
                .nearLegReceiveCurrencyAmount(receiveNearDateNotional)
                .farLegPayCurrencyAmount(payFarDateNotional)
                .farLegReceiveCurrencyAmount(receiveFarDateNotional);
      } else {
        // security master
        builder = builder.nearDateFxRate(nearDateFxRate).fxRate(farDateFxRate);
      }

      return Either.right(builder.build().toTradeDetails(tradeInfo));
    } catch (RuntimeException ex) {
      log.debug(ex.getMessage(), ex);
      return Either.left(new ErrorItem(Error.CONVERSION_ERROR, ex.getMessage()));
    }
  }
}
