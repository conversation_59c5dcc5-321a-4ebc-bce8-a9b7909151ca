package com.solum.xplain.core.portfolio.value.item;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import com.solum.xplain.extensions.enums.CallPutType;
import com.solum.xplain.extensions.enums.CapFloorType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.time.LocalTime;
import lombok.Data;

@Data
public class OptionTradeDetailsView {

  @ConfigurableViewQuery(sortable = true)
  private CapFloorType capFloorType;

  @ConfigurableViewQuery(sortable = true)
  private CallPutType callPutType;

  private LocalTime expiryTime;
  private String expiryZone;
  private LocalDate expiryDate;
  private String expiryDateConvention;

  private LocalDate premiumDate;
  private String premiumDateConvention;
  private Double premiumValue;
  private String premiumCurrency;

  private String swaptionSettlementType;

  @ConfigurableViewQuery(sortable = true)
  private String optionStyle;

  @ConfigurableViewQuery(sortable = true)
  private Double strike;

  @Nullable
  @ConfigurableViewQuery(sortable = true)
  @Schema(description = "Other option strike for Fx Collar trades.")
  private Double otherOptionStrike;

  @Nullable
  @ConfigurableViewQuery(sortable = true)
  @Schema(
      description =
          "Other option counter notional for Fx Collar trades. (Null for reference trades)")
  private Double otherOptionCounterNotional;
}
