package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.form.FxSwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredDifferentFxSwapLegIdentifiersValidator
    implements ConstraintValidator<RequiredDifferentFxSwapLegIdentifiers, FxSwapTradeForm> {

  public boolean isValid(FxSwapTradeForm form, ConstraintValidatorContext context) {
    return form.getPayLegExtIdentifier() == null
        || form.getReceiveLegExtIdentifier() == null
        || !form.getPayLegExtIdentifier().equalsIgnoreCase(form.getReceiveLegExtIdentifier());
  }
}
