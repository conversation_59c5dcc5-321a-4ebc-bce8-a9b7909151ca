package com.solum.xplain.core.portfolio.calendars;

import static com.solum.xplain.extensions.calendar.XplainHolidayCalendars.cdsCalendarCcy;
import static com.solum.xplain.extensions.calendar.XplainHolidayCalendars.fxCalendarCcy;
import static com.solum.xplain.extensions.calendar.XplainHolidayCalendars.swapCalendarCcy;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.index.RateIndex;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import com.solum.xplain.extensions.calendar.XplainHolidayCalendars;
import com.solum.xplain.extensions.index.OffshoreIndices;
import io.atlassian.fugue.Checked;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TradeCalendarUtils {

  public static HolidayCalendarId getCdsTradeCalendar(String currency) {
    return cdsCalendarCcy(Currency.of(currency));
  }

  public static HolidayCalendarId getFxTradeCalendar(String domesticCcy, String foreignCcy) {
    var domesticCurrency = Currency.of(domesticCcy);
    var foreignCurrency = Currency.of(foreignCcy);
    var ccyPair = CurrencyPair.of(domesticCurrency, foreignCurrency).toConventional();
    return fxCalendarCcy(ccyPair);
  }

  public static HolidayCalendarId getCapFloorTradeCalendar(String index, Boolean isOffshore) {
    return tryParseIbor(index, isOffshore)
        .orElseThrow(() -> new IllegalArgumentException("Invalid index: " + index));
  }

  public static HolidayCalendarId getSwapTradeCalendar(
      SwapTradeLegCalendarForm leg1, SwapTradeLegCalendarForm leg2) {
    var payLegCalendar = getLegCalendar(leg1.getIndex(), leg1.getIsOffshore(), leg1.getCurrency());
    var receiveLegCalendar =
        getLegCalendar(leg2.getIndex(), leg2.getIsOffshore(), leg2.getCurrency());
    return payLegCalendar.combinedWith(receiveLegCalendar);
  }

  public static HolidayCalendarId getCalendarByCurrency(String currency) {
    return XplainHolidayCalendars.swapCalendarCcy(Currency.of(currency));
  }

  public static String getSwapTradeCalendar(TradeLegDetails leg1, TradeLegDetails leg2) {
    var payLegCalendar = getLegCalendar(leg1.getIndex(), leg1.getIsOffshore(), leg1.getCurrency());
    var receiveLegCalendar =
        getLegCalendar(leg2.getIndex(), leg2.getIsOffshore(), leg2.getCurrency());
    return payLegCalendar.combinedWith(receiveLegCalendar).getName();
  }

  public static HolidayCalendarId getLegCalendar(String index, Boolean isOffshore, String ccy) {
    return tryParseIbor(index, isOffshore)
        .or(() -> tryParseOffshoreOvernight(index, isOffshore))
        .orElseGet(() -> swapCalendarCcy(Currency.of(ccy)));
  }

  private static Optional<HolidayCalendarId> tryParseIbor(String index, Boolean isOffshore) {
    return Checked.now(() -> IborIndex.of(index))
        .map(i -> toOffshoreIbor(isOffshore, i))
        .map(TradeCalendarUtils::getIborLegCalendar)
        .toOptional();
  }

  private static IborIndex toOffshoreIbor(Boolean isOffshore, IborIndex index) {
    if (BooleanUtils.isTrue(isOffshore)) {
      return OffshoreIndices.lookupOffshoreIbor(index).orElse(index);
    }
    return index;
  }

  private static HolidayCalendarId getIborLegCalendar(IborIndex iborIndex) {
    return iborIndex
        .getEffectiveDateOffset()
        .getAdjustment()
        .getCalendar()
        .combinedWith(iborIndex.getEffectiveDateOffset().getCalendar());
  }

  private static Optional<HolidayCalendarId> tryParseOffshoreOvernight(
      String index, Boolean isOffshore) {
    if (!BooleanUtils.isTrue(isOffshore)) {
      return Optional.empty();
    }

    return Checked.now(() -> OvernightIndex.of(index))
        .toOptional()
        .flatMap(OffshoreIndices::lookupOffshoreOvernight)
        .map(RateIndex::getFixingCalendar);
  }
}
