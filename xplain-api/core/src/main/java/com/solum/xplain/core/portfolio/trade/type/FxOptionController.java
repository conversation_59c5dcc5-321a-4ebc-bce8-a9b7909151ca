package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.FxOptionTradeForm;
import com.solum.xplain.core.portfolio.value.FxOptionTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/fx-opt")
@AllArgsConstructor
public class FxOptionController
    implements BespokeTradeTypedController<FxOptionTradeForm, FxOptionTradeView> {
  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, FxOptionTradeView> toViewFunction(PortfolioItem e) {
    return FxOptionTradeView.of(e);
  }
}
