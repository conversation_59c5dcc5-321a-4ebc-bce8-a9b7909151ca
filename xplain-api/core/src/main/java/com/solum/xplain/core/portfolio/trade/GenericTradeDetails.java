package com.solum.xplain.core.portfolio.trade;

import java.io.Serializable;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class GenericTradeDetails implements Serializable {
  private String assetType;
  private String subAssetType;
  private String additionalInfo;
  private Double notional;
  private String underlying;
  private String optionPosition;
}
