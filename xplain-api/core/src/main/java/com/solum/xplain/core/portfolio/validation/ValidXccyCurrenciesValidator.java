package com.solum.xplain.core.portfolio.validation;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.portfolio.form.SwapLegForm;
import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import com.solum.xplain.core.portfolio.form.XccyTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.apache.commons.lang3.StringUtils;

public class ValidXccyCurrenciesValidator
    implements ConstraintValidator<ValidXccyCurrencies, XccyTradeForm> {

  static final String DIFFERENT_CURRENCIES_REQUIRED =
      "{com.solum.xplain.core.portfolio.validation.XccyDifferentCurrenciesRequired}";

  static final String TRADE_CCY_MUST_MATCH_LEG_CURRENCIES =
      "{com.solum.xplain.core.portfolio.validation.XccyTradeCurrencyMustMatchLegCurrencies}";

  public boolean isValid(XccyTradeForm form, ConstraintValidatorContext context) {
    if (form == null) {
      return true;
    }
    var leg1 = form.getLeg1();
    var leg2 = form.getLeg2();
    var tradeCcy = form.getTradeCurrency();
    if (hasCurrency(leg1) && hasCurrency(leg2) && isNotEmpty(tradeCcy)) {
      if (hasSameCurrency(leg1.getNotionalCurrency(), leg2.getNotionalCurrency())) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate(DIFFERENT_CURRENCIES_REQUIRED)
            .addPropertyNode(SwapTradeForm.Fields.leg2)
            .addPropertyNode(SwapLegForm.Fields.notionalCurrency)
            .addConstraintViolation();
        return false;
      }
      if (!hasSameCurrency(leg1.getNotionalCurrency(), tradeCcy)
          && !hasSameCurrency(leg2.getNotionalCurrency(), tradeCcy)) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate(TRADE_CCY_MUST_MATCH_LEG_CURRENCIES)
            .addPropertyNode(XccyTradeForm.Fields.tradeCurrency)
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }

  private boolean hasCurrency(SwapLegForm form) {
    return form != null && form.getNotionalCurrency() != null;
  }

  private boolean hasSameCurrency(String f1, String f2) {
    return StringUtils.equals(f1, f2);
  }
}
