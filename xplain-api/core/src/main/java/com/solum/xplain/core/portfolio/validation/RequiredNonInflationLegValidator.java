package com.solum.xplain.core.portfolio.validation;

import static com.solum.xplain.core.portfolio.value.CalculationType.INFLATION;
import static org.apache.commons.lang3.ObjectUtils.allNotNull;

import com.solum.xplain.core.portfolio.form.SwapTradeForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredNonInflationLegValidator
    implements ConstraintValidator<RequiredNonInflationLeg, SwapTradeForm> {

  public boolean isValid(SwapTradeForm form, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    if (allNotNull(form.getLeg1(), form.getLeg2())) {
      int inflationLegNo = 0;
      if (form.getLeg1().getCalculationType() == INFLATION) {
        inflationLegNo = 1;
      } else if (form.getLeg2().getCalculationType() == INFLATION) {
        inflationLegNo = 2;
      }
      if (inflationLegNo > 0) {
        context
            .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
            .addPropertyNode("leg" + inflationLegNo)
            .addPropertyNode("calculationType")
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
