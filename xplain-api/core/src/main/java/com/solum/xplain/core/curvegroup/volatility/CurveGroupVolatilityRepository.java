package com.solum.xplain.core.curvegroup.volatility;

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static io.atlassian.fugue.Eithers.cond;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curvegroup.CurveGroupEntryCountSupport;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryCount;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter;
import com.solum.xplain.core.curvegroup.volatility.entity.CapletVolatilityNode;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceNode;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeValueView;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeView;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeValueView;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeView;
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewView;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceForm;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceSearch;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceUpdateForm;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueView;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.market.validation.VolatilityValueKey;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
public class CurveGroupVolatilityRepository
    extends GenericUniqueVersionedEntityRepository<VolatilitySurface> {
  private final MongoOperations mongoOperations;
  private final VolatilityMapper mapper;
  private final ConversionService conversionService;
  private final CurveGroupEntryCountSupport countSupport;

  public CurveGroupVolatilityRepository(
      MongoOperations mongoOperations,
      VolatilityMapper mapper,
      ConversionService conversionService,
      CurveGroupEntryCountSupport countSupport) {
    super(mongoOperations, mapper);
    this.mongoOperations = mongoOperations;
    this.mapper = mapper;
    this.conversionService = conversionService;
    this.countSupport = countSupport;
  }

  public static Criteria uniqueEntityCriteria(String groupId, String surfaceName) {
    return groupIdCriteria(groupId).and(VersionedNamedEntity.Fields.name).is(surfaceName);
  }

  private static Criteria groupIdCriteria(String groupId) {
    return where(VolatilitySurface.Fields.curveGroupId).is(groupId);
  }

  @Override
  protected Criteria uniqueEntityCriteria(VolatilitySurface entity) {
    return uniqueEntityCriteria(entity.getCurveGroupId(), entity.getName());
  }

  @Override
  protected Sort defaultEntitiesSort() {
    return Sort.by(VersionedNamedEntity.Fields.name);
  }

  @Override
  protected VolatilitySurface beforeStoring(VolatilitySurface entity) {
    entity.orderNodes();
    entity.orderCapletNodes();
    return entity;
  }

  public Either<ErrorItem, EntityId> createSurface(String groupId, VolatilitySurfaceForm form) {
    var curve = mapper.fromForm(form, groupId, VolatilitySurface.newOf());
    return insert(curve, form.getVersionForm());
  }

  public Either<ErrorItem, EntityId> updateSurface(
      String groupId, String surfaceId, LocalDate version, VolatilitySurfaceUpdateForm form) {
    return surfaceInGroup(groupId, surfaceId)
        .flatMap(entityId -> entityExact(entityId, version))
        .map(
            entity ->
                update(
                    entity,
                    form.getVersionForm(),
                    copiedEntity -> mapper.fromForm(form, copiedEntity)));
  }

  public Either<ErrorItem, EntityId> archiveSurface(
      String groupId, String surfaceId, LocalDate version, ArchiveEntityForm form) {
    return surfaceInGroup(groupId, surfaceId)
        .flatMap(entityId -> entityExact(entityId, version))
        .map(entity -> archive(entity, form));
  }

  public Either<ErrorItem, EntityId> deleteSurface(
      String groupId, String surfaceId, LocalDate version) {
    return surfaceInGroup(groupId, surfaceId)
        .flatMap(entityId -> entityExact(entityId, version))
        .flatMap(this::delete);
  }

  public List<VolatilitySurfaceView> getSurfaceViews(
      String groupId, LocalDate stateDate, VersionedEntityFilter filter, TableFilter tableFilter) {
    return getSurfaceViews(
        groupId, new BitemporalDate(stateDate), filter, tableFilter, Sort.unsorted());
  }

  public List<VolatilitySurfaceView> getSurfaceViews(
      String groupId,
      BitemporalDate stateDate,
      VersionedEntityFilter filter,
      TableFilter tableFilter,
      Sort sort) {
    var criteria = groupIdCriteria(groupId).andOperator(tableFilterCriteria(tableFilter));
    return entities(stateDate, filter, criteria, sort).stream().map(this::toView).toList();
  }

  private Criteria tableFilterCriteria(TableFilter tableFilter) {
    return tableFilter.criteria(VolatilitySurfaceView.class, conversionService);
  }

  public Either<ErrorItem, VolatilitySurfaceView> getActiveSurfaceView(
      String groupId, String surfaceId, LocalDate stateDate) {
    return getActiveSurface(groupId, surfaceId, stateDate).map(this::toView);
  }

  public List<VolatilitySurfaceView> getSurfaceVersionViews(String groupId, String surfaceId) {
    return surfaceInGroup(groupId, surfaceId)
        .map(this::entityVersions)
        .map(vv -> vv.stream().map(this::toView).toList())
        .getOrElse(List.of());
  }

  public List<VolatilitySurface> getActiveSurfaces(String groupId, BitemporalDate stateDate) {
    return entities(stateDate, active(), groupIdCriteria(groupId));
  }

  public Either<ErrorItem, VolatilitySurface> getActiveSurface(
      String groupId, String surfaceId, LocalDate stateDate) {
    return surfaceInGroup(groupId, surfaceId)
        .flatMap(entityId -> entity(entityId, new BitemporalDate(stateDate), active()));
  }

  public DateList getFutureVersions(String groupId, VolatilitySurfaceSearch search) {
    var searchCriteria = uniqueEntityCriteria(groupId, search.getName());
    return futureVersionsByCriteria(searchCriteria, search.getStateDate());
  }

  public VersionedList<VolatilityNodeView> getSurfaceNodesViews(
      String groupId, String surfaceId, LocalDate version) {
    return surfaceInGroup(groupId, surfaceId)
        .map(entityId -> nodes(entityId, version, mapper::fromNodeToView))
        .getOrElse(VersionedList.empty());
  }

  public VersionedList<VolatilityNodeValueView> getSurfaceNodesValuesViews(
      String groupId,
      String surfaceId,
      LocalDate version,
      Map<String, CalculationMarketValueView> volatilities) {
    return surfaceInGroup(groupId, surfaceId)
        .map(
            entityId ->
                nodes(
                    entityId,
                    version,
                    (n, k) -> mapper.toNodeValueView(n, volatilities.get(k.getKey()))))
        .getOrElse(VersionedList.empty());
  }

  private <T> VersionedList<T> nodes(
      String surfaceId,
      LocalDate version,
      BiFunction<VolatilitySurfaceNode, VolatilityValueKey, T> mapper) {
    return entityExact(surfaceId, version)
        .map(
            v ->
                new VersionedList<>(
                    v.getValidFrom(),
                    v.getNodes().stream()
                        .map(n -> mapper.apply(n, n.getAtmKey(v.index())))
                        .filter(Objects::nonNull)
                        .toList()))
        .getOrElse(VersionedList.empty());
  }

  public VersionedList<CapletVolatilityNodeView> getSurfaceCapletNodesViews(
      String groupId, String surfaceId, LocalDate version) {
    return surfaceInGroup(groupId, surfaceId)
        .map(entityId -> capletNodes(entityId, version, mapper::fromCapletNodeToView))
        .getOrElse(VersionedList.empty());
  }

  public VersionedList<CapletVolatilityNodeValueView> getSurfaceCapletNodesValuesViews(
      String groupId,
      String surfaceId,
      LocalDate version,
      Map<String, CalculationMarketValueView> volatilities) {
    return surfaceInGroup(groupId, surfaceId)
        .map(
            entityId ->
                capletNodes(
                    entityId,
                    version,
                    (n, k) -> mapper.toCapletNodeValueView(n, value(volatilities, k.getKey()))))
        .getOrElse(VersionedList.empty());
  }

  private Double value(Map<String, CalculationMarketValueView> volatilities, String key) {
    return Optional.ofNullable(volatilities.get(key))
        .map(CalculationMarketValueView::getValue)
        .map(BigDecimal::doubleValue)
        .orElse(null);
  }

  private <T> VersionedList<T> capletNodes(
      String surfaceId,
      LocalDate version,
      BiFunction<CapletVolatilityNode, VolatilityValueKey, T> mapper) {
    return entityExact(surfaceId, version)
        .map(
            v ->
                new VersionedList<>(
                    v.getValidFrom(),
                    v.getCapletVolatilities().stream()
                        .map(n -> mapper.apply(n, n.getKey(v.index())))
                        .filter(Objects::nonNull)
                        .toList()))
        .getOrElse(VersionedList.empty());
  }

  public List<VolatilitySurfaceSkewView> getSurfaceSkewsViews(
      String groupId, String surfaceId, LocalDate version) {
    return surfaceInGroup(groupId, surfaceId)
        .flatMap(entityId -> entityExact(entityId, version))
        .map(s -> s.getSkewNodes().stream().map(mapper::toSkewView).toList())
        .getOrElse(List.of());
  }

  public Either<ErrorItem, VolatilitySurfaceSkewView> getSurfaceSkewView(
      String groupId, String surfaceId, LocalDate version, String surfaceSkewId) {
    return surfaceInGroup(groupId, surfaceId)
        .flatMap(entityId -> entityExact(entityId, version))
        .flatMap(
            s ->
                s.getSkewNodes().stream()
                    .filter(n -> n.getSurfaceSkewId().equals(surfaceSkewId))
                    .findAny()
                    .map(mapper::toSkewView)
                    .map(Either::<ErrorItem, VolatilitySurfaceSkewView>right)
                    .orElseGet(
                        () -> Either.left(OBJECT_NOT_FOUND.entity("Surface skew not found"))));
  }

  public VersionedList<VolatilityNodeView> getSurfaceSkewNodesViews(
      String groupId, String surfaceId, LocalDate version, String surfaceSkewId) {
    return surfaceInGroup(groupId, surfaceId)
        .map(entityId -> skewNodes(entityId, version, surfaceSkewId, mapper::fromNodeToView))
        .getOrElse(VersionedList.empty());
  }

  public VersionedList<VolatilityNodeValueView> getSurfaceSkewNodesValueViews(
      String groupId,
      String surfaceId,
      LocalDate version,
      String surfaceSkewId,
      Map<String, CalculationMarketValueView> volatilities) {
    return surfaceInGroup(groupId, surfaceId)
        .map(
            entityId ->
                skewNodes(
                    entityId,
                    version,
                    surfaceSkewId,
                    (n, k) -> mapper.toNodeValueView(n, volatilities.get(k.getKey()))))
        .getOrElse(VersionedList.empty());
  }

  private <T> VersionedList<T> skewNodes(
      String surfaceId,
      LocalDate version,
      String surfaceSkewId,
      BiFunction<VolatilitySurfaceNode, VolatilityValueKey, T> mapper) {
    return entityExact(surfaceId, version)
        .map(
            v ->
                new VersionedList<>(
                    v.getValidFrom(),
                    v.getSkewNodes().stream()
                        .filter(s -> surfaceSkewId.equals(s.getSurfaceSkewId()))
                        .flatMap(
                            s ->
                                v.getNodes().stream()
                                    .map(
                                        n ->
                                            mapper.apply(
                                                n,
                                                n.getSkewKey(
                                                    v.index(),
                                                    s.getSkewValue(),
                                                    v.getSkewType().getSkewType())))
                                    .filter(Objects::nonNull))
                        .toList()))
        .getOrElse(VersionedList.empty());
  }

  private VolatilitySurfaceView toView(VolatilitySurface surface) {
    return mapper.toView(surface);
  }

  private Either<ErrorItem, String> surfaceInGroup(String groupId, String surfaceId) {
    var criteria = groupIdCriteria(groupId).and(VersionedEntity.Fields.entityId).is(surfaceId);
    var isInGroup = mongoOperations.exists(query(criteria), VolatilitySurface.class);
    return cond(isInGroup, OBJECT_NOT_FOUND.entity("Volatility surface not found"), surfaceId);
  }

  public List<CurveGroupEntryCount> activeEntriesCount(CurveGroupEntryFilter filter) {
    return countSupport.activeEntriesCount(filter, VolatilitySurface.class);
  }
}
