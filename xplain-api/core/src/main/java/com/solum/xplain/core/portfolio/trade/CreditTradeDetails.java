package com.solum.xplain.core.portfolio.trade;

import com.opengamma.strata.basics.StandardId;
import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.core.product.ProductType;
import com.solum.xplain.extensions.enums.CreditDocClause;
import com.solum.xplain.extensions.enums.CreditSector;
import com.solum.xplain.extensions.enums.CreditSeniority;
import com.solum.xplain.extensions.enums.PositionType;
import com.solum.xplain.extensions.utils.StandardIdUtils;
import java.io.Serializable;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CreditTradeDetails implements Serializable {

  // CDS fields
  private CreditSeniority seniority;
  private String corpTicker;
  // Credit Index fields
  private Integer creditIndexVersion;
  private Integer creditIndexSeries;
  // Credit Index tranche fields
  private String creditIndexTranche;
  // Common fields
  private String entityLongName; // Credit index or Legal entity name
  private String reference;
  private Double upfront;
  private LocalDate upfrontDate;
  private String upfrontConvention;
  private CreditSector sector;
  private PositionType protection;
  private CreditDocClause docClause;

  public StandardId legalEntityStandardId(Currency currency, ProductType productType) {
    var curveName = curveName(currency, productType);
    return StandardIdUtils.curveIdStandardId(curveName);
  }

  private String curveName(Currency currency, ProductType productType) {
    return CreditTradeDetailsCurveNameResolver.curveName(
        currency.getCode(), productType, reference, seniority, docClause, creditIndexTranche);
  }
}
