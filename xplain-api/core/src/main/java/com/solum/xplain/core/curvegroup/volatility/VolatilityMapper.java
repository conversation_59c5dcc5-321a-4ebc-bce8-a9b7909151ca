package com.solum.xplain.core.curvegroup.volatility;

import com.opengamma.strata.data.MarketData;
import com.solum.xplain.core.common.AuditUserMapper;
import com.solum.xplain.core.common.ObjectIdMapper;
import com.solum.xplain.core.common.versions.VersionedEntityMapper;
import com.solum.xplain.core.curvegroup.volatility.entity.CapletVolatilityNode;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurface;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceNode;
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceSkew;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeForm;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeValueView;
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeView;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeForm;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeValueView;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeView;
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilitySurfaceNodeValue;
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewForm;
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewView;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceForm;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceUpdateForm;
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView;
import com.solum.xplain.core.curvemarket.marketvalue.CalculationMarketValueView;
import com.solum.xplain.core.market.mapping.MarketDataUtils;
import com.solum.xplain.core.market.validation.VolatilityValueKey;
import java.util.function.Function;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(
    uses = {ObjectIdMapper.class, AuditUserMapper.class},
    imports = {CollectionUtils.class},
    nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface VolatilityMapper extends VersionedEntityMapper<VolatilitySurface> {
  VolatilityMapper MAPPER_INSTANCE = Mappers.getMapper(VolatilityMapper.class);

  static Function<VolatilitySurfaceNode, VolatilityNodeValueView> toNodeValueViewForXva(
      MarketData marketData, VolatilityValueKey key) {
    return node ->
        marketData
            .findValue(MarketDataUtils.quoteId(key.getKey()))
            .map(value -> MAPPER_INSTANCE.toNodeValueView(node, value))
            .orElse(null);
  }

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(target = "curveGroupId", source = "groupId")
  VolatilitySurface fromForm(
      VolatilitySurfaceForm form, String groupId, @MappingTarget VolatilitySurface surface);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "entityId", ignore = true)
  @Mapping(target = "recordDate", ignore = true)
  @Mapping(target = "modifiedBy", ignore = true)
  @Mapping(target = "modifiedAt", ignore = true)
  @Mapping(target = "state", ignore = true)
  @Mapping(target = "comment", source = "form.versionForm.comment")
  @Mapping(target = "validFrom", source = "form.versionForm.validFrom")
  @Mapping(target = "curveGroupId", ignore = true)
  @Mapping(target = "name", ignore = true)
  VolatilitySurface fromForm(
      VolatilitySurfaceUpdateForm form, @MappingTarget VolatilitySurface surface);

  @Mapping(target = "versionForm", ignore = true)
  VolatilitySurfaceUpdateForm toUpdateForm(VolatilitySurface surface);

  @Mapping(target = "surfaceSkewId", expression = "java(new org.bson.types.ObjectId().toString())")
  VolatilitySurfaceSkew fromForm(VolatilitySurfaceSkewForm form);

  VolatilityNodeForm toNodeForm(VolatilitySurfaceNode node);

  VolatilitySurfaceSkewForm toSkewForm(VolatilitySurfaceSkew skew);

  CapletVolatilityNodeForm toNodeForm(CapletVolatilityNode node);

  @Mapping(target = "key", source = "key.key")
  VolatilityNodeView fromNodeToView(VolatilitySurfaceNode node, VolatilityValueKey key);

  @Mapping(target = "value", source = "value.value")
  VolatilityNodeValueView toNodeValueView(
      VolatilitySurfaceNode node, CalculationMarketValueView value);

  VolatilityNodeValueView toNodeValueView(VolatilitySurfaceNode node, double value);

  VolatilitySurfaceNodeValue toNodeValue(VolatilitySurfaceNode node, String key, double value);

  @Mapping(target = "key", source = "key.key")
  CapletVolatilityNodeView fromCapletNodeToView(CapletVolatilityNode node, VolatilityValueKey key);

  @Mapping(target = "value", source = "value")
  CapletVolatilityNodeValueView toCapletNodeValueView(CapletVolatilityNode node, Double value);

  VolatilitySurfaceSkewView toSkewView(VolatilitySurfaceSkew skew);

  @Mapping(
      target = "numberOfSwaptionVolatilityNodes",
      expression = "java(CollectionUtils.size(surface.getNodes()))")
  @Mapping(
      target = "numberOfCapletVolatilityNodes",
      expression = "java(CollectionUtils.size(surface.getCapletVolatilities()))")
  @Mapping(
      target = "numberOfSkewNodes",
      expression = "java(CollectionUtils.size(surface.getSkewNodes()))")
  VolatilitySurfaceView toView(VolatilitySurface surface);
}
