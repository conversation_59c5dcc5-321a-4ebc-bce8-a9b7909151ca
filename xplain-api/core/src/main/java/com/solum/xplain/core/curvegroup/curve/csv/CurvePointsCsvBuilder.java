package com.solum.xplain.core.curvegroup.curve.csv;

import com.solum.xplain.core.common.csv.CsvField;
import com.solum.xplain.core.common.csv.CsvOutputFile;
import com.solum.xplain.core.common.csv.CsvRow;
import com.solum.xplain.core.common.value.ChartPoint;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public final class CurvePointsCsvBuilder {

  private static final String CURVE_FIELD = "Curve Name";
  private static final String DATE_FIELD = "Date";
  private static final String VALUE_FIELD = "Value";
  private static final String VALUE_TYPE_FIELD = "Value Type";
  private static final String DISCOUNT_FACTOR_FIELD = "Discount Factor";
  private static final String MARKET_RATES_FIELD = "Market Rate";

  private final String valueFieldHeader;
  private final String predefinedValueType;
  private final List<CurvePoint> rows = new ArrayList<>();

  public static CurvePointsCsvBuilder ofSingleValueType(String valueType) {
    return new CurvePointsCsvBuilder(valueType, valueType);
  }

  public static CurvePointsCsvBuilder ofVariableValueType() {
    return new CurvePointsCsvBuilder(VALUE_FIELD, null);
  }

  public static CurvePointsCsvBuilder ofDiscountFactors() {
    return new CurvePointsCsvBuilder(DISCOUNT_FACTOR_FIELD, DISCOUNT_FACTOR_FIELD);
  }

  public static CurvePointsCsvBuilder ofMarketRates() {
    return new CurvePointsCsvBuilder(MARKET_RATES_FIELD, MARKET_RATES_FIELD);
  }

  public CurvePointsCsvBuilder withPoints(List<ChartPoint> points, String curveName) {
    return withPoints(points, predefinedValueType, curveName);
  }

  public CurvePointsCsvBuilder withPoints(
      List<ChartPoint> points, String valueType, String curveName) {
    rows.addAll(
        points.stream()
            .map(p -> new CurvePoint(curveName, p.getDate(), p.getCalculatedValue(), valueType))
            .toList());
    return this;
  }

  public CurvePointsCsvBuilder withOnlyDatePoints(List<ChartPoint> points, String curveName) {
    rows.addAll(
        points.stream()
            .map(p -> new CurvePoint(curveName, p.getDate(), null, predefinedValueType))
            .toList());
    return this;
  }

  public CsvOutputFile toCsv() {
    var headers = List.of(CURVE_FIELD, DATE_FIELD, valueFieldHeader, VALUE_TYPE_FIELD);
    return new CsvOutputFile(headers, rows.stream().map(this::toRow).toList());
  }

  private CsvRow toRow(CurvePoint point) {
    return new CsvRow(
        List.of(
            new CsvField(CURVE_FIELD, point.curveName),
            new CsvField(DATE_FIELD, point.date),
            new CsvField(valueFieldHeader, point.value),
            new CsvField(VALUE_TYPE_FIELD, point.valueType)));
  }

  @AllArgsConstructor
  private static class CurvePoint {
    private final String curveName;
    private final LocalDate date;
    private final Double value;
    private final String valueType;
  }
}
