package com.solum.xplain.core.ccyexposure.entity;

import com.solum.xplain.core.common.Archivable;
import com.solum.xplain.core.common.diff.AuditableDiffable;
import com.solum.xplain.core.common.diff.VersionDiffs;
import com.solum.xplain.core.users.AuditUser;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.mongodb.core.mapping.Document;

/** Represents a currency exposure. The related cashflows are stored in the Cashflow entity. */
@Document(collation = "en", collection = CcyExposure.CCY_EXPOSURE_COLLECTION)
@ToString(callSuper = true)
@Data
@FieldNameConstants
@RequiredArgsConstructor
public class CcyExposure extends AuditableDiffable<CcyExposure> implements Archivable<CcyExposure> {

  public static final String CCY_EXPOSURE_COLLECTION = "ccyExposure";

  @CreatedBy private AuditUser createdBy;
  @CreatedDate private LocalDateTime createdAt;
  private boolean archived;

  /** Unique name of the exposure. */
  @Nonnull private String name;

  /** Currency of the exposure. This can be changed, but would be rare (e.g. initial user error) */
  @Nonnull private String currency;

  @Nullable private String description;

  private CcyExposure() {
    // for CcyExposureBuilder in tests
  }

  // for test
  CcyExposure(@Nonnull String id, @Nonnull String name, @Nonnull String currency) {
    super();
    setId(id);
    this.name = name;
    this.currency = currency;
  }

  @Override
  public VersionDiffs diff(CcyExposure obj) {
    return VersionDiffs.of(
        new DiffBuilder<>(this, obj, ToStringStyle.DEFAULT_STYLE)
            .append(CcyExposure.Fields.currency, this.currency, obj.currency)
            .append(CcyExposure.Fields.description, this.description, obj.description)
            .build());
  }
}
