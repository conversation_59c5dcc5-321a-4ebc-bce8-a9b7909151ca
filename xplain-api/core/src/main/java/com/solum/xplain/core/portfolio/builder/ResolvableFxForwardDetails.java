package com.solum.xplain.core.portfolio.builder;

import static com.solum.xplain.core.portfolio.builder.validation.TradeBuilderValidatorUtils.validateTradeCurrency;
import static com.solum.xplain.core.portfolio.calendars.TradeCalendarUtils.getFxTradeCalendar;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.product.common.PayReceive;
import com.solum.xplain.core.portfolio.csv.ResolvableFxTradeDetailsBuilder;
import com.solum.xplain.core.portfolio.trade.TradeDetails;
import com.solum.xplain.core.portfolio.trade.TradeInfoDetails;
import com.solum.xplain.core.portfolio.trade.TradeLegDetails;
import java.time.LocalDate;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@Builder
public class ResolvableFxForwardDetails implements ResolvableTradeDetails {
  @NonNull private final Currency receiveCurrency;
  private final Double receiveCurrencyAmount;
  @NonNull private final Currency payCurrency;
  private final Double payCurrencyAmount;
  private final Double fxRate;
  @NonNull private final String businessDayConvention;
  @NonNull private final LocalDate paymentDate;
  private final String payLegExtIdentifier;
  private final String receiveLegExtIdentifier;

  public static class ResolvableFxForwardDetailsBuilder
      implements ResolvableFxTradeDetailsBuilder<ResolvableFxForwardDetails> {}

  @Override
  public TradeDetails toTradeDetails(TradeInfoDetails tradeInfo) {
    validateTradeCurrency(
        tradeInfo.getTradeCurrency(), payCurrency.getCode(), receiveCurrency.getCode());
    var calendar = getFxTradeCalendar(receiveCurrency.getCode(), payCurrency.getCode());
    TradeDetails tradeDetails = TradeDetails.newOf(tradeInfo);
    tradeDetails.setBusinessDayConvention(businessDayConvention);
    tradeDetails.setEndDate(paymentDate);
    tradeDetails.setStartDate(paymentDate);
    tradeDetails.setReceiveLeg(
        tradeLegDetails(
            PayReceive.RECEIVE, receiveCurrency, receiveCurrencyAmount, receiveLegExtIdentifier));
    tradeDetails.setPayLeg(
        tradeLegDetails(PayReceive.PAY, payCurrency, payCurrencyAmount, payLegExtIdentifier));
    tradeDetails.setCalendar(calendar.getName());
    tradeDetails.setFxRate(fxRate);
    return tradeDetails;
  }

  private TradeLegDetails tradeLegDetails(
      PayReceive payReceive, Currency currency, Double amount, String legIdentifier) {
    TradeLegDetails legDetails = new TradeLegDetails();
    legDetails.setExtLegIdentifier(legIdentifier);
    legDetails.setPayReceive(payReceive);
    legDetails.setCurrency(currency.getCode());
    legDetails.setNotional(amount);
    return legDetails;
  }
}
