package com.solum.xplain.core.portfolio.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to validate that the near and far notionals for the leg matching the base currency are
 * the same.
 */
@Documented
@Constraint(validatedBy = RequiredSameBaseCurrencyNotionalsValidator.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface RequiredSameBaseCurrencyNotionals {
  String message() default
      "{com.solum.xplain.api.portfolio.validation.RequiredSameBaseCurrencyNotionals" + ".message}";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};
}
