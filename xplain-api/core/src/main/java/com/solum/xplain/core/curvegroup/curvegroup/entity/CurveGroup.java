package com.solum.xplain.core.curvegroup.curvegroup.entity;

import static com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationStatus.NOT_CALIBRATED;

import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.common.diff.Diffable;
import com.solum.xplain.core.common.diff.VersionDiffs;
import com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationMarketData;
import com.solum.xplain.core.curvegroup.curvegroup.value.CalibrationStatus;
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements;
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType;
import com.solum.xplain.core.portfolio.value.CalculationStrippingType;
import com.solum.xplain.core.users.AuditUser;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collation = "en", collection = CurveGroup.CURVE_GROUP_COLLECTION)
@EqualsAndHashCode(exclude = {"createdAt", "updatedAt"})
@FieldNameConstants
public class CurveGroup implements Diffable<CurveGroup> {
  public static final String CURVE_GROUP_COLLECTION = "curveGroup";
  public static final Pattern SIMPLE_YMD_TIME_REGEX =
      Pattern.compile("P?((\\d+Y)?(\\d+M)?(\\d+W)?(\\d+D)?)");

  @Id private String id;

  @CreatedBy private AuditUser createdBy;

  @LastModifiedBy private AuditUser modifiedBy;

  @CreatedDate private LocalDateTime createdAt;

  @LastModifiedDate private LocalDateTime updatedAt;

  private String name;

  private CalculationDiscountingType calibrationCurrency;
  private CalculationStrippingType calibrationStrippingType;
  private InstrumentPriceRequirements calibrationPriceRequirements;

  private CalibrationStatus calibrationStatus = NOT_CALIBRATED;

  private LocalDateTime calibratedAt;
  private LocalDate calibrationDate;
  private CalibrationMarketData calibrationMarketDataGroup;

  private boolean archived;

  private List<AuditLog> auditLogs;

  public CurveGroup archived() {
    this.archived = true;
    return this;
  }

  public CurveGroup clearCalibrationResults() {
    this.clearCalibratedCurves();
    return this;
  }

  public void clearCalibratedCurves() {
    this.setCalibrationStatus(NOT_CALIBRATED);
    this.setCalibratedAt(null);
    this.setCalibrationDate(null);
    this.setCalibrationCurrency(null);
    this.setCalibrationStrippingType(null);
    this.setCalibrationMarketDataGroup(null);
    this.setCalibrationPriceRequirements(null);
  }

  public void addAuditLog(AuditLog log) {
    if (auditLogs == null) {
      auditLogs = new ArrayList<>();
    }
    auditLogs.add(log);
  }

  @Override
  public VersionDiffs diff(CurveGroup obj) {
    return VersionDiffs.of(
        new DiffBuilder<>(this, obj, ToStringStyle.DEFAULT_STYLE)
            .append("archived", this.archived, obj.archived)
            .append("name", this.name, obj.name)
            .build());
  }
}
