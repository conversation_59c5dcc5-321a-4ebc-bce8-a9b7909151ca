package com.solum.xplain.core.portfolio.form;

import static org.apache.commons.lang3.BooleanUtils.isTrue;

import com.solum.xplain.core.portfolio.trade.OnboardingDetails;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;

@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Builder(access = AccessLevel.PRIVATE)
public class OnboardingDetailsForm {
  @Schema(description = "Deal cost value")
  private Double dealCost;

  @Schema(description = "Accounting cost value")
  private Double accountingCost;

  @Schema(description = "NAV Effective Date")
  private LocalDate vendorOnboardingDate;

  @Schema(description = "If Xplain cost check required")
  private Boolean xplainCostCheck;

  @Schema(description = "If market conformity check required")
  private Boolean marketConfCheck;

  @Schema(description = "If vendor conformity check required")
  private Boolean vendorCheck;

  @Schema(description = "Xplain check Validation date")
  private LocalDate xplainCheckVerified;

  @Schema(description = "Market check Validation date")
  private LocalDate marketCheckVerified;

  @Schema(description = "Vendor check Validation date")
  private LocalDate vendorCheckVerified;

  public static OnboardingDetailsForm fromDetails(@Nullable OnboardingDetails details) {
    if (details == null) {
      return new OnboardingDetailsForm();
    }
    return OnboardingDetailsForm.builder()
        .dealCost(details.getDealCost())
        .accountingCost(details.getAccountingCost())
        .vendorOnboardingDate(details.getVendorOnboardingDate())
        .xplainCostCheck(details.getXplainCostCheck())
        .marketConfCheck(details.getMarketConfCheck())
        .vendorCheck(details.getVendorCheck())
        .xplainCheckVerified(details.getXplainCheckVerified())
        .marketCheckVerified(details.getMarketCheckVerified())
        .vendorCheckVerified(details.getVendorCheckVerified())
        .build();
  }

  public OnboardingDetails details() {
    var details = new OnboardingDetails();
    details.setDealCost(dealCost);
    details.setAccountingCost(accountingCost);
    details.setVendorOnboardingDate(vendorOnboardingDate);
    details.setXplainCostCheck(isTrue(xplainCostCheck));
    details.setMarketConfCheck(isTrue(marketConfCheck));
    details.setVendorCheck(isTrue(vendorCheck));
    details.setXplainCheckVerified(xplainCheckVerified);
    details.setMarketCheckVerified(marketCheckVerified);
    details.setVendorCheckVerified(vendorCheckVerified);
    return details;
  }
}
