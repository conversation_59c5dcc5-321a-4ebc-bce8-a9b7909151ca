package com.solum.xplain.core.curvegroup.curve.validation;

import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.collect.named.Named;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import java.util.Collection;
import java.util.List;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CurveNodeValidationConstants {

  private static final List<String> BRL_CONVENTIONS =
      ConventionalCurveConfigurations.ALL_CONVENTIONAL_CURVES.stream()
          .filter(v -> v.getCurrency() == Currency.BRL)
          .map(ConventionalCurveConvention::getNodeConventions)
          .flatMap(Collection::stream)
          .map(Named::getName)
          .toList();

  public static boolean isBrlConvention(@Nullable String conventionName) {
    if (StringUtils.isEmpty(conventionName)) {
      return false;
    }
    return BRL_CONVENTIONS.contains(conventionName);
  }
}
