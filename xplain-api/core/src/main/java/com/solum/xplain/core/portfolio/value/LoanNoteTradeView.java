package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.CoreProductType.LOAN_NOTE;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.LoanNoteTradeForm;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class LoanNoteTradeView extends LoanNoteTradeForm implements TradeView {
  private String updatedBy;
  private LocalDateTime updatedAt;
  private String tradeId;
  private String calendar;

  public static Either<ErrorItem, LoanNoteTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == LOAN_NOTE) {
      var view = new LoanNoteTradeView();
      var details = item.getTradeDetails();

      view.setStartDate(details.getStartDate());
      view.setEndDate(details.getEndDate());
      view.setRegularStartDate(details.getFirstRegularStartDate());
      view.setRegularEndDate(details.getLastRegularEndDate());
      view.setCreditSpread(details.getLoanNoteTradeDetails().getCreditSpread());
      view.setReference(details.getLoanNoteTradeDetails().getReference());
      view.setSettlementOffsetDays(details.getLoanNoteTradeDetails().getSettlementOffsetDays());
      view.setFixedRate(details.getLoanNoteTradeDetails().getFixedRate());

      view.setCurrency(details.getReceiveLeg().getCurrency());
      view.setNotionalValue(details.getReceiveLeg().getNotional());
      view.setDayCount(details.getReceiveLeg().getDayCount());
      view.setFrequency(details.getReceiveLeg().getPaymentFrequency());

      view.setCalendar(details.getCalendar());
      view.setBusinessDayConvention(details.getBusinessDayConvention());
      view.setStubConvention(details.getStubConvention());
      view.setRollConvention(details.getRollConvention());

      view.updateCommonView(item);
      view.withTradeInfo(details);
      return Either.right(view);
    } else {
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Product type is not expected " + item.getProductType()));
    }
  }
}
