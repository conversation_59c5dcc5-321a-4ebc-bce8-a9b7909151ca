package com.solum.xplain.core.customfield.value;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@FieldNameConstants
public class CustomFieldNameView {

  @Schema(description = "Immutable unique internal identifier for field name")
  @NotEmpty
  private String id;

  @Schema(description = "Immutable unique identifier for name of custom field")
  @NotEmpty
  private String externalId;

  @Schema(description = "Name for custom field")
  @NotEmpty
  private String name;
}
