package com.solum.xplain.core.portfolio.trade;

import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewField;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewQuery;
import com.solum.xplain.core.viewconfig.annotation.ConfigurableViewSubfield;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class CustomTradeField implements Serializable {
  public static class FieldName {
    public static final String CUSTOM_FIELD_VALUE = "customFieldValue";
  }

  @ConfigurableViewSubfield(classifier = "customField")
  private String externalFieldId;

  @ConfigurableViewField(FieldName.CUSTOM_FIELD_VALUE)
  @ConfigurableViewQuery(sortable = true)
  private String value;
}
