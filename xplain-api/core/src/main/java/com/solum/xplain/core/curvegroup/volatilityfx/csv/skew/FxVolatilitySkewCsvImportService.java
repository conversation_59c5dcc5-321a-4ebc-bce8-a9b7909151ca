package com.solum.xplain.core.curvegroup.volatilityfx.csv.skew;

import static com.solum.xplain.core.common.CollectionUtils.join;
import static com.solum.xplain.core.common.EntityId.entityId;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.duplicateItem;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.futureVersionExists;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.newVersionViable;
import static com.solum.xplain.core.common.csv.ImportErrorUtils.orErrors;
import static com.solum.xplain.core.error.Error.IMPORT_ERROR;
import static com.solum.xplain.core.error.Error.MISSING_ENTRY;
import static java.util.stream.Collectors.joining;

import com.google.common.collect.Sets;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.CsvParserResult;
import com.solum.xplain.core.common.csv.ImportItems;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.common.csv.ItemKey;
import com.solum.xplain.core.common.csv.LoggingImportService;
import com.solum.xplain.core.common.csv.NewVersionFormV2Utils;
import com.solum.xplain.core.common.value.NewVersionFormV2;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapper;
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityRepository;
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatility;
import com.solum.xplain.core.curvegroup.volatilityfx.entity.CurveGroupFxVolatilityNode;
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityForm;
import com.solum.xplain.core.curvegroup.volatilityfx.value.CurveGroupFxVolatilityNodeForm;
import com.solum.xplain.core.curvegroup.volatilityfx.value.FxVolatilitySkewForm;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.error.LogItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class FxVolatilitySkewCsvImportService extends LoggingImportService {

  private static final String PARENT_ENTITY_IDENTIFIER = "FX Volatility";

  private final FxVolatilitySkewCsvLoader csvLoader;
  private final CurveGroupFxVolatilityMapper mapper;
  private final CurveGroupFxVolatilityRepository repository;

  public FxVolatilitySkewCsvImportService(
      AuditEntryService auditEntryService,
      FxVolatilitySkewCsvLoader csvLoader,
      CurveGroupFxVolatilityMapper mapper,
      CurveGroupFxVolatilityRepository repository) {
    super(auditEntryService);
    this.csvLoader = csvLoader;
    this.mapper = mapper;
    this.repository = repository;
  }

  @Override
  public String getCollection() {
    return CurveGroupFxVolatility.CURVE_GROUP_FX_VOLATILITY_COLLECTION;
  }

  @Override
  public String getObjectName() {
    return "FX Volatility skews";
  }

  @Transactional
  public Either<List<ErrorItem>, EntityId> importVolatilitySkews(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    // NOTE: if FX Volatility (or nodes) does not exist - import will fail
    return validVolatility(groupId, importOptions.getStateDate())
        .flatMap(
            v ->
                csvLoader
                    .parse(bytes, importOptions.parsingMode())
                    .map(forms -> importSkews(v, importOptions, forms)))
        .fold(
            err -> toErrorReturn(importOptions.getDuplicateAction(), err),
            result -> toReturn(importOptions.getDuplicateAction(), result))
        .map(r -> entityId(groupId));
  }

  private Either<List<ErrorItem>, CurveGroupFxVolatility> validVolatility(
      String groupId, LocalDate stateDate) {
    return repository
        .getActiveVolatility(groupId, BitemporalDate.newOf(stateDate))
        .leftMap(e -> cannotImportSkewsError())
        .flatMap(v -> Eithers.cond(!v.getNodes().isEmpty(), cannotImportSkewsError(), v))
        .leftMap(List::of);
  }

  private ErrorItem cannotImportSkewsError() {
    return new ErrorItem(
        IMPORT_ERROR, "FX Volatility skews cannot be imported as nodes do not exist");
  }

  protected ImportResult importSkews(
      CurveGroupFxVolatility volatility,
      ImportOptions importOptions,
      CsvParserResult<FxVolatilitySkewForm> parserResult) {
    var skews = parserResult.getParsedLines();
    var importItems =
        ImportItems.<FxVolatilitySkewForm, FxVolatilitySkewKey, FxVolatilitySkewForm>builder()
            .existingActiveItems(nodeSkewForms(volatility))
            .existingItemToKeyFn(FxVolatilitySkewKey::from)
            .importItems(skews)
            .importItemToKeyFn(FxVolatilitySkewKey::from)
            .build();
    var logs = importForVolatility(volatility, importItems, importOptions);
    return new ImportResult(logs, parserResult.getWarnings());
  }

  private List<FxVolatilitySkewForm> nodeSkewForms(CurveGroupFxVolatility volatility) {
    return volatility.getNodes().stream()
        .filter(CurveGroupFxVolatilityNode::hasSkew)
        .map(mapper::toSkewForm)
        .distinct()
        .toList();
  }

  private List<LogItem> importForVolatility(
      CurveGroupFxVolatility volatility,
      ImportItems<FxVolatilitySkewForm, FxVolatilitySkewKey, FxVolatilitySkewForm> importItems,
      ImportOptions importOptions) {
    var duplicateAction = importOptions.getDuplicateAction();
    return switch (duplicateAction) {
      case ERROR -> onError(volatility, importItems, importOptions);
      case REPLACE_DELETE, REPLACE, APPEND_DELETE, APPEND ->
          updateSkews(volatility, importItems, importOptions);
    };
  }

  private List<LogItem> onError(
      CurveGroupFxVolatility volatility,
      ImportItems<FxVolatilitySkewForm, FxVolatilitySkewKey, FxVolatilitySkewForm> importItems,
      ImportOptions importOptions) {
    var errors = validate(volatility, importItems, importOptions);
    return asLogItems(errors);
  }

  private List<ErrorItem> validate(
      CurveGroupFxVolatility volatility,
      ImportItems<FxVolatilitySkewForm, FxVolatilitySkewKey, ?> importItems,
      ImportOptions importOptions) {
    var stateDate = importOptions.getStateDate();

    var duplicateErrors = validateDuplicateItems(importItems);
    var missingItemErrors = validateMissingItems(importItems);
    var futureVersionsErrors = validateFutureVersions(volatility, stateDate);
    var viableNewVersionErrors = validateViableNewVersion(volatility, stateDate);
    return join(duplicateErrors, missingItemErrors, futureVersionsErrors, viableNewVersionErrors);
  }

  private List<ErrorItem> validateMissingItems(ImportItems<?, FxVolatilitySkewKey, ?> importItems) {
    return importItems.getSpareKeys().stream().map(this::missingNodeError).toList();
  }

  private ErrorItem missingNodeError(FxVolatilitySkewKey nodeKey) {
    return new ErrorItem(
        MISSING_ENTRY,
        String.format(
            "%s %s in %s %s is missing",
            StringUtils.capitalize(nodeKey.getEntityTypeName()),
            nodeKey.getIdentifier(),
            StringUtils.lowerCase(nodeKey.getParentEntityTypeName()),
            PARENT_ENTITY_IDENTIFIER));
  }

  private List<ErrorItem> validateDuplicateItems(
      ImportItems<?, FxVolatilitySkewKey, ?> importItems) {
    return importItems.getDuplicateKeys().stream()
        .map(k -> duplicateItem(k.getIdentifier()))
        .toList();
  }

  private List<ErrorItem> validateViableNewVersion(
      CurveGroupFxVolatility volatility, LocalDate stateDate) {
    var versionDate = volatility.getValidFrom();
    return orErrors(
        Objects.isNull(versionDate) || stateDate.isAfter(versionDate),
        newVersionViable(PARENT_ENTITY_IDENTIFIER));
  }

  private List<ErrorItem> validateFutureVersions(
      CurveGroupFxVolatility volatility, LocalDate stateDate) {
    var hasFutureVersions =
        repository.getFutureVersions(volatility.getEntityId(), stateDate).notEmpty();
    return orErrors(hasFutureVersions, futureVersionExists(PARENT_ENTITY_IDENTIFIER));
  }

  private List<LogItem> updateSkews(
      CurveGroupFxVolatility volatility,
      ImportItems<FxVolatilitySkewForm, FxVolatilitySkewKey, FxVolatilitySkewForm> importItems,
      ImportOptions importOptions) {
    var nodeForms = forms(importItems, importOptions);
    var eitherId =
        update(
            volatility,
            nodeForms,
            NewVersionFormV2Utils.fromImportOptions(
                importOptions, volatility.getValidFrom(), importOptions::getFutureVersionsAction));
    var updateDescription = description(importItems, importOptions);
    return List.of(createLogItem(updateDescription, eitherId));
  }

  protected List<FxVolatilitySkewForm> forms(
      ImportItems<FxVolatilitySkewForm, FxVolatilitySkewKey, FxVolatilitySkewForm> importItems,
      ImportOptions importOptions) {
    var spareKeys = keysToRemove(importItems, importOptions);
    var replaceableKeys = keysToReplace(importItems, importOptions);
    var removableKeys = Sets.union(spareKeys, replaceableKeys);

    var preservedForms = preservedItemForms(importItems, removableKeys);
    var newForms = newItemsForms(importItems, replaceableKeys);
    return join(preservedForms, newForms);
  }

  private Set<FxVolatilitySkewKey> keysToRemove(
      ImportItems<?, FxVolatilitySkewKey, ?> importItems, ImportOptions importOptions) {
    return importOptions.getDuplicateAction().isDeleteAction()
        ? importItems.getSpareKeys()
        : Set.of();
  }

  private Set<FxVolatilitySkewKey> keysToReplace(
      ImportItems<?, FxVolatilitySkewKey, ?> importItems, ImportOptions importOptions) {
    return importOptions.getDuplicateAction().isReplaceAction()
        ? importItems.getDuplicateKeys()
        : Set.of();
  }

  private List<FxVolatilitySkewForm> newItemsForms(
      ImportItems<FxVolatilitySkewForm, FxVolatilitySkewKey, ?> importItems,
      Set<FxVolatilitySkewKey> keysToReplace) {
    return Sets.union(keysToReplace, importItems.getNewKeys()).stream()
        .map(importItems::importItem)
        .toList();
  }

  private List<FxVolatilitySkewForm> preservedItemForms(
      ImportItems<?, FxVolatilitySkewKey, FxVolatilitySkewForm> importItems,
      Set<FxVolatilitySkewKey> keysToRemove) {
    return Sets.difference(importItems.getExistingKeys(), keysToRemove).stream()
        .map(importItems::existingItem)
        .toList();
  }

  private String description(
      ImportItems<?, FxVolatilitySkewKey, ?> importItems, ImportOptions importOptions) {
    var newKeys = importItems.getNewKeys();
    var removedKeys = keysToRemove(importItems, importOptions);
    var replacedKeys = keysToReplace(importItems, importOptions);
    return String.format(
        "Updated %s. Added %d %s,replaced %d %s and removed %d %s",
        PARENT_ENTITY_IDENTIFIER,
        newKeys.size(),
        keysString(newKeys),
        replacedKeys.size(),
        keysString(replacedKeys),
        removedKeys.size(),
        keysString(removedKeys));
  }

  private String keysString(Set<FxVolatilitySkewKey> keys) {
    if (keys.isEmpty()) {
      return StringUtils.EMPTY;
    } else {
      return keys.stream().map(ItemKey::getIdentifier).collect(joining(", ", "[", "]"));
    }
  }

  private Either<ErrorItem, EntityId> update(
      CurveGroupFxVolatility volatility,
      List<FxVolatilitySkewForm> skewForms,
      NewVersionFormV2 versionForm) {
    CurveGroupFxVolatilityForm form = mapper.toForm(volatility);
    form.setVersionForm(versionForm);
    form.setNodes(nodesForms(volatility));
    form.setSkews(skewForms);
    return repository.createVolatility(volatility.getEntityId(), form); // Will update if exists
  }

  private List<CurveGroupFxVolatilityNodeForm> nodesForms(CurveGroupFxVolatility volatility) {
    return volatility.getNodes().stream().map(mapper::toNodeForm).toList();
  }
}
