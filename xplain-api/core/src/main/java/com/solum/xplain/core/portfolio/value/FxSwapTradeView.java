package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.error.Error.UNEXPECTED_TYPE;
import static com.solum.xplain.core.portfolio.value.FxFormUtils.assignFxFields;
import static com.solum.xplain.core.portfolio.value.FxLongShort.LONG;
import static com.solum.xplain.core.portfolio.value.FxLongShort.SHORT;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.CoreProductType;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import com.solum.xplain.core.portfolio.form.FxSwapNotionalsForm;
import com.solum.xplain.core.portfolio.form.FxSwapTradeForm;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FxSwapTradeView extends FxSwapTradeForm implements TradeView {
  private String tradeId;
  private String updatedBy;
  private LocalDateTime updatedAt;
  private String calendar;

  public static Either<ErrorItem, FxSwapTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == CoreProductType.FXSWAP) {
      var view = new FxSwapTradeView();
      view.withTradeInfo(item.getTradeDetails());
      view.updateCommonView(item);
      view.setNearLegPaymentDate(item.getTradeDetails().getStartDate());
      view.setPaymentDate(item.getTradeDetails().getEndDate());
      view.setBusinessDayConvention(item.getTradeDetails().getBusinessDayConvention());

      var baseCcy = item.getTradeDetails().getInfo().getTradeCurrency();
      assignFxFields(item.getTradeDetails(), view, baseCcy);
      view.setLongShort(
          baseCcy.equals(item.getTradeDetails().getPayLeg().getCurrency()) ? SHORT : LONG);
      var notionalsForm = toNotionalsForm(item, baseCcy);
      view.setNotionalsForm(notionalsForm);

      view.setPayLegExtIdentifier(item.getTradeDetails().getPayLeg().getExtLegIdentifier());
      view.setReceiveLegExtIdentifier(item.getTradeDetails().getReceiveLeg().getExtLegIdentifier());
      view.setCalendar(item.getTradeDetails().getCalendar());
      return Either.right(view);
    } else {
      return Either.left(
          new ErrorItem(UNEXPECTED_TYPE, "Product type is not expected " + item.getProductType()));
    }
  }

  private static FxSwapNotionalsForm toNotionalsForm(VersionedTradeEntity item, String baseCcy) {
    var baseIsPayLeg = baseCcy.equals(item.getTradeDetails().getPayLeg().getCurrency());

    var payNotionalNearDate = item.getTradeDetails().getPayLeg().getNearNotional();
    var payNotionalFarDate = item.getTradeDetails().getPayLeg().getNotional();
    var receiveNotionalNearDate = item.getTradeDetails().getReceiveLeg().getNearNotional();
    var receiveNotionalFarDate = item.getTradeDetails().getReceiveLeg().getNotional();

    var notionalsForm = new FxSwapNotionalsForm();
    notionalsForm.setNearDateBaseNotional(
        baseIsPayLeg ? payNotionalNearDate : receiveNotionalNearDate);
    notionalsForm.setNearDateCounterNotional(
        baseIsPayLeg ? receiveNotionalNearDate : payNotionalNearDate);
    notionalsForm.setFarDateBaseNotional(
        baseIsPayLeg ? payNotionalFarDate : receiveNotionalFarDate);
    notionalsForm.setFarDateCounterNotional(
        baseIsPayLeg ? receiveNotionalFarDate : payNotionalFarDate);
    return notionalsForm;
  }
}
