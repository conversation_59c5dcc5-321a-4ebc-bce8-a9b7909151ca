package com.solum.xplain.core.portfolio.trade.type;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.portfolio.TradeTypeControllerService;
import com.solum.xplain.core.portfolio.form.LoanNoteTradeForm;
import com.solum.xplain.core.portfolio.value.LoanNoteTradeView;
import io.atlassian.fugue.Either;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Getter
@RestController
@RequestMapping("/portfolio/{id}/trades/loan-note")
@AllArgsConstructor
public class LoanNoteController
    implements BespokeTradeTypedController<LoanNoteTradeForm, LoanNoteTradeView> {
  private final TradeTypeControllerService service;

  @Override
  public Either<ErrorItem, LoanNoteTradeView> toViewFunction(PortfolioItem e) {
    return LoanNoteTradeView.of(e);
  }
}
