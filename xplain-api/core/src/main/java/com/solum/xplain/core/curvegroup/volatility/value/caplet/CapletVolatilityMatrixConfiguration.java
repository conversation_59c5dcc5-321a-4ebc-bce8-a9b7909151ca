package com.solum.xplain.core.curvegroup.volatility.value.caplet;

import com.opengamma.strata.basics.date.Tenor;
import com.solum.xplain.core.common.value.VersionedList;
import com.solum.xplain.core.common.value.VolatilityMatrix;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

public class CapletVolatilityMatrixConfiguration
    extends VolatilityMatrix<CapletVolatilityNodeView> {
  private CapletVolatilityMatrixConfiguration(
      List<CapletVolatilityNodeView> content, LocalDate versionDate) {
    super(content, versionDate);
  }

  public static CapletVolatilityMatrixConfiguration configurationFromList(
      List<CapletVolatilityNodeView> nodes, LocalDate versionDate) {
    return new CapletVolatilityMatrixConfiguration(nodes, versionDate);
  }

  public static CapletVolatilityMatrixConfiguration configurationFromList(
      VersionedList<CapletVolatilityNodeView> versionedList) {
    return new CapletVolatilityMatrixConfiguration(
        versionedList.getList(), versionedList.getVersionDate());
  }

  @Override
  protected Comparator<String> rowComparator() {
    return Comparator.comparing(Tenor::parse);
  }

  @Override
  protected Comparator<String> columnComparator() {
    return Comparator.comparing(BigDecimal::new);
  }
}
