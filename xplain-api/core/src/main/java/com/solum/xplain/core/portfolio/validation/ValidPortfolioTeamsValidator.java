package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.company.CompanyTeamValidationService;
import com.solum.xplain.core.company.form.CompanyTeamPortfolio;
import com.solum.xplain.core.company.validation.ValidCompanyTeams;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class ValidPortfolioTeamsValidator
    implements ConstraintValidator<ValidCompanyTeams, CompanyTeamPortfolio> {

  private final CompanyTeamValidationService companyTeamValidationService;

  public ValidPortfolioTeamsValidator(CompanyTeamValidationService companyTeamValidationService) {
    this.companyTeamValidationService = companyTeamValidationService;
  }

  @Override
  public boolean isValid(CompanyTeamPortfolio form, ConstraintValidatorContext context) {

    if (form.getEntityId() != null
        && form.getCompanyId() != null
        && form.getAllowedTeamsForm() != null) {

      boolean validTeams =
          companyTeamValidationService.validCompanyEntityTeams(
              form.getEntityId(), form.getCompanyId(), form.getAllowedTeamsForm());
      if (!validTeams) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate("{ValidCompanyTeams.NotValid}")
            .addPropertyNode("allowedTeamsForm")
            .addPropertyNode("teamIds")
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
