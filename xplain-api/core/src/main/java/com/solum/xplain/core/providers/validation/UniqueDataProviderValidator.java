package com.solum.xplain.core.providers.validation;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.common.RequestPathVariablesSupport;
import com.solum.xplain.core.providers.DataProviderRepository;
import com.solum.xplain.core.providers.value.DataProviderCreateForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.stereotype.Component;

@Component
public class UniqueDataProviderValidator
    implements ConstraintValidator<UniqueDataProvider, DataProviderCreateForm> {
  private final DataProviderRepository repository;
  private final RequestPathVariablesSupport requestPathVariablesSupport;

  public UniqueDataProviderValidator(
      DataProviderRepository repository, RequestPathVariablesSupport requestPathVariablesSupport) {
    this.repository = repository;
    this.requestPathVariablesSupport = requestPathVariablesSupport;
  }

  @Override
  public boolean isValid(DataProviderCreateForm form, ConstraintValidatorContext context) {
    if (isNotEmpty(form.getName())) {
      String groupId = requestPathVariablesSupport.getPathVariable("id");
      boolean hasDuplicate = repository.existsByExternalId(form.getExternalId(), groupId);

      if (hasDuplicate) {
        context.disableDefaultConstraintViolation();
        context
            .buildConstraintViolationWithTemplate("NotUnique")
            .addPropertyNode("externalId")
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
