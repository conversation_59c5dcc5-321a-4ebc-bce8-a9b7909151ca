package com.solum.xplain.core.providers;

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;

import com.solum.xplain.core.audit.entity.AuditLog;
import com.solum.xplain.core.common.diff.Diffable;
import com.solum.xplain.core.common.diff.VersionDiffs;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.providers.enums.DataProviderType;
import com.solum.xplain.core.users.AuditUser;
import io.atlassian.fugue.Either;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.FieldNameConstants;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document(collection = DataProvider.DATA_PROVIDER_COLLECTION)
@EqualsAndHashCode(exclude = {"createdAt", "lastModifiedAt"})
@FieldNameConstants
public class DataProvider implements Diffable<DataProvider> {
  public static final String DATA_PROVIDER_COLLECTION = "dataProvider";
  public static final String XPLAIN_PROVIDER_CODE = "XPLAIN";
  public static final String NAV_PROVIDER_CODE = "NAV";
  public static final List<String> INTERNAL_PROVIDER_CODES =
      List.of(XPLAIN_PROVIDER_CODE, NAV_PROVIDER_CODE);

  @Id private String id;

  private String name;

  private String externalId;

  private Set<DataProviderType> types;

  private boolean archived;

  @CreatedBy private AuditUser createdBy;

  @LastModifiedBy private AuditUser lastModifiedBy;

  @CreatedDate private LocalDateTime createdAt;

  @LastModifiedDate private LocalDateTime lastModifiedAt;

  private List<AuditLog> auditLogs;

  public void addAuditLog(AuditLog log) {
    if (auditLogs == null) {
      auditLogs = new ArrayList<>();
    }
    auditLogs.add(log);
  }

  public DataProvider archived() {
    this.archived = true;
    return this;
  }

  public Either<ErrorItem, DataProvider> allowModify() {
    if (INTERNAL_PROVIDER_CODES.contains(externalId)) {
      return Either.left(OPERATION_NOT_ALLOWED.entity("System provider can not be archived!"));
    }
    return Either.right(this);
  }

  @Override
  public VersionDiffs diff(DataProvider obj) {
    return VersionDiffs.of(
        new DiffBuilder<>(this, obj, ToStringStyle.DEFAULT_STYLE)
            .append("externalId", this.externalId, obj.externalId)
            .append("name", this.name, obj.name)
            .append("types", this.types, obj.types)
            .build());
  }
}
