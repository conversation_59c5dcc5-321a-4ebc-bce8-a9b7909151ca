package com.solum.xplain.core.ccyexposure.validation;

import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import com.solum.xplain.core.ccyexposure.CcyExposureRepository;
import com.solum.xplain.core.ccyexposure.value.CcyExposureCreateForm;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UniqueCcyExposureNameValidator
    implements ConstraintValidator<UniqueCcyExposureName, CcyExposureCreateForm> {

  @Autowired private CcyExposureRepository repository;

  @Override
  public boolean isValid(
      CcyExposureCreateForm ccyExposureCreateForm,
      ConstraintValidatorContext constraintValidatorContext) {
    if (isNotEmpty(ccyExposureCreateForm.getName())) {
      boolean hasDuplicate =
          repository.existsByNameAndArchivedFalse(ccyExposureCreateForm.getName());
      if (hasDuplicate) {
        constraintValidatorContext.disableDefaultConstraintViolation();
        constraintValidatorContext
            .buildConstraintViolationWithTemplate("NotUnique")
            .addPropertyNode("name")
            .addConstraintViolation();
        return false;
      }
    }
    return true;
  }
}
