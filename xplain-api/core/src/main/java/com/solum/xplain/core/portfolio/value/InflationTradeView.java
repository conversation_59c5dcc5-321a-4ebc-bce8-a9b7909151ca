package com.solum.xplain.core.portfolio.value;

import static com.solum.xplain.core.portfolio.CoreProductType.INFLATION;

import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.VersionedTradeEntity;
import io.atlassian.fugue.Either;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class InflationTradeView extends SwapTradeView {

  public static Either<ErrorItem, InflationTradeView> of(VersionedTradeEntity item) {
    if (item.getProductType() == INFLATION) {
      var view = new InflationTradeView();
      view.withTradeDetails(item);
      return Either.right(view);
    } else {
      return Either.left(
          new ErrorItem(
              Error.UNEXPECTED_TYPE, "Product type is not " + "expected " + item.getProductType()));
    }
  }
}
