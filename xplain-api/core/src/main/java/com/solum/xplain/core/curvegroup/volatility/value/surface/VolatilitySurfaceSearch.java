package com.solum.xplain.core.curvegroup.volatility.value.surface;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;

@Data
@ParameterObject
@RequiredArgsConstructor
public class VolatilitySurfaceSearch {

  @NotEmpty private final String name;

  @NotNull private final LocalDate stateDate;
}
