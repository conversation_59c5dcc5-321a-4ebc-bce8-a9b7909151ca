package com.solum.xplain.core.curvegroup.ratefx;

import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.csv.ImportOptions;
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository;
import com.solum.xplain.core.curvegroup.ratefx.csv.FxRatesNodeCsvImportService;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurveGroupFxRatesImportService {

  private final CurveGroupRepository curveGroupRepository;
  private final FxRatesNodeCsvImportService nodeCsvImportService;

  public CurveGroupFxRatesImportService(
      CurveGroupRepository curveGroupRepository, FxRatesNodeCsvImportService nodeCsvImportService) {
    this.curveGroupRepository = curveGroupRepository;
    this.nodeCsvImportService = nodeCsvImportService;
  }

  @Transactional
  public Either<List<ErrorItem>, EntityId> uploadNodes(
      String groupId, ImportOptions importOptions, byte[] bytes) {
    return curveGroupRepository
        .getGroup(groupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(g -> nodeCsvImportService.importRates(groupId, importOptions, bytes))
        .flatMap(entityId -> clearCalibrationResults(entityId.getId()));
  }

  private Either<List<ErrorItem>, EntityId> clearCalibrationResults(String groupId) {
    return curveGroupRepository
        .clearCalibrationResults(groupId)
        .leftMap(ErrorItem.ListOfErrors::from);
  }
}
