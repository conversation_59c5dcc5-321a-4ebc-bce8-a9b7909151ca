package com.solum.xplain.core.curvegroup.curve;

import static com.solum.xplain.core.common.CollectionUtils.chunked;
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active;
import static com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConfigurations.lookupByName;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static com.solum.xplain.core.error.Error.VALIDATION_ERROR;
import static org.springframework.data.mongodb.core.query.Criteria.where;
import static org.springframework.data.mongodb.core.query.Query.query;

import com.opengamma.strata.basics.ReferenceData;
import com.solum.xplain.core.calibrationapi.CurveCalibrationResultsProvider;
import com.solum.xplain.core.common.EntityId;
import com.solum.xplain.core.common.filter.VersionedEntityFilter;
import com.solum.xplain.core.common.value.ArchiveEntityForm;
import com.solum.xplain.core.common.value.DateList;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.common.versions.GenericUniqueVersionedEntityRepository;
import com.solum.xplain.core.common.versions.VersionedEntity;
import com.solum.xplain.core.common.versions.VersionedNamedEntity;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationIndexComparator;
import com.solum.xplain.core.curvegroup.CurveGroupEntryCountSupport;
import com.solum.xplain.core.curvegroup.conventions.ConventionalCurveConvention;
import com.solum.xplain.core.curvegroup.curve.dto.CalibratedCurvesOptions;
import com.solum.xplain.core.curvegroup.curve.dto.GetCalibratedCurvesRequest;
import com.solum.xplain.core.curvegroup.curve.entity.Curve;
import com.solum.xplain.core.curvegroup.curve.entity.CurveCalibrationResult;
import com.solum.xplain.core.curvegroup.curve.value.CurveForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveNodeCalculatedView;
import com.solum.xplain.core.curvegroup.curve.value.CurveSearch;
import com.solum.xplain.core.curvegroup.curve.value.CurveUpdateForm;
import com.solum.xplain.core.curvegroup.curve.value.CurveView;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryCount;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupEntryFilter;
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateForm;
import com.solum.xplain.core.curvemarket.CurveConfigMarketStateQuotes;
import com.solum.xplain.core.error.ErrorItem;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Repository;

@Repository
public class CurveGroupCurveRepository extends GenericUniqueVersionedEntityRepository<Curve> {

  private final MongoOperations mongoOperations;
  private final CurveMapper curveMapper;
  private final CurveGroupEntryCountSupport countSupport;
  private final CurveNodeCalculatedViewMapper curveNodeCalculatedViewMapper;
  private final CurveCalibrationResultsProvider calibrationResultsProvider;

  public CurveGroupCurveRepository(
      MongoOperations mongoOperations,
      CurveMapper curveMapper,
      CurveGroupEntryCountSupport countSupport,
      CurveNodeCalculatedViewMapper curveNodeCalculatedViewMapper,
      CurveCalibrationResultsProvider calibrationResultsProvider) {
    super(mongoOperations, curveMapper);
    this.mongoOperations = mongoOperations;
    this.curveMapper = curveMapper;
    this.countSupport = countSupport;
    this.curveNodeCalculatedViewMapper = curveNodeCalculatedViewMapper;
    this.calibrationResultsProvider = calibrationResultsProvider;
  }

  public static Criteria uniqueEntityCriteria(String curveGroupId, String curveName) {
    return groupIdCriteria(curveGroupId).and(VersionedNamedEntity.Fields.name).is(curveName);
  }

  private static Criteria groupIdCriteria(String curveGroupId) {
    return where(Curve.Fields.curveGroupId).is(curveGroupId);
  }

  @Override
  protected Criteria uniqueEntityCriteria(Curve entity) {
    return uniqueEntityCriteria(entity.getCurveGroupId(), entity.getName());
  }

  public Either<ErrorItem, EntityId> createCurve(String groupId, CurveForm curveForm) {
    return validateNodeActions(curveForm.getName(), curveForm)
        .map(f -> curveMapper.fromForm(curveForm, groupId, Curve.newOf()))
        .flatMap(curve -> insert(curve, curveForm.getVersionForm()));
  }

  public Either<ErrorItem, EntityId> updateCurve(
      String groupId, String curveId, LocalDate version, CurveUpdateForm f) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entityExact(entityId, version))
        .flatMap(entity -> validateUpdate(entity, f))
        .map(
            entity ->
                update(
                    entity,
                    f.getVersionForm(),
                    copiedEntity -> curveMapper.fromForm(f, copiedEntity)));
  }

  private Either<ErrorItem, Curve> validateUpdate(Curve curve, CurveUpdateForm form) {
    if (!Objects.equals(curve.getCurveType().name(), form.getCurveType())) {
      var message = String.format("Curve type can not be updated for %s", curve.getName());
      return Either.left(VALIDATION_ERROR.entity(message));
    }
    return validateNodeActions(curve.getName(), form).map(f -> curve);
  }

  private Either<ErrorItem, CurveUpdateForm> validateNodeActions(
      String curveName, CurveUpdateForm form) {
    if (!CurvesUtils.is3MCurve(curveName)
        && !StringUtils.isAllEmpty(form.getClashAction(), form.getMinGap())) {
      var message =
          String.format(
              "Node Clash Priority and Minimum Node Gap can not be set for curve %s", curveName);
      return Either.left(VALIDATION_ERROR.entity(message));
    }
    return Either.right(form);
  }

  public Either<ErrorItem, EntityId> archiveCurve(
      String groupId, String curveId, LocalDate version, ArchiveEntityForm f) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entityExact(entityId, version))
        .map(entity -> archive(entity, f));
  }

  public Either<ErrorItem, EntityId> deleteCurve(
      String groupId, String curveId, LocalDate version) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entityExact(entityId, version))
        .flatMap(this::delete);
  }

  public Either<ErrorItem, CurveView> getActiveCurveView(
      String groupId, String curveId, BitemporalDate stateDate) {
    return getActiveCurve(groupId, curveId, stateDate).map(this::toView);
  }

  public List<CurveView> getCurveViews(
      String groupId, BitemporalDate stateDate, VersionedEntityFilter filter) {
    var criteria = groupIdCriteria(groupId);
    return entities(stateDate, filter, criteria).stream()
        .map(this::toView)
        .sorted(curveComparator())
        .toList();
  }

  public List<CurveView> getCurveViews(
      String groupId,
      BitemporalDate stateDate,
      GetCalibratedCurvesRequest getCalibratedCurvesRequest,
      VersionedEntityFilter filter) {
    return entities(stateDate, filter, groupIdCriteria(groupId)).stream()
        .map(curve -> this.toView(curve, getCalibratedCurvesRequest))
        .sorted(curveComparator())
        .toList();
  }

  public List<CurveView> getCurveVersionViews(String groupId, String curveId) {
    return curveInGroup(groupId, curveId)
        .map(this::entityVersions)
        .map(vv -> vv.stream().map(this::toView).toList())
        .getOrElse(List.of());
  }

  public Either<ErrorItem, Curve> getActiveCurve(
      String groupId, String curveId, BitemporalDate stateDate) {
    return curveInGroup(groupId, curveId)
        .flatMap(entityId -> entity(entityId, stateDate, active()));
  }

  public List<Curve> getActiveCurves(String groupId, BitemporalDate stateDate) {
    return entities(stateDate, active(), groupIdCriteria(groupId));
  }

  public DateList getFutureVersions(String groupId, CurveSearch search) {
    var searchCriteria = uniqueEntityCriteria(groupId, search.getName());
    return futureVersionsByCriteria(searchCriteria, search.getStateDate());
  }

  public List<CurveNodeCalculatedView> getCurveNodes(
      CurveGroupView curveGroup,
      String curveId,
      BitemporalDate version,
      CurveConfigMarketStateQuotes marketStateQuotes,
      CalibratedCurvesOptions calibrationOptions) {
    return getActiveCurve(curveGroup.getId(), curveId, version)
        .map(
            c ->
                curveNodeCalculatedViewMapper.toNodeViews(
                    c,
                    marketStateQuotes.quotes(),
                    calibrationOptions.getValuationDate(),
                    resultEntity(c, calibrationOptions, marketStateQuotes.stateForm())))
        .getOrElse(List.of());
  }

  public List<CurveNodeCalculatedView> getCurveNodes(
      CurveGroupView curveGroup,
      String curveId,
      BitemporalDate version,
      CurveConfigMarketStateQuotes marketStateQuotes,
      CalibratedCurvesOptions calibrationOptions,
      ReferenceData referenceData) {
    return getActiveCurve(curveGroup.getId(), curveId, version)
        .map(
            c ->
                curveNodeCalculatedViewMapper.toNodeViews(
                    c,
                    marketStateQuotes.quotes(),
                    calibrationOptions.getValuationDate(),
                    resultEntity(c, calibrationOptions, marketStateQuotes.stateForm()),
                    referenceData))
        .getOrElse(List.of());
  }

  public void clearCalibrationResults(String groupId) {
    var idsStream =
        mongoOperations.query(Curve.class).matching(query(groupIdCriteria(groupId))).stream()
            .map(Curve::getEntityId);
    chunked(idsStream, 1000).forEach(this::removeCurveCalibrationResults);
  }

  private void removeCurveCalibrationResults(List<String> curveIds) {
    calibrationResultsProvider.clearCalibrationResults(curveIds);
  }

  private Either<ErrorItem, String> curveInGroup(String groupId, String curveId) {
    var criteria = groupIdCriteria(groupId).and(VersionedEntity.Fields.entityId).is(curveId);
    var curveInGroup = mongoOperations.exists(query(criteria), Curve.class);
    return Eithers.cond(curveInGroup, OBJECT_NOT_FOUND.entity("Curve not found"), curveId);
  }

  private CurveView toView(Curve curve) {
    return curveMapper.toView(curve, resultEntity(curve));
  }

  private CurveView toView(Curve curve, GetCalibratedCurvesRequest request) {
    var calibrationOptions =
        new CalibratedCurvesOptions(
            request.getValuationDate(),
            request.getDiscountingType(),
            request.getCalibrationStrippingType());
    var stateForm =
        new CurveConfigMarketStateForm(
            request.getMarketDataGroupId(),
            null,
            request.getMarketDataSource(),
            request.getStateDate(),
            request.getCurveDate(),
            request.getPriceRequirements());
    return curveMapper.toView(curve, resultEntity(curve, calibrationOptions, stateForm));
  }

  @Nullable
  public CurveCalibrationResult resultEntity(
      Curve c, CalibratedCurvesOptions calibrationOptions, CurveConfigMarketStateForm stateForm) {
    return calibrationResultsProvider.resultEntity(c.getEntityId(), calibrationOptions, stateForm);
  }

  private Comparator<CurveView> curveComparator() {
    return (c1, c2) ->
        Comparator.comparing(ConventionalCurveConvention::getCurrency)
            .thenComparing(ConventionalCurveConvention::getSortOrder)
            .thenComparing(CurveConfigurationIndexComparator.INSTANCE)
            .compare(
                lookupByName(c1.getName(), c1.getCurveType()).orElseThrow(curveNotFoundError(c1)),
                lookupByName(c2.getName(), c2.getCurveType()).orElseThrow(curveNotFoundError(c2)));
  }

  private static Supplier<IllegalArgumentException> curveNotFoundError(CurveView curveView) {
    return () ->
        new IllegalArgumentException(
            "Curve %s with type %s not found"
                .formatted(curveView.getName(), curveView.getCurveType()));
  }

  public CurveCalibrationResult resultEntity(Curve c) {
    return calibrationResultsProvider.latestResult(c.getEntityId());
  }

  public List<CurveGroupEntryCount> activeEntriesCount(CurveGroupEntryFilter filter) {
    return countSupport.activeEntriesCount(filter, Curve.class);
  }
}
