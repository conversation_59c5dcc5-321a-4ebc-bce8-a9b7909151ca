package com.solum.xplain.core.portfolio.validation;

import com.solum.xplain.core.portfolio.value.CalculationType;
import java.util.Collection;
import java.util.function.Supplier;
import java.util.stream.Stream;

public class CalculationTypeSupplier implements Supplier<Collection<String>> {
  @Override
  public Collection<String> get() {
    return Stream.of(CalculationType.values()).map(Enum::name).toList();
  }
}
