package com.solum.xplain.core.curvegroup.curvebond.entity;

import static com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition.ofBondCurve;
import static java.lang.String.format;
import static java.time.format.DateTimeFormatter.ofPattern;

import com.solum.xplain.core.curvegroup.conventions.CurveConvention;
import com.solum.xplain.core.curvegroup.instrument.InstrumentDefinition;
import java.time.LocalDate;
import lombok.Data;

@Data
public class BondCurveNode {

  private LocalDate maturityDate;
  private String cusip;
  private double coupon;

  public InstrumentDefinition instrument(CurveConvention curve) {
    return ofBondCurve(curve.getName(), curve.getCurrency().getCode(), mdk(), mdkName());
  }

  private String mdkFormat(String template) {
    return format(template, maturityDate.format(ofPattern("ddMMMyyyy")), cusip);
  }

  public String mdk() {
    return mdkFormat("%S_%S");
  }

  public String mdkName() {
    return mdkFormat("%S %S");
  }
}
