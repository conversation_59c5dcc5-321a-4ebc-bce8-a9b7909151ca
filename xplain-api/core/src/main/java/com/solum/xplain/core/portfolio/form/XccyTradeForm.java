package com.solum.xplain.core.portfolio.form;

import static com.solum.xplain.core.portfolio.CoreProductType.XCCY;

import com.solum.xplain.core.common.validation.CurrenciesSupplier;
import com.solum.xplain.core.common.validation.ValidStringSet;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.trade.TradeValue;
import com.solum.xplain.core.portfolio.validation.RequiredNonInflationLeg;
import com.solum.xplain.core.portfolio.validation.ValidXccyCurrencies;
import com.solum.xplain.core.portfolio.value.ParsableToTradeValue;
import io.atlassian.fugue.Either;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

@Data
@ValidXccyCurrencies
@RequiredNonInflationLeg
@FieldNameConstants
public class XccyTradeForm extends SwapTradeForm implements ParsableToTradeValue {

  @NotEmpty
  @ValidStringSet(CurrenciesSupplier.class)
  private String tradeCurrency;

  @Override
  public Either<ErrorItem, TradeValue> toTradeValue() {
    return createTradeDetails(toTradeInfo()).map(details -> defaultTradeValue(XCCY, details));
  }

  @Override
  protected String tradeCurrency() {
    return tradeCurrency;
  }
}
