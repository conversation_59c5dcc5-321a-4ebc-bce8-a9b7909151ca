package com.solum.xplain.core.portfolio.trade.type

import static com.solum.xplain.core.authentication.Authorities.MODIFY_TRADE
import static com.solum.xplain.core.authentication.Authorities.VIEW_TRADE
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.allocationTradeForm
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.allocationTradeFormWith
import static com.solum.xplain.core.test.ControllerTestHelper.userWithAuthority
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.customfield.CustomFieldNameRepository
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.PortfolioItemBuilder
import com.solum.xplain.core.portfolio.ReferenceTradesProvider
import com.solum.xplain.core.portfolio.TradeTypeControllerService
import com.solum.xplain.core.portfolio.form.AllocationTradeForm
import com.solum.xplain.core.portfolio.form.CustomTradeFieldForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.portfolio.value.PortfolioItemRefDetailsView
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.core.test.TestSecurityConfig
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [AllocationTradeController])
@Import([TestSecurityConfig])
class AllocationTradeControllerTest extends Specification {

  private static final String URI = "/portfolio/{id}/trades/allocated"
  private static final String PORTFOLIO_ID = "portfolio"

  @SpringBean
  TradeTypeControllerService service = Mock()

  @SpringBean
  ReferenceTradesProvider referenceTradesProvider = Mock()

  @SpringBean
  PortfolioItemRepository portfolioItemRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = Mock()

  @SpringBean
  CustomFieldNameRepository fieldNameRepository = Mock()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  def "should get allocation trade"() {
    setup:
    1 * service.tradeView(PORTFOLIO_ID, "1", { it.getActualDate() == LocalDate.parse("2020-01-01") }) >> right(PortfolioItemBuilder.allocationTrade())
    1 * referenceTradesProvider.fetchReferenceTrade("refId", _ as LocalDate) >> Optional.of(new PortfolioItemRefDetailsView())
    when:
    def results = mockMvc.perform(get(URI + "/1", PORTFOLIO_ID)
      .param("stateDate", "2020-01-01")
      .with(userWithAuthority(VIEW_TRADE))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("externalTradeId") >= 0
    }
  }

  @Unroll
  def "should create new allocation with form #form, role #role, and response #response"() {
    setup:
    service.insert(PORTFOLIO_ID, _ as AllocationTradeForm) >> right(entityId("1"))
    referenceTradesProvider.existsActiveReferenceTrade(allocationTradeForm().getReferenceTradeId(), allocationTradeForm().getVersionForm().getValidFrom(), null) >> true
    referenceTradesProvider.fetchReferenceTrade(_, allocationTradeForm().getVersionForm().getValidFrom()) >>
      Optional.of(new PortfolioItemRefDetailsView(productType: CoreProductType.IRS))
    fieldNameRepository.existsByExternalId("F1") >> true
    fieldNameRepository.existsByExternalId("F2") >> true

    when:
    def results = mockMvc.perform(post(URI, PORTFOLIO_ID)
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | form                                                                                                                    | code | response
    VIEW_TRADE   | allocationTradeForm()                                                                                                   | 403  | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | allocationTradeForm()                                                                                                   | 200  | """{"id":"1"}"""
    MODIFY_TRADE | allocationTradeFormWith(["csaDiscountingGroup": "USD"])                                                                 | 200  | """{"id":"1"}"""
    MODIFY_TRADE | allocationTradeFormWith(["referenceTradeId": null])                                                                     | 412  | "NotEmpty.allocationTradeForm.referenceTradeId"
    MODIFY_TRADE | allocationTradeFormWith(["referenceTradeId": "REF_NOT_EXISTS"])                                                         | 412  | "ValidReferenceTradeId"
    MODIFY_TRADE | allocationTradeFormWith(["allocationNotional": null])                                                                   | 412  | "NotNull.allocationTradeForm.allocationNotional"
    MODIFY_TRADE | allocationTradeFormWith(["allocationNotional": -100])                                                                   | 412  | "Positive.allocationTradeForm.allocationNotional"
    MODIFY_TRADE | allocationTradeFormWith(["positionType": "SELL"])                                                                       | 412  | "Position must be null"
    MODIFY_TRADE | allocationTradeFormWith(["csaDiscountingGroup": "XAU"])                                                                 | 412  | "ValidStringSet.allocationTradeForm.csaDiscountingGroup"
    MODIFY_TRADE | allocationTradeFormWith(["customFields": [new CustomTradeFieldForm("F1", "F1")]])                                       | 200  | """{"id":"1"}"""
    MODIFY_TRADE | allocationTradeFormWith(["customFields": [new CustomTradeFieldForm("F3", "F1")]])                                       | 412  | "Custom Field Name does not exist!"
    MODIFY_TRADE | allocationTradeFormWith(["customFields": [new CustomTradeFieldForm("F1", "F2"), new CustomTradeFieldForm("F1", "F2")]]) | 412  | "Custom Trade Field must be unique"
  }

  @Unroll
  def "should update allocation with role #role then #responseStatus"() {
    setup:
    service.update(PORTFOLIO_ID, LocalDate.parse("2020-01-01"), _ as AllocationTradeForm, "1") >> right(entityId("1"))
    referenceTradesProvider.existsActiveReferenceTrade(allocationTradeForm().getReferenceTradeId(), allocationTradeForm().getVersionForm().getValidFrom(), null) >> true
    referenceTradesProvider.fetchReferenceTrade(_, allocationTradeForm().getVersionForm().getValidFrom()) >>
      Optional.of(new PortfolioItemRefDetailsView(productType: CoreProductType.IRS))

    when:
    def results = mockMvc.perform(put(URI + "/1/2020-01-01", PORTFOLIO_ID)
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(allocationTradeForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == responseStatus

    where:
    role         | responseStatus
    MODIFY_TRADE | 200
    VIEW_TRADE   | 403
  }
}
