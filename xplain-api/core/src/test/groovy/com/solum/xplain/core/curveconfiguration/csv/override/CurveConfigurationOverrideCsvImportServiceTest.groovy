package com.solum.xplain.core.curveconfiguration.csv.override

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND
import static com.solum.xplain.core.common.csv.DuplicateAction.APPEND_DELETE
import static com.solum.xplain.core.common.csv.DuplicateAction.ERROR
import static com.solum.xplain.core.common.csv.DuplicateAction.REPLACE
import static com.solum.xplain.core.common.csv.ParsingMode.STRICT
import static com.solum.xplain.core.common.value.CurrentVersionAction.UPDATE
import static com.solum.xplain.core.common.value.FutureVersionsAction.KEEP
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FIXED_IBOR_SWAP
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FX_SWAP
import static com.solum.xplain.core.error.Error.DUPLICATE_ENTRY
import static com.solum.xplain.core.error.Error.FUTURE_VERSION_EXISTS
import static com.solum.xplain.core.error.Error.MISSING_ENTRY
import static com.solum.xplain.core.error.Error.NEW_VERSION_VIABLE
import static com.solum.xplain.core.error.Error.PARSING_ERROR
import static io.atlassian.fugue.Either.right

import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.csv.CsvParserResult
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.curveconfiguration.entity.CurveConfiguration
import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationOverride
import com.solum.xplain.core.curveconfiguration.entity.CurveConfigurationProviderOverride
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationProviderOverrideForm
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationSearchForm
import com.solum.xplain.core.curveconfiguration.value.CurveConfigurationUpdateForm
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import io.atlassian.fugue.Either
import java.time.LocalDate
import spock.lang.Specification

class CurveConfigurationOverrideCsvImportServiceTest extends Specification {
  def static PAST_VERSION_DATE = LocalDate.parse("2019-01-01")
  def static STATE_DATE = LocalDate.parse("2020-01-01")
  def static FUTURE_VERSION_DATE = LocalDate.parse("2020-07-07")
  def static VERSION_COMMENT = "version comment"
  def static FILE_CONTENT = [] as byte[]
  def static NAME = "CONFIG"

  def auditEntryService = Mock(AuditEntryService)
  def repository = Mock(CurveConfigurationRepository)
  def loader = Mock(CurveConfigurationOverrideCsvLoader)

  CurveConfigurationOverrideCsvImportService service =
  new CurveConfigurationOverrideCsvImportService(auditEntryService, loader, repository)

  def "should return parse errors"() {
    setup:
    1 * repository.findAllActiveItems(STATE_DATE) >> [new CurveConfiguration(name: NAME)]
    1 * loader.parse(_, STRICT, ["CONFIG"] as Set<String>) >> Either.left(List.of(PARSING_ERROR.entity("ERR")))
    0 * repository.insert(_)
    0 * repository.update(_, _, _)
    0 * repository.archive(_, _, _)
    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = service.importOverrides(importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == PARSING_ERROR
    errors[0].description == "ERR"
  }

  def "should return NEW_VERSION_VIABLE for NEW curve config override when ERROR"() {
    setup:
    1 * loader.parse(_, STRICT, ["CONFIG"] as Set<String>) >> right(parserResult([parsedForm()]))
    1 * repository.findAllActiveItems(STATE_DATE) >> [new CurveConfiguration(name: NAME, validFrom: PAST_VERSION_DATE)]
    1 * repository.futureVersions(new CurveConfigurationSearchForm(NAME, STATE_DATE)) >> new DateList([])
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = service.importOverrides(importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == NEW_VERSION_VIABLE
    errors[0].description == "New version is viable for CONFIG"
  }

  def "should return FUTURE_VERSION_EXISTS (and NEW_VERSION_VIABLE) for NEW curve config override when ERROR"() {
    setup:
    1 * loader.parse(_, STRICT, ["CONFIG"] as Set<String>) >> right(parserResult([parsedForm()]))
    1 * repository.findAllActiveItems(STATE_DATE) >> [new CurveConfiguration(name: NAME, validFrom: PAST_VERSION_DATE)]
    1 * repository.futureVersions(new CurveConfigurationSearchForm(NAME, STATE_DATE)) >> new DateList([FUTURE_VERSION_DATE])
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = service.importOverrides(importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 2
    errors[0].reason == FUTURE_VERSION_EXISTS
    errors[0].description == "CONFIG has future version(s)"
    errors[1].reason == NEW_VERSION_VIABLE
    errors[1].description == "New version is viable for CONFIG"
  }

  def "should return DUPLICATE_ENTRY and NEW_VERSION_VIABLE for DUPLICATE when ERROR"() {
    setup:
    1 * loader.parse(_, STRICT, ["CONFIG"] as Set<String>) >> right(parserResult([parsedForm()]))
    1 * repository.findAllActiveItems(STATE_DATE) >> [
      new CurveConfiguration(
      name: NAME,
      validFrom: PAST_VERSION_DATE,
      overrides: [
        new CurveConfigurationOverride(
        priority: 1,
        instruments: [(FIXED_IBOR_SWAP): provider()]
        )
      ]
      )
    ]
    1 * repository.futureVersions(new CurveConfigurationSearchForm(NAME, STATE_DATE)) >> new DateList([])
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = service.importOverrides(importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 2
    errors[0].reason == DUPLICATE_ENTRY
    errors[0].description == "Order: 1 instrument: FIXED_IBOR_SWAP already exists"
    errors[1].reason == NEW_VERSION_VIABLE
    errors[1].description == "New version is viable for CONFIG"
  }

  def "should return DUPLICATE_ENTRY and FUTURE_VERSION_EXISTS for DUPLICATE when ERROR"() {
    setup:
    1 * loader.parse(_, STRICT, ["CONFIG"] as Set<String>) >> right(parserResult([parsedForm()]))
    1 * repository.findAllActiveItems(STATE_DATE) >> [
      new CurveConfiguration(
      name: NAME,
      validFrom: STATE_DATE,
      overrides: [
        new CurveConfigurationOverride(
        priority: 1,
        instruments: [(FIXED_IBOR_SWAP): provider()]
        )
      ]
      )
    ]
    1 * repository.futureVersions(new CurveConfigurationSearchForm(NAME, STATE_DATE)) >> new DateList([FUTURE_VERSION_DATE])
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = service.importOverrides(importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 2
    errors[0].reason == DUPLICATE_ENTRY
    errors[0].description == "Order: 1 instrument: FIXED_IBOR_SWAP already exists"
    errors[1].reason == FUTURE_VERSION_EXISTS
    errors[1].description == "CONFIG has future version(s)"
  }

  def "should return MISSING_ENTRY when ERROR"() {
    setup:
    1 * loader.parse(_, STRICT, ["CONFIG"] as Set<String>) >> right(parserResult([parsedForm()]))
    1 * repository.findAllActiveItems(STATE_DATE) >> [
      new CurveConfiguration(
      name: NAME,
      validFrom: STATE_DATE,
      overrides: [
        new CurveConfigurationOverride(
        priority: 1,
        instruments: [(FX_SWAP): provider()]
        )
      ]
      )
    ]
    1 * repository.futureVersions(new CurveConfigurationSearchForm(NAME, STATE_DATE)) >> new DateList([])
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, ERROR, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = service.importOverrides(importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    def errors = result.left().get() as List<ErrorItem>
    errors.size() == 1
    errors[0].reason == MISSING_ENTRY
    errors[0].description == "Order: 1 instrument: FX_SWAP is missing"
  }

  def "should create NEW override when APPEND"() {
    setup:
    1 * loader.parse(_, STRICT, ["CONFIG"] as Set<String>) >> right(parserResult([parsedForm()]))
    1 * repository.findAllActiveItems(STATE_DATE) >> [
      new CurveConfiguration(
      entityId: "id",
      name: NAME,
      validFrom: STATE_DATE,
      overrides: [
        new CurveConfigurationOverride(
        priority: 1,
        instruments: [(FX_SWAP): provider()]
        )
      ]
      )
    ]
    0 * repository.futureVersions(_)
    1 * repository.update(
      "id",
      STATE_DATE, { it ->
        assert it.overrides[0].instruments[FIXED_IBOR_SWAP] != null
        assert it.overrides[0].instruments[FX_SWAP] != null
      } as CurveConfigurationUpdateForm) >> right(entityId("entityId")

      )
    0 * repository.archive(_, _, _)

    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = service.importOverrides(importOptions, FILE_CONTENT)
    then:
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId("id")
  }


  def "should create NEW override and archive MISSING when APPEND_DELETE"() {
    setup:
    1 * loader.parse(_, STRICT, ["CONFIG"] as Set<String>) >> right(parserResult([parsedForm()]))
    1 * repository.findAllActiveItems(STATE_DATE) >> [
      new CurveConfiguration(
      entityId: "id",
      name: NAME,
      validFrom: STATE_DATE,
      overrides: [
        new CurveConfigurationOverride(
        priority: 1,
        instruments: [(FX_SWAP): provider()]
        )
      ]
      )
    ]
    0 * repository.futureVersions(_)

    1 * repository.update(
      "id",
      STATE_DATE, { it ->
        assert it.overrides[0].instruments[FIXED_IBOR_SWAP].primary == "P1"
        assert it.overrides[0].instruments[FIXED_IBOR_SWAP].secondary == "P2"
        assert it.overrides[0].instruments[FX_SWAP] == null
      } as CurveConfigurationUpdateForm) >> right(entityId("entityId")

      )
    1 * auditEntryService.newEntryWithLogs(_, _)

    when:
    def importOptions = new ImportOptions(STATE_DATE, APPEND_DELETE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = service.importOverrides(importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId("id")
  }

  def "should replace DUPLICATE override when REPLACE"() {
    setup:
    1 * loader.parse(_, STRICT, ["CONFIG"] as Set<String>) >> right(parserResult([parsedForm()]))
    1 * repository.findAllActiveItems(STATE_DATE) >> [
      new CurveConfiguration(
      entityId: "id",
      name: NAME,
      validFrom: STATE_DATE,
      overrides: [
        new CurveConfigurationOverride(
        priority: 1,
        instruments: [(FIXED_IBOR_SWAP): provider()]
        )
      ]
      )
    ]
    0 * repository.futureVersions(_)
    1 * repository.update(
      "id",
      STATE_DATE, { it ->
        assert it.overrides[0].instruments[FIXED_IBOR_SWAP].primary == "P1"
        assert it.overrides[0].instruments[FIXED_IBOR_SWAP].secondary == "P2"
      } as CurveConfigurationUpdateForm) >> right(entityId("entityId")

      )
    1 * auditEntryService.newEntryWithLogs(_, _) >> right(entityId("id"))

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = service.importOverrides(importOptions, FILE_CONTENT)

    then:
    result.isRight()
    def ids = result.right().get() as List<EntityId>
    ids.size() == 1
    ids[0] == entityId("id")
  }

  def "should return error for overlapping curve names"() {
    setup:
    1 * loader.parse(_, STRICT, ["CONFIG"] as Set<String>) >> right(parserResult([parsedForm(2)]))
    1 * repository.findAllActiveItems(STATE_DATE) >> [
      new CurveConfiguration(
      entityId: "id",
      name: NAME,
      validFrom: STATE_DATE,
      overrides: [
        new CurveConfigurationOverride(
        priority: 1,
        instruments: [
          (FIXED_IBOR_SWAP): new CurveConfigurationProviderOverride(primary: "BBG", assetNames: ["EUR 3M"], fxPairs: [], sectors: [])])
      ]
      )
    ]
    0 * repository.futureVersions(_)
    0 * repository.update(_, _, _ as CurveConfigurationUpdateForm)
    1 * auditEntryService.newEntryWithLogs(_, _) >> right(entityId("id"))

    when:
    def importOptions = new ImportOptions(STATE_DATE, REPLACE, STRICT, VERSION_COMMENT, UPDATE, KEEP, KEEP, null)
    def result = service.importOverrides(importOptions, FILE_CONTENT)

    then:
    result.isLeft()
    result.left().get()[0].reason == Error.VALIDATION_ERROR
    result.left().get()[0].description == "Overlapping overrides on [EUR 3M] for instrument FIXED_IBOR_SWAP"
  }

  def parsedForm(Integer order = 1) {
    CurveConfigurationInstrumentCsvOverrides.newOf(
      NAME,
      [
        new CurveConfigurationInstrumentCsvOverride(
        order,
        FIXED_IBOR_SWAP,
        CurveConfigurationProviderOverrideForm.newOf("P1", "P2", ["EUR 3M"], [], [])
        )
      ]
      )
  }

  CsvParserResult<CurveConfigurationInstrumentCsvOverrides> parserResult(List<CurveConfigurationInstrumentCsvOverrides> parsedRows) {
    return new CsvParserResult<>(parsedRows, [])
  }

  def provider() {
    return new CurveConfigurationProviderOverride(primary: "BBG")
  }
}
