package com.solum.xplain.core.classifiers

import com.google.common.collect.Ordering
import spock.lang.Specification
import spock.lang.Unroll

class SwapConventionComparatorTest extends Specification {

  @Unroll
  def "should order swap conventions #input to #output"() {
    expect:
    output == Ordering.from(SwapConventionComparator.INSTANCE).sortedCopy(input)

    where:
    input                           | output
    []                              | []
    [null, null]                    | [null, null]
    ["A", "C", "B"]                 | ["A", "B", "C"]
    ["ABC", "ABC"]                  | ["ABC", "ABC"]
    ["1M", "2Y", "12M", "3M", "2W"] | ["2W", "1M", "3M", "12M", "2Y"]
    ["ABC-12M-A", "ABC-3M-B"]       | ["ABC-12M-A", "ABC-3M-B"]
    ["ABC-1M-3M", "ABC-3M-1M"]      | ["ABC-1M-3M", "ABC-3M-1M"]
    ["ABC-1M-3M", "ABC-1M-1M"]      | ["ABC-1M-1M", "ABC-1M-3M"]
    ["ABC-12M-3M", "ABC-3M"]        | ["ABC-12M-3M", "ABC-3M"]
  }
}
