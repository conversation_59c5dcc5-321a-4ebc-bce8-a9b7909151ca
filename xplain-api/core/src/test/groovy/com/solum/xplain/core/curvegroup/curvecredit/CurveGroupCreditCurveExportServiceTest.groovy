package com.solum.xplain.core.curvegroup.curvecredit

import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.CDS
import static com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveNodeType.FUNDING
import static java.time.LocalDate.now

import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.curvecredit.classifiers.CreditCurveType
import com.solum.xplain.core.curvegroup.curvecredit.csv.node.CreditCurveNodeExportView
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveCreditNodeCalculatedView
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveFundingNodeCalculatedView
import com.solum.xplain.core.curvegroup.curvecredit.value.CreditCurveView
import com.solum.xplain.core.curvegroup.curvecredit.value.MdkExportForm
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView
import com.solum.xplain.core.curvemarket.CurveMarketSample
import com.solum.xplain.core.curvemarket.InstrumentMarketKeyDefinitionExportService
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport
import com.solum.xplain.extensions.enums.CreditDocClause
import com.solum.xplain.shared.utils.filter.TableFilter
import io.atlassian.fugue.Either
import java.time.LocalDate
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Sort
import spock.lang.Specification

class CurveGroupCreditCurveExportServiceTest extends Specification {
  def static GROUP_ID = "groupId"
  def static STATE_DATE = now()
  def static CURVE_DATE = now().minusDays(1)

  def curveGroupRepository = Mock(CurveGroupRepository)
  def repository = Mock(CurveGroupCreditCurveRepository)
  def marketDataQuotesSupport = Mock(MarketDataQuotesSupport)
  def definitionExportService = Mock(InstrumentMarketKeyDefinitionExportService)

  def service = new CurveGroupCreditCurveExportService(curveGroupRepository, repository, marketDataQuotesSupport, definitionExportService)


  def setup() {
    def curveGroup = new CurveGroupView(id: GROUP_ID)
    curveGroupRepository.getEither(GROUP_ID) >> Either.right(curveGroup)
    marketDataQuotesSupport.getFullQuotes(CurveMarketSample.MARKET_STATE_FORM) >> [:]
  }

  def "should get credit curves CSV"() {
    setup:
    def stateDate = BitemporalDate.newOf(STATE_DATE)
    def sort = Sort.unsorted()
    1 * repository.getCurveViews(GROUP_ID, stateDate, _, _, sort) >> [
      new CreditCurveView(
      curveType: CreditCurveType.CDS,
      reference: "REF",
      corpTicker: "BARC",
      entityLongName: "legalEntity",
      recoveryRate: 1.0,
      currency: "EUR",
      calendar: "EUTA",
      quoteConvention: "POINTS_UPFRONT",
      fixedCoupon: 0.01,
      seniority: "SNRFOR",
      numberOfCdsNodes: 11,
      numberOfFundingNodes: 12,
      validFrom: LocalDate.parse("2019-01-01"),
      sector: "FINANCIALS",
      docClause: CreditDocClause.CR14,
      name: "BARC_EUR_SNRFOR_CR14"),
      new CreditCurveView(
      curveType: CreditCurveType.CREDIT_INDEX,
      recoveryRate: 1.0,
      currency: "USD",
      calendar: "EUTA",
      quoteConvention: "POINTS_UPFRONT",
      fixedCoupon: 0.01,
      numberOfCdsNodes: 11,
      numberOfFundingNodes: 12,
      validFrom: LocalDate.parse("2019-01-01"),
      entityLongName: "CDX_NA_IG",
      creditIndexStartDate: LocalDate.parse("2020-01-01"),
      creditIndexVersion: 1,
      creditIndexSeries: 2,
      creditIndexFactor: BigDecimal.ONE,
      reference: "REFERENCE_CODE",
      name: "REFERENCE_CODE_USD"
      ),
      new CreditCurveView(
      curveType: CreditCurveType.CREDIT_INDEX_TRANCHE,
      recoveryRate: 1.0,
      currency: "USD",
      calendar: "EUTA",
      quoteConvention: "POINTS_UPFRONT",
      fixedCoupon: 0.01,
      numberOfCdsNodes: 11,
      numberOfFundingNodes: 12,
      validFrom: LocalDate.parse("2019-01-01"),
      entityLongName: "CDX_NA_IG",
      creditIndexTranche: "3-6",
      creditIndexStartDate: LocalDate.parse("2020-01-01"),
      creditIndexVersion: 1,
      creditIndexSeries: 2,
      creditIndexFactor: BigDecimal.ONE,
      reference: "TRANCHE_CODE",
      name: "TRANCHE_CODE_USD"
      )
    ]

    when:
    def result = service.getAllCurvesCsv(
      GROUP_ID,
      stateDate,
      TableFilter.emptyTableFilter(),
      null,
      sort
      )

    then:
    result.isRight()

    new String(result.getOrNull().bytes.byteArray, "UTF-8") ==
      getClass().getResourceAsStream("/curvegroup/curvecredit/csv/CreditCurves.csv").getText("UTF-8")
  }

  def "should return credit curve CDS nodes CSV"() {
    setup:
    1 * repository.curveCdsNodes(_, _, _, _, _, _) >> [
      new CreditCurveCreditNodeCalculatedView(
      creditSpread: BigDecimal.ONE,
      dataSource: "BBG",
      ticker: "Tick",
      survivalProbability: 5.0,
      key: "KEY",
      tenor: "1Y",
      type: CDS
      )
    ]

    1 * repository.getActiveCurveView(_, _, _) >> Either.right(new CreditCurveView(
      name: "BARC_EUR_SNRFOR_CR14",
      corpTicker: "BARC",
      currency: "EUR",
      seniority: "SNRFOR",
      docClause: CreditDocClause.CR14
      ))

    when:
    def result = service.getCurveCdsNodesCsv(
      GROUP_ID,
      "curveId",
      now(),
      now(),
      CurveMarketSample.MARKET_STATE_FORM,
      null)

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") ==
      "Curve Id,Node Type,Tenor\n" +
      "BARC_EUR_SNRFOR_CR14,CDS,1Y\n"
  }

  def "should return credit curve funding nodes CSV"() {
    setup:

    1 * repository.getFundingNodes(_, _, _, _, _) >> [
      new CreditCurveFundingNodeCalculatedView(
      creditSpread: BigDecimal.ONE,
      dataSource: "BBG",
      ticker: "Tick",
      key: "KEY",
      tenor: "1Y"
      )
    ]

    1 * repository.getActiveCurveView(_, _, _) >> Either.right(new CreditCurveView(
      name: "BARC_EUR_SNRFOR_CR14",
      corpTicker: "BARC",
      currency: "EUR",
      seniority: "SNRFOR",
      docClause: CreditDocClause.CR14
      ))

    when:
    def result = service.getCurveFundingNodesCsv(GROUP_ID,
      "curveId",
      now(),
      CurveMarketSample.MARKET_STATE_FORM,
      null)

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") ==
      "Curve Id,Node Type,Tenor\n" +
      "BARC_EUR_SNRFOR_CR14,FUNDING,1Y\n"
  }

  def "should return all credit curves CDS and Funding nodes CSV"() {
    setup:
    def fundingNode = new CreditCurveNodeExportView("BARC_EUR_SNRFOR_CR14", FUNDING, "1Y")
    def fundingNode2 = new CreditCurveNodeExportView("BNPP_EUR_SNRFOR_MM14", FUNDING, "1Y")
    def cdsNode = new CreditCurveNodeExportView("BARC_EUR_SNRFOR_CR14", CDS, "1Y")
    def cdsNode2 = new CreditCurveNodeExportView("BNPP_EUR_SNRFOR_MM14", CDS, "1Y")
    def stateDate = BitemporalDate.newOf(STATE_DATE)
    def sort = Sort.unsorted()
    1 * repository.getCurveViews(GROUP_ID, stateDate, _, _, sort) >>
      [
        new CreditCurveView(
        entityId: "id1",
        corpTicker: "BARC",
        currency: "EUR",
        seniority: "SNRFOR",
        docClause: CreditDocClause.CR14,
        name: "BARC_EUR_SNRFOR_CR14"),
        new CreditCurveView(
        entityId: "id2",
        corpTicker: "BNPP",
        currency: "EUR",
        seniority: "SNRFOR",
        docClause: CreditDocClause.MM14,
        name: "BNPP_EUR_SNRFOR_MM14")
      ]

    1 * repository.allCurveNodes(_, "id1", _) >> [cdsNode, fundingNode]
    1 * repository.allCurveNodes(_, "id2", _) >> [cdsNode2, fundingNode2]

    when:
    def result = service.getAllCurvesNodesCsv(
      GROUP_ID,
      stateDate,
      TableFilter.emptyTableFilter(),
      sort
      )

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") ==
      "Curve Id,Node Type,Tenor\n" +
      "BARC_EUR_SNRFOR_CR14,CDS,1Y\n" +
      "BARC_EUR_SNRFOR_CR14,FUNDING,1Y\n" +
      "BNPP_EUR_SNRFOR_MM14,CDS,1Y\n" +
      "BNPP_EUR_SNRFOR_MM14,FUNDING,1Y\n"
  }

  def "should export funding nodes MDK "() {
    setup:
    1 * repository.getActiveCurves(_, _) >> []
    1 * definitionExportService.instrumentMdkDefinitions(
      "FundingNodes",
      [],
      {},
      {},
      now(),
      "cc") >> Either.right(FileResponseEntity.zipFile(new ByteArrayResource("a".bytes), "name"))
    when:
    def result = service.getFundingNodesMDKDefinitionsCsv(
      GROUP_ID,
      new MdkExportForm(now(), [], "cc")
      )
    then:
    result.isRight()
  }

  def "should export CDS nodes MDK definitions"() {
    setup:
    1 * repository.getActiveCurves(_, _) >> []
    1 * definitionExportService.instrumentMdkDefinitions(
      "CDSNodes",
      [],
      {},
      {},
      now(),
      "cc") >> Either.right(FileResponseEntity.zipFile(new ByteArrayResource("a".bytes), "name"))
    when:
    def result = service.getCdsNodesMDKDefinitionCsv(GROUP_ID, new MdkExportForm(now(), [], "cc"))
    then:
    result.isRight()
  }

  def "should export Credit Index nodes MDK definitions"() {
    setup:
    1 * repository.getActiveCurves(_, _) >> []
    1 * definitionExportService.instrumentMdkDefinitions(
      "IndexNodes",
      [],
      {},
      {},
      now(),
      "cc") >> Either.right(FileResponseEntity.zipFile(new ByteArrayResource("a".bytes), "name"))
    when:
    def result = service.getIndexNodesMDKDefinitionCsv(GROUP_ID, new MdkExportForm(now(), [], "cc"))
    then:
    result.isRight()
  }
}
