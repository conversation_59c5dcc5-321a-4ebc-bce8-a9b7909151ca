package com.solum.xplain.core.portfolio.validation

import com.solum.xplain.core.portfolio.form.IrsTradeForm
import com.solum.xplain.core.portfolio.form.SwapLegForm
import com.solum.xplain.core.portfolio.form.XccyTradeForm
import jakarta.validation.ConstraintValidatorContext
import org.testcontainers.shaded.org.apache.commons.lang3.BooleanUtils
import spock.lang.Specification
import spock.lang.Unroll

class RequiredSameCurrenciesValidatorTest extends Specification {
  @Unroll
  "validation should return result for form #form"() {
    setup:
    def validator = new RequiredSameCurrenciesValidator()
    def context = Mock(ConstraintValidatorContext)
    def constraintBuilder = Mock(ConstraintValidatorContext.ConstraintViolationBuilder)
    def nodeContext = Mock(ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext)

    BooleanUtils.toInteger(!result) * context.disableDefaultConstraintViolation()
    _ * context.getDefaultConstraintMessageTemplate() >> "RequiredSameCurrencies"
    _ * context.buildConstraintViolationWithTemplate("RequiredSameCurrencies") >> constraintBuilder
    def p = property
    property.size() * constraintBuilder.addPropertyNode({
      p.contains(it)
    }) >> nodeContext
    property.size() * nodeContext.addPropertyNode({
      it == "notionalCurrency"
    }) >> nodeContext
    _ * nodeContext.addConstraintViolation()


    expect:
    validator.isValid(form, context) == result

    where:
    form                                                | property | result
    new IrsTradeForm(
      leg1: new SwapLegForm(notionalCurrency: "EUR"),
      leg2: new SwapLegForm(notionalCurrency: "USD")
      )                                                   | ["leg2"] | false
    new IrsTradeForm(
      leg1: new SwapLegForm(notionalCurrency: "EUR"),
      leg2: new SwapLegForm(notionalCurrency: "EUR")
      )                                                   | []       | true
    new IrsTradeForm(
      leg1: new SwapLegForm(),
      leg2: new SwapLegForm()
      )                                                   | []       | true
    new XccyTradeForm()                                 | []       | true
  }
}
