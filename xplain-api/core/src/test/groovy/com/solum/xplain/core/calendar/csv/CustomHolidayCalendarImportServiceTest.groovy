package com.solum.xplain.core.calendar.csv

import static com.solum.xplain.core.common.csv.ParsingMode.STRICT

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.date.HolidayCalendarIds
import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.audit.entity.AuditEntry
import com.solum.xplain.core.calendar.HolidayCalendarController
import com.solum.xplain.core.calendar.entity.CustomHolidayCalendar
import com.solum.xplain.core.calendar.form.CustomHolidayCalendarForm
import com.solum.xplain.core.calendar.repository.CustomHolidayCalendarRepository
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.csv.CsvParserResult
import com.solum.xplain.core.common.csv.DuplicateAction
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.csv.ParsingMode
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.error.LogItem
import io.atlassian.fugue.Either
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.Month
import java.util.stream.Stream
import spock.lang.Specification

class CustomHolidayCalendarImportServiceTest extends Specification {
  public static final LocalDate HOLIDAY = LocalDate.of(2023, Month.SEPTEMBER, 29)
  public static final LocalDate WORKING_DAY_1 = LocalDate.of(2023, Month.SEPTEMBER, 30)
  public static final LocalDate WORKING_DAY_2 = LocalDate.of(2023, Month.OCTOBER, 1)
  public static final LocalDate WORKING_DAY_3 = LocalDate.of(2023, Month.OCTOBER, 7)
  CustomHolidayCalendarRepository repository = Mock(CustomHolidayCalendarRepository)
  HolidayCalendarCsvLoader loader = Mock(HolidayCalendarCsvLoader)
  AuditEntryService auditEntryService = Mock(AuditEntryService)

  CustomHolidayCalendarImportService importService = new CustomHolidayCalendarImportService(auditEntryService, repository, loader)

  def "should insert new calendar with #duplicateAction"() {
    given:
    def importOptions = new ImportOptions(LocalDate.of(2023, Month.SEPTEMBER, 26), duplicateAction, STRICT, "new calendar", null, FutureVersionsAction.KEEP, null, null)
    byte[] bytes = [0, 1, 2, 3]
    repository.findActiveByCalendarId(HolidayCalendarIds.JPTO, _ as BitemporalDate) >> Optional.empty()
    loader.parse(bytes, STRICT) >> Either.right(parsedFile())

    when:
    def either = importService.uploadCustomHolidayCalendar("JPTO", importOptions, bytes)

    then:
    either.isRight()
    with(either.getOrNull()) {
      assert size() == 1
      assert it[0].id == "JPTO"
    }

    and:
    1 * auditEntryService.newEntryWithLogs(_ as AuditEntry, _ as List<LogItem>) >> { entry, logs -> Either.right(entry) }
    1 * repository.createCalendar(HolidayCalendarIds.JPTO, _ as CustomHolidayCalendarForm) >> { calendarId, CustomHolidayCalendarForm form ->
      assert form.holidays == [HOLIDAY] as Set<LocalDate>
      assert form.weekendDays == [DayOfWeek.SATURDAY, DayOfWeek.SUNDAY] as Set<DayOfWeek>
      assert form.workingDays == [WORKING_DAY_1, WORKING_DAY_2, WORKING_DAY_3] as Set<LocalDate>
      assert form.versionForm.validFrom == LocalDate.EPOCH
      assert form.versionForm.futureVersionsAction == importOptions.futureVersionsAction
      assert form.versionForm.comment == importOptions.comment
      assert form.versionForm.stateDate == importOptions.stateDate
      return EntityId.entityId("JPTO")
    }
    0 * repository.updateCalendar(_, _)

    where:
    duplicateAction << [DuplicateAction.REPLACE, DuplicateAction.APPEND]
  }

  def "should not insert calendar which is the same as the standard calendar"() {
    given:
    def importOptions = new ImportOptions(LocalDate.of(2023, Month.SEPTEMBER, 26), DuplicateAction.REPLACE, STRICT, "new calendar", null, FutureVersionsAction.KEEP, null, null)
    byte[] bytes = [0, 1, 2, 3]
    repository.findActiveByCalendarId(HolidayCalendarIds.JPTO, _ as BitemporalDate) >> Optional.empty()
    def standardHolidays = HolidayCalendarIds.JPTO.resolve(ReferenceData.standard()).holidays(LocalDate.ofYearDay(HolidayCalendarController.MIN_YEAR, 1), LocalDate.ofYearDay(HolidayCalendarController.MAX_YEAR + 1, 1))
    loader.parse(bytes, STRICT) >> Either.right(parsedFile(standardHolidays))

    when:
    def either = importService.uploadCustomHolidayCalendar("JPTO", importOptions, bytes)

    then:
    either.isRight()
    with(either.getOrNull()) {
      assert size() == 1
      assert it[0].id == "JPTO"
    }

    and:
    1 * auditEntryService.newEntryWithLogs(_ as AuditEntry, _ as List<LogItem>) >> { entry, logs -> Either.right(entry) }
    0 * repository.createCalendar(HolidayCalendarIds.JPTO, _)
    0 * repository.updateCalendar(_, _)
  }

  def "should insert calendar which is the same as the standard calendar if we are deleting future archived records"() {
    given:
    def importOptions = new ImportOptions(LocalDate.of(2023, Month.SEPTEMBER, 26), DuplicateAction.REPLACE, STRICT, "new calendar", null, FutureVersionsAction.DELETE, null, null)
    byte[] bytes = [0, 1, 2, 3]
    repository.findActiveByCalendarId(HolidayCalendarIds.JPTO, _ as BitemporalDate) >> Optional.empty()
    def standardHolidays = HolidayCalendarIds.JPTO.resolve(ReferenceData.standard()).holidays(LocalDate.ofYearDay(HolidayCalendarController.MIN_YEAR, 1), LocalDate.ofYearDay(HolidayCalendarController.MAX_YEAR + 1, 1))
    loader.parse(bytes, STRICT) >> Either.right(parsedFile(standardHolidays))

    when:
    def either = importService.uploadCustomHolidayCalendar("JPTO", importOptions, bytes)

    then:
    either.isRight()
    with(either.getOrNull()) {
      assert size() == 1
      assert it[0].id == "JPTO"
    }

    and:
    1 * auditEntryService.newEntryWithLogs(_ as AuditEntry, _ as List<LogItem>) >> { entry, logs -> Either.right(entry) }
    1 * repository.createCalendar(HolidayCalendarIds.JPTO, _ as CustomHolidayCalendarForm) >> { calendarId, CustomHolidayCalendarForm form ->
      assert form.versionForm.validFrom == LocalDate.EPOCH
      assert form.versionForm.futureVersionsAction == importOptions.futureVersionsAction
      assert form.versionForm.comment == importOptions.comment
      assert form.versionForm.stateDate == importOptions.stateDate
      return EntityId.entityId("JPTO")
    }
    0 * repository.updateCalendar(_, _)
  }

  def "should not insert calendar with parse error"() {
    given:
    def importOptions = new ImportOptions(LocalDate.of(2023, Month.SEPTEMBER, 26), DuplicateAction.REPLACE, STRICT, "new calendar", null, FutureVersionsAction.KEEP, null, null)
    byte[] bytes = [0, 1, 2, 3]
    loader.parse(bytes, STRICT) >> Either.left(List.of(new ErrorItem(Error.IMPORT_ERROR, "test error")))

    when:
    def either = importService.uploadCustomHolidayCalendar("JPTO", importOptions, bytes)

    then:
    either.isLeft()
    with(either.left().get()) {
      assert size() == 1
      assert it[0].reason == Error.IMPORT_ERROR
      assert it[0].description == "test error"
    }

    and:
    1 * auditEntryService.newEntryWithLogs(_ as AuditEntry, _ as List<LogItem>) >> { entry, logs -> Either.right(entry) }
    0 * repository.createCalendar(HolidayCalendarIds.JPTO, _ as CustomHolidayCalendarForm)
    0 * repository.updateCalendar(_, _)
  }

  def "should check for existing record"() {
    given:
    def importOptions = new ImportOptions(LocalDate.of(2023, Month.SEPTEMBER, 26), DuplicateAction.ERROR, STRICT, "new calendar", null, FutureVersionsAction.KEEP, null, null)
    byte[] bytes = [0, 1, 2, 3]
    repository.findActiveByCalendarId(HolidayCalendarIds.JPTO, _ as BitemporalDate) >> Optional.of(new CustomHolidayCalendar().tap { entityId = "JPTO" })
    loader.parse(bytes, STRICT) >> Either.right(parsedFile())

    when:
    def either = importService.uploadCustomHolidayCalendar("JPTO", importOptions, bytes)

    then:
    either.isLeft()
    with(either.left().get()) {
      assert size() == 1
      assert it[0].reason == Error.DUPLICATE_ENTRY
      assert it[0].description == "JPTO already exists"
    }


    and:
    1 * auditEntryService.newEntryWithLogs(_ as AuditEntry, _ as List<LogItem>) >> { entry, logs -> Either.right(entry) }
    0 * repository.createCalendar(HolidayCalendarIds.JPTO, _)
    0 * repository.updateCalendar(_, _)
  }

  def "should check for matching standard calendar"() {
    given:
    def importOptions = new ImportOptions(LocalDate.of(2023, Month.SEPTEMBER, 26), DuplicateAction.ERROR, STRICT, "new calendar", null, FutureVersionsAction.KEEP, null, null)
    byte[] bytes = [0, 1, 2, 3]
    repository.findActiveByCalendarId(HolidayCalendarIds.JPTO, _ as BitemporalDate) >> Optional.empty()
    def standardHolidays = HolidayCalendarIds.JPTO.resolve(ReferenceData.standard()).holidays(LocalDate.ofYearDay(HolidayCalendarController.MIN_YEAR, 1), LocalDate.ofYearDay(HolidayCalendarController.MAX_YEAR + 1, 1))
    loader.parse(bytes, STRICT) >> Either.right(parsedFile(standardHolidays))

    when:
    def either = importService.uploadCustomHolidayCalendar("JPTO", importOptions, bytes)

    then:
    either.isLeft()
    with(either.left().get()) {
      assert size() == 1
      assert it[0].reason == Error.DUPLICATE_ENTRY
      assert it[0].description == "JPTO already exists"
    }

    and:
    1 * auditEntryService.newEntryWithLogs(_ as AuditEntry, _ as List<LogItem>) >> { entry, logs -> Either.right(entry) }
    0 * repository.createCalendar(HolidayCalendarIds.JPTO, _)
    0 * repository.updateCalendar(_, _)
  }

  def "should update existing calendar"() {
    given:
    def importOptions = new ImportOptions(LocalDate.of(2023, Month.SEPTEMBER, 26), DuplicateAction.REPLACE, STRICT, "new calendar", null, FutureVersionsAction.KEEP, null, null)
    byte[] bytes = [0, 1, 2, 3]
    def existing = new CustomHolidayCalendar().tap { entityId = "JPTO" }
    repository.findActiveByCalendarId(HolidayCalendarIds.JPTO, _ as BitemporalDate) >> Optional.of(existing)
    loader.parse(bytes, STRICT) >> Either.right(parsedFile())

    when:
    def either = importService.uploadCustomHolidayCalendar("JPTO", importOptions, bytes)

    then:
    either.isRight()
    with(either.getOrNull()) {
      assert size() == 1
      assert it[0].id == "JPTO"
    }

    and:
    1 * auditEntryService.newEntryWithLogs(_ as AuditEntry, _ as List<LogItem>) >> { entry, logs -> Either.right(entry) }
    0 * repository.createCalendar(HolidayCalendarIds.JPTO, _ as CustomHolidayCalendarForm)
    1 * repository.updateCalendar(_, _) >> { e, form ->
      assert e === existing
      assert form.holidays == [HOLIDAY] as Set<LocalDate>
      assert form.weekendDays == [DayOfWeek.SATURDAY, DayOfWeek.SUNDAY] as Set<DayOfWeek>
      assert form.workingDays == [WORKING_DAY_1, WORKING_DAY_2, WORKING_DAY_3] as Set<LocalDate>
      assert form.versionForm.validFrom == LocalDate.EPOCH
      assert form.versionForm.futureVersionsAction == importOptions.futureVersionsAction
      assert form.versionForm.comment == importOptions.comment
      assert form.versionForm.stateDate == importOptions.stateDate
      return EntityId.entityId("JPTO")
    }
  }

  private CsvParserResult<HolidayCalendarImportCsvRow> parsedFile() {
    def rows = [
      new HolidayCalendarImportCsvRow().tap {
        holiday = HOLIDAY
        workingDay = WORKING_DAY_1
        weekendDay = DayOfWeek.SATURDAY
      },
      new HolidayCalendarImportCsvRow().tap {
        workingDay = WORKING_DAY_2
        weekendDay = DayOfWeek.SUNDAY
      },
      new HolidayCalendarImportCsvRow().tap {
        workingDay = WORKING_DAY_3
      }
    ]
    return new CsvParserResult<HolidayCalendarImportCsvRow>(rows, [])
  }

  private CsvParserResult<HolidayCalendarImportCsvRow> parsedFile(Stream<LocalDate> holidays) {
    def rows = holidays.map { holiday ->
      new HolidayCalendarImportCsvRow().tap {
        it.holiday = holiday
      }
    }.toList()
    return new CsvParserResult<HolidayCalendarImportCsvRow>(rows, [])
  }
}
