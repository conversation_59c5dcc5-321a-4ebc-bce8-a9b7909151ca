package com.solum.xplain.core.portfolio.trade.type

import static com.solum.xplain.core.authentication.Authorities.MODIFY_TRADE
import static com.solum.xplain.core.authentication.Authorities.VIEW_TRADE
import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.fraTradeForm
import static com.solum.xplain.core.portfolio.TradeFormsBuilder.fraTradeFormWith
import static com.solum.xplain.core.test.ControllerTestHelper.userWithAuthority
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.portfolio.TradeTypeControllerService
import com.solum.xplain.core.portfolio.form.FraTradeForm
import com.solum.xplain.core.portfolio.repository.PortfolioItemRepository
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.core.test.TestSecurityConfig
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [FraController])
@Import([TestSecurityConfig])
class FraControllerTest extends Specification {

  static String URI = "/portfolio/{id}/trades/fra"
  static String PORTFOLIO_ID = "000000000000000000000001"

  @SpringBean
  TradeTypeControllerService service = Mock()

  @SpringBean
  PortfolioItemRepository portfolioItemRepository = Mock()

  @SpringBean
  RequestPathVariablesSupport requestPathVariablesSupport = new RequestPathVariablesSupport()

  @Autowired
  ObjectMapper objectMapper

  @Autowired
  MockMvc mockMvc

  @Unroll
  def "should create new FRA with form #form role #role and response #response"() {
    setup:
    service.insert(PORTFOLIO_ID, _ as FraTradeForm) >> right(entityId("1"))
    portfolioItemRepository.hasPortfolioItemByExternalTradeId({ it.getActualDate() == LocalDate.parse("2020-01-01") }, PORTFOLIO_ID, "DUPLICATE", null) >> true
    when:
    def results = mockMvc.perform(post(URI, PORTFOLIO_ID)
      .with(userWithAuthority(role))
      .with(csrf())
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:

    results != null
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | form                                                       | code | response
    VIEW_TRADE   | fraTradeForm()                                             | 403  | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | fraTradeForm()                                             | 200  | """{"id":"1"}"""
    MODIFY_TRADE | fraTradeFormWith("tradeDate", null)                        | 200  | """{"id":"1"}"""
    MODIFY_TRADE | fraTradeFormWith("csaDiscountingGroup", "USD")             | 200  | """{"id":"1"}"""
    MODIFY_TRADE | fraTradeFormWith("startDate", null)                        | 412  | "NotNull.fraTradeForm.startDate"
    MODIFY_TRADE | fraTradeFormWith("endDate", null)                          | 412  | "NotNull.fraTradeForm.endDate"
    MODIFY_TRADE | fraTradeFormWith("externalTradeId", null)                  | 412  | "NotEmpty.fraTradeForm.externalTradeId"
    MODIFY_TRADE | fraTradeFormWith("externalTradeId", "WITH SPACES")         | 412  | "ValidIdentifier.fraTradeForm.externalTradeId"
    MODIFY_TRADE | fraTradeFormWith("externalTradeId", "lowercase")           | 412  | "ValidIdentifier.fraTradeForm.externalTradeId"
    MODIFY_TRADE | fraTradeFormWith("externalTradeId", "DUPLICATE")           | 412  | "UniqueTradeId"
    MODIFY_TRADE | fraTradeFormWith("notionalCurrency", null)                 | 412  | "NotEmpty.fraTradeForm.notionalCurrency"
    MODIFY_TRADE | fraTradeFormWith("notionalValue", null)                    | 412  | "NotNull.fraTradeForm.notionalValue"
    MODIFY_TRADE | fraTradeFormWith("notionalValue", Double.valueOf(-10))     | 412  | "Positive.fraTradeForm.notionalValue"
    MODIFY_TRADE | fraTradeFormWith("calculationIborIndex", null)             | 412  | "NotEmpty.fraTradeForm.calculationIborIndex"
    MODIFY_TRADE | fraTradeFormWith("calculationFixedRateInitialValue", null) | 412  | "NotNull.fraTradeForm.calculationFixedRateInitialValue"
    MODIFY_TRADE | fraTradeFormWith("csaDiscountingGroup", "XAU")             | 412  | "ValidStringSet.fraTradeForm.csaDiscountingGroup"
    MODIFY_TRADE | fraTradeFormWith("calculationIborIndex", "CHF-LIBOR-6M")   | 200  | """{"id":"1"}"""
  }

  @Unroll
  def "should update FRA when role #role then responseStatus #responseStatus and response #response"() {
    setup:
    service.update(PORTFOLIO_ID, LocalDate.parse("2021-01-01"), _ as FraTradeForm, "TRADE_ID") >> right(entityId("1"))

    when:
    def results = mockMvc.perform(put("$URI/TRADE_ID/2021-01-01", PORTFOLIO_ID)
      .with(csrf())
      .with(userWithAuthority(role))
      .content(objectMapper.writeValueAsString(fraTradeForm()))
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == responseStatus
      getContentAsString().indexOf(response) >= 0
    }

    where:
    role         | responseStatus | response
    VIEW_TRADE   | 403            | "OPERATION_NOT_ALLOWED"
    MODIFY_TRADE | 200            | """{"id":"1"}"""
  }
}
