package com.solum.xplain.core.curvegroup.volatility

import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.now

import com.google.common.io.ByteStreams
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.value.VersionedList
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.curvegroup.CurveGroupRepository
import com.solum.xplain.core.curvegroup.curvegroup.value.CurveGroupView
import com.solum.xplain.core.curvegroup.volatility.classifier.CapletValuationModel
import com.solum.xplain.core.curvegroup.volatility.classifier.VolatilitySurfaceType
import com.solum.xplain.core.curvegroup.volatility.entity.VolatilitySurfaceBuilder
import com.solum.xplain.core.curvegroup.volatility.value.caplet.CapletVolatilityNodeValueView
import com.solum.xplain.core.curvegroup.volatility.value.node.VolatilityNodeValueView
import com.solum.xplain.core.curvegroup.volatility.value.skew.VolatilitySurfaceSkewView
import com.solum.xplain.core.curvegroup.volatility.value.surface.VolatilitySurfaceView
import com.solum.xplain.core.curvemarket.CurveMarketSample
import com.solum.xplain.core.curvemarket.InstrumentMarketKeyDefinitionExportService
import com.solum.xplain.core.curvemarket.MarketDataQuotesSupport
import io.atlassian.fugue.Pair
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Sort
import spock.lang.Specification

class CurveGroupVolatilityExportServiceTest extends Specification {
  def static GROUP_ID = "groupId"
  def static SURFACE_ID = "surfaceId"
  def static CONFIGURATION_ID = "cc"
  def static STATE_DATE = now()
  def static VERSION_DATE = NewVersionFormV2.ROOT_DATE

  def curveGroupRepository = Mock(CurveGroupRepository)
  def volatilityRepository = Mock(CurveGroupVolatilityRepository)
  def marketDataQuotesSupport = Mock(MarketDataQuotesSupport)
  def exportMdkDefinitions = Mock(InstrumentMarketKeyDefinitionExportService)

  def service = new CurveGroupVolatilityExportService(
  curveGroupRepository,
  volatilityRepository,
  marketDataQuotesSupport,
  exportMdkDefinitions)

  def setup() {
    def curveGroup = new CurveGroupView(id: GROUP_ID)
    curveGroupRepository.getEither(GROUP_ID) >> right(curveGroup)
    marketDataQuotesSupport.getFullQuotes(CurveMarketSample.MARKET_STATE_FORM) >> [:]
  }

  def "should get volatility surfaces csv"() {
    setup:
    def views = [
      new VolatilitySurfaceView(name: "CHF 1M Vols",
      xInterpolator: "Linear",
      xExtrapolatorLeft: "Flat",
      xExtrapolatorRight: "Flat",
      yInterpolator: "Linear",
      yExtrapolatorLeft: "Flat",
      yExtrapolatorRight: "Flat",
      skewType: VolatilitySurfaceType.ATM_ONLY,
      sabr: false,
      sabrBeta: null,
      sabrShift: null,
      capletValuationModel: CapletValuationModel.NORMAL),
      new VolatilitySurfaceView(name: "CHF 3M Vols",
      xInterpolator: "DoubleQuadratic",
      xExtrapolatorLeft: "Exception",
      xExtrapolatorRight: "Exponential",
      yInterpolator: "Linear",
      yExtrapolatorLeft: "Interpolator",
      yExtrapolatorRight: "Linear",
      skewType: VolatilitySurfaceType.STRIKE,
      sabr: true,
      sabrBeta: 0.1,
      sabrShift: 0.2,
      capletValuationModel: CapletValuationModel.NORMAL),
      new VolatilitySurfaceView(name: "CHF 6M Vols",
      xInterpolator: "LogLinear",
      xExtrapolatorLeft: "LogLinear",
      xExtrapolatorRight: "ProductLinear",
      yInterpolator: "LogNaturalSplineDiscountFactor",
      yExtrapolatorLeft: "QuadraticLeft",
      yExtrapolatorRight: "Flat",
      skewType: VolatilitySurfaceType.MONEYNESS,
      sabr: false,
      sabrBeta: null,
      sabrShift: null,
      capletValuationModel: CapletValuationModel.BLACK),
      new VolatilitySurfaceView(name: "AUD 3M Vols",
      xInterpolator: "Linear",
      xExtrapolatorLeft: "Flat",
      xExtrapolatorRight: "Flat",
      yInterpolator: "Linear",
      yExtrapolatorLeft: "Flat",
      yExtrapolatorRight: "Flat",
      skewType: VolatilitySurfaceType.ATM_ONLY,
      sabr: false,
      sabrBeta: null,
      sabrShift: null,
      capletValuationModel: CapletValuationModel.NORMAL),
    ]

    1 * volatilityRepository.getSurfaceViews(GROUP_ID, STATE_DATE, active(), emptyTableFilter()) >> views

    when:
    def result = service.getAllSurfacesCsvBytes(GROUP_ID, STATE_DATE, emptyTableFilter(), null)

    then:
    result.isRight()
    def expectedCsv = ByteStreams.toByteArray(getClass().getResourceAsStream("/curvegroup/volatility/csv/surface/VolatilitySurfaces.csv"))
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == new String(expectedCsv, "UTF-8")
  }

  def "should get all surfaces volatility matrix nodes as csv"() {
    setup:
    def stateDate = BitemporalDate.newOf(STATE_DATE)
    1 * volatilityRepository.getSurfaceViews(GROUP_ID, stateDate, active(), emptyTableFilter(), Sort.by("name")) >>
      [
        new VolatilitySurfaceView(
        curveGroupId: GROUP_ID,
        entityId: SURFACE_ID,
        validFrom: VERSION_DATE,
        name: "NAME")
      ]
    1 * volatilityRepository.getSurfaceNodesValuesViews(GROUP_ID, SURFACE_ID, VERSION_DATE, _) >>
      new VersionedList(VERSION_DATE,
      [
        new VolatilityNodeValueView(expiry: "1Y", tenor: "3Y", value: 1D),
        new VolatilityNodeValueView(expiry: "1Y", tenor: "2Y"),
        new VolatilityNodeValueView(expiry: "1M", tenor: "2Y")
      ])

    when:
    def result = service.getAllSurfacesNodesCsvBytes(
      GROUP_ID,
      stateDate,
      CurveMarketSample.MARKET_STATE_FORM,
      emptyTableFilter(),
      null
      )

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == """\
      Curve Name,Expiry,Tenor
      NAME,1M,2Y
      NAME,1Y,2Y
      NAME,1Y,3Y
    """.stripIndent(true)
  }

  def "should get surface volatility matrix nodes as csv"() {
    setup:
    1 * volatilityRepository.getActiveSurfaceView(GROUP_ID, SURFACE_ID, STATE_DATE) >>
      right(new VolatilitySurfaceView(
      curveGroupId: GROUP_ID,
      entityId: SURFACE_ID,
      validFrom: VERSION_DATE,
      name: "NAME"))
    1 * volatilityRepository.getSurfaceNodesValuesViews(GROUP_ID, SURFACE_ID, VERSION_DATE, _) >>
      new VersionedList(VERSION_DATE,
      [
        new VolatilityNodeValueView(expiry: "1Y", tenor: "2Y"),
        new VolatilityNodeValueView(expiry: "1Y", tenor: "3Y", value: 1D)
      ])

    when:
    def result = service.getSurfaceNodesCsvBytes(
      GROUP_ID,
      SURFACE_ID,
      STATE_DATE,
      CurveMarketSample.MARKET_STATE_FORM,
      null
      )

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == "Curve Name,Expiry,Tenor\nNAME,1Y,2Y\nNAME,1Y,3Y\n"
  }

  def "should get all surfaces caplet volatility matrix nodes as csv"() {
    setup:
    1 * volatilityRepository.getSurfaceViews(GROUP_ID, STATE_DATE, active(), emptyTableFilter()) >>
      [
        new VolatilitySurfaceView(
        curveGroupId: GROUP_ID,
        entityId: SURFACE_ID,
        validFrom: VERSION_DATE,
        name: "NAME")
      ]
    1 * volatilityRepository.getSurfaceCapletNodesValuesViews(GROUP_ID, SURFACE_ID, VERSION_DATE, _) >>
      new VersionedList(VERSION_DATE,
      [
        new CapletVolatilityNodeValueView(tenor: "1Y", strike: BigDecimal.ONE),
        new CapletVolatilityNodeValueView(tenor: "2Y", strike: BigDecimal.TEN, value: 1D)
      ])

    when:
    def result = service.getAllSurfacesCapletNodesCsvBytes(
      GROUP_ID,
      STATE_DATE,
      CurveMarketSample.MARKET_STATE_FORM,
      emptyTableFilter(),
      null
      )

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") ==
      "Curve Name,Maturity,Strike\nNAME,1Y,1\nNAME,2Y,10\n"
  }

  def "should get surface caplet volatility matrix nodes as csv "() {
    setup:
    1 * volatilityRepository.getActiveSurfaceView(GROUP_ID, SURFACE_ID, STATE_DATE) >>
      right(new VolatilitySurfaceView(
      curveGroupId: GROUP_ID,
      entityId: SURFACE_ID,
      validFrom: VERSION_DATE,
      name: "NAME"))

    1 * volatilityRepository.getSurfaceCapletNodesValuesViews(GROUP_ID, SURFACE_ID, VERSION_DATE, _) >>
      new VersionedList(VERSION_DATE,
      [
        new CapletVolatilityNodeValueView(tenor: "1Y", strike: BigDecimal.ONE),
        new CapletVolatilityNodeValueView(tenor: "2Y", strike: BigDecimal.TEN, value: 1D)
      ])

    when:
    def result = service.getSurfaceCapletNodesCsvBytes(
      GROUP_ID,
      SURFACE_ID,
      STATE_DATE,
      CurveMarketSample.MARKET_STATE_FORM,
      null
      )

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") ==
      "Curve Name,Maturity,Strike\nNAME,1Y,1\nNAME,2Y,10\n"
  }

  def "should get all surfaces skews as csv"() {
    setup:
    1 * volatilityRepository.getSurfaceViews(GROUP_ID, STATE_DATE, active(), emptyTableFilter()) >>
      [
        new VolatilitySurfaceView(
        curveGroupId: GROUP_ID,
        entityId: SURFACE_ID,
        validFrom: VERSION_DATE,
        name: "NAME")
      ]
    1 * volatilityRepository.getSurfaceSkewsViews(GROUP_ID, SURFACE_ID, VERSION_DATE) >>
      [
        new VolatilitySurfaceSkewView(surfaceSkewId: "1", skewValue: BigDecimal.ONE),
        new VolatilitySurfaceSkewView(surfaceSkewId: "2", skewValue: BigDecimal.TEN)
      ]

    when:
    def result = service.getAllSurfacesSkewsCsvBytes(GROUP_ID, STATE_DATE, null)

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == """\
                Name,Value
                NAME,1
                NAME,10
                """.stripIndent()
  }

  def "should get surface skews as csv "() {
    setup:
    1 * volatilityRepository.getActiveSurfaceView(GROUP_ID, SURFACE_ID, VERSION_DATE) >>
      right(new VolatilitySurfaceView(
      curveGroupId: GROUP_ID,
      entityId: SURFACE_ID,
      validFrom: VERSION_DATE,
      name: "NAME"))

    1 * volatilityRepository.getSurfaceSkewsViews(GROUP_ID, SURFACE_ID, VERSION_DATE) >>
      [
        new VolatilitySurfaceSkewView(surfaceSkewId: "1", skewValue: BigDecimal.ONE),
        new VolatilitySurfaceSkewView(surfaceSkewId: "2", skewValue: BigDecimal.TEN)
      ]

    when:
    def result = service.getSurfaceSkewsCsvBytes(
      GROUP_ID,
      SURFACE_ID,
      VERSION_DATE,
      STATE_DATE,
      null
      )

    then:
    result.isRight()
    new String(result.getOrNull().bytes.byteArray, "UTF-8") == """\
                Name,Value
                NAME,1
                NAME,10
                """.stripIndent()
  }

  def "should export node MDK definitions for surfaces"() {
    setup:
    def surface = VolatilitySurfaceBuilder.surface()
    1 * volatilityRepository.getActiveSurfaces(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [surface]

    1 * exportMdkDefinitions.instrumentMdkDefinitions(
      "SwaptionATMVols",
      [surface],
      _,
      _,
      STATE_DATE,
      CONFIGURATION_ID
      ) >> right(FileResponseEntity.zipFile(new ByteArrayResource("a".bytes), "name"))

    when:
    def result = service.getSurfacesNodesMdkDefinitions(GROUP_ID, STATE_DATE, [], CONFIGURATION_ID)

    then:
    result.isRight()
  }

  def "should export caplet MDK definitions for surfaces"() {
    setup:
    def surface = VolatilitySurfaceBuilder.surface()
    1 * volatilityRepository.getActiveSurfaces(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [surface]

    1 * exportMdkDefinitions.instrumentMdkDefinitions(
      "CapFloorStrikeVols",
      [surface],
      _,
      _,
      STATE_DATE,
      CONFIGURATION_ID
      ) >> right(FileResponseEntity.zipFile(new ByteArrayResource("a".bytes), "name"))

    when:
    def result = service.getSurfacesCapletMdkDefinitions(GROUP_ID, STATE_DATE, [], CONFIGURATION_ID)

    then:
    result.isRight()
  }

  def "should export skews MDK definitions for surfaces"() {
    setup:
    def surface = VolatilitySurfaceBuilder.surface()
    1 * volatilityRepository.getActiveSurfaces(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [surface]

    1 * exportMdkDefinitions.instrumentMdkDefinitions(
      "SwaptionSkews",
      [surface],
      {},
      {},
      STATE_DATE,
      CONFIGURATION_ID
      ) >> right(FileResponseEntity.zipFile(new ByteArrayResource("a".bytes), "name"))

    when:
    def result = service.getSurfacesSkewsMdkDefinitions(GROUP_ID, STATE_DATE, [], CONFIGURATION_ID)

    then:
    result.isRight()
  }

  def "should export all MDK definitions for surfaces"() {
    setup:
    def surface = VolatilitySurfaceBuilder.surface()
    1 * volatilityRepository.getActiveSurfaces(GROUP_ID, { it.getActualDate() == STATE_DATE }) >> [surface]

    1 * exportMdkDefinitions.instrumentMdkDefinitionFiles(
      [surface],
      {},
      STATE_DATE,
      "EUR 3M Vols_SwaptionATMVols",
      CONFIGURATION_ID
      ) >> List.of(Pair.pair("name", new ByteArrayResource("a".bytes)))

    1 * exportMdkDefinitions.instrumentMdkDefinitionFiles(
      [surface],
      {},
      STATE_DATE,
      "EUR 3M Vols_SwaptionSkews",
      CONFIGURATION_ID
      ) >> List.of(Pair.pair("name2", new ByteArrayResource("a".bytes)))

    1 * exportMdkDefinitions.instrumentMdkDefinitionFiles(
      [surface],
      {},
      STATE_DATE,
      "EUR 3M Vols_CapFloorStrikeVols",
      CONFIGURATION_ID
      ) >> List.of(Pair.pair("name3", new ByteArrayResource("a".bytes)))

    when:
    def result = service.getSurfacesMdkDefinitions(GROUP_ID, STATE_DATE, [], CONFIGURATION_ID)

    then:
    result.isRight()
  }
}
