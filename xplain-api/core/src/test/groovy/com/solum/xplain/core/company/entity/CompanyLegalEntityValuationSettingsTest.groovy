package com.solum.xplain.core.company.entity


import static com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceRequirements.bidRequirements

import spock.lang.Specification

class CompanyLegalEntityValuationSettingsTest extends Specification {

  def "should verify #field equality checking #closure"() {
    setup:
    var settings = new CompanyLegalEntityValuationSettings()
    settings.tap(closure)

    expect:
    !settings.valueEquals(new CompanyLegalEntityValuationSettings())

    where:
    field                                      | closure
    ValuationSettings.Fields.marketDataGroup   | { it -> it.marketDataGroup = new ValuationSettingsMarketDataGroup() }
    ValuationSettings.Fields.configurationType | { it -> it.configurationType = "CT" }
    ValuationSettings.Fields.discountingType   | { it -> it.discountingType = "DSC" }
    ValuationSettings.Fields.triangulationCcy  | { it -> it.triangulationCcy = "TRY" }
    ValuationSettings.Fields.reportingCurrency | { it -> it.reportingCurrency = "CCY" }
    ValuationSettings.Fields.useCsaDiscounting | { it -> it.useCsaDiscounting = true }
    ValuationSettings.Fields.priceRequirements | { it -> it.priceRequirements = bidRequirements() }
  }
}
