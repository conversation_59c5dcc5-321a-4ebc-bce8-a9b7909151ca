package com.solum.xplain.core.market

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.ASK_ONLY
import static com.solum.xplain.core.market.value.MdkProviderBidAskType.BID_ONLY
import static com.solum.xplain.core.providers.enums.DataProviderType.MARKET
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right
import static java.time.LocalDate.parse
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.classifiers.ClassifiersAggregator
import com.solum.xplain.core.classifiers.CoreClassifiersProvider
import com.solum.xplain.core.common.RequestPathVariablesSupport
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.validation.UniqueEntitySupport
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.market.repository.MarketDataKeyRepository
import com.solum.xplain.core.market.service.MarketDataKeyProviderUploadService
import com.solum.xplain.core.market.service.MarketDataKeyUploadService
import com.solum.xplain.core.market.value.MarketDataKeyForm
import com.solum.xplain.core.market.value.MarketDataKeySearchForm
import com.solum.xplain.core.market.value.MarketDataKeyUpdateForm
import com.solum.xplain.core.market.value.MarketDataKeyView
import com.solum.xplain.core.market.value.MarketDataKeyView.Fields
import com.solum.xplain.core.market.value.MarketDataProviderTickerForm
import com.solum.xplain.core.providers.DataProviderRepository
import com.solum.xplain.core.test.MockMvcConfiguration
import com.solum.xplain.shared.utils.filter.TableFilter
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Sort
import org.springframework.data.domain.Sort.Order
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.util.LinkedMultiValueMap
import org.springframework.util.MultiValueMap
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [MarketDataKeyController])
class MarketDataKeyControllerTest extends Specification {
  @SpringBean
  MarketDataKeyControllerService service = Mock()

  @SpringBean
  DataProviderRepository repository = Mock()

  @SpringBean
  MarketDataKeyRepository keyRepository = Mock()

  @SpringBean
  MarketDataKeyUploadService keyUploadService = Mock()

  @SpringBean
  MarketDataKeyProviderUploadService providerUploadService = Mock()

  @SpringBean
  RequestPathVariablesSupport support = new RequestPathVariablesSupport()

  @SpringBean
  ClassifiersAggregator classifiersAggregator = new ClassifiersAggregator([new CoreClassifiersProvider()])

  @SpringBean
  UniqueEntitySupport uniqueEntitySupport = Mock()

  @Autowired
  private MockMvc mockMvc

  @Autowired
  private ObjectMapper objectMapper

  def "should get market data key scrollable with default entities sorting"() {
    setup:
    def defaultSort = Sort.by(
    Fields.assetGroup,
    Fields.instrumentType,
    Fields.key)
    def stateDate = parse("2019-05-05")

    1 * service.marketDataKeyScrollable({
      verifyAll(it, BitemporalDate) {
        actualDate == stateDate
      }
    }, {
      verifyAll(it, ScrollRequest) {
        sort == defaultSort
      }
    }, _, _, _) >> ScrollableEntry.empty()

    when:
    def results = mockMvc.perform(get("/market-data-keys")
    .with(csrf())
    .param("stateDate", stateDate.toString())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("content") >= 0
    }
  }


  def "when sort is present then default sort is overridden"() {
    setup:
    def customSort = Sort.by(Order.desc("name"))
    def stateDate = parse("2019-05-05")

    1 * service.marketDataKeyScrollable({
      verifyAll(it, BitemporalDate) {
        actualDate == stateDate
      }
    }, {
      verifyAll(it, ScrollRequest) {
        sort == customSort
      }
    }, _, _, _) >> ScrollableEntry.empty()

    when:
    def results = mockMvc.perform(get("/market-data-keys")
    .with(csrf())
    .param("stateDate", stateDate.toString())
    .param("sort", "name,desc")
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("content") >= 0
    }
  }

  @Unroll
  def "should get market data key view with #responseBody"() {
    setup:
    service.get("id", parse("2019-05-05")) >> found

    def results = mockMvc.perform(get("/market-data-keys/id")
    .with(csrf())
    .param("stateDate", "2019-05-05")
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    found                                    | code | responseBody
    right(new MarketDataKeyView(key: "key")) | 200  | "key"
    left(Error.OBJECT_NOT_FOUND)             | 422  | "OBJECT_NOT_FOUND"
  }

  @Unroll
  def "should create market data key with response #responseBody"() {
    setup:
    repository.existsByType("BBG", MARKET) >> true
    repository.existsByType("TTD", MARKET) >> false
    service.insert(_ as MarketDataKeyForm) >> entityId("id")
    keyRepository.entityByKey({
      it.getActualDate() == parse("2020-01-01")
    }, "K1", active()) >> Optional.of(new MarketDataKey())
    keyRepository.entityByKey({
      it.getActualDate() == parse("2020-01-01")
    }, _ as String, active()) >> Optional.empty()

    def results = mockMvc.perform(post("/market-data-keys")
    .with(csrf())
    .content(objectMapper.writeValueAsString(form))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                                                                                  | code | responseBody
    creditKeyForm({
      c -> c
    })                                                                             | 200  | "id"
    fxForm({
      c -> c
    })                                                                                    | 200  | "id"
    ratesKeyForm({
      c -> c
    })                                                                              | 200  | "id"
    creditKeyForm({
      c -> c.replace("key", "UPPER-CASE")
    })                                                | 200  | "id"
    creditKeyForm({
      c -> c.replace("key", "lower-case")
    })                                                | 412  | "The value must be upper-case."
    creditKeyForm({
      c -> c.replace("key", "K1")
    })                                                        | 412  | "Market Data Key must be unique"
    creditKeyForm({
      c -> c.replace("key", "")
    })                                                          | 412  | "NotEmpty"
    creditKeyForm({
      c -> c.remove("key")
    })                                                               | 412  | "NotEmpty"
    creditKeyForm({
      c -> c.replace("name", "")
    })                                                         | 412  | "NotEmpty"
    creditKeyForm({
      c -> c.remove("instrumentType")
    })                                                    | 412  | "NotNull.marketDataKeyForm.instrumentType"
    fxForm({
      c -> c.replace("key", "K2")
    })                                                               | 412  | "FX Rate key is not valid"
    creditKeyForm({
      c -> c.replace("assetGroup", "UNKNOWN")
    })                                            | 412  | "Invalid Classifier value"
    creditKeyForm({
      c -> {
        c.replace("instrumentType", "UNKNOWN"); c.replace("assetGroup", "CUSTOM")
      }
    }) | 412  | "Invalid Classifier value"
    creditKeyForm({
      c -> c.replace("instrumentType", "FX_RATE")
    })                                        | 412  | "Invalid asset class instrument"
    ratesKeyForm({
      c -> c.replace("instrumentType", "FX_RATE")
    })                                         | 412  | "Invalid asset class instrument"
    fxForm({
      c -> c.replace("instrumentType", "CREDIT_INDEX")
    })                                          | 412  | "Invalid asset class instrument"
    creditKeyForm({
      c ->
      c.replace("providerTickers",
      [providerTicker({
          a ->
          a.replace("code", "TTD")
        })
      ])
    })                                                                                                    | 412  | "ValidDataProviderCode"
    creditKeyForm({
      c ->
      c.replace("providerTickers",
      [providerTicker({
          a ->
          a.replace("ticker", "TEST TEST")
        })
      ])
    })                                                                                                    | 200  | "id"
    creditKeyForm({
      c ->
      c.replace("providerTickers",
      [providerTicker({
          a ->
          a.remove("ticker")
        })
      ])
    })                                                                                                    | 412  | "NotEmpty"
    creditKeyForm({
      c ->
      c.replace("providerTickers",
      [providerTicker({
          a ->
          a.remove("bidAskType")
        })
      ])
    })                                                                                                    | 200  | "id"
    creditKeyForm({
      c ->
      c.replace("providerTickers",
      [providerTicker({
          a ->
          a.remove("factor")
        })
      ])
    })                                                                                                    | 412  | "NotNull.marketDataKeyForm.providerTickers"
    creditKeyForm({
      c ->
      c.replace("providerTickers",
      [providerTicker({
          a ->
          a.replace("factor", -1)
        })
      ])
    })                                                                                                    | 412  | "Positive.marketDataKeyForm.providerTickers"
    creditKeyForm({
      c ->
      c.replace("providerTickers",
      [providerTicker({
          a ->
          a.replace("bidAskType", BID_ONLY)
        }),
        providerTicker({
          a ->
          a.replace("bidAskType", ASK_ONLY)
        })
      ])
    })                                                                                                    | 412  | "UniqueMarketDataKeyProvider"
  }

  static creditKeyForm(Closure c) {
    [
      "key"            : "K",
      "name"           : "n",
      "assetGroup"     : "CREDIT",
      "instrumentType" : "CREDIT_INDEX",
      "providerTickers": [providerTicker({
          a -> a
        })],
      "versionForm"    : [
        "comment"             : "comment",
        "validFrom"           : "2020-01-01",
        "stateDate"           : "2020-01-01",
        "futureVersionsAction": "KEEP"
      ]
    ].with(true, c)
  }

  static providerTicker(Closure c) {
    [
      "code"      : "BBG",
      "ticker"    : "T",
      "bidAskType": "BID_ONLY",
      "factor"    : 1
    ].with(true, c)
  }

  static ratesKeyForm(Closure c) {
    creditKeyForm({
      f ->
      f.replace("assetGroup", "RATES")
      f.replace("instrumentType", "IBOR_FUTURE")
    }).with(true, c)
  }

  static fxForm(Closure c) {
    creditKeyForm({
      f ->
      f.replace("key", "EUR/GBP")
      f.replace("assetGroup", "FX")
      f.replace("instrumentType", "FX_RATE")
    }).with(true, c)
  }

  @Unroll
  def "should update market data key with result #updated and response #responseBody"() {
    setup:
    repository.existsByType("BBG", MARKET) >> true
    repository.existsByType("TTD", MARKET) >> false
    service.update("id", parse("2019-05-05"), _ as MarketDataKeyUpdateForm) >> updated

    def results = mockMvc.perform(put("/market-data-keys/id/2019-05-05")
    .with(csrf())
    .content(objectMapper.writeValueAsString(
    new MarketDataKeyUpdateForm(
    name: "n",
    assetGroup: "CREDIT",
    instrumentType: "CREDIT_INDEX",
    providerTickers: [new MarketDataProviderTickerForm(
      code: "BBG",
      ticker: "T",
      bidAskType: "BID_ONLY",
      factor: 1.0)],
    versionForm: NewVersionFormV2.newDefault())))
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    updated                      | code | responseBody
    right(entityId("id"))        | 200  | "id"
    left(Error.OBJECT_NOT_FOUND) | 422  | "OBJECT_NOT_FOUND"
  }

  @Unroll
  def "should delete market data key with #responseBody"() {
    setup:
    service.delete("id", parse("2020-01-01")) >> deleted

    def results = mockMvc.perform(put("/market-data-keys/id/2020-01-01/delete")
    .with(csrf())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    deleted                      | code | responseBody
    right(entityId("id"))        | 200  | "id"
    left(Error.OBJECT_NOT_FOUND) | 422  | "OBJECT_NOT_FOUND"
  }

  @Unroll
  def "should get market data key versions with response #responseBody"() {
    setup:
    service.versionsList("id") >> []

    def results = mockMvc.perform(get("/market-data-keys/id/versions")
    .with(csrf())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("[]") >= 0
    }
  }

  @Unroll
  def "should get market data keys future versions with response #responseBody"() {
    setup:
    service.futureVersions(_ as MarketDataKeySearchForm) >> new DateList([])

    def results = mockMvc.perform(get("/market-data-keys/future-versions/search")
    .params(form)
    .with(csrf())
    .contentType(MediaType.APPLICATION_JSON))
    .andReturn()

    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }

    where:
    form                                   | code | responseBody
    searchForm()                           | 200  | "\"dates\":[]"
    searchForm(m -> m.remove("key"))       | 200  | "\"dates\":[]"
    searchForm(m -> m.remove("stateDate")) | 412  | "NotNull.marketDataKeySearchForm.stateDate"
  }

  MultiValueMap<String, String> searchForm(Closure c = {
    f -> f
  }) {
    MultiValueMap<String, String> map = new LinkedMultiValueMap()
    map.add("key", "key")
    map.add("stateDate", "2022-01-01")
    return map.tap(c)
  }

  def "should export market data key providers with default sort"() {
    setup:
    def defaultSort = Sort.by("key", "providerTickers.code", "providerTickers.ticker")
    def stateDate = parse("2022-12-28")

    when:
    def result = mockMvc.perform(get("/market-data-keys/providers/csv")
    .param("stateDate", stateDate.toString())
    .with(csrf()))

    then:
    1 * service.getMarketDataKeyTickerCsvBytes(
    TableFilter.emptyTableFilter(),
    defaultSort, {
      verifyAll(it, BitemporalDate) {
        actualDate == stateDate
      }
    },
    null) >> FileResponseEntity.csvFile(new ByteArrayResource("test".bytes), "name")

    result.andExpect(status().is2xxSuccessful())
  }
}
