package com.solum.xplain.calculation.pnlexplain

import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.DISCOUNT_EUR
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.LOCAL_CURRENCY
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS
import static com.solum.xplain.core.users.UserBuilder.user
import static io.atlassian.fugue.Either.left
import static io.atlassian.fugue.Either.right

import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.calculation.CalculationResult
import com.solum.xplain.calculation.CalculationService
import com.solum.xplain.calculation.events.CalculationDeletedEvent
import com.solum.xplain.calculation.form.CalculationConfigForm
import com.solum.xplain.calculation.pnlexplain.entity.PnlExplainCalculation
import com.solum.xplain.calculation.pnlexplain.entity.PnlExplainCalculationResult
import com.solum.xplain.calculation.pnlexplain.events.PnlExplainCalculationRequestedEvent
import com.solum.xplain.calculation.pnlexplain.repository.PnlExplainCalculationRepository
import com.solum.xplain.calculation.pnlexplain.repository.PnlExplainCalculationResultRepository
import com.solum.xplain.calculation.pnlexplain.value.PnlExplainCalculationForm
import com.solum.xplain.calculation.pnlexplain.value.result.PortfolioItemCalculatedPnlExplainResult
import com.solum.xplain.calculation.repository.CalculationResultRepository
import com.solum.xplain.calculation.value.CalculationDiscounting
import com.solum.xplain.calculation.value.CalculationOptions
import com.solum.xplain.core.audit.AuditEntryService
import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.common.EntityId
import com.solum.xplain.core.common.value.CalculationDiscountingForm
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.curvemarket.InstrumentOverrideCurveDateDetails
import com.solum.xplain.core.curvemarket.pricetype.InstrumentPriceType
import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.portfolio.repository.PortfolioRepository
import com.solum.xplain.core.portfolio.value.PortfolioView
import java.time.LocalDate
import java.util.function.UnaryOperator
import org.bson.types.ObjectId
import org.springframework.context.ApplicationEventPublisher
import org.springframework.security.authentication.TestingAuthenticationToken
import spock.lang.Specification

class PnlExplainControllerServiceTest extends Specification {

  static def STATE_DATE_LOCAL = LocalDate.of(2021, 01, 02)
  static def STATE_DATE = BitemporalDate.newOf(STATE_DATE_LOCAL)
  static def CURVE_DATE = LocalDate.of(2021, 01, 01)
  static def VALUATION_DATE = LocalDate.of(2021, 01, 01)
  static def CURVE_DATE_2 = LocalDate.of(2021, 01, 02)
  static def VALUATION_DATE_2 = LocalDate.of(2021, 01, 02)
  static EntityId PNL_EXPLAIN_CALCULATION_ID = EntityId.entityId(ObjectId.get())
  static def PORTFOLIO_ID = "portfolio1"

  PnlExplainCalculationRepository pnlExplainCalculationRepository = Mock()
  PnlExplainCalculationResultRepository pnlExplainCalculationResultRepository = Mock()
  CalculationResultRepository calculationResultRepository = Mock()
  PortfolioRepository portfolioRepository = Mock()
  AuditEntryService auditEntryService = Mock()
  AuthenticationContext authenticationContext = Mock()
  CalculationService calculationService = Mock()
  ApplicationEventPublisher eventPublisher = Mock()
  PnlExplainCalculationService pnlExplainCalculationResultService = Mock()

  PnlExplainControllerService service = new PnlExplainControllerService(
  pnlExplainCalculationRepository,
  pnlExplainCalculationResultRepository,
  calculationResultRepository,
  portfolioRepository,
  auditEntryService,
  authenticationContext,
  calculationService,
  eventPublisher,
  pnlExplainCalculationResultService
  )

  def "should get all pnl explain calculations"() {
    when:
    service.getPnlExplainCalculations()

    then:
    1 * pnlExplainCalculationRepository.findAll()
  }


  def "should perform pnl explain calculation"() {
    setup:
    def objectId = ObjectId.get()
    def portfolioExternalId = "extPortfolioId"
    CalculationOptions options = Mock(CalculationOptions)
    options.getStateDate() >> STATE_DATE
    options.getReportingCcy() >> Currency.EUR
    options.getDiscounting() >> new CalculationDiscounting(LOCAL_CURRENCY, OIS, USD, false)
    def portfolioView = new PortfolioView(id: objectId.toHexString(), externalPortfolioId: portfolioExternalId, allowAllTeams: true)
    1 * authenticationContext.userEither(_) >> right(user("id"))
    1 * portfolioRepository.getEither(objectId.toHexString()) >> right(portfolioView)

    1 * eventPublisher.publishEvent(it -> {
      def event = (PnlExplainCalculationRequestedEvent) it
      assert event.getPnlExplainCalculation().getId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert event.getPnlExplainCalculation().getPortfolioExternalId() == portfolioExternalId
      assert event.getPnlExplainCalculation().getFirstValuationDate() == VALUATION_DATE
      assert event.getPnlExplainCalculation().getSecondValuationDate() == VALUATION_DATE_2
      assert event.getPnlExplainCalculation().getReportingCurrency() == "EUR"
    })

    1 * calculationService.calculate(portfolioView, VALUATION_DATE, CURVE_DATE, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-01 - 2021-01-01"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      return right(EntityId.entityId("id"))
    }

    1 * calculationService.calculate(portfolioView, VALUATION_DATE, CURVE_DATE_2, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-01 - 2021-01-02"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      right(EntityId.entityId("id"))
    }

    1 * calculationService.calculate(portfolioView, VALUATION_DATE_2, CURVE_DATE, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-02 - 2021-01-01"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE_2

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      right(EntityId.entityId("id"))
    }

    1 * calculationService.calculate(portfolioView, VALUATION_DATE_2, CURVE_DATE_2, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-02 - 2021-01-02"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE_2

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      right(EntityId.entityId("id"))
    }

    1 * calculationService.calculate(portfolioView, VALUATION_DATE, _ as InstrumentOverrideCurveDateDetails, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def extractor = (InstrumentOverrideCurveDateDetails) args[2]
      extractor.reportingCurveDate == CURVE_DATE_2
      extractor.nonOverrideCurveDate == CURVE_DATE
      extractor.overrideCurveDate.instruments() == [CoreInstrumentType.FX_VOL, CoreInstrumentType.FX_VOL_SKEW]
      extractor.overrideCurveDate.date() == CURVE_DATE_2

      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-01 - 2021-01-02 (Vega)"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      right(EntityId.entityId("id"))
    }

    def auth = new TestingAuthenticationToken(user(), null)

    when:
    def result = service.calculateResults(auth, objectId.toHexString(), pnlForm(), PNL_EXPLAIN_CALCULATION_ID)

    then:
    result.isRight()
  }

  def "should perform pnl explain calculation2"() {
    setup:
    def objectId = ObjectId.get()
    def form = pnlForm()
    def portfolioExternalId = "extPortfolioId"
    CalculationOptions options = Mock(CalculationOptions)
    options.getStateDate() >> STATE_DATE
    options.getReportingCcy() >> Currency.EUR
    options.getDiscounting() >> new CalculationDiscounting(LOCAL_CURRENCY, OIS, USD, false)
    def portfolioView = new PortfolioView(id: objectId.toHexString(), externalPortfolioId: portfolioExternalId, allowAllTeams: true)
    1 * authenticationContext.userEither(_) >> right(user("id"))
    1 * portfolioRepository.getEither(objectId.toHexString()) >> right(portfolioView)

    1 * eventPublisher.publishEvent(it -> {
      def event = (PnlExplainCalculationRequestedEvent) it
      assert event.getPnlExplainCalculation().getId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert event.getPnlExplainCalculation().getPortfolioExternalId() == portfolioExternalId
      assert event.getPnlExplainCalculation().getFirstValuationDate() == VALUATION_DATE
      assert event.getPnlExplainCalculation().getSecondValuationDate() == VALUATION_DATE_2
      assert event.getPnlExplainCalculation().getReportingCurrency() == "EUR"
    })

    1 * calculationService.calculate(portfolioView, VALUATION_DATE, CURVE_DATE, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-01 - 2021-01-01"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      return right(EntityId.entityId("id"))
    }

    1 * calculationService.calculate(portfolioView, VALUATION_DATE, CURVE_DATE_2, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-01 - 2021-01-02"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      right(EntityId.entityId("id"))
    }

    1 * calculationService.calculate(portfolioView, VALUATION_DATE_2, CURVE_DATE, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-02 - 2021-01-01"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE_2

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      right(EntityId.entityId("id"))
    }

    1 * calculationService.calculate(portfolioView, VALUATION_DATE_2, CURVE_DATE_2, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-02 - 2021-01-02"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE_2

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      right(EntityId.entityId("id"))
    }

    1 * calculationService.calculate(portfolioView, VALUATION_DATE, _ as InstrumentOverrideCurveDateDetails, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def extractor = (InstrumentOverrideCurveDateDetails) args[2]
      extractor.reportingCurveDate == CURVE_DATE_2
      extractor.nonOverrideCurveDate == CURVE_DATE
      extractor.overrideCurveDate.instruments() == [CoreInstrumentType.FX_VOL, CoreInstrumentType.FX_VOL_SKEW]
      extractor.overrideCurveDate.date() == CURVE_DATE_2

      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-01 - 2021-01-02 (Vega)"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      right(EntityId.entityId("id"))
    }

    def auth = new TestingAuthenticationToken(user(), null)

    when:
    def result = service.calculateResults(auth, objectId.toHexString(), pnlForm(), PNL_EXPLAIN_CALCULATION_ID)

    then:
    result.isRight()
  }

  def "should delete pnl explain calculation if a portfolio calculation fails"() {
    setup:
    def objectId = ObjectId.get()
    def form = pnlForm()
    def portfolioExternalId = "extPortfolioId"
    def calcResultId = ObjectId.get()
    CalculationOptions options = Mock(CalculationOptions)
    options.getStateDate() >> STATE_DATE
    options.getReportingCcy() >> Currency.EUR
    options.getDiscounting() >> new CalculationDiscounting(LOCAL_CURRENCY, OIS, USD, false)
    def portfolioView = new PortfolioView(id: objectId.toHexString(), externalPortfolioId: portfolioExternalId, allowAllTeams: true)
    2 * authenticationContext.userEither(_) >> right(user("id"))
    2 * portfolioRepository.getEither(objectId.toHexString()) >> right(portfolioView)

    2 * eventPublisher.publishEvent(it -> {
      if (it instanceof PnlExplainCalculationRequestedEvent) {
        def event = (PnlExplainCalculationRequestedEvent) it
        assert event.getPnlExplainCalculation().getId() == PNL_EXPLAIN_CALCULATION_ID.getId()
        assert event.getPnlExplainCalculation().getPortfolioExternalId() == portfolioExternalId
        assert event.getPnlExplainCalculation().getFirstValuationDate() == VALUATION_DATE
        assert event.getPnlExplainCalculation().getSecondValuationDate() == VALUATION_DATE_2
        assert event.getPnlExplainCalculation().getReportingCurrency() == "EUR"
      } else {
        assert it instanceof CalculationDeletedEvent
        def event = (CalculationDeletedEvent) it
        assert event.calculationId == calcResultId
      }
    })

    1 * calculationService.calculate(portfolioView, VALUATION_DATE, CURVE_DATE, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-01 - 2021-01-01"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      return right(EntityId.entityId(calcResultId))
    }

    1 * calculationService.calculate(portfolioView, VALUATION_DATE, CURVE_DATE_2, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>) >> {
      List<?> args ->
      def result = (CalculationResult) args[5].apply(new CalculationResult())
      assert result.getPnlExplainCalculationId() == PNL_EXPLAIN_CALCULATION_ID.getId()
      assert result.getName() == "Pnl Explain Calculation 2021-01-01 - 2021-01-02"
      assert ((BitemporalDate) args[3]).getActualDate() == VALUATION_DATE

      def calculationForm = (PnlExplainCalculationForm) args[4]
      calculationForm.priceRequirements().curvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().dscCurvesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().fxRatesPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsPriceType == InstrumentPriceType.MID_PRICE
      calculationForm.priceRequirements().volsSkewsPriceType == InstrumentPriceType.MID_PRICE

      return left(Error.CALCULATION_ERROR.entity("Market data not found!"))
    }

    1 * pnlExplainCalculationRepository.getById(PNL_EXPLAIN_CALCULATION_ID.id) >> PnlExplainCalculation.builder().build()
    1 * calculationResultRepository.calculationResultsForPnlExplainCalculation(PNL_EXPLAIN_CALCULATION_ID.id) >> [
      new CalculationResult(id: calcResultId)
    ]
    1 * pnlExplainCalculationRepository.deleteById(PNL_EXPLAIN_CALCULATION_ID.id)
    1 * pnlExplainCalculationResultRepository.deleteByPnlExplainCalculationId(PNL_EXPLAIN_CALCULATION_ID.id)

    0 * calculationService.calculate(portfolioView, VALUATION_DATE_2, CURVE_DATE, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>)
    0 * calculationService.calculate(portfolioView, VALUATION_DATE_2, CURVE_DATE_2, _ as BitemporalDate, _ as PnlExplainCalculationForm, _ as UnaryOperator<CalculationResult>)


    def auth = new TestingAuthenticationToken(user(), null)

    when:
    def result = service.calculateResults(auth, objectId.toHexString(), pnlForm(), PNL_EXPLAIN_CALCULATION_ID)

    then:
    result.isLeft()
    def error = result.left().get() as ErrorItem
    error.reason == Error.CALCULATION_ERROR
  }

  def "should get pnl explain calculation result"() {
    setup:
    1 * pnlExplainCalculationResultService.getResultByPnlCalculation(PNL_EXPLAIN_CALCULATION_ID.getId()) >>
    new PnlExplainCalculationResult(
    null,
    null,
    null,
    null,
    [PortfolioItemCalculatedPnlExplainResult.builder().tradeId("tradeId").build()]
    )

    when:
    def result = service.getResultForPnlCalculation(PNL_EXPLAIN_CALCULATION_ID.getId())

    then:
    result.getTradePnlExplainResults().size() == 1
    result.getTradePnlExplainResults()[0].tradeId == "tradeId"
  }

  def "should get pnl explain calculation"() {
    when:
    service.getPnlExplainCalculation(PNL_EXPLAIN_CALCULATION_ID.getId())

    then:
    1 * pnlExplainCalculationRepository.getById(PNL_EXPLAIN_CALCULATION_ID.getId())
  }

  def "should get pnl explain calculations for portfolio"() {
    when:
    service.getPnlExplainCalculationsForPortfolio(PORTFOLIO_ID)

    then:
    1 * pnlExplainCalculationRepository.findAllByPortfolioId(PORTFOLIO_ID)
  }

  def "should delete pnl explain and results calculation by id"() {
    setup:
    def id = PNL_EXPLAIN_CALCULATION_ID.getId()
    def calcResultId = ObjectId.get()
    def pnlCalc = PnlExplainCalculation.builder().id(id).build()
    1 * pnlExplainCalculationRepository.getById(id) >> pnlCalc
    1 * calculationResultRepository.calculationResultsForPnlExplainCalculation(id) >> [new CalculationResult(id: calcResultId)]

    1 * pnlExplainCalculationRepository.deleteById(id)
    1 * pnlExplainCalculationResultRepository.deleteByPnlExplainCalculationId(id)

    when:
    def result = service.deletePnlExplainCalculation(id)

    then:
    result.isRight()
    def right = result.right().get() as EntityId
    right.id == id

    and: "cascades to calculation results"
    1 * eventPublisher.publishEvent({ CalculationDeletedEvent it ->
      assert it.calculationId == calcResultId
    })
  }

  def "should return error when trying to delete pnl calculation that doesn't exist"() {
    setup:
    def id = PNL_EXPLAIN_CALCULATION_ID.getId()
    1 * pnlExplainCalculationRepository.getById(id) >> null

    0 * pnlExplainCalculationRepository.deleteById(id)
    0 * pnlExplainCalculationResultRepository.deleteByPnlExplainCalculationId(id)
    0 * calculationResultRepository.calculationResultsForPnlExplainCalculation(id)
    0 * eventPublisher.publishEvent(_)

    when:
    def result = service.deletePnlExplainCalculation(id)

    then:
    result.isLeft()
    def left = result.left().get() as ErrorItem
    left.reason == Error.OBJECT_NOT_FOUND
    left.description == "Pnl Explain Calculation not found"
  }

  def pnlForm() {
    new PnlExplainCalculationForm(
    firstValuationDate  : VALUATION_DATE,
    secondValuationDate : VALUATION_DATE_2,
    stateDate           : STATE_DATE_LOCAL,
    calculationCurrency : "EUR",
    curveDiscountingForm: new CalculationDiscountingForm(DISCOUNT_EUR.name(), OIS.name(), USD.getCode(), false),
    configurationType   : "SINGLE",
    curveConfiguration  : new CalculationConfigForm("id", null),
    marketDataGroupId   : "marketDataId",
    marketDataSource    : "RAW_PRIMARY",
    )
  }
}
