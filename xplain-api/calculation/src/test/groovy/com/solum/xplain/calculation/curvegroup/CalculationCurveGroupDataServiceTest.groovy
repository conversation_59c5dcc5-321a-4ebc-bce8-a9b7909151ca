package com.solum.xplain.calculation.curvegroup

import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.curvegroup.curve.classifier.CurveType.INDEX_BASIS
import static com.solum.xplain.core.curvegroup.curve.classifier.CurveType.XCCY
import static com.solum.xplain.core.curvemarket.datasource.MarketDataSourceType.OVERLAY
import static com.solum.xplain.core.portfolio.PortfolioItemBuilder.irsPortfolioItem
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.DISCOUNT_EUR
import static com.solum.xplain.core.portfolio.value.CalculationDiscountingType.LOCAL_CURRENCY
import static com.solum.xplain.core.portfolio.value.CalculationStrippingType.OIS

import com.opengamma.strata.basics.ReferenceData
import com.opengamma.strata.basics.currency.Currency
import com.solum.xplain.calculation.discounting.DiscountablePortfolioItem
import com.solum.xplain.calculation.discounting.DiscountablePortfolioItemCsaResolver
import com.solum.xplain.calculation.discounting.DiscountablePortfolioItemCurrencyResolver
import com.solum.xplain.calculation.discounting.PortfolioItemDiscountIndexResolver
import com.solum.xplain.calculation.discounting.ResolvedDiscountingCalibrationBundles
import com.solum.xplain.calculation.discounting.currency.TradeCurrencyResolverFactory
import com.solum.xplain.calculation.discounting.currency.TradeDiscountCurrencyResolver
import com.solum.xplain.calculation.discounting.tradeinfosubsets.IrsPortfolioItemDiscountingGroupSpecification
import com.solum.xplain.calculation.discounting.tradeinfosubsets.PortfolioItemDiscountingGroupSpecification
import com.solum.xplain.calculation.form.CalculationConfigForm
import com.solum.xplain.calculation.form.CalculationForm
import com.solum.xplain.calculation.repository.GenericPortfolioItemDiscountingCriteriaBuilder
import com.solum.xplain.calculation.trades.CalculationTrades
import com.solum.xplain.calculation.value.CalculationDiscounting
import com.solum.xplain.calibration.discounting.OisConfigurations
import com.solum.xplain.calibration.market.CalibrationMarketDataService
import com.solum.xplain.calibration.rates.group.csa.CsaDiscountingGroupsBuilder
import com.solum.xplain.calibration.rates.group.ois.combined.OisLocalCcySingleFallbackDiscountingGroupsBuilder
import com.solum.xplain.calibration.rates.group.ois.localccy.LocalCcyDiscountingGroupsBuilder
import com.solum.xplain.calibration.rates.group.ois.singleccy.SingleCcyDiscountingGroupsBuilder
import com.solum.xplain.calibration.rates.group.single.SingleDiscountingGroupsBuilder
import com.solum.xplain.calibration.settings.CalibrationSettingsService
import com.solum.xplain.core.common.AuditUserMapperImpl
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver
import com.solum.xplain.core.curveconfiguration.CurveConfigurationRepository
import com.solum.xplain.core.curveconfiguration.CurveConfigurationType
import com.solum.xplain.core.curvegroup.curve.entity.Curve
import com.solum.xplain.core.curvegroup.curvebond.entity.BondCurve
import com.solum.xplain.core.curvegroup.curvegroup.entity.CurveGroup
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapper
import com.solum.xplain.core.curvegroup.volatilityfx.CurveGroupFxVolatilityMapperImpl
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.value.CalculationDiscountingType
import com.solum.xplain.core.portfolio.value.CalculationStrippingType
import com.solum.xplain.core.settings.product.ProductSettingsResolver
import io.atlassian.fugue.Either
import java.time.LocalDate
import java.util.stream.Stream
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ContextConfiguration(classes = [CurveGroupFxVolatilityMapperImpl.class, AuditUserMapperImpl.class])
class CalculationCurveGroupDataServiceTest extends Specification {

  def static VALUATION_DATE = LocalDate.of(2021, 01, 02)
  def static STATE_DATE = BitemporalDate.newOf(LocalDate.of(2021, 01, 03))

  def static MDID = "mdId"
  def static NON_FX_CC = "cc1"
  def static NON_FX_CG = "cg1"
  def static FX_CC = "cc2"
  def static FX_CG = "cg2"

  def static PORTFOLIO_ITEM = irsPortfolioItem()

  ProductSettingsResolver PRODUCT_SETTINGS = Mock()
  TradeDiscountCurrencyResolver CURRENCY_RESOLVER = Mock()

  CalculationTrades calculationTrades = Mock()

  CurveConfigurationRepository curveConfigurationRepository = Mock()
  CalibrationMarketDataService calibrationMarketDataService = Mock()
  CalculationCurvesDataService curveGroupDataService = Mock()
  CalibrationSettingsService settingsService = Mock()
  TradeCurrencyResolverFactory currencyResolverFactory = Mock()
  @SpringBean
  ReferenceData referenceData = ReferenceData.standard()
  @Autowired
  CurveGroupFxVolatilityMapper curveGroupFxVolatilityMapper

  CalculationCurveGroupDataService service

  def setup() {
    service = new CalculationCurveGroupDataService(
      curveConfigurationRepository,
      calibrationMarketDataService,
      curveGroupDataService,
      settingsService,
      currencyResolverFactory,
      referenceData,
      curveGroupFxVolatilityMapper
      )
    1 * calculationTrades.uniqueProductTypes() >> Set.of(CoreProductType.IRS, CoreProductType.FXFWD)
    1 * calculationTrades.tradeDiscountingCriteriaStream() >> Stream.of(tradeDscCriteria())
    _ * curveGroupDataService.getCreditCurves(_, _) >> []
    _ * curveGroupDataService.getVolatilities(_, _) >> []
  }

  def "should resolve calculation data for SINGLE type"() {
    setup:
    def form = mockForm(CurveConfigurationType.SINGLE)
    mockSingleTypeCalls()
    settingsService.productSettingsResolver(STATE_DATE) >> PRODUCT_SETTINGS

    def ccyResolver = new DiscountablePortfolioItemCurrencyResolver(CURRENCY_RESOLVER)
    var localCcyBuilder = new LocalCcyDiscountingGroupsBuilder<>(ccyResolver)
    var liborBuilder = new SingleDiscountingGroupsBuilder<>(
      PortfolioItemDiscountIndexResolver.fromDiscountSettings(OisConfigurations.of(VALUATION_DATE), PRODUCT_SETTINGS),
      Set.of())
    liborBuilder.withItem(new DiscountablePortfolioItem(tradeDscSpec()))
    def expectedDiscounting = new OisLocalCcySingleFallbackDiscountingGroupsBuilder<>(
      localCcyBuilder, liborBuilder, Set.of(), ccyResolver
      )
    def expectedCalibrationBundles = new ResolvedDiscountingCalibrationBundles(
      expectedDiscounting.build { it },
      [:]
      )

    when:
    def result = service.resolveCalculationData(form, calculationTrades, VALUATION_DATE, STATE_DATE)

    then:
    result.isRight()
    result.getOrNull().builders().size() == 1
    result.getOrNull().builders()[0].curveGroup == new CurveGroup(name: "nonfxcg")
    result.getOrNull().builders()[0].curves == [curve()]
    result.getOrNull().builders()[0].discountingGroupsBuilder == expectedDiscounting
    result.getOrNull().builders()[0].calibrationBundles == expectedCalibrationBundles
  }

  def "should resolve calculation data for SINGLE type and SINGLE CCY"() {
    setup:
    def form = mockForm(CurveConfigurationType.SINGLE, DISCOUNT_EUR)
    mockSingleTypeCalls()

    var expectedDiscounting = new SingleCcyDiscountingGroupsBuilder(Currency.EUR)
    expectedDiscounting.withItem(new DiscountablePortfolioItem(tradeDscSpec()))
    def expectedCalibrationBundles = new ResolvedDiscountingCalibrationBundles(
      expectedDiscounting.build { it },
      [:]
      )

    when:
    def result = service.resolveCalculationData(form, calculationTrades, VALUATION_DATE, STATE_DATE)

    then:
    result.isRight()
    result.getOrNull().builders().size() == 1
    result.getOrNull().builders()[0].curveGroup == new CurveGroup(name: "nonfxcg")
    result.getOrNull().builders()[0].curves == [curve()]
    result.getOrNull().builders()[0].discountingGroupsBuilder == expectedDiscounting
    result.getOrNull().builders()[0].calibrationBundles == expectedCalibrationBundles
  }

  def "should resolve calculation data for FX_V_IRS type"() {
    setup:
    def form = mockForm(CurveConfigurationType.FX_V_IRS)
    mockFxNonFxTypeCalls()

    settingsService.productSettingsResolver(STATE_DATE) >> PRODUCT_SETTINGS

    when:
    def result = service.resolveCalculationData(form, calculationTrades, VALUATION_DATE, STATE_DATE)

    then:
    result.isRight()
    result.getOrNull().builders().size() == 2
    result.getOrNull().builders()[0].curveGroup == new CurveGroup(name: "nonfxcg")
    result.getOrNull().builders()[0].curves == [curve()]
    result.getOrNull().builders()[0].discountingGroupsBuilder instanceof OisLocalCcySingleFallbackDiscountingGroupsBuilder
    result.getOrNull().builders()[1].curveGroup == new CurveGroup(name: "fxcg")
    result.getOrNull().builders()[1].curves == [new Curve(name: "fxcurve", recordDate: null, curveType: XCCY)]
    result.getOrNull().builders()[1].discountingGroupsBuilder instanceof OisLocalCcySingleFallbackDiscountingGroupsBuilder
  }

  def "should resolve calculation data for CSA type"() {
    setup:
    def form = mockForm(CurveConfigurationType.SINGLE, DISCOUNT_EUR, OIS, true)
    mockSingleTypeCalls()
    def csaResolver = new DiscountablePortfolioItemCsaResolver()
    var singleCcyBuilder = new SingleCcyDiscountingGroupsBuilder(Currency.EUR)
    var expectedDiscounting = new CsaDiscountingGroupsBuilder(csaResolver, singleCcyBuilder)
    expectedDiscounting.withItem(new DiscountablePortfolioItem(tradeDscSpec()))
    def expectedCalibrationBundles = new ResolvedDiscountingCalibrationBundles(
      expectedDiscounting.build { it },
      [:]
      )

    when:
    def result = service.resolveCalculationData(form, calculationTrades, VALUATION_DATE, STATE_DATE)

    then:
    result.isRight()
    result.getOrNull().builders().size() == 1
    result.getOrNull().builders()[0].curveGroup == new CurveGroup(name: "nonfxcg")
    result.getOrNull().builders()[0].curves == [curve()]
    result.getOrNull().builders()[0].discountingGroupsBuilder == expectedDiscounting
    result.getOrNull().builders()[0].calibrationBundles == expectedCalibrationBundles
  }

  private CalculationForm mockForm(CurveConfigurationType type, CalculationDiscountingType calcType = LOCAL_CURRENCY, CalculationStrippingType strippingType = OIS, boolean useCsa = false) {
    def form = Mock(CalculationForm)
    form.marketDataSource() >> OVERLAY
    form.getMarketDataGroupId() >> MDID
    form.calculationDiscounting() >> new CalculationDiscounting(calcType, strippingType, USD, useCsa)
    form.configurationType() >> type
    form.calculationConfiguration() >> new CalculationConfigForm(NON_FX_CC, [])
    form.fxCalculationConfiguration() >> new CalculationConfigForm(FX_CC, [])
    form
  }

  private void mockSingleTypeCalls() {
    def instrResolver1 = Mock(CurveConfigurationInstrumentResolver)
    instrResolver1.getCurveGroupId() >> NON_FX_CG
    1 * curveConfigurationRepository.curveConfigInstrResolver(STATE_DATE, NON_FX_CC) >> Either.right(instrResolver1)

    0 * curveConfigurationRepository.curveConfigInstrResolver(STATE_DATE, FX_CC)

    1 * curveGroupDataService.getCurveGroup(NON_FX_CG) >> Either.right(new CurveGroup(name: "nonfxcg"))
    1 * curveGroupDataService.getCurves(NON_FX_CG, STATE_DATE) >> [curve()]
    1 * curveGroupDataService.getBondCurves(NON_FX_CG, STATE_DATE) >> [new BondCurve(name: "nonfxcurve", recordDate: null)]

    0 * curveGroupDataService.getCurveGroup(FX_CG)
    0 * curveGroupDataService.getCurves(FX_CG, STATE_DATE)

    currencyResolverFactory.getResolver(PRODUCT_SETTINGS) >> CURRENCY_RESOLVER
    CURRENCY_RESOLVER.resolveCurrency(_ as PortfolioItemDiscountingGroupSpecification) >> Currency.EUR
  }

  private void mockFxNonFxTypeCalls() {
    def instrResolver1 = Mock(CurveConfigurationInstrumentResolver)
    instrResolver1.getCurveGroupId() >> NON_FX_CG
    1 * curveConfigurationRepository.curveConfigInstrResolver(STATE_DATE, NON_FX_CC) >> Either.right(instrResolver1)

    def instrResolver2 = Mock(CurveConfigurationInstrumentResolver)
    instrResolver2.getCurveGroupId() >> FX_CG
    1 * curveConfigurationRepository.curveConfigInstrResolver(STATE_DATE, FX_CC) >> Either.right(instrResolver2)

    1 * curveGroupDataService.getCurveGroup(NON_FX_CG) >> Either.right(new CurveGroup(name: "nonfxcg"))
    1 * curveGroupDataService.getCurves(NON_FX_CG, STATE_DATE) >> [curve()]
    1 * curveGroupDataService.getBondCurves(NON_FX_CG, STATE_DATE) >> [new BondCurve(name: "nonfxcurve", recordDate: null)]

    1 * curveGroupDataService.getCurveGroup(FX_CG) >> Either.right(new CurveGroup(name: "fxcg"))
    1 * curveGroupDataService.getCurves(FX_CG, STATE_DATE) >> [new Curve(name: "fxcurve", recordDate: null, curveType: XCCY)]
    1 * curveGroupDataService.getBondCurves(FX_CG, STATE_DATE) >> [new BondCurve(name: "fxcurve", recordDate: null)]

    currencyResolverFactory.getResolver(PRODUCT_SETTINGS) >> CURRENCY_RESOLVER
    CURRENCY_RESOLVER.resolveCurrency(_ as PortfolioItemDiscountingGroupSpecification) >> Currency.EUR
  }

  private Curve curve() {
    new Curve(name: "xxx", recordDate: null, curveType: INDEX_BASIS)
  }

  static def tradeDscSpec() {
    def portfolioItem = PORTFOLIO_ITEM
    return new IrsPortfolioItemDiscountingGroupSpecification(
      portfolioItem.tradeDetails.info.tradeCurrency,
      portfolioItem.tradeDetails.payLeg.index,
      portfolioItem.tradeDetails.payLeg.type,
      portfolioItem.tradeDetails.payLeg.initialValue != null ? portfolioItem.tradeDetails.payLeg.initialValue : 0.0,
      portfolioItem.tradeDetails.payLeg.isOffshore != null ? portfolioItem.tradeDetails.payLeg.isOffshore : false,
      portfolioItem.tradeDetails.receiveLeg.index,
      portfolioItem.tradeDetails.receiveLeg.type,
      portfolioItem.tradeDetails.receiveLeg.initialValue != null ? portfolioItem.tradeDetails.receiveLeg.initialValue : 0.0,
      portfolioItem.tradeDetails.receiveLeg.isOffshore != null ? portfolioItem.tradeDetails.receiveLeg.isOffshore : false,
      portfolioItem.tradeDetails.info.csaDiscountingGroup
      )
  }

  static def tradeDscCriteria() {
    def portfolioItem = PORTFOLIO_ITEM
    return new GenericPortfolioItemDiscountingCriteriaBuilder()
      .productType(portfolioItem.productType)
      .tradeCurrency(portfolioItem.tradeDetails.info.tradeCurrency)
      .counterpartyType(portfolioItem.tradeDetails.info.counterPartyType)
      .csaDiscountingGroup(portfolioItem.tradeDetails.info.csaDiscountingGroup)
      .payLegCurrency(portfolioItem.tradeDetails.payLeg.currency)
      .payLegIndex(portfolioItem.tradeDetails.payLeg.index)
      .payLegType(portfolioItem.tradeDetails.payLeg.type)
      .payLegSpread(portfolioItem.tradeDetails.payLeg.initialValue != null ? portfolioItem.tradeDetails.payLeg.initialValue : 0.0)
      .payLegIsOffshore(portfolioItem.tradeDetails.payLeg.isOffshore != null ? portfolioItem.tradeDetails.payLeg.isOffshore : false)
      .receiveLegCurrency(portfolioItem.tradeDetails.receiveLeg.currency)
      .receiveLegIndex(portfolioItem.tradeDetails.receiveLeg.index)
      .receiveLegType(portfolioItem.tradeDetails.receiveLeg.type)
      .receiveLegSpread(portfolioItem.tradeDetails.receiveLeg.initialValue != null ? portfolioItem.tradeDetails.receiveLeg.initialValue : 0.0)
      .receiveLegIsOffshore(portfolioItem.tradeDetails.receiveLeg.isOffshore != null ? portfolioItem.tradeDetails.receiveLeg.isOffshore : false)
      .build()
  }
}
