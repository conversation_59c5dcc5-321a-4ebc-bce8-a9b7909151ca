package com.solum.xplain.calculation.exposure.calculation

import com.solum.xplain.calculation.exposure.netexposure.value.CcyExposureCalculatedCashFlow
import com.solum.xplain.calculation.exposure.schedule.CcyExposureScheduleFrequency
import com.solum.xplain.calculation.exposure.schedule.CcyExposureScheduleGenerator
import com.solum.xplain.core.ccyexposure.value.CashflowView
import com.solum.xplain.core.ccyexposure.value.CombinedCcyExposureView
import java.time.LocalDate
import spock.lang.Specification

class CcyExposureScheduleGeneratorTest extends Specification {\

  def "should generate weekly schedule"() {
    setup:
    def generator = CcyExposureScheduleGenerator.of(
      horizonStartDate,
      horizonEndDate,
      CcyExposureScheduleFrequency.WEEKLY
      )

    when:
    def dates = generator.generate(
      Collections.emptyList()
      ).scheduleDates()

    then:
    dates == expectedSchedule

    where:
    horizonStartDate          | horizonEndDate            | expectedSchedule
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 1, 8)  | [LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 8)]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 1, 15) | [LocalDate.of(2020, 1, 1), LocalDate.of(2020, 1, 8), LocalDate.of(2020, 1, 15)]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 1, 22) | [
      LocalDate.of(2020, 1, 1),
      LocalDate.of(2020, 1, 8),
      LocalDate.of(2020, 1, 15),
      LocalDate.of(2020, 1, 22)
    ]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 1, 25) | [
      LocalDate.of(2020, 1, 4),
      LocalDate.of(2020, 1, 11),
      LocalDate.of(2020, 1, 18),
      LocalDate.of(2020, 1, 25)
    ]
  }

  def "should generate monthly schedule"() {
    setup:
    def generator = CcyExposureScheduleGenerator.of(
      horizonStartDate,
      horizonEndDate,
      CcyExposureScheduleFrequency.MONTHLY
      )

    when:
    def dates = generator.generate(
      Collections.emptyList()
      ).scheduleDates()

    then:
    dates == expectedSchedule

    where:
    horizonStartDate          | horizonEndDate            | expectedSchedule
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 2, 1)  | [LocalDate.of(2020, 1, 1), LocalDate.of(2020, 2, 1)]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 3, 1)  | [LocalDate.of(2020, 1, 1), LocalDate.of(2020, 2, 1), LocalDate.of(2020, 3, 1)]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 4, 1)  | [
      LocalDate.of(2020, 1, 1),
      LocalDate.of(2020, 2, 1),
      LocalDate.of(2020, 3, 1),
      LocalDate.of(2020, 4, 1)
    ]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 4, 15) | [
      LocalDate.of(2020, 1, 15),
      LocalDate.of(2020, 2, 15),
      LocalDate.of(2020, 3, 15),
      LocalDate.of(2020, 4, 15)
    ]
  }

  def "should generate quarterly schedule"() {
    setup:
    def generator = CcyExposureScheduleGenerator.of(
      horizonStartDate,
      horizonEndDate,
      CcyExposureScheduleFrequency.QUARTERLY
      )

    when:
    def dates = generator.generate(
      Collections.emptyList()
      ).scheduleDates()

    then:
    dates == expectedSchedule

    where:
    horizonStartDate          | horizonEndDate            | expectedSchedule
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 4, 1)  | [LocalDate.of(2020, 1, 1), LocalDate.of(2020, 4, 1)]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 7, 1)  | [LocalDate.of(2020, 1, 1), LocalDate.of(2020, 4, 1), LocalDate.of(2020, 7, 1)]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 10, 1) | [
      LocalDate.of(2020, 1, 1),
      LocalDate.of(2020, 4, 1),
      LocalDate.of(2020, 7, 1),
      LocalDate.of(2020, 10, 1)
    ]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2020, 10, 15)| [
      LocalDate.of(2020, 1, 15),
      LocalDate.of(2020, 4, 15),
      LocalDate.of(2020, 7, 15),
      LocalDate.of(2020, 10, 15)
    ]
  }

  def "should generate yearly schedule"() {
    setup:
    def generator = CcyExposureScheduleGenerator.of(
      horizonStartDate,
      horizonEndDate,
      CcyExposureScheduleFrequency.YEARLY
      )

    when:
    def dates = generator.generate(
      Collections.emptyList()
      ).scheduleDates()

    then:
    dates == expectedSchedule

    where:
    horizonStartDate          | horizonEndDate            | expectedSchedule
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2021, 1, 1)  | [LocalDate.of(2020, 1, 1), LocalDate.of(2021, 1, 1)]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2022, 1, 1)  | [LocalDate.of(2020, 1, 1), LocalDate.of(2021, 1, 1), LocalDate.of(2022, 1, 1)]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2023, 1, 1)  | [
      LocalDate.of(2020, 1, 1),
      LocalDate.of(2021, 1, 1),
      LocalDate.of(2022, 1, 1),
      LocalDate.of(2023, 1, 1)
    ]
    LocalDate.of(2020, 1, 1)  | LocalDate.of(2023, 1, 15) | [
      LocalDate.of(2020, 1, 15),
      LocalDate.of(2021, 1, 15),
      LocalDate.of(2022, 1, 15),
      LocalDate.of(2023, 1, 15)
    ]
  }

  def "should generate per date schedule"() {
    setup:
    def generator = CcyExposureScheduleGenerator.of(
      LocalDate.of(2020, 1, 1) ,
      LocalDate.of(2021, 1, 1),
      CcyExposureScheduleFrequency.PER_DATE
      )

    when:
    def cashflows = exposureWithCf([
      cfv("2020-01-01", 0.0),
      cfv("2020-01-02", 0.0),
      cfv("2020-01-03", 0.0),
      cfv("2020-01-05", 0.0),
      cfv("2020-02-01", 0.0)
    ])

    def dates = generator.generate([cashflows]).scheduleDates()

    then:
    dates == [
      LocalDate.of(2020, 1, 1),
      LocalDate.of(2020, 1, 2),
      LocalDate.of(2020, 1, 3),
      LocalDate.of(2020, 1, 5),
      LocalDate.of(2020, 2, 1)
    ]
  }

  def "should remove first date from schedule if before horizon start date"() {
    setup:
    def horizonStartDate = LocalDate.of(2020, 1, 1)
    def horizonEndDate = LocalDate.of(2020, 1, 2)

    def generator = CcyExposureScheduleGenerator.of(
      horizonStartDate,
      horizonEndDate,
      CcyExposureScheduleFrequency.WEEKLY
      )

    when:
    def cashflows = exposureWithCf([
      cfv("2020-01-01", 0.0),
      cfv("2020-01-02", 0.0),
      cfv("2020-01-03", 0.0),
      cfv("2020-02-01", 0.0)
    ])

    def dates = generator.generate([cashflows]).scheduleDates()

    then:
    dates.size() == 1
    dates[0] == LocalDate.of(2020, 1, 2)
  }

  def cf(String date) {
    return new CcyExposureCalculatedCashFlow(
      null,
      LocalDate.parse(date),
      0.0,
      false,
      0.0
      )
  }

  def cfv(String date, Double amount) {
    new CashflowView(date: LocalDate.parse(date), amount: amount)
  }

  def exposureWithCf(List<CashflowView> cashflowViews) {
    def v = new CombinedCcyExposureView()
    v.setCashflows(cashflowViews)
    return v
  }
}
