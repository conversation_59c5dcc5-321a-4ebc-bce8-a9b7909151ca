package com.solum.xplain.calculation.simulation.ccyexposure.events;

import static com.solum.xplain.calculation.CalculationResult.CALCULATION_RESULT_COLLECTION;

import com.google.common.collect.ImmutableList;
import com.opengamma.strata.basics.currency.CurrencyPair;
import com.opengamma.strata.market.ShiftType;
import com.solum.xplain.calculation.CalculationResult;
import com.solum.xplain.calculation.PortfolioCalculationData;
import com.solum.xplain.calculation.events.TradesCalculationRequestedEvent;
import com.solum.xplain.calculation.integration.CalculationRequestProducer;
import com.solum.xplain.calculation.integration.CalculationTradeCountHolder;
import com.solum.xplain.calculation.integration.cache.CalibrationCacheService;
import com.solum.xplain.calculation.repository.CalculationResultRepository;
import com.solum.xplain.calculation.simulation.ccyexposure.data.CcyExposureSimulationCalculationData;
import com.solum.xplain.calculation.simulation.ccyexposure.repository.CcyExposureSimulationCalculationRepository;
import com.solum.xplain.calculation.simulation.ccyexposure.value.CcyExposureCalculationKey;
import com.solum.xplain.calculation.simulation.ccyexposure.value.ShiftDataType;
import com.solum.xplain.calculation.simulation.ccyexposure.value.ShiftForm;
import com.solum.xplain.calculation.value.TradeCalculationRequest;
import com.solum.xplain.core.audit.AuditEntryService;
import com.solum.xplain.core.audit.entity.AuditEntry;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.portfolio.PortfolioItem;
import com.solum.xplain.core.users.AuditUser;
import com.solum.xplain.extensions.simulations.SimulationType;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.function.UnaryOperator;
import lombok.AllArgsConstructor;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.AuditorAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Component
@AllArgsConstructor
public class CcyExposureSimulationCalculationExecutor {

  private static final Logger LOGGER =
      LoggerFactory.getLogger(CcyExposureSimulationCalculationExecutor.class);

  private static final String PORTFOLIO_BASE_CALCULATION_NAME =
      "%s Ccy Exposure Simulation Base Calculation";
  private static final String PORTFOLIO_SHIFT_CALCULATION_NAME =
      "%s Ccy Exposure Simulation Calculation - (%s %s Shift)";

  private final CalculationTradeCountHolder calculationTradeCountHolder;
  private final CcyExposureSimulationCalculationRepository
      ccyExposureSimulationCalculationRepository;
  private final CalibrationCacheService cacheService;
  private final CalculationRequestProducer requestProducer;
  private final AuditEntryService auditEntryService;
  private final AuditorAware<AuditUser> auditUserAuditorAware;
  private final CalculationResultRepository calculationResultRepository;

  @Async
  @EventListener
  public void process(CcyExposureSimulationRequestedEvent event) {
    var simulationData = event.getSimulationData();
    var currencyPair = event.getCurrencyPair();
    var valuationDate = event.getValuationDate();
    var singleValuationTradeCount = numberCalculationTrades(event);
    var totalTradeValuationCount = simulationTradesCount(event, singleValuationTradeCount);
    var simulationId = event.getSimulationData().getSimulationId();

    var spotShift = event.getSpotShift();
    var volatilityShift = event.getVolatilityShift();

    var simulation =
        event
            .getSimulationData()
            .performed(
                event.getName(),
                totalTradeValuationCount,
                event.getCurrentUser(),
                valuationDate,
                spotShift,
                volatilityShift,
                currencyPair);
    ccyExposureSimulationCalculationRepository.saveSimulation(simulation);
    calculationTradeCountHolder.registerCalculation(
        simulationId.toHexString(), totalTradeValuationCount);

    triggerBaseCalculation(simulationData, simulation.getName());
    triggerShiftCalculations(
        simulationData,
        spotShift,
        ShiftDataType.SPOT,
        currencyPair,
        singleValuationTradeCount,
        simulation.getName());
    triggerShiftCalculations(
        simulationData,
        volatilityShift,
        ShiftDataType.VOLATILITY,
        currencyPair,
        singleValuationTradeCount,
        simulation.getName());
  }

  // no shift
  private void triggerBaseCalculation(
      CcyExposureSimulationCalculationData simulationData, String simulationName) {
    var simulationId = simulationData.getSimulationId().toHexString();
    var calibrationWarnings = new CopyOnWriteArraySet<ErrorItem>();
    UnaryOperator<ErrorItem> errorConsumer =
        errorItem -> {
          calibrationWarnings.add(errorItem);
          return errorItem;
        };

    var baseCalculationData = simulationData.baseCalculationData(errorConsumer);
    var calculationId = baseCalculationData.getCalculationId().toHexString();

    logErrors(simulationData, calculationId, calibrationWarnings);
    var key = new CcyExposureCalculationKey(simulationId, null, BigDecimal.ZERO);
    var enhancementFn =
        calculationResultEnhancementFunction(
            key, String.format(PORTFOLIO_BASE_CALCULATION_NAME, simulationName));
    registerCalculationResult(baseCalculationData, enhancementFn, calculationId);

    baseCalculationData
        .getCalculationTrades()
        .tradesFlux()
        .window(1000)
        .flatMap(Flux::collectList)
        .map(items -> createValuationRequests(simulationId, items, baseCalculationData))
        .then()
        .block();
  }

  private void triggerShiftCalculations(
      CcyExposureSimulationCalculationData simulationData,
      ShiftForm shiftForm,
      ShiftDataType shiftDataType,
      CurrencyPair currencyPair,
      Long singleValuationTradeCount,
      String simulationName) {
    var requestData =
        new ShiftCalculationRequestData(
            simulationData,
            shiftForm.getShiftType(),
            shiftDataType,
            currencyPair,
            singleValuationTradeCount);

    for (int i = 1; i <= shiftForm.getNumberShifts(); i++) {
      var positiveShiftValue = shiftForm.getShiftSize().multiply(BigDecimal.valueOf(i));
      var negativeShiftValue = positiveShiftValue.negate();
      triggerShiftCalculation(positiveShiftValue, requestData, simulationName);
      triggerShiftCalculation(negativeShiftValue, requestData, simulationName);
    }
  }

  private String shiftCalculationName(
      BigDecimal shiftSize, ShiftDataType shiftDataType, String simulationName) {
    String shiftSizeFormatted =
        shiftSize.doubleValue() > 0 ? "+" + shiftSize : shiftSize.toPlainString();
    return String.format(
        PORTFOLIO_SHIFT_CALCULATION_NAME, simulationName, shiftDataType, shiftSizeFormatted);
  }

  private void triggerShiftCalculation(
      BigDecimal shiftSize, ShiftCalculationRequestData requestData, String simulationName) {
    var simulationId = requestData.simulationData.getSimulationId().toHexString();
    var calibrationWarnings = new CopyOnWriteArraySet<ErrorItem>();
    UnaryOperator<ErrorItem> errorConsumer =
        errorItem -> {
          calibrationWarnings.add(errorItem);
          return errorItem;
        };
    var portfolioData =
        requestData.simulationData.calculationData(
            errorConsumer,
            requestData.shiftType,
            requestData.shiftDataType,
            shiftSize.doubleValue(),
            requestData.currencyPair);
    var calculationId = portfolioData.getCalculationId().toHexString();
    var key = new CcyExposureCalculationKey(simulationId, requestData.shiftDataType, shiftSize);
    var enhancementFn =
        calculationResultEnhancementFunction(
            key, shiftCalculationName(shiftSize, requestData.shiftDataType, simulationName));
    registerCalculationResult(portfolioData, enhancementFn, calculationId);

    logErrors(requestData.simulationData, calculationId, calibrationWarnings);

    portfolioData
        .getCalculationTrades()
        .tradesFlux()
        .window(1000)
        .flatMap(Flux::collectList)
        .map(items -> createValuationRequests(simulationId, items, portfolioData))
        .then()
        .block();
  }

  private TradesCalculationRequestedEvent tradesCalculationRequestedEvent(
      ObjectId calculationId,
      PortfolioCalculationData data,
      UnaryOperator<CalculationResult> enhancementFn) {
    var currentUser = auditUserAuditorAware.getCurrentAuditor().orElse(null);
    return TradesCalculationRequestedEvent.newOf(calculationId, data, enhancementFn, currentUser);
  }

  private UnaryOperator<CalculationResult> calculationResultEnhancementFunction(
      CcyExposureCalculationKey calculationKey, String name) {
    return result -> {
      result.setCcyExposureCalculationKey(calculationKey);
      result.setName(name);
      return result;
    };
  }

  private Mono<Void> createValuationRequests(
      String simulationId, List<PortfolioItem> items, PortfolioCalculationData data) {
    items.stream()
        .map(i -> calculationRequest(simulationId, i, data))
        .forEach(requestProducer::sendRequest);
    return Mono.empty();
  }

  private TradeCalculationRequest calculationRequest(
      String simulationId, PortfolioItem portfolioItem, PortfolioCalculationData data) {
    var calculationId = data.getCalculationId().toHexString();
    var calculationData = data.result(portfolioItem.getProductType());
    return TradeCalculationRequest.newOfSimulation(
        simulationId,
        SimulationType.CCY_EXPOSURE,
        calculationId,
        data.valuationDate(),
        data.reportingCcy(),
        portfolioItem,
        calculationData);
  }

  private Long numberCalculationTrades(CcyExposureSimulationRequestedEvent event) {
    var simulationData = event.getSimulationData();
    return simulationData.getCalculationTrades().tradesCount();
  }

  private void registerCalculationResult(
      PortfolioCalculationData portfolioData,
      UnaryOperator<CalculationResult> enhancementFn,
      String calculationId) {
    var event =
        tradesCalculationRequestedEvent(
            portfolioData.getCalculationId(), portfolioData, enhancementFn);
    var tradesCount = event.getCalculationData().getCalculationTrades().tradesCount();
    calculationResultRepository.saveCalculation(event.toResult(tradesCount), event.toCharts());
    calculationTradeCountHolder.registerCalculation(calculationId, tradesCount);
    cacheService.cacheResults(calculationId, event.getCalculationData());
    LOGGER.debug(
        "Started valuating {} trades for portfolio {}",
        tradesCount,
        event.getCalculationData().getPortfolio().getExternalPortfolioId());
  }

  private Long simulationTradesCount(
      CcyExposureSimulationRequestedEvent event, Long singleValuationTradeCount) {
    // initial value, no shifts
    long numberValuations = 1;

    // + total number of spot shifts (+ve, -ve)
    numberValuations += event.getSpotShift().getNumberShifts() * 2;

    // + total number of spot shifts (+ve, -ve)
    numberValuations += event.getVolatilityShift().getNumberShifts() * 2;

    // total number of trades
    return numberValuations * singleValuationTradeCount;
  }

  private void logErrors(
      CcyExposureSimulationCalculationData simulationData,
      String calculationId,
      CopyOnWriteArraySet<ErrorItem> calibrationWarnings) {
    var warnings =
        ImmutableList.<ErrorItem>builder()
            .addAll(simulationData.getCalculationCurveDataHolder().warnings())
            .addAll(calibrationWarnings)
            .build();

    var auditEntry =
        AuditEntry.of(
            CALCULATION_RESULT_COLLECTION,
            String.format(
                "Portfolio %s simulation calculation completed",
                simulationData.getPortfolio().getExternalPortfolioId()),
            calculationId);
    auditEntryService.newEntryWithLogs(auditEntry, warnings);
  }

  private record ShiftCalculationRequestData(
      CcyExposureSimulationCalculationData simulationData,
      ShiftType shiftType,
      ShiftDataType shiftDataType,
      CurrencyPair currencyPair,
      Long singleValuationTradeCount) {}
}
