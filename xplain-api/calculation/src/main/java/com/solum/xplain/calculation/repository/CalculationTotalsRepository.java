package com.solum.xplain.calculation.repository;

import static com.solum.xplain.core.utils.PathUtils.joinPaths;
import static com.solum.xplain.core.utils.ReflectionUtils.propertyName;
import static com.solum.xplain.core.utils.mongo.MongoVariables.VALUE_PREFIX;
import static java.util.Optional.ofNullable;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.ROOT;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.addFields;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.fields;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.group;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.replaceRoot;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.sort;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.unwind;
import static org.springframework.data.mongodb.core.aggregation.Fields.UNDERSCORE_ID;
import static org.springframework.data.mongodb.core.aggregation.Fields.field;
import static org.springframework.data.mongodb.core.aggregation.VariableOperators.mapItemsOf;
import static org.springframework.data.mongodb.core.query.Criteria.where;

import com.google.common.collect.ImmutableList;
import com.solum.xplain.calculation.CalculationPortfolioItem;
import com.solum.xplain.calculation.CalculationResult;
import com.solum.xplain.calculation.CalculationResultProjections;
import com.solum.xplain.calculation.Spot01ValueView;
import com.solum.xplain.calculation.value.CalculationPortfolioItemView;
import com.solum.xplain.calculation.value.CalculationResultTotalsForm;
import com.solum.xplain.calculation.value.CalculationResultsTotals;
import com.solum.xplain.calculation.value.CashFlowValue;
import com.solum.xplain.calculation.value.CashFlowValueView;
import com.solum.xplain.calculation.value.CashFlowsView;
import com.solum.xplain.calculation.value.DV01Combined;
import com.solum.xplain.calculation.value.DV01TradeValue;
import com.solum.xplain.calculation.value.DV01TradeValues;
import com.solum.xplain.calculation.value.DiscountCcyCashFlows;
import com.solum.xplain.calculation.value.GroupedValues;
import com.solum.xplain.calculation.value.HierarchicalGroupedValues;
import com.solum.xplain.calculation.value.Metrics;
import com.solum.xplain.calculation.value.Spot01Combined;
import com.solum.xplain.calculation.value.Spot01TradeValue;
import com.solum.xplain.core.common.GroupRequest;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.shared.utils.filter.TableFilter;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import org.bson.Document;
import org.springframework.core.convert.ConversionService;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperationContext;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;

@Repository
@AllArgsConstructor
public class CalculationTotalsRepository {
  private final MongoOperations mongoOperations;
  private final ConversionService conversionService;
  private final CalculationResultRepository calculationResultRepository;

  public Either<ErrorItem, CalculationResultsTotals> calculationResultTotals(
      String calculationResultId,
      TableFilter tableFilter,
      GroupRequest groupRequest,
      CalculationResultTotalsForm form) {
    return calculationResultRepository
        .calculationEntity(calculationResultId)
        .map(e -> calculationResultTotals(tableFilter, groupRequest, form, e));
  }

  private CalculationResultsTotals calculationResultTotals(
      TableFilter tableFilter,
      GroupRequest groupRequest,
      CalculationResultTotalsForm form,
      CalculationResult result) {

    ImmutableList.Builder<AggregationOperation> builder =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(
                    where(CalculationPortfolioItem.Fields.calculationResultId).is(result.getId())));
    builder.add(CalculationResultProjections.calculationResultProjection());
    if (form.isAll()) {
      builder.add(
          match(tableFilter.criteria(CalculationPortfolioItemView.class, conversionService)));
    } else {
      builder.add(match(tradeIdsCriteria(form)));
    }
    List<AggregationOperation> operations = builder.build();

    GroupedValues totals = calculateTotals(operations);
    List<HierarchicalGroupedValues> groupedByProductType =
        calculateTotals(operations, groupRequest);

    return new CalculationResultsTotals(totals, groupedByProductType);
  }

  private GroupedValues calculateTotals(List<AggregationOperation> operations) {
    return mongoOperations
        .aggregate(
            newAggregation(
                CalculationPortfolioItem.class,
                ImmutableList.<AggregationOperation>builder()
                    .addAll(operations)
                    .add(
                        group()
                            .count()
                            .as(GroupedValues.Fields.tradesCount)
                            .sum(CalculationPortfolioItemView.Fields.metricsPresentValue)
                            .as(GroupedValues.Fields.totalPv)
                            .sum(CalculationPortfolioItemView.Fields.metricsDv01)
                            .as(GroupedValues.Fields.totalDv01)
                            .sum(CalculationPortfolioItemView.Fields.metricsBr01)
                            .as(GroupedValues.Fields.totalBr01)
                            .sum(CalculationPortfolioItemView.Fields.metricsInf01)
                            .as(GroupedValues.Fields.totalInf01)
                            .sum(CalculationPortfolioItemView.Fields.metricsCs01)
                            .as(GroupedValues.Fields.totalCs01)
                            .sum(CalculationPortfolioItemView.Fields.metricsDeltaFwd)
                            .as(GroupedValues.Fields.totalDeltaFwd)
                            .sum(CalculationPortfolioItemView.Fields.metricsDeltaSpot)
                            .as(GroupedValues.Fields.totalDeltaSpot)
                            .sum(CalculationPortfolioItemView.Fields.metricsPv01)
                            .as(GroupedValues.Fields.totalPv01)
                            .sum(CalculationPortfolioItemView.Fields.metricsCleanPresentValue)
                            .as(GroupedValues.Fields.totalCleanPv)
                            .sum(CalculationPortfolioItemView.Fields.metricsPvGamma)
                            .as(GroupedValues.Fields.totalGamma)
                            .sum(CalculationPortfolioItemView.Fields.metricsPvVega)
                            .as(GroupedValues.Fields.totalVega)
                            .sum(CalculationPortfolioItemView.Fields.metricsPvTheta)
                            .as(GroupedValues.Fields.totalTheta))
                    .build()),
            GroupedValues.class)
        .getUniqueMappedResult();
  }

  private List<HierarchicalGroupedValues> calculateTotals(
      List<AggregationOperation> operations, GroupRequest request) {
    return mongoOperations
        .aggregate(
            newAggregation(
                CalculationPortfolioItem.class,
                ImmutableList.<AggregationOperation>builder()
                    .addAll(operations)
                    .addAll(getGroupByOperations(request))
                    .add(hierarchicalAggregationProjection(request.getRowGroupCols()))
                    .add(sort(Sort.by(UNDERSCORE_ID)))
                    .build()),
            HierarchicalGroupedValues.class)
        .getMappedResults();
  }

  public CashFlowsView cashFlows(
      String calculationId, TableFilter tableFilter, CalculationResultTotalsForm form) {

    return CashFlowsView.of(
        calculationResultRepository
            .calculationEntity(calculationId)
            .map(result -> cashFlows(tableFilter, form, result))
            .getOr(Collections::emptyList));
  }

  public List<DiscountCcyCashFlows> cashFlows(
      TableFilter tableFilter, CalculationResultTotalsForm form, CalculationResult result) {
    List<AggregationOperation> operations = cashFlowAggregation(tableFilter, form, result);

    return mongoOperations
        .aggregate(
            newAggregation(CalculationPortfolioItem.class, operations), CashFlowValueView.class)
        .getMappedResults()
        .stream()
        .collect(
            Collectors.groupingBy(c -> new CashflowKey(c.getDiscountCcy(), c.getPaymentDate())))
        .entrySet()
        .stream()
        .map(
            e ->
                DiscountCcyCashFlows.of(
                    e.getKey().paymentDate, e.getKey().discountCcy, e.getValue()))
        .sorted(DiscountCcyCashFlows.dateCurrencyComparator())
        .toList();
  }

  public Stream<CashFlowValueView> cashFlowsStream(
      String calculationResultId, TableFilter tableFilter, CalculationResultTotalsForm form) {

    return calculationResultRepository
        .calculationEntity(calculationResultId)
        .map(
            result ->
                mongoOperations.aggregateStream(
                    newAggregation(
                        CalculationPortfolioItem.class,
                        cashFlowAggregation(tableFilter, form, result)),
                    CashFlowValueView.class))
        .getOr(Stream::empty);
  }

  private List<AggregationOperation> cashFlowAggregation(
      TableFilter tableFilter, CalculationResultTotalsForm form, CalculationResult result) {
    ImmutableList.Builder<AggregationOperation> builder =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(
                    where(CalculationPortfolioItem.Fields.calculationResultId).is(result.getId())),
                CalculationResultProjections.calculationResultProjection()
                    .and(CalculationPortfolioItem.Fields.metrics)
                    .as(CalculationPortfolioItem.Fields.metrics));
    if (form.isAll()) {
      builder.add(
          match(tableFilter.criteria(CalculationPortfolioItemView.class, conversionService)));
    } else {
      builder.add(match(tradeIdsCriteria(form)));
    }
    builder
        .add(
            unwind(propertyName(CalculationPortfolioItem.Fields.metrics, Metrics.Fields.cashFlows)))
        .add(
            addFields()
                .addField(
                    propertyName(
                        CalculationPortfolioItem.Fields.metrics,
                        Metrics.Fields.cashFlows,
                        CashFlowValueView.Fields.discountCcy))
                .withValueOf(field(CalculationPortfolioItem.Fields.discountingCcy))
                .build())
        .add(
            replaceRoot(
                propertyName(CalculationPortfolioItem.Fields.metrics, Metrics.Fields.cashFlows)))
        .add(
            group(
                    CashFlowValueView.Fields.paymentDate,
                    CashFlowValueView.Fields.currency,
                    CashFlowValueView.Fields.discountCcy)
                .sum(CashFlowValueView.Fields.forecastPay)
                .as(CashFlowValueView.Fields.forecastPay)
                .sum(CashFlowValueView.Fields.forecastReceive)
                .as(CashFlowValueView.Fields.forecastReceive)
                .sum(CashFlowValueView.Fields.forecastValue)
                .as(CashFlowValueView.Fields.forecastValue)
                .sum(CashFlowValueView.Fields.forecastValueCalculationCcy)
                .as(CashFlowValueView.Fields.forecastValueCalculationCcy)
                .sum(CashFlowValueView.Fields.presentReceive)
                .as(CashFlowValueView.Fields.presentReceive)
                .sum(CashFlowValueView.Fields.presentPay)
                .as(CashFlowValueView.Fields.presentPay)
                .sum(CashFlowValueView.Fields.presentValue)
                .as(CashFlowValueView.Fields.presentValue)
                .sum(CashFlowValueView.Fields.presentValueCalculationCcy)
                .as(CashFlowValueView.Fields.presentValueCalculationCcy)
                .first(CashFlowValueView.Fields.discountFactor)
                .as(CashFlowValueView.Fields.discountFactor))
        .add(
            project(
                    CashFlowValueView.Fields.forecastPay,
                    CashFlowValueView.Fields.forecastReceive,
                    CashFlowValueView.Fields.forecastValue,
                    CashFlowValueView.Fields.forecastValueCalculationCcy,
                    CashFlowValueView.Fields.presentReceive,
                    CashFlowValueView.Fields.presentPay,
                    CashFlowValueView.Fields.presentValue,
                    CashFlowValueView.Fields.presentValueCalculationCcy,
                    CashFlowValueView.Fields.discountFactor)
                .and(propertyName(UNDERSCORE_ID, CashFlowValueView.Fields.currency))
                .as(CashFlowValue.Fields.currency)
                .and(propertyName(UNDERSCORE_ID, CashFlowValueView.Fields.paymentDate))
                .as(CashFlowValue.Fields.paymentDate)
                .and(propertyName(UNDERSCORE_ID, CashFlowValueView.Fields.discountCcy))
                .as(CashFlowValueView.Fields.discountCcy))
        .add(
            sort(
                Sort.Direction.ASC,
                CashFlowValueView.Fields.paymentDate,
                CashFlowValueView.Fields.currency,
                CashFlowValueView.Fields.discountCcy));

    return builder.build();
  }

  public DV01Combined dv01TradeValues(
      String calculationResultId, TableFilter tableFilter, CalculationResultTotalsForm form) {
    return calculationResultRepository
        .calculationEntity(calculationResultId)
        .map(result -> dv01TradeValues(tableFilter, form, result))
        .getOr(() -> DV01Combined.EMPTY);
  }

  public Either<ErrorItem, DV01Combined> getDv01TradeValues(String resultId, TableFilter filter) {
    var form = new CalculationResultTotalsForm();
    form.setAll(true);
    return calculationResultRepository
        .calculationEntity(resultId)
        .map(result -> dv01TradeValues(filter, form, result));
  }

  private DV01Combined dv01TradeValues(
      TableFilter tableFilter, CalculationResultTotalsForm form, CalculationResult result) {

    ImmutableList.Builder<AggregationOperation> builder =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(
                    where(CalculationPortfolioItem.Fields.calculationResultId).is(result.getId())),
                match(where(CalculationPortfolioItem.Fields.metrics).exists(true)),
                CalculationResultProjections.calculationResultProjection()
                    .and(CalculationPortfolioItem.Fields.metrics)
                    .as(CalculationPortfolioItem.Fields.metrics));
    if (form.isAll()) {
      builder.add(
          match(tableFilter.criteria(CalculationPortfolioItemView.class, conversionService)));
    } else {
      builder.add(match(tradeIdsCriteria(form)));
    }
    builder.add(replaceRoot(CalculationPortfolioItem.Fields.metrics));
    builder.add(
        project(
            Metrics.Fields.dv01TradeValues,
            Metrics.Fields.br01TradeValues,
            Metrics.Fields.inf01TradeValues,
            Metrics.Fields.cs01TradeValues,
            Metrics.Fields.deltaFwdTradeValues,
            Metrics.Fields.deltaSpotTradeValues));
    var aggregation = newAggregation(CalculationPortfolioItem.class, builder.build());
    DV01TradeValues dv01 = new DV01TradeValues();
    DV01TradeValues br01 = new DV01TradeValues();
    DV01TradeValues inf01 = new DV01TradeValues();
    DV01TradeValues cs01 = new DV01TradeValues();
    DV01TradeValues deltaFwd = new DV01TradeValues();
    DV01TradeValues deltaSpot = new DV01TradeValues();
    try (var stream = mongoOperations.aggregateStream(aggregation, Metrics.class)) {
      stream.forEach(
          r -> {
            ofNullable(r.getDv01TradeValues()).stream()
                .flatMap(Collection::stream)
                .forEach(dv01::add);
            ofNullable(r.getBr01TradeValues()).stream()
                .flatMap(Collection::stream)
                .forEach(br01::add);
            ofNullable(r.getInf01TradeValues()).stream()
                .flatMap(Collection::stream)
                .forEach(inf01::add);
            ofNullable(r.getCs01TradeValues()).stream()
                .flatMap(Collection::stream)
                .forEach(cs01::add);
            ofNullable(r.getDeltaFwdTradeValues()).stream()
                .flatMap(Collection::stream)
                .forEach(deltaFwd::add);
            ofNullable(r.getDeltaSpotTradeValues()).stream()
                .flatMap(Collection::stream)
                .forEach(deltaSpot::add);
          });
    }
    return new DV01Combined(
        sorted01Values(dv01),
        sorted01Values(br01),
        sorted01Values(inf01),
        sorted01Values(cs01),
        sorted01Values(deltaFwd),
        sorted01Values(deltaSpot));
  }

  private List<DV01TradeValue> sorted01Values(DV01TradeValues values) {
    return values.getTradeValues().values().stream()
        .sorted(Comparator.comparing(DV01TradeValue::getCurveName))
        .toList();
  }

  public Spot01Combined spot01TradeValues(
      String calculationResultId, TableFilter tableFilter, CalculationResultTotalsForm form) {
    return calculationResultRepository
        .calculationEntity(calculationResultId)
        .map(result -> spot01TradeValues(tableFilter, form, result))
        .getOr(() -> new Spot01Combined(List.of()));
  }

  private Spot01Combined spot01TradeValues(
      TableFilter tableFilter, CalculationResultTotalsForm form, CalculationResult result) {
    var spot01Path =
        joinPaths(CalculationPortfolioItem.Fields.metrics, Metrics.Fields.spot01TradeValues);

    ImmutableList.Builder<AggregationOperation> builder =
        ImmutableList.<AggregationOperation>builder()
            .add(
                match(
                    where(CalculationPortfolioItem.Fields.calculationResultId).is(result.getId())),
                match(where(CalculationPortfolioItem.Fields.metrics).exists(true)),
                match(where(spot01Path).exists(true)),
                CalculationResultProjections.calculationResultProjection()
                    .and(CalculationPortfolioItem.Fields.metrics)
                    .as(CalculationPortfolioItem.Fields.metrics));
    if (form.isAll()) {
      builder.add(
          match(tableFilter.criteria(CalculationPortfolioItemView.class, conversionService)));
    } else {
      builder.add(match(tradeIdsCriteria(form)));
    }

    builder.add(
        unwind(spot01Path),
        replaceRoot(spot01Path),
        group(Spot01TradeValue.Fields.currencyPair, Spot01TradeValue.Fields.fxSpot)
            .sum(Spot01TradeValue.Fields.spot01)
            .as(Spot01TradeValue.Fields.spot01),
        project()
            .and(joinPaths(UNDERSCORE_ID, Spot01TradeValue.Fields.currencyPair))
            .as(Spot01ValueView.Fields.currencyPair)
            .and(joinPaths(UNDERSCORE_ID, Spot01TradeValue.Fields.fxSpot))
            .as(Spot01ValueView.Fields.fxSpot)
            .and(Spot01TradeValue.Fields.spot01)
            .as(Spot01ValueView.Fields.spot01));

    var results =
        mongoOperations
            .aggregate(
                newAggregation(CalculationPortfolioItem.class, builder.build()),
                Spot01ValueView.class)
            .getMappedResults()
            .stream()
            .sorted(Comparator.comparing(Spot01ValueView::getCurrencyPair))
            .toList();

    return new Spot01Combined(results);
  }

  private Criteria tradeIdsCriteria(CalculationResultTotalsForm form) {
    return where(CalculationPortfolioItem.Fields.tradeId)
        .in(form.getItemIds() == null ? Collections.emptyList() : form.getItemIds());
  }

  private List<AggregationOperation> getGroupByOperations(GroupRequest request) {

    List<AggregationOperation> operations = new ArrayList<>();
    operations.add(
        group(fields(request.getRowGroupCols().toArray(String[]::new)))
            .count()
            .as(GroupedValues.Fields.tradesCount)
            .sum(CalculationPortfolioItemView.Fields.metricsPresentValue)
            .as(GroupedValues.Fields.totalPv)
            .sum(CalculationPortfolioItemView.Fields.metricsDv01)
            .as(GroupedValues.Fields.totalDv01)
            .sum(CalculationPortfolioItemView.Fields.metricsBr01)
            .as(GroupedValues.Fields.totalBr01)
            .sum(CalculationPortfolioItemView.Fields.metricsInf01)
            .as(GroupedValues.Fields.totalInf01)
            .sum(CalculationPortfolioItemView.Fields.metricsCs01)
            .as(GroupedValues.Fields.totalCs01)
            .sum(CalculationPortfolioItemView.Fields.metricsDeltaFwd)
            .as(GroupedValues.Fields.totalDeltaFwd)
            .sum(CalculationPortfolioItemView.Fields.metricsDeltaSpot)
            .as(GroupedValues.Fields.totalDeltaSpot)
            .sum(CalculationPortfolioItemView.Fields.metricsPv01)
            .as(GroupedValues.Fields.totalPv01)
            .sum(CalculationPortfolioItemView.Fields.metricsCleanPresentValue)
            .as(GroupedValues.Fields.totalCleanPv)
            .sum(CalculationPortfolioItemView.Fields.metricsPvGamma)
            .as(GroupedValues.Fields.totalGamma)
            .sum(CalculationPortfolioItemView.Fields.metricsPvVega)
            .as(GroupedValues.Fields.totalVega)
            .sum(CalculationPortfolioItemView.Fields.metricsPvTheta)
            .as(GroupedValues.Fields.totalTheta));

    List<String> fields = request.getRowGroupCols();
    return getGroupByOperations(operations, fields);
  }

  private List<AggregationOperation> getGroupByOperations(
      List<AggregationOperation> operations, List<String> groupFields) {

    List<String> groupBy = groupFields.subList(0, groupFields.size() - 1);
    if (groupBy.isEmpty()) {
      return operations;
    }

    operations.add(
        group(groupBy.toArray(String[]::new))
            .sum(GroupedValues.Fields.tradesCount)
            .as(GroupedValues.Fields.tradesCount)
            .sum(GroupedValues.Fields.totalPv)
            .as(GroupedValues.Fields.totalPv)
            .sum(GroupedValues.Fields.totalDv01)
            .as(GroupedValues.Fields.totalDv01)
            .sum(GroupedValues.Fields.totalBr01)
            .as(GroupedValues.Fields.totalBr01)
            .sum(GroupedValues.Fields.totalInf01)
            .as(GroupedValues.Fields.totalInf01)
            .sum(GroupedValues.Fields.totalCs01)
            .as(GroupedValues.Fields.totalCs01)
            .sum(GroupedValues.Fields.totalDeltaFwd)
            .as(GroupedValues.Fields.totalDeltaFwd)
            .sum(GroupedValues.Fields.totalDeltaSpot)
            .as(GroupedValues.Fields.totalDeltaSpot)
            .sum(GroupedValues.Fields.totalPv01)
            .as(GroupedValues.Fields.totalPv01)
            .sum(GroupedValues.Fields.totalCleanPv)
            .as(GroupedValues.Fields.totalCleanPv)
            .sum(GroupedValues.Fields.totalVega)
            .as(GroupedValues.Fields.totalVega)
            .sum(GroupedValues.Fields.totalGamma)
            .as(GroupedValues.Fields.totalGamma)
            .sum(GroupedValues.Fields.totalTheta)
            .as(GroupedValues.Fields.totalTheta)
            .push(ROOT)
            .as(HierarchicalGroupedValues.Fields.groupedValues));
    return getGroupByOperations(operations, groupBy);
  }

  private ProjectionOperation hierarchicalAggregationProjection(List<String> fields) {
    List<String> fieldNames = ImmutableList.copyOf(fields);
    ProjectionOperation operation =
        project(
            UNDERSCORE_ID,
            GroupedValues.Fields.tradesCount,
            GroupedValues.Fields.totalPv,
            GroupedValues.Fields.totalDv01,
            GroupedValues.Fields.totalBr01,
            GroupedValues.Fields.totalInf01,
            GroupedValues.Fields.totalCs01,
            GroupedValues.Fields.totalDeltaFwd,
            GroupedValues.Fields.totalDeltaSpot,
            GroupedValues.Fields.totalPv01,
            GroupedValues.Fields.totalTheta,
            GroupedValues.Fields.totalCleanPv,
            GroupedValues.Fields.totalGamma,
            GroupedValues.Fields.totalVega);

    String parentName = HierarchicalGroupedValues.Fields.groupedValues;
    if (fieldNames.size() > 1) {
      operation =
          operation
              .and(
                  mapItemsOf(parentName)
                      .as(HierarchicalGroupedValues.Fields.groupedValues)
                      .andApply(
                          context ->
                              toMap(
                                  fieldNames,
                                  1,
                                  propertyName(
                                      HierarchicalGroupedValues.Fields.groupedValues,
                                      HierarchicalGroupedValues.Fields.groupedValues),
                                  context)))
              .as(HierarchicalGroupedValues.Fields.groupedValues);
    }

    return operation;
  }

  private Document toMap(
      List<String> fieldNames, int index, String parentName, AggregationOperationContext context) {
    String fieldName = fieldNames.get(index);
    Document document =
        new Document()
            .append(
                UNDERSCORE_ID,
                VALUE_PREFIX
                    + propertyName(
                        HierarchicalGroupedValues.Fields.groupedValues, UNDERSCORE_ID, fieldName))
            .append(
                GroupedValues.Fields.tradesCount,
                toGroupedValuesField(GroupedValues.Fields.tradesCount))
            .append(
                GroupedValues.Fields.totalDv01,
                toGroupedValuesField(GroupedValues.Fields.totalDv01))
            .append(
                GroupedValues.Fields.totalPv, toGroupedValuesField(GroupedValues.Fields.totalPv))
            .append(
                GroupedValues.Fields.totalDv01,
                toGroupedValuesField(GroupedValues.Fields.totalDv01))
            .append(
                GroupedValues.Fields.totalBr01,
                toGroupedValuesField(GroupedValues.Fields.totalBr01))
            .append(
                GroupedValues.Fields.totalInf01,
                toGroupedValuesField(GroupedValues.Fields.totalInf01))
            .append(
                GroupedValues.Fields.totalCs01,
                toGroupedValuesField(GroupedValues.Fields.totalCs01))
            .append(
                GroupedValues.Fields.totalDeltaFwd,
                toGroupedValuesField(GroupedValues.Fields.totalDeltaFwd))
            .append(
                GroupedValues.Fields.totalDeltaSpot,
                toGroupedValuesField(GroupedValues.Fields.totalDeltaSpot))
            .append(
                GroupedValues.Fields.totalPv01,
                toGroupedValuesField(GroupedValues.Fields.totalPv01))
            .append(
                GroupedValues.Fields.totalTheta,
                toGroupedValuesField(GroupedValues.Fields.totalTheta))
            .append(
                GroupedValues.Fields.totalCleanPv,
                toGroupedValuesField(GroupedValues.Fields.totalCleanPv))
            .append(
                GroupedValues.Fields.totalGamma,
                toGroupedValuesField(GroupedValues.Fields.totalGamma))
            .append(
                GroupedValues.Fields.totalVega,
                toGroupedValuesField(GroupedValues.Fields.totalVega));
    final int newIndex = ++index;
    if (newIndex < fieldNames.size()) {
      document.append(
          HierarchicalGroupedValues.Fields.groupedValues,
          mapItemsOf(parentName)
              .as(HierarchicalGroupedValues.Fields.groupedValues)
              .andApply(
                  ctx ->
                      toMap(
                          fieldNames,
                          newIndex,
                          propertyName(parentName, HierarchicalGroupedValues.Fields.groupedValues),
                          ctx))
              .toDocument(context));
    }
    return document;
  }

  private String toGroupedValuesField(String path) {
    return propertyName(VALUE_PREFIX.concat(HierarchicalGroupedValues.Fields.groupedValues), path);
  }

  private record CashflowKey(String discountCcy, LocalDate paymentDate) {}
}
