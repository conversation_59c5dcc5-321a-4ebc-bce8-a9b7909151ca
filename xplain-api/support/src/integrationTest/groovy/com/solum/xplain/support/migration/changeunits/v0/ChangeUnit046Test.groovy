package com.solum.xplain.support.migration.changeunits.v0

import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.PortfolioItem
import com.solum.xplain.core.portfolio.PortfolioItemEntity
import com.solum.xplain.secmaster.entity.SecMasterTradeReadEntity
import com.solum.xplain.secmaster.entity.SecMasterTradeWriteEntity
import jakarta.annotation.Resource
import org.bson.Document
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ChangeUnit046Test extends IntegrationSpecification {

  static def PORTFOLIO_ITEM = "portfolioItem"
  static def PORTFOLIO_ITEM_ENTITY = "portfolioItemEntity"
  static def SEC_MASTER_TRADE_READ_ENTITY = "secMasterTradeReadEntity"
  static def SEC_MASTER_TRADE_WRITE_ENTITY = "secMasterTradeWriteEntity"
  static def TRADE_DETAILS = "tradeDetails"
  static def VERSIONS = "versions"
  static def VALUE = "value"
  static def EXTERNAL_TRADE_ID = "externalTradeId"
  static def STATE = "state"
  static def CLIENT_METRICS = "clientMetrics"

  @Resource
  MongoTemplate mongoTemplate

  def cleanup() {
    mongoTemplate.dropCollection(PORTFOLIO_ITEM)
    mongoTemplate.dropCollection(PORTFOLIO_ITEM_ENTITY)
    mongoTemplate.dropCollection(SEC_MASTER_TRADE_READ_ENTITY)
    mongoTemplate.dropCollection(SEC_MASTER_TRADE_WRITE_ENTITY)
  }

  def "should rename genericTradeDetails to customTradeDetails for trade read entities"() {
    setup:
    def changeUnit = new ChangeUnit046(mongoTemplate)
    def genericDetails = genericTradeDetails()
    def genericDetails2 = genericTradeDetails("DEF")

    def entity = new Document()
      .append(TRADE_DETAILS, tradeDetails())

    def entity2 = new Document()
      .append(TRADE_DETAILS, tradeDetails(genericDetails2))

    def entityNoTradeDetails = new Document()

    def entityNoGenericDetails = new Document()
      .append(TRADE_DETAILS, new Document()
      .append("positionType", "BUY"))

    mongoTemplate.insert(entity, collectionName)
    mongoTemplate.insert(entity2, collectionName)
    mongoTemplate.insert(entityNoTradeDetails, collectionName)
    mongoTemplate.insert(entityNoGenericDetails, collectionName)

    when:
    changeUnit.beforeExecution()

    then:
    var resultEntities = mongoTemplate.findAll(entityClass)
    resultEntities.size() == 4

    with(resultEntities[0]) {
      tradeDetails.customTradeDetails.assetType == genericDetails.assetType
      tradeDetails.customTradeDetails.subAssetType == genericDetails.subAssetType
      tradeDetails.customTradeDetails.notional == genericDetails.notional
    }

    with(resultEntities[1]) {
      tradeDetails.customTradeDetails.assetType == genericDetails2.assetType
      tradeDetails.customTradeDetails.subAssetType == genericDetails2.subAssetType
      tradeDetails.customTradeDetails.notional == genericDetails2.notional
    }

    with(resultEntities[2]) {
      tradeDetails == null
    }

    with(resultEntities[3]) {
      tradeDetails.customTradeDetails == null
      tradeDetails.positionType.name() == "BUY"
    }

    where:
    collectionName                | entityClass
    PORTFOLIO_ITEM                | PortfolioItem.class
    SEC_MASTER_TRADE_READ_ENTITY  | SecMasterTradeReadEntity.class
  }

  def "should rename genericTradeDetails to customTradeDetails for trade write entities"() {
    setup:
    def changeUnit = new ChangeUnit046(mongoTemplate)
    def genericDetails = genericTradeDetails()
    def genericDetails2 = genericTradeDetails("DEF")
    // the following fields are added to test that they are not affected by the change unit
    def tradeId = "trade1"
    def tradeId2 = "trade2"
    def tradeId3 = "trade3"
    def clientMetrics = new Document().append("presentValue", 1.0)
    def clientMetrics2 = new Document().append("presentValue", 2.0)
    def clientMetrics3 = new Document().append("presentValue", 3.0)
    def entityState = State.ACTIVE
    def entityState2 = State.ARCHIVED
    def entityState3 = State.DELETED
    //

    def version = new Document()
      .append(STATE, entityState)
      .append(VALUE,
      new Document()
      .append(TRADE_DETAILS, tradeDetails(genericDetails))
      .append(CLIENT_METRICS, clientMetrics)
      )

    def version2 = new Document()
      .append(STATE, entityState2)
      .append(VALUE,
      new Document()
      .append(TRADE_DETAILS, tradeDetails(genericDetails2))
      .append(CLIENT_METRICS, clientMetrics2)
      )

    def versionWithoutGenericDetails = new Document()
      .append(STATE, entityState3)
      .append(VALUE,
      new Document()
      .append(TRADE_DETAILS, tradeDetailsWithoutGenericDetails())
      .append(CLIENT_METRICS, clientMetrics3)
      )

    def entity = new Document()
      .append(EXTERNAL_TRADE_ID, tradeId)
      .append(VERSIONS, [version, version2, version])

    def entity2 = new Document()
      .append(EXTERNAL_TRADE_ID, tradeId2)
      .append(VERSIONS, [version2, version, version])

    def entityWithoutGenericDetails = new Document()
      .append(EXTERNAL_TRADE_ID, tradeId3)
      .append(VERSIONS, [
        versionWithoutGenericDetails,
        versionWithoutGenericDetails,
        versionWithoutGenericDetails
      ])

    mongoTemplate.insert(entity, collectionName)
    mongoTemplate.insert(entity2, collectionName)
    mongoTemplate.insert(entityWithoutGenericDetails, collectionName)

    when:
    changeUnit.beforeExecution()

    then:
    var resultEntities = mongoTemplate.findAll(entityClass)
    resultEntities.size() == 3

    with(resultEntities[0]) {
      versions.size() == 3
      with(versions[0]) {
        state == entityState
        value.clientMetrics.presentValue == 1.0d
        value.tradeDetails.customTradeDetails.assetType == genericDetails.assetType
        value.tradeDetails.customTradeDetails.subAssetType == genericDetails.subAssetType
        value.tradeDetails.customTradeDetails.notional == genericDetails.notional
      }
      with(versions[1]) {
        state == entityState2
        value.clientMetrics.presentValue == 2.0d
        value.tradeDetails.customTradeDetails.assetType == genericDetails2.assetType
        value.tradeDetails.customTradeDetails.subAssetType == genericDetails2.subAssetType
        value.tradeDetails.customTradeDetails.notional == genericDetails2.notional
      }
      with(versions[2]) {
        state == entityState
        value.clientMetrics.presentValue == 1.0d
        value.tradeDetails.customTradeDetails.assetType == genericDetails.assetType
        value.tradeDetails.customTradeDetails.subAssetType == genericDetails.subAssetType
        value.tradeDetails.customTradeDetails.notional == genericDetails.notional
      }
    }

    with(resultEntities[1]) {
      versions.size() == 3
      with(versions[0]) {
        state == entityState2
        value.clientMetrics.presentValue == 2.0d
        value.tradeDetails.customTradeDetails.assetType == genericDetails2.assetType
        value.tradeDetails.customTradeDetails.subAssetType == genericDetails2.subAssetType
        value.tradeDetails.customTradeDetails.notional == genericDetails2.notional
      }
      with(versions[1]) {
        state == entityState
        value.clientMetrics.presentValue == 1.0d
        value.tradeDetails.customTradeDetails.assetType == genericDetails.assetType
        value.tradeDetails.customTradeDetails.subAssetType == genericDetails.subAssetType
        value.tradeDetails.customTradeDetails.notional == genericDetails.notional
      }
      with(versions[2]) {
        state == entityState
        value.clientMetrics.presentValue == 1.0d
        value.tradeDetails.customTradeDetails.assetType == genericDetails.assetType
        value.tradeDetails.customTradeDetails.subAssetType == genericDetails.subAssetType
        value.tradeDetails.customTradeDetails.notional == genericDetails.notional
      }
    }

    with(resultEntities[2]) {
      versions.size() == 3
      with(versions[0]) {
        state == entityState3
        value.clientMetrics.presentValue == 3.0d
        value.tradeDetails.customTradeDetails == null
      }
      with(versions[1]) {
        state == entityState3
        value.clientMetrics.presentValue == 3.0d
        value.tradeDetails.customTradeDetails == null
      }
      with(versions[2]) {
        state == entityState3
        value.clientMetrics.presentValue == 3.0d
        value.tradeDetails.customTradeDetails == null
      }
    }

    and:
    if(checkExternalTradeId) {
      ((PortfolioItemEntity) resultEntities[0]).externalTradeId == tradeId
      ((PortfolioItemEntity) resultEntities[1]).externalTradeId == tradeId2
      ((PortfolioItemEntity) resultEntities[2]).externalTradeId == tradeId3
    }

    where:
    entityClass                     | collectionName                | checkExternalTradeId
    PortfolioItemEntity.class       | PORTFOLIO_ITEM_ENTITY         | true
    SecMasterTradeWriteEntity.class | SEC_MASTER_TRADE_WRITE_ENTITY | false
  }

  def tradeDetailsWithoutGenericDetails() {
    return new Document()
  }

  def tradeDetails(Document genericDetails = genericTradeDetails()) {
    return new Document()
      .append("genericTradeDetails", genericDetails)
  }

  def genericTradeDetails(String assetType = "ABC") {
    return new Document()
      .append("assetType", assetType)
      .append("subAssetType", "ABC1")
      .append("notional", Double.valueOf(1_000_000))
      .append("currency", "GBP")
  }
}
