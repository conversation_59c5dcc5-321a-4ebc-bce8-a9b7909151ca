package com.solum.xplain.support.migration.changeunits.v_2_02_0;

import com.solum.xplain.support.migration.changeunits.ChangeUnitSupport;
import io.mongock.api.annotations.BeforeExecution;
import io.mongock.api.annotations.ChangeUnit;
import io.mongock.api.annotations.Execution;
import io.mongock.api.annotations.RollbackBeforeExecution;
import io.mongock.api.annotations.RollbackExecution;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;

/** Adds an index on the companyLegalEntityNav collection which improves NAV lookups. */
@RequiredArgsConstructor
@ChangeUnit(order = "v02.02.00_03", id = "v2.02.0_03", author = "xplain")
@Slf4j
public class CU03AddEntityLevelNavIndex {
  static final String COLLECTION = "companyLegalEntityNav";
  static final Map<String, Sort.Direction> INDEX_DEFINITION =
      Map.of(
          "date", Sort.Direction.DESC,
          "groupId", Sort.Direction.ASC,
          "archivedAt", Sort.Direction.ASC,
          "key", Sort.Direction.ASC);
  static final String[] FIELDS = INDEX_DEFINITION.keySet().toArray(new String[0]);
  static final String INDEX = ChangeUnitSupport.indexName(FIELDS);

  private final MongoTemplate mongoTemplate;

  @BeforeExecution
  public void beforeExecution() {

    var hasIndex = ChangeUnitSupport.indexExists(mongoTemplate, COLLECTION, INDEX, FIELDS);

    if (hasIndex) {
      log.warn(
          "Index {} (or equivalent) already exists on {} collection, skipping creation.",
          INDEX,
          COLLECTION);
      return;
    }

    ChangeUnitSupport.createIndex(mongoTemplate, COLLECTION, INDEX, INDEX_DEFINITION);
  }

  @Execution
  public void execute() {
    // Execution not needed
  }

  @RollbackExecution
  public void rollback() {
    // Rollback not needed
  }

  @RollbackBeforeExecution
  public void rollbackBefore() {
    // Rollback not needed
  }
}
