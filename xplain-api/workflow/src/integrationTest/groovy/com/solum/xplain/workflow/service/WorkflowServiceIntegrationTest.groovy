//file:noinspection UnnecessaryQualifiedReference
package com.solum.xplain.workflow.service

import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.workflow.value.AssignmentRuleView.assignableTo
import static com.solum.xplain.workflow.value.AssignmentRuleView.candidateTeams
import static com.solum.xplain.workflow.value.AssignmentRuleView.excludedUsers
import static com.solum.xplain.workflow.value.BoundaryEventView.boundaryEvent
import static com.solum.xplain.workflow.value.ProcessDefinitionView.process
import static com.solum.xplain.workflow.value.ProcessFlowView.from
import static com.solum.xplain.workflow.value.StartEventView.startEvent
import static com.solum.xplain.workflow.value.UserTaskDefinitionView.withInputField
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.solum.xplain.core.error.Error
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.viewconfig.value.FieldType
import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.event.ProcessDoneEvent
import com.solum.xplain.workflow.event.StepDoneEvent
import com.solum.xplain.workflow.provider.WorkflowProvider
import com.solum.xplain.workflow.repository.DataModificationCommandQueue
import com.solum.xplain.workflow.service.command.ProcessSetContext
import com.solum.xplain.workflow.service.command.StepBulkCompareAndSetStatus
import com.solum.xplain.workflow.service.command.StepBulkSetAssignee
import com.solum.xplain.workflow.service.command.StepBulkSetOutcome
import com.solum.xplain.workflow.service.command.StepCreate
import com.solum.xplain.workflow.service.command.StepSetStatus
import com.solum.xplain.workflow.service.command.StepUpdateOutcome
import com.solum.xplain.workflow.value.CatchingEffect
import com.solum.xplain.workflow.value.EventType
import com.solum.xplain.workflow.value.ProcessContextMutator
import com.solum.xplain.workflow.value.ProcessDefinitionView
import com.solum.xplain.workflow.value.WorkflowStatus
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.Semaphore
import java.util.concurrent.TimeUnit
import java.util.function.Predicate
import org.bson.types.ObjectId
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.spockframework.spring.SpringSpy
import org.springframework.beans.MutablePropertyValues
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.event.EventListener
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.test.context.ActiveProfiles
import spock.lang.Timeout

@SpringBootTest
@ActiveProfiles("test")
class WorkflowServiceIntegrationTest extends IntegrationSpecification {
  static Logger log = LoggerFactory.getLogger(WorkflowServiceIntegrationTest)

  @SpringSpy
  DataModificationCommandQueue commandQueue

  @Autowired
  WorkflowService workflowService

  @Autowired
  MongoOperations mongoOperations

  @Autowired
  TestEventReceiver testEventReceiver

  def user = user("userId")

  @TestConfiguration
  static class TestConfig {
    @Bean
    TestEventReceiver testEventReceiver() {
      return new TestEventReceiver()
    }

    @Bean
    TestProcessProvider testProcessProvider() {
      return new TestProcessProvider()
    }
  }

  static class TestProcessProvider implements WorkflowProvider {
    def simpleWorkflow = process("testProcess")
    .startWith("start")
    .serviceStep("step1", { StepStateOps<Context, State> ops ->
      if (ops.getContext().step1ThrowsError) {
        ops.throwError(new ErrorItem(Error.OPERATION_NOT_ALLOWED, "step1ThrowsError is true"))
      }
      ops.setOutcome(new MutablePropertyValues(Map.of("step1Outcome", "aaa")))
    }, false)
    .exclusiveGateway("checkStep1")
    .serviceStep("step2", { StepStateOps<Context, State> ops ->
      ops.setOutcome(new MutablePropertyValues(Map.of("step2Outcome", "bbb")))
    }, false)
    .serviceStep("step3", { StepStateOps<Context, State> ops ->
      ops.setOutcome(new MutablePropertyValues(Map.of("step3Outcome", "ccc")))
    }, true)
    .userTask("step4-userTask",
    assignableTo(candidateTeams("{'667aa1bf2e9cd34d9107a225'}"), excludedUsers("{'userId'}")),
    withInputField("input", FieldType.STRING).asProperty("step4Outcome")
    )
    .serviceStep("stepX", { StepStateOps<Context, State> ops ->
      ops.setOutcome(new MutablePropertyValues(Map.of("stepXOutcome", "xxx")))
    }, true)
    .startEvents(startEvent("jumpToStep2", EventType.ERROR, CatchingEffect.INTERRUPTING))
    .boundaryEvents(
    boundaryEvent("step1ThrewError", EventType.ERROR, CatchingEffect.INTERRUPTING)
    .attachedTo("step1")
    )
    .flows(
    from("start").to("step1"),
    from("step1").to("checkStep1"),
    from("checkStep1").when("state.step1Outcome == 'aaa'").to("step2"),
    from("checkStep1").to("stepX"),
    from("step2").when("state.step2Outcome != 'bbb'").to("stepX"),
    from("step2").when("state.step3Outcome == 'ccc'").to("stepX"),
    from("step2").when("state.step2Outcome == 'bbb'").to("step3"),
    from("step3").to("step4-userTask"),
    from("jumpToStep2").to("step2"),
    from("step1ThrewError").to("stepX")
    )
    .build()

    @Override
    Collection<ProcessDefinitionView> provideProcessDefinitions() {
      return [simpleWorkflow]
    }
  }

  static class TestEventReceiver {
    Semaphore counter
    List<StepDoneEvent> events
    Semaphore milestone

    Predicate<ProcessDoneEvent> processFilter
    List<ProcessDoneEvent> processDoneEvents = new CopyOnWriteArrayList<>()
    Semaphore processWait

    void configureExpectedSteps(int steps) {
      counter = new Semaphore(1 - steps)
      events = new ArrayList<>(steps)
      milestone = new Semaphore(0)
    }

    void configureExpectedProcess(String businessKey) {
      log.info("Process to complete: {}", businessKey)
      processWait = new Semaphore(0)
      processFilter = { ProcessDoneEvent e -> e.businessKey() == businessKey }
      if (processDoneEvents.stream().anyMatch(processFilter)) {
        log.info("Process was already done when we started waiting")
        processWait.release()
      }
    }

    @EventListener
    void processDone(ProcessDoneEvent e) {
      processDoneEvents.add(e)
      if (processFilter.test(e)) {
        log.info("Process {} done", e.businessKey())
        processWait.release()
      }
    }

    ProcessDoneEvent waitForProcess() {
      log.info("Wait for process to complete")
      if (processWait.tryAcquire(10, TimeUnit.SECONDS)) {
        log.info("Process done")
      } else {
        log.error("Timed out waiting for process")
      }
      return processDoneEvents.stream().filter(processFilter).findFirst().orElse(null)
    }

    @EventListener
    void stepDone(StepDoneEvent e) {
      events.add(e)
      counter.release()
    }

    List<StepDoneEvent> waitForAllSteps() {
      counter.acquire()
      return events
    }

    void waitForMilestone() {
      milestone.acquire()
    }

    void releaseMilestone() {
      milestone.release()
    }
  }

  static class Context implements Serializable {
    boolean step1ThrowsError
  }

  static class State implements Serializable {
    String step1Outcome
    String step2Outcome
    String step3Outcome
    String step4Outcome
  }

  static class FormData implements Serializable {
    String input
  }

  def cleanup() {
    mongoOperations.findAll(StepInstance).each {
      println("Step: $it")
    }
    mongoOperations.remove(new Query(), ProcessExecution)
    mongoOperations.remove(new Query(), StepInstance)
  }

  @Timeout(5) // If this times out, it will mean the async stuff has got stuck somewhere as it should run in <1s.
  def "should execute simple linear workflow"() {
    given:
    def expectedSteps = 5
    testEventReceiver.configureExpectedSteps(expectedSteps - 1)

    when: "workflow is started and we reach user task"
    workflowService.startProcess("testProcess", "testKey", new Context(), State)
    testEventReceiver.waitForMilestone()


    then: "all step commands up to user task have been applied"
    expectedSteps * commandQueue.append(_ as StepCreate) >> { StepCreate step ->
      def result = callRealMethod()
      if (step.def().id() == "step4-userTask") {
        testEventReceiver.releaseMilestone()
      }
      result
    }
    (expectedSteps - 2) * commandQueue.append(_ as StepUpdateOutcome) // Gateway doesn't set outcome
    (expectedSteps - 1) * commandQueue.append(_ as StepSetStatus)

    and: "events up to user task have fired"
    def interimEvents = testEventReceiver.waitForAllSteps()
    interimEvents.size() == expectedSteps - 1
    with (interimEvents[0]) {
      assert stepId() == "step1"
      assert outcome().size() == 1
      assert outcome().contains("step1Outcome")
      assert status() == WorkflowStatus.DONE
    }
    with (interimEvents[1]) {
      assert stepId() == "checkStep1"
      assert outcome().size() == 0
      assert status() == WorkflowStatus.DONE
    }
    with (interimEvents[2]) {
      assert stepId() == "step2"
      assert outcome().size() == 1
      assert outcome().contains("step2Outcome")
      assert status() == WorkflowStatus.DONE
    }
    with (interimEvents[3]) {
      assert stepId() == "step3"
      assert outcome().size() == 1
      assert outcome().contains("step3Outcome")
      assert status() == WorkflowStatus.DONE
    }

    when: "make sure all updates are flushed"
    commandQueue.flush()

    then: "we are in a good state to suspend"
    with (mongoOperations.findOne(query(where(StepInstance.Fields.businessKey).is("testKey")
      .and(StepInstance.Fields.stepId).is("step4-userTask")), StepInstance)) { step4Instance ->
        step4Instance.reportable
        step4Instance.status == WorkflowStatus.ACTIVE
        step4Instance.candidate.includedTeams == [new ObjectId("667aa1bf2e9cd34d9107a225")]
        step4Instance.candidate.excludedUserIds == ["userId"]
      }
    with (mongoOperations.findOne(query(where(ProcessExecution.Fields.businessKey).is("testKey")), ProcessExecution)) {
      it.currentState.step1Outcome == "aaa"
      it.currentState.step2Outcome == "bbb"
      it.currentState.step3Outcome == "ccc"
      it.currentState.step4Outcome == null
      it.status == WorkflowStatus.ACTIVE
    }

    when: "user task is claimed and all updates are flushed"
    workflowService.claimUserTasks("testProcess", ["testKey"], "step4-userTask", AuditUser.of(user))
    waitForExecutionToFinish()
    commandQueue.flush()

    then: "we are in a good state to submit"
    1 * commandQueue.append(_ as StepBulkSetAssignee)
    with (mongoOperations.findOne(query(where(StepInstance.Fields.businessKey).is("testKey")
      .and(StepInstance.Fields.stepId).is("step4-userTask")), StepInstance)) { step4Instance ->
        step4Instance.assignee.userId == user.id
      }

    when: "user task is submitted"
    testEventReceiver.configureExpectedSteps(1)
    if (separateSubmitAndComplete) {
      workflowService.submitUserTaskFormData("testProcess", ["testKey"], "step4-userTask", new FormData(input: "ddd"))
      workflowService.completeUserTasks("testProcess", ["testKey"], "step4-userTask")
    } else {
      workflowService.submitAndCompleteUserTasks("testProcess", ["testKey"], "step4-userTask", new FormData(input: "ddd"))
    }
    def events = testEventReceiver.waitForAllSteps()

    then: "remaining step commands have been applied"
    0 * commandQueue.append(_ as StepCreate)
    (separateSubmitAndComplete ? 0 : 1) * commandQueue.append(_ as StepUpdateOutcome)
    (separateSubmitAndComplete ? 1 : 0) * commandQueue.append(_ as StepBulkSetOutcome)
    1 * commandQueue.append(_ as StepSetStatus)

    and: "all events were fired"
    events.size() == 1
    with (events[0]) {
      assert stepId() == "step4-userTask"
      assert outcome().size() == 1
      assert outcome().contains("step4Outcome")
      assert status() == WorkflowStatus.DONE
    }

    when: "make sure all updates are flushed"
    waitForExecutionToFinish()
    commandQueue.awaitNextFlush()

    then: "database records are as expected"
    mongoOperations.count(query(where(ProcessExecution.Fields.businessKey).is("testKey")), ProcessExecution) == 1
    // Only reportable steps are left in the database at the end
    mongoOperations.count(query(where(StepInstance.Fields.businessKey).is("testKey")), StepInstance) == 2

    with (mongoOperations.findOne(query(where(StepInstance.Fields.businessKey).is("testKey")
      .and(StepInstance.Fields.stepId).is("step4-userTask")), StepInstance)) { step4Instance ->
        step4Instance.reportable
        step4Instance.status == WorkflowStatus.DONE
      }

    with (mongoOperations.findOne(query(where(ProcessExecution.Fields.businessKey).is("testKey")), ProcessExecution)) {
      it.currentState.step1Outcome == "aaa"
      it.currentState.step2Outcome == "bbb"
      it.currentState.step3Outcome == "ccc"
      it.currentState.step4Outcome == "ddd"
      it.status == WorkflowStatus.DONE
    }

    when: "workflow is deleted"
    workflowService.removeProcessHierarchy("testProcess", "testKey")

    then: "database records are gone"
    mongoOperations.count(query(where(ProcessExecution.Fields.rootBusinessKey).is("testKey")), ProcessExecution) == 0
    mongoOperations.count(query(where(StepInstance.Fields.rootBusinessKey).is("testKey")), StepInstance) == 0

    where:
    separateSubmitAndComplete << [false, true]
  }

  @Timeout(5)
  def "should handle restart via event"() {
    given:
    testEventReceiver.configureExpectedSteps(4)
    testEventReceiver.configureExpectedProcess("testKey")

    when: "workflow is started and we reach user task"
    workflowService.startProcess("testProcess", "testKey", new Context(), State)
    testEventReceiver.waitForMilestone()

    then: "all step commands up to user task have been applied"
    5 * commandQueue.append(_ as StepCreate) >> { StepCreate step ->
      def result = callRealMethod()
      if (step.def().id() == "step4-userTask") {
        testEventReceiver.releaseMilestone()
      }
      result
    }
    3 * commandQueue.append(_ as StepUpdateOutcome) // Gateway doesn't set outcome
    4 * commandQueue.append(_ as StepSetStatus)

    when: "event is fired to restart at earlier step but with current state"
    testEventReceiver.waitForAllSteps()
    testEventReceiver.configureExpectedSteps(2)
    workflowService.fireEvents("testProcess", mutator, List.of("testKey"), "jumpToStep2")
    def events = testEventReceiver.waitForAllSteps()

    then: "remaining step commands have been applied"
    1 * commandQueue.append(_ as StepBulkCompareAndSetStatus)
    2 * commandQueue.append(_ as StepUpdateOutcome)
    2 * commandQueue.append(_ as StepSetStatus)
    numUpdateProcessInputCalls * commandQueue.append(_ as ProcessSetContext)

    and: "all events were fired"
    events.size() == 2
    with (events[0]) {
      assert stepId() == "step2"
      assert outcome().size() == 1
      assert outcome().contains("step2Outcome")
      assert status() == WorkflowStatus.DONE
    }
    with (events[1]) {
      assert stepId() == "stepX"
      assert outcome().size() == 1
      assert outcome().contains("stepXOutcome")
      assert status() == WorkflowStatus.DONE
    }

    when: "make sure all updates are flushed"
    commandQueue.flush()

    and: "process is completed"
    testEventReceiver.waitForProcess()
    commandQueue.awaitNextFlush() // because process done is emitted but remove non-reportable may still be in queue

    then: "database records are as expected"
    mongoOperations.count(query(where(ProcessExecution.Fields.businessKey).is("testKey")), ProcessExecution) == 1
    // Only reportable steps are left in the database at the end
    mongoOperations.count(query(where(StepInstance.Fields.businessKey).is("testKey")), StepInstance) == 3

    with (mongoOperations.findOne(query(where(StepInstance.Fields.businessKey).is("testKey")
      .and(StepInstance.Fields.stepId).is("step4-userTask")), StepInstance)) { step4Instance ->
        step4Instance.status == WorkflowStatus.INTERRUPTED
      }

    with (mongoOperations.findOne(query(where(ProcessExecution.Fields.businessKey).is("testKey")), ProcessExecution)) {
      it.currentState.step1Outcome == "aaa"
      it.currentState.step2Outcome == "bbb"
      it.currentState.step3Outcome == "ccc"
      it.currentState.step4Outcome == null
      it.status == WorkflowStatus.DONE
    }

    where:
    mutator                                         | numUpdateProcessInputCalls
    new TestMutator()                               | 0
    new TestMutatorWithMutation() | 1
  }

  @Timeout(5)
  def "should handle step error via boundary event"() {
    given:
    testEventReceiver.configureExpectedSteps(1)
    testEventReceiver.configureExpectedProcess("testKey")

    when: "workflow is started and we divert via boundary handler"
    workflowService.startProcess("testProcess", "testKey", new Context(step1ThrowsError: true), State)
    def events = testEventReceiver.waitForAllSteps()

    then: "all events were fired"
    events.size() == 1
    with (events[0]) {
      assert stepId() == "stepX"
      assert outcome().size() == 1
      assert outcome().contains("stepXOutcome")
      assert status() == WorkflowStatus.DONE
    }

    when: "make sure all updates are flushed"
    commandQueue.flush()
    testEventReceiver.waitForProcess()
    commandQueue.awaitNextFlush()

    then: "database records are as expected"
    mongoOperations.count(query(where(ProcessExecution.Fields.businessKey).is("testKey")), ProcessExecution) == 1
    mongoOperations.count(query(where(StepInstance.Fields.businessKey).is("testKey")), StepInstance) == 2

    with (mongoOperations.findOne(query(where(StepInstance.Fields.businessKey).is("testKey")
      .and(StepInstance.Fields.stepId).is("step1")), StepInstance)) { step1Instance ->
        step1Instance.status == WorkflowStatus.ERROR
      }

    with (mongoOperations.findOne(query(where(ProcessExecution.Fields.businessKey).is("testKey")), ProcessExecution)) {
      it.currentState.step1Outcome == null
      it.status == WorkflowStatus.DONE
    }
  }

  private def waitForExecutionToFinish() {
    workflowService.stepExecutionService.awaitQuiescence(500, TimeUnit.MILLISECONDS)
  }

  class TestMutator implements ProcessContextMutator {
    @Override
    ProcessExecutionContextMutation mutate(ProcessExecution execution) {
      return new ProcessExecutionContextMutation(false, null)
    }
  }

  class TestMutatorWithMutation<C extends Serializable> implements ProcessContextMutator {

    @Override
    ProcessExecutionContextMutation mutate(ProcessExecution execution) {
      return new ProcessExecutionContextMutation(true, new Context())
    }
  }
}
