package com.solum.xplain.workflow.repository

import com.solum.xplain.workflow.entity.CacheableWorkflowEntity
import com.solum.xplain.workflow.value.WorkflowStatus
import jakarta.annotation.Nonnull
import org.bson.types.ObjectId
import org.springframework.cache.Cache
import org.springframework.data.mongodb.core.BulkOperations
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.convert.MongoConverter
import org.springframework.data.mongodb.core.query.Query
import spock.lang.Specification

class DataModificationCommandQueueTest extends Specification {
  MongoOperations mongoOperations = Mock() {
    getConverter() >> Mock(MongoConverter)
  }

  WorkflowDataCacheService workflowDataCacheService = Mock()
  BulkOperations bulkOperations = Mock()
  BulkOperations bulkOperations2 = Mock()
  Cache cache = Mock()
  DataModificationCommandQueue commandQueue = new DataModificationCommandQueue(mongoOperations, workflowDataCacheService)

  class EntityClass implements CacheableWorkflowEntity {
    static boolean cacheable = true
    static WorkflowStatus status = WorkflowStatus.ACTIVE
    @Override
    ObjectId getId() {
      return ObjectId.getSmallestWithDate(new Date())
    }

    @Override
    boolean cacheable() {
      return cacheable
    }

    @Override
    WorkflowStatus getStatus() {
      return status
    }
  }

  class EntityClass2 implements CacheableWorkflowEntity {
    @Override
    ObjectId getId() {
      return ObjectId.getSmallestWithDate(new Date())
    }

    @Override
    boolean cacheable() {
      return false
    }

    @Override
    WorkflowStatus getStatus() {
      return WorkflowStatus.ACTIVE
    }
  }

  class EntityInsertCommand implements CacheSettingDataModificationCommand<EntityClass> {
    @Override
    Class<? super EntityClass> getEntity() {
      return EntityClass
    }

    @Nonnull
    @Override
    EntityClass apply(BulkOperations bulkOps) {
      def inserted = new EntityClass()
      bulkOps.insert(inserted)
      return inserted
    }
  }

  class EntityRemoveAllCommand implements CacheClearingDataModificationCommand<EntityClass> {
    @Override
    Class<? super EntityClass> getEntity() {
      return EntityClass
    }

    @Override
    void apply(BulkOperations bulkOps) {
      bulkOps.remove(new Query())
    }
  }

  def "should create bulk ops for new entity and apply change immediately (cacheable=#cacheable)"() {
    given:
    workflowDataCacheService.getCache(EntityClass) >> cache
    EntityClass.cacheable = cacheable
    EntityClass.status = status

    when: "first occurence of command for entity"
    commandQueue.append(new EntityInsertCommand())
    def bulkOps = commandQueue.bulkOpsCache.get(EntityClass)

    then: "assign bulk ops and update cache"
    bulkOps != null
    (cacheable && status != WorkflowStatus.DONE ? 1 : 0) * cache.put(_ as ObjectId, _ as EntityClass)
    (cacheable && status == WorkflowStatus.DONE ? 1 : 0) * cache.evict(_ as ObjectId)

    when: "second occurence of command for entity"
    commandQueue.append(new EntityInsertCommand())

    then: "use same bulk ops and update cache again"
    commandQueue.bulkOpsCache.get(EntityClass) == bulkOps
    (cacheable && status != WorkflowStatus.DONE ? 1 : 0) * cache.put(_ as ObjectId, _ as EntityClass)
    (cacheable && status == WorkflowStatus.DONE ? 1 : 0) * cache.evict(_ as ObjectId)

    where:
    cacheable | status
    true      | WorkflowStatus.ACTIVE
    false     | WorkflowStatus.ACTIVE
    true      | WorkflowStatus.FINALIZING
    false     | WorkflowStatus.FINALIZING
    true      | WorkflowStatus.DONE
    false     | WorkflowStatus.DONE
  }

  def "should create bulk ops for bulk change and empty cache"() {
    given:
    workflowDataCacheService.getCache(EntityClass) >> cache

    when: "first occurence of command for entity"
    commandQueue.append(new EntityRemoveAllCommand())

    then: "create bulk ops and update cache"
    def bulkOps = commandQueue.bulkOpsCache.get(EntityClass)
    1 * cache.clear()

    when: "second occurence of command for entity"
    commandQueue.append(new EntityRemoveAllCommand())

    then: "apply command and update cache only"
    commandQueue.bulkOpsCache.get(EntityClass) == bulkOps
    1 * cache.clear()
  }

  def "should execute all bulk ops when flushing"() {
    given:
    commandQueue.with {
      bulkOpsCache.put(EntityClass, bulkOperations)
      bulkOpsCache.put(EntityClass2, bulkOperations2)
    }

    when:
    commandQueue.flush()

    then:
    1 * bulkOperations.execute()
    1 * bulkOperations2.execute()
  }
}
