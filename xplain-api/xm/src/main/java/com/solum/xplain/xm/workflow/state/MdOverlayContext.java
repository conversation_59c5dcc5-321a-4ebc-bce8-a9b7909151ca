package com.solum.xplain.xm.workflow.state;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.curveconfiguration.entity.MarketDataProviders;
import com.solum.xplain.core.mdvalue.value.ValueBidAskType;
import com.solum.xplain.xm.excmngmt.process.data.Instrument;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultBreak;
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultResolution;
import com.solum.xplain.xm.excmngmt.process.data.ProviderData;
import com.solum.xplain.xm.excmngmt.process.value.BreakTestCalculationsOverlay;
import com.solum.xplain.xm.excmngmt.stat.value.CalculationStatisticalZScoreData;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.experimental.FieldNameConstants;

/**
 * Context for the overlay phase of the market data workflow.
 *
 * @param stateDate bitemporal date selected to run the dashboard for
 * @param marketDataGroupId MDG selected to run the dashboard for
 * @param instrument instrument (MDK) this subprocess is cleaning data for
 * @param pricePoint price point (Bid/Mid/Ask) this subprocess is cleaning data for
 * @param curveConfigId ID of curve configuration this subprocess is cleaning data for
 * @param curveConfigName name of curve configuration
 * @param curveGroupId ID of associated curve group
 * @param providers configured provider mapping for this instrument for this curve configuration
 * @param allRawData all unadjusted provider data
 * @param allPreliminaryData preliminary output data for the primary and secondary providers
 * @param preliminaryBreaks tests which caused a break in preliminary phase for the primary provider
 * @param preliminaryResolution resolution applied to the primary provider (may contain null
 *     properties)
 * @param zScoreData
 */
@FieldNameConstants
public record MdOverlayContext(
    BitemporalDate stateDate,
    String marketDataGroupId,
    String marketDataGroupName,
    Instrument instrument,
    ValueBidAskType pricePoint,
    String curveConfigId,
    String curveConfigName,
    String curveGroupId,
    MarketDataProviders providers,
    List<ProviderData> allRawData,
    List<ProviderData> allPreliminaryData,
    List<InstrumentResultBreak> preliminaryBreaks,
    InstrumentResultResolution preliminaryResolution,
    BreakTestCalculationsOverlay breakTestCalculations,
    Map<String, CalculationStatisticalZScoreData> zScoreData)
    implements MdPhaseContext {

  private @Nullable ProviderData findPrimaryProviderData(ValueBidAskType pricePoint) {
    String primaryProvider = providers.getPrimary();
    if (primaryProvider == null) {
      return null;
    }
    return allPreliminaryData.stream()
        .filter(
            data ->
                primaryProvider.equals(data.getProvider()) && pricePoint == data.getBidAskType())
        .findFirst()
        .orElse(null);
  }

  public @Nullable ProviderData getPrimaryProviderData() {
    return findPrimaryProviderData(this.pricePoint);
  }

  public @Nullable ProviderData primaryProviderData(ValueBidAskType pricePoint) {
    return findPrimaryProviderData(pricePoint);
  }

  public @Nullable BigDecimal previousValue() {
    ProviderData providerData = getPrimaryProviderData();
    return providerData != null ? providerData.getPreviousValue() : null;
  }

  public @Nullable ProviderData getSecondaryProviderData() {
    String secondaryProvider = providers.getSecondary();
    if (secondaryProvider == null) {
      return null;
    }
    return allPreliminaryData.stream()
        .filter(
            data ->
                secondaryProvider.equals(data.getProvider())
                    && this.pricePoint == data.getBidAskType())
        .findFirst()
        .orElse(null);
  }
}
