package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryclearing;

import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.dashboards.repository.DashboardRepository;
import com.solum.xplain.xm.dashboardsteps.DashboardEvent;
import com.solum.xplain.xm.dashboardsteps.DashboardEvent.Type;
import com.solum.xplain.xm.dashboardsteps.dashboardfinalize.FinalizeDashboardEvent;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MdBatchPreliminaryClearingEventListener {

  private final DashboardRepository dashboardRepository;
  private final ApplicationEventPublisher publisher;

  private final MdBatchPreliminaryClearingStepsInitializer clearingStepsInitializer;
  private final MdBatchPreliminaryClearingStepsFinalizer clearingStepsFinalizer;
  private final MdBatchPreliminaryClearingCleanup stepCleanup;

  public MdBatchPreliminaryClearingEventListener(
      DashboardRepository dashboardRepository,
      ApplicationEventPublisher publisher,
      MdBatchPreliminaryClearingStepsInitializer clearingStepsInitializer,
      MdBatchPreliminaryClearingStepsFinalizer clearingStepsFinalizer,
      MdBatchPreliminaryClearingCleanup stepCleanup) {
    this.dashboardRepository = dashboardRepository;
    this.publisher = publisher;
    this.clearingStepsInitializer = clearingStepsInitializer;
    this.clearingStepsFinalizer = clearingStepsFinalizer;
    this.stepCleanup = stepCleanup;
  }

  @EventListener
  public void onEvent(MdBatchPreliminaryClearingEvent event) {
    log.debug("Received MdBatchPreliminaryClearingEvent: {}", event);
    if (DashboardEvent.Type.REQUESTED == event.getType()) {
      dashboard(event.getDashboardId()).ifPresent(this::initializeClearing);
    } else if (DashboardEvent.Type.UPDATED == event.getType()) {
      dashboard(event.getDashboardId()).ifPresent(this::finalizeClearing);
    } else if (Type.COMPLETED == event.getType()) {
      dashboard(event.getDashboardId()).ifPresent(this::finalizeDashboard);
    }
  }

  private void initializeClearing(Dashboard dashboard) {
    var completed =
        clearingStepsInitializer
            .execute(dashboard)
            .toOptional()
            .filter(step -> step.getStatus() == StepStatus.COMPLETED)
            .isPresent();

    if (dashboard(dashboard.getId()).isEmpty()) {
      stepCleanup.execute(dashboard.getId());
    }
    if (completed) {
      onClearingCompleted(dashboard);
    }
  }

  private void finalizeClearing(Dashboard dashboard) {
    clearingStepsFinalizer
        .execute(dashboard)
        .toOptional()
        .filter(step -> step.getStatus() == StepStatus.COMPLETED)
        .ifPresent(step -> onClearingCompleted(dashboard));
  }

  private void onClearingCompleted(Dashboard dashboard) {
    publisher.publishEvent(
        MdBatchPreliminaryClearingEvent.builder()
            .dashboardId(dashboard.getId())
            .type(DashboardEvent.Type.COMPLETED)
            .build());
  }

  private void finalizeDashboard(Dashboard dashboard) {
    publisher.publishEvent(new FinalizeDashboardEvent(dashboard.getId()));
  }

  private Optional<Dashboard> dashboard(String dashboardId) {
    return dashboardRepository.dashboard(dashboardId).toOptional();
  }
}
