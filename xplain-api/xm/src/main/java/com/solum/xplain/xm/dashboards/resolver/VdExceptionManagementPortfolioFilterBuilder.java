package com.solum.xplain.xm.dashboards.resolver;

import static com.solum.xplain.core.portfolio.value.PortfolioFilter.activePortfolios;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot;
import com.solum.xplain.core.portfolio.PortfolioTeamFilterProvider;
import com.solum.xplain.core.portfolio.repository.PortfolioRepository;
import com.solum.xplain.core.portfolio.value.PortfolioCondensedView;
import com.solum.xplain.xm.dashboards.entity.filter.CompanyEntitiesPortfolioFilter;
import com.solum.xplain.xm.dashboards.entity.filter.VdExceptionManagementPortfolioFilter;
import com.solum.xplain.xm.dashboards.forms.PricingSlotPortfolioForm;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class VdExceptionManagementPortfolioFilterBuilder {
  private static final String ALL_PRICING_SLOTS_FILTER = "All / All / All / All";
  private static final String PRICING_SLOT_FILTER_TEMPLATE = "%s / %s";

  private final PortfolioTeamFilterProvider teamFilterProvider;
  private final PortfolioRepository portfolioRepository;
  private final PortfoliosFilterBuilder filterBuilder;

  public VdExceptionManagementPortfolioFilter build(List<PricingSlotPortfolioForm> portfolios) {
    var filter = teamFilterProvider.provideFilter();
    var allPortfolios = portfolioRepository.portfolioCondensedViewList(activePortfolios(), filter);

    var pricingSlotFilters = buildPricingSlots(portfolios, allPortfolios);
    var labels = buildLabels(pricingSlotFilters, allPortfolios);
    var portfoliosMap =
        portfolios.stream()
            .collect(
                Collectors.groupingBy(
                    PricingSlotPortfolioForm::portfolioId,
                    mapping(PricingSlotPortfolioForm::pricingSlot, toList())));
    return VdExceptionManagementPortfolioFilter.newOf(portfoliosMap, labels);
  }

  private Map<PricingSlot, List<CompanyEntitiesPortfolioFilter>> buildPricingSlots(
      List<PricingSlotPortfolioForm> portfolios, List<PortfolioCondensedView> allPortfolios) {
    return portfolios.stream()
        .collect(
            groupingBy(
                PricingSlotPortfolioForm::pricingSlot,
                Collectors.mapping(PricingSlotPortfolioForm::portfolioId, toList())))
        .entrySet()
        .stream()
        .collect(Collectors.toMap(Entry::getKey, v -> filter(v.getValue(), allPortfolios)));
  }

  private List<CompanyEntitiesPortfolioFilter> filter(
      List<String> portfolioIds, List<PortfolioCondensedView> allPortfolios) {
    return filterBuilder.buildCompanyEntities(portfolioIds, allPortfolios);
  }

  private List<String> buildLabels(
      Map<PricingSlot, List<CompanyEntitiesPortfolioFilter>> filters,
      List<PortfolioCondensedView> allPortfolios) {
    if (filters.size() == PricingSlot.values().length) {
      return List.of(ALL_PRICING_SLOTS_FILTER);
    } else {
      return filters.entrySet().stream()
          .flatMap(
              f ->
                  filterBuilder.buildCompanyLabels(f.getValue(), allPortfolios).stream()
                      .map(label -> formatLabel(f.getKey(), label)))
          .toList();
    }
  }

  private String formatLabel(PricingSlot pricingSlot, String companyLabel) {
    return PRICING_SLOT_FILTER_TEMPLATE.formatted(pricingSlot.getLabel(), companyLabel);
  }
}
