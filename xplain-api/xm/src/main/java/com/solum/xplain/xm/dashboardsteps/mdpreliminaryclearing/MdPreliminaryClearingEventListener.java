package com.solum.xplain.xm.dashboardsteps.mdpreliminaryclearing;

import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.dashboards.repository.DashboardRepository;
import com.solum.xplain.xm.dashboardsteps.DashboardEvent;
import com.solum.xplain.xm.dashboardsteps.mdoverlayrun.MdOverlayRunEvent;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MdPreliminaryClearingEventListener {

  private final DashboardRepository dashboardRepository;
  private final ApplicationEventPublisher publisher;

  private final MdPreliminaryClearingStepsInitializer clearingStepsInitializer;
  private final MdPreliminaryClearingStepsFinalizer clearingStepsFinalizer;

  private final MdPreliminaryClearingCleanup stepCleanup;

  public MdPreliminaryClearingEventListener(
      DashboardRepository dashboardRepository,
      ApplicationEventPublisher publisher,
      MdPreliminaryClearingStepsInitializer clearingStepsInitializer,
      MdPreliminaryClearingStepsFinalizer clearingStepsFinalizer,
      MdPreliminaryClearingCleanup stepCleanup) {
    this.dashboardRepository = dashboardRepository;
    this.publisher = publisher;
    this.clearingStepsInitializer = clearingStepsInitializer;
    this.clearingStepsFinalizer = clearingStepsFinalizer;
    this.stepCleanup = stepCleanup;
  }

  @EventListener
  public void onEvent(MdPreliminaryClearingEvent event) {
    log.debug("Received MdPreliminaryClearingEvent: {}", event);
    if (DashboardEvent.Type.REQUESTED == event.getType()) {
      dashboard(event.getDashboardId()).ifPresent(this::initializeClearing);
    } else if (DashboardEvent.Type.UPDATED == event.getType()) {
      dashboard(event.getDashboardId()).ifPresent(this::finalizeClearing);
    } else if (DashboardEvent.Type.COMPLETED == event.getType()) {
      publisher.publishEvent(
          MdOverlayRunEvent.builder()
              .dashboardId(event.getDashboardId())
              .type(DashboardEvent.Type.REQUESTED)
              .build());
    }
  }

  private void initializeClearing(Dashboard dashboard) {
    clearingStepsInitializer
        .execute(dashboard)
        .toOptional()
        .ifPresent(steps -> processClearingSteps(dashboard, steps));
    if (dashboard(dashboard.getId()).isEmpty()) {
      stepCleanup.execute(dashboard.getId());
    }
  }

  private void processClearingSteps(Dashboard dashboard, List<DashboardEntryMd> steps) {
    steps.stream()
        .map(s -> StepStatus.COMPLETED == s.getStatus())
        .reduce(Boolean::logicalAnd)
        .filter(BooleanUtils::isTrue)
        .ifPresent(r -> onClearingCompleted(dashboard));
  }

  private void finalizeClearing(Dashboard dashboard) {
    clearingStepsFinalizer
        .execute(dashboard)
        .toOptional()
        .filter(ee -> ee.stream().allMatch(e -> e.getStatus() == StepStatus.COMPLETED))
        .ifPresent(steps -> onClearingCompleted(dashboard));
  }

  private void onClearingCompleted(Dashboard dashboard) {
    publisher.publishEvent(
        MdPreliminaryClearingEvent.builder()
            .dashboardId(dashboard.getId())
            .type(DashboardEvent.Type.COMPLETED)
            .build());
  }

  private Optional<Dashboard> dashboard(String dashboardId) {
    return dashboardRepository.dashboard(dashboardId).toOptional();
  }
}
