package com.solum.xplain.xm.workflow.steps.vd.resolution;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.excmngmt.enums.EntryResultStatus;
import com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.VdPhaseContext;
import com.solum.xplain.xm.workflow.state.VdPhaseState;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DoNotPriceStep implements ServiceStepExecutor<VdPhaseState, VdPhaseContext> {
  @Override
  public void runStep(StepStateOps<VdPhaseState, VdPhaseContext> ops) {
    ops.setOutcome(
        new MutablePropertyValues()
            .add(
                VdPhaseState.Fields.valuePendingApproval,
                new AttributedValue(null, IpvProvidersType.DO_NOT_PRICE, null))
            .add(VdPhaseState.Fields.entryStatus, EntryResultStatus.WAITING_APPROVAL));
  }
}
