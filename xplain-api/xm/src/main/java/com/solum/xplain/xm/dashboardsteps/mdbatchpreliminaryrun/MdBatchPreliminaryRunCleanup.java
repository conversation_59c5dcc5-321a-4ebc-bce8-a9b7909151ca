package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryrun;

import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository;
import com.solum.xplain.xm.dashboardsteps.DashboardStepCleanup;
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class MdBatchPreliminaryRunCleanup implements DashboardStepCleanup {

  private final ExceptionManagementCalculationRepository repository;
  private final DashboardEntryRepository entryRepository;

  @Override
  public void execute(String dashboardId) {
    repository.deletePreliminary(dashboardId);
    entryRepository.deleteMdBatchEntries(dashboardId, DashboardStep.MD_BATCH_PRELIMINARY_RUN);
  }
}
