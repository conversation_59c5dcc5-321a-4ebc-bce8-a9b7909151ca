package com.solum.xplain.xm.dashboardsteps.mdoverlayclearing;

import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository;
import com.solum.xplain.xm.dashboardsteps.DashboardStepCleanup;
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class MdOverlayClearingCleanup implements DashboardStepCleanup {

  private final TaskExecutionRepository taskExecutionRepository;
  private final DashboardEntryRepository entryRepository;

  @Override
  public void execute(String dashboardId) {
    taskExecutionRepository.overlayDeleted(dashboardId);
    entryRepository.deleteMdEntries(dashboardId, DashboardStep.MD_OVERLAY_CLEARING);
  }
}
