package com.solum.xplain.xm.dashboardsteps.mdpreliminaryclearing;

import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd;
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor;
import com.solum.xplain.xm.excmngmt.process.ExceptionManagementCalculationRepository;
import com.solum.xplain.xm.excmngmt.process.data.ExceptionManagementResult;
import com.solum.xplain.xm.tasks.entity.TaskExecution;
import com.solum.xplain.xm.tasks.enums.TaskExecutionStatus;
import com.solum.xplain.xm.tasks.service.MdTaskExecutionService;
import io.atlassian.fugue.Either;
import java.util.List;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

@Component
public class MdPreliminaryClearingStepsInitializer {

  private final DashboardStepProcessor processor;
  private final MdTaskExecutionService taskExecutionService;
  private final ExceptionManagementCalculationRepository exceptionManagementRepository;

  public MdPreliminaryClearingStepsInitializer(
      DashboardStepProcessor processor,
      MdTaskExecutionService taskExecutionService,
      ExceptionManagementCalculationRepository exceptionManagementRepository) {
    this.processor = processor;
    this.taskExecutionService = taskExecutionService;
    this.exceptionManagementRepository = exceptionManagementRepository;
  }

  public Either<List<ErrorItem>, List<DashboardEntryMd>> execute(Dashboard dashboard) {
    var stateDate = processor.getStateDate();
    return exceptionManagementRepository
        .entityByDashboard(dashboard.getId())
        .leftMap(ErrorItem.ListOfErrors::from)
        .flatMap(r -> process(dashboard, r, stateDate));
  }

  private Either<List<ErrorItem>, List<DashboardEntryMd>> process(
      Dashboard dashboard, ExceptionManagementResult result, BitemporalDate stateDate) {
    var taskExecutions = taskExecutionService.createTaskExecutions(dashboard, result, stateDate);
    var step = step(stateDate, result, taskExecutions);
    return processor.createMdSteps(List.of(step));
  }

  private DashboardEntryMd step(
      BitemporalDate stateDate, ExceptionManagementResult result, List<TaskExecution> tasks) {
    var breaksCount = breaksCount(tasks);
    var step =
        DashboardEntryMd.newOfPreliminaryClearing(
                result.getDashboardId(), result.getId(), breaksCount)
            .started(stateDate);
    if (isStepCompleted(tasks)) {
      step.completed();
    }
    return (DashboardEntryMd) step;
  }

  private boolean isStepCompleted(List<TaskExecution> tasks) {
    return tasks.stream().allMatch(e -> e.getStatus() == TaskExecutionStatus.APPROVED);
  }

  private Long breaksCount(List<TaskExecution> tasks) {
    return tasks.stream()
        .map(t -> ObjectUtils.defaultIfNull(t.getBreaksCount(), 0L))
        .reduce(0L, Long::sum);
  }
}
