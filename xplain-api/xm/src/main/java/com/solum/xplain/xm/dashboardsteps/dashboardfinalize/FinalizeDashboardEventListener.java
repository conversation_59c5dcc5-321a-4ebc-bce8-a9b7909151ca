package com.solum.xplain.xm.dashboardsteps.dashboardfinalize;

import com.solum.xplain.xm.dashboards.repository.DashboardEntryRepository;
import com.solum.xplain.xm.dashboards.repository.DashboardRepository;
import com.solum.xplain.xm.dashboardsteps.DashboardEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class FinalizeDashboardEventListener extends DashboardEventListener {

  public FinalizeDashboardEventListener(
      DashboardRepository dashboardRepository, DashboardEntryRepository entryRepository) {
    super(dashboardRepository, entryRepository);
  }

  @Async
  @EventListener
  public void onEvent(FinalizeDashboardEvent event) {
    log.debug("Received FinalizeDashboardEvent: {}", event);
    try {
      dashboardRepository
          .dashboard(event.getDashboardId())
          .toOptional()
          .filter(this::dashboardCompleted)
          .ifPresent(d -> dashboardRepository.markFinished(d.getId()));
    } catch (Exception e) {
      log.error("Failed to finalize dashboard {}", event.getDashboardId(), e);
      throw e;
    }
  }
}
