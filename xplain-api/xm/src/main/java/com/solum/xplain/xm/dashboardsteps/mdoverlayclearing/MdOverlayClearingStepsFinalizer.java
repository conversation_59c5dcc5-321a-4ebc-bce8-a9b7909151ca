package com.solum.xplain.xm.dashboardsteps.mdoverlayclearing;

import static java.util.stream.Collectors.toList;

import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd;
import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.dashboardsteps.DashboardStepProcessor;
import com.solum.xplain.xm.tasks.repository.TaskExecutionRepository;
import com.solum.xplain.xm.tasks.value.UniqueExcptMngmntTask;
import io.atlassian.fugue.Either;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;

@Component
public class MdOverlayClearingStepsFinalizer {

  private final DashboardStepProcessor processor;
  private final TaskExecutionRepository taskExecutionRepository;

  public MdOverlayClearingStepsFinalizer(
      DashboardStepProcessor processor, TaskExecutionRepository taskExecutionRepository) {
    this.processor = processor;
    this.taskExecutionRepository = taskExecutionRepository;
  }

  public Either<List<ErrorItem>, List<DashboardEntryMd>> execute(
      Dashboard dashboard, MdOverlayClearingEvent event) {
    return completeSteps(clearingSteps(dashboard), event);
  }

  private List<DashboardEntryMd> clearingSteps(Dashboard dashboard) {
    return processor.getMdSteps(dashboard).stream()
        .filter(e -> e.getStep() == DashboardStep.MD_OVERLAY_CLEARING)
        .toList();
  }

  private Either<List<ErrorItem>, List<DashboardEntryMd>> completeSteps(
      List<DashboardEntryMd> clearingSteps, MdOverlayClearingEvent event) {
    return clearingSteps.stream()
        .filter(e -> e.getStatus() == StepStatus.IN_PROGRESS)
        .filter(
            e ->
                e.matches(
                    event.getCompanyId(),
                    event.getLegalEntityId(),
                    event.getCurveConfigurationId()))
        .filter(this::isStepCompleted)
        .map(DashboardEntryMd::completed)
        .map(DashboardEntryMd.class::cast)
        .collect(Collectors.collectingAndThen(toList(), processor::updateMdSteps))
        .map(updatedSteps -> clearingSteps);
  }

  private boolean isStepCompleted(DashboardEntryMd entry) {
    var curveConfigurationId = entry.curveConfigurationReferenceId();
    var companyReferenceId = entry.companyReferenceId();
    var legalEntityReferenceId = entry.entityReferenceId();

    var uniqueTask =
        UniqueExcptMngmntTask.overlayTask(
            entry.getDashboardId(),
            entry.getResultId(),
            curveConfigurationId,
            companyReferenceId,
            legalEntityReferenceId);
    return taskExecutionRepository.allTasksCompleted(uniqueTask);
  }
}
