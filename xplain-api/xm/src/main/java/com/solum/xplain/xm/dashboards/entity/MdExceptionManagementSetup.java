package com.solum.xplain.xm.dashboards.entity;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

@Data
@AllArgsConstructor
@FieldNameConstants
public class MdExceptionManagementSetup {

  @NonNull private EntityReference marketDataGroup;

  @NonNull private List<CurveConfigurationInstrumentResolver> curveConfigurationResolvers;

  @Nullable private LocalDate previousDate;

  private Boolean relevantOnly;

  /**
   * This is all entities with their respective view. This has to be saved this way instead of a
   * Map<MdValuationSettingView, List<String>> because mongo doesn't support complex key in maps
   *
   * <p>This is marked as nullable because legacy dashboard would not be able to load from mongo
   * otherwise.
   *
   * @see com.solum.xplain.core.company.value.CompanyValuationSettingsResolver
   */
  @Nullable private List<MdUniqueValuationSettings> applicableLegalEntitiesSettings;

  public static MdExceptionManagementSetup newOf(
      @NonNull EntityReference marketDataGroup,
      @NonNull List<CurveConfigurationInstrumentResolver> curveConfigurationResolvers,
      @Nullable LocalDate previousDate,
      boolean relevantOnly) {
    return new MdExceptionManagementSetup(
        marketDataGroup,
        curveConfigurationResolvers,
        previousDate,
        relevantOnly,
        Collections.emptyList());
  }

  public static MdExceptionManagementSetup newOf(
      @NonNull EntityReference marketDataGroup,
      @NonNull List<CurveConfigurationInstrumentResolver> curveConfigurationResolvers,
      @Nullable LocalDate previousDate,
      boolean relevantOnly,
      @NonNull Map<MdValuationSetting, List<String>> applicableLegalEntitiesSettings) {
    return new MdExceptionManagementSetup(
        marketDataGroup,
        curveConfigurationResolvers,
        previousDate,
        relevantOnly,
        applicableLegalEntitiesSettings.entrySet().stream()
            .map(it -> new MdUniqueValuationSettings(it.getKey(), it.getValue()))
            .toList());
  }
}
