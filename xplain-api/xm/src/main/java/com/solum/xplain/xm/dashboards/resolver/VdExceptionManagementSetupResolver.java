package com.solum.xplain.xm.dashboards.resolver;

import static com.solum.xplain.core.common.CollectionUtils.convertCollectionTo;
import static com.solum.xplain.core.error.Error.OBJECT_NOT_FOUND;
import static java.util.stream.Collectors.groupingBy;

import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.EitherUtils;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.CompanyPortfolioSettingsResolver;
import com.solum.xplain.core.company.value.CompanyLegalEntitySettingsView;
import com.solum.xplain.core.company.value.IpvValuationSettingsView;
import com.solum.xplain.core.company.value.IpvValuationsProvidersView;
import com.solum.xplain.core.company.value.PortfolioSettings;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.group.IpvDataGroupRepository;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupCondensedView;
import com.solum.xplain.core.ipv.group.value.IpvDataGroupReference;
import com.solum.xplain.xm.dashboards.entity.IpvDataGroupProducts;
import com.solum.xplain.xm.dashboards.entity.PortfolioDashboardSettings;
import com.solum.xplain.xm.dashboards.entity.VdExceptionManagementSetup;
import com.solum.xplain.xm.dashboards.entity.filter.VdExceptionManagementPortfolioFilter;
import com.solum.xplain.xm.dashboards.validator.VdExceptionManagementSetupValidator;
import io.atlassian.fugue.Either;
import io.atlassian.fugue.Eithers;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class VdExceptionManagementSetupResolver {

  private static final String NO_PORTFOLIO_SETTINGS_ERROR =
      "No portfolio valuation settings were found: No settings matching portfolios filter";

  private final CompanyPortfolioSettingsResolver settingsResolver;
  private final IpvDataGroupRepository ipvDataGroupRepository;
  private final VdExceptionManagementSetupValidator vdSetupValidator;

  public Either<List<ErrorItem>, VdExceptionManagementSetup> resolve(
      BitemporalDate stateDate,
      LocalDate dashboardDate,
      VdExceptionManagementPortfolioFilter portfoliosFilter) {

    return filteredSettings(stateDate, portfoliosFilter)
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(settings -> vdSetup(portfoliosFilter, settings))
        .flatMap(s -> validSetup(stateDate, dashboardDate, s));
  }

  private Either<ErrorItem, List<PortfolioSettings<PortfolioDashboardSettings>>> filteredSettings(
      BitemporalDate stateDate, VdExceptionManagementPortfolioFilter portfoliosFilter) {
    var filteredSettings = portfoliosSettings(stateDate, portfoliosFilter);
    return Eithers.cond(
        !filteredSettings.isEmpty(),
        OBJECT_NOT_FOUND.entity(NO_PORTFOLIO_SETTINGS_ERROR),
        filteredSettings);
  }

  private List<PortfolioSettings<PortfolioDashboardSettings>> portfoliosSettings(
      BitemporalDate stateDate, VdExceptionManagementPortfolioFilter portfoliosFilter) {
    var pricingSlotFn = pricingSlotResolverFn();
    return settingsResolver
        .portfoliosSettings(stateDate, portfoliosFilter.getPortfolios().keySet())
        .stream()
        .flatMap(s -> toPortfolioIpvValuationSettings(s, portfoliosFilter, pricingSlotFn).stream())
        .toList();
  }

  private Function<String, IpvDataGroupReference> pricingSlotResolverFn() {
    var ipvDataGroups =
        CollectionUtils.toMap(
            ipvDataGroupRepository.allIpvDataGroupCondensedViews(),
            IpvDataGroupCondensedView::getId);
    return ipvDataGroupId ->
        Optional.ofNullable(ipvDataGroups.get(ipvDataGroupId))
            .map(
                group ->
                    IpvDataGroupReference.newOf(
                        group.getId(), group.getName(), group.getPricingSlot()))
            .orElse(null);
  }

  private VdExceptionManagementSetup vdSetup(
      VdExceptionManagementPortfolioFilter filter,
      List<PortfolioSettings<PortfolioDashboardSettings>> portfoliosSettings) {
    var settings = convertCollectionTo(portfoliosSettings, PortfolioSettings::getSettings);
    return VdExceptionManagementSetup.newOf(filter, settings);
  }

  private Either<List<ErrorItem>, VdExceptionManagementSetup> validSetup(
      BitemporalDate stateDate, LocalDate dashboardDate, VdExceptionManagementSetup setup) {
    return EitherUtils.errorsOrRight(
        vdSetupValidator.validate(stateDate, dashboardDate, setup), setup);
  }

  private Optional<PortfolioSettings<PortfolioDashboardSettings>> toPortfolioIpvValuationSettings(
      PortfolioSettings<CompanyLegalEntitySettingsView> portfolioSettings,
      VdExceptionManagementPortfolioFilter portfoliosFilter,
      Function<String, IpvDataGroupReference> pricingSlotFn) {
    var portfolioId = portfolioSettings.getView().getId();
    var groupProducts =
        Optional.ofNullable(portfolioSettings.getSettings().getIpvSettings())
            .map(IpvValuationSettingsView::getProducts)
            .stream()
            .map(Map::entrySet)
            .flatMap(Collection::stream)
            .filter(i -> validIpvDataGroup(i.getValue()))
            .collect(
                groupingBy(
                    i -> pricingSlotFn.apply(i.getValue().getIpvDataGroupView().getEntityId())));

    var products =
        groupProducts.entrySet().stream()
            .filter(v -> portfoliosFilter.matches(portfolioId, v.getKey().getPricingSlot()))
            .map(
                e ->
                    IpvDataGroupProducts.newOf(
                        e.getKey(),
                        e.getValue().stream()
                            .collect(Collectors.toMap(Entry::getKey, Entry::getValue))))
            .toList();

    //    if (products.isEmpty()) {
    //      return Optional.empty();
    //    }

    var settings =
        PortfolioSettings.settings(
            portfolioSettings.getView(), settings(portfolioSettings, products));
    return Optional.of(settings);
  }

  private boolean validIpvDataGroup(IpvValuationsProvidersView ipvValuationsProvidersView) {
    return ipvValuationsProvidersView.getIpvDataGroupView() != null
        && ipvValuationsProvidersView.getIpvDataGroupView().getEntityId() != null;
  }

  private PortfolioDashboardSettings settings(
      PortfolioSettings<CompanyLegalEntitySettingsView> portfolioSettings,
      List<IpvDataGroupProducts> pricingSlots) {
    var e = new PortfolioDashboardSettings();
    e.setCompany(
        EntityReference.newOf(
            portfolioSettings.getView().getCompanyId(),
            portfolioSettings.getView().getExternalCompanyId()));
    e.setEntity(
        EntityReference.newOf(
            portfolioSettings.getView().getEntityId(),
            portfolioSettings.getView().getExternalEntityId()));
    e.setPortfolio(
        EntityReference.newOf(
            portfolioSettings.getView().getId(),
            portfolioSettings.getView().getExternalPortfolioId()));
    // Market data
    e.setMarketDataGroup(
        EntityReference.newOfNullable(
            portfolioSettings.getSettings().getValuationSettings().getMarketDataGroupId(),
            portfolioSettings.getSettings().getValuationSettings().getMarketDataGroupName()));

    // IPV
    e.setIpvDataGroupProducts(pricingSlots);
    e.setSlaDeadline(portfolioSettings.getSettings().getIpvSettings().getSlaDeadline());
    return e;
  }
}
