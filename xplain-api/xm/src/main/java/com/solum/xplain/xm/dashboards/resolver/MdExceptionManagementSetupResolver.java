package com.solum.xplain.xm.dashboards.resolver;

import static com.solum.xplain.core.common.EitherUtils.errorsOrRight;
import static java.util.Optional.ofNullable;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.daterange.DateRange;
import com.solum.xplain.core.common.team.EntityTeamFilter;
import com.solum.xplain.core.common.value.EntityNameView;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.repository.CompanyRepository;
import com.solum.xplain.core.company.repository.CompanyValuationSettingsRepository;
import com.solum.xplain.core.company.value.ValuationSettingsView;
import com.solum.xplain.core.config.properties.PreviewFeatureProperties;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationInstrumentResolver;
import com.solum.xplain.core.curveconfiguration.CurveConfigurationValuationsResolver;
import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup;
import com.solum.xplain.core.error.Error;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.instrument.AssetGroup;
import com.solum.xplain.core.market.repository.MarketDataGroupRepository;
import com.solum.xplain.core.mdvalue.MarketDataValueRepository;
import com.solum.xplain.trs.market.TrsMarketDataGroupRepository;
import com.solum.xplain.trs.value.TrsAssetClassGroup;
import com.solum.xplain.xm.dashboards.entity.MdExceptionManagementSetup;
import com.solum.xplain.xm.dashboards.entity.MdValuationSetting;
import com.solum.xplain.xm.dashboards.entity.TrsMdExceptionManagementSetup;
import com.solum.xplain.xm.dashboards.validator.MdExceptionManagementSetupValidator;
import com.solum.xplain.xm.excmngmt.process.CompanyEntityDataProviderSettingsResolver;
import com.solum.xplain.xm.excmngmt.process.value.LegalEntityTrsDataProviderResolver;
import io.atlassian.fugue.Either;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class MdExceptionManagementSetupResolver {

  private static final List<AssetGroup> TRS_ASSET_GROUPS =
      Arrays.asList(TrsAssetClassGroup.values());
  private static final List<AssetGroup> CORE_ASSET_GROUPS = Arrays.asList(CoreAssetGroup.values());

  private final MarketDataGroupRepository marketDataGroupRepository;
  private final Optional<TrsMarketDataGroupRepository> trsMarketDataGroupRepositoryProvider;
  private final MarketDataValueRepository marketDataValueRepository;
  private final CurveConfigurationValuationsResolver curveConfigurationValuationsResolver;
  private final Optional<CompanyEntityDataProviderSettingsResolver> settingsResolverProvider;
  private final MdExceptionManagementSetupValidator mdSetupValidator;
  private final CompanyRepository companyRepository;
  private final CompanyValuationSettingsRepository companyValuationSettingsRepository;
  private final PreviewFeatureProperties previewFeatureProperties;

  public Either<List<ErrorItem>, Optional<MdExceptionManagementSetup>> resolve(
      BitemporalDate stateDate, LocalDate dashboardDate, String marketDataGroupId) {
    return resolve(stateDate, dashboardDate, marketDataGroupId, false);
  }

  public Either<List<ErrorItem>, Optional<MdExceptionManagementSetup>> resolve(
      BitemporalDate stateDate,
      LocalDate dashboardDate,
      String marketDataGroupId,
      boolean relevantOnly) {
    if (StringUtils.isBlank(marketDataGroupId)) {
      return Either.right(Optional.empty());
    }
    return mdExceptionManagementSetup(stateDate, marketDataGroupId, dashboardDate, relevantOnly)
        .flatMap(s -> validSetup(stateDate, dashboardDate, s))
        .map(Optional::of);
  }

  public Either<List<ErrorItem>, Optional<TrsMdExceptionManagementSetup>> resolveTrs(
      BitemporalDate stateDate, LocalDate dashboardDate, String marketDataGroupId) {
    if (StringUtils.isBlank(marketDataGroupId)
        || trsMarketDataGroupRepositoryProvider.isEmpty()
        || settingsResolverProvider.isEmpty()) {
      return Either.right(Optional.empty());
    }
    return trsMDExceptionManagementSetupEither(stateDate, marketDataGroupId, dashboardDate)
        .flatMap(s -> validSetup(stateDate, dashboardDate, s))
        .map(Optional::of);
  }

  public Either<List<ErrorItem>, Optional<MdExceptionManagementSetup>> resolveBatch(
      BitemporalDate stateDate, DateRange dateRange, String marketDataGroupId) {
    if (StringUtils.isBlank(marketDataGroupId)) {
      return Either.right(Optional.empty());
    }
    return mdExceptionManagementSetup(stateDate, marketDataGroupId, null, false)
        .flatMap(s -> validBatchSetup(stateDate, dateRange, s))
        .map(Optional::of);
  }

  public Either<List<ErrorItem>, Optional<TrsMdExceptionManagementSetup>> resolveBatchTrs(
      BitemporalDate stateDate, DateRange dateRange, String marketDataGroupId) {
    if (StringUtils.isBlank(marketDataGroupId)) {
      return Either.right(Optional.empty());
    }
    return trsMDExceptionManagementSetupEither(stateDate, marketDataGroupId, null)
        .flatMap(s -> validBatchSetup(stateDate, dateRange, s))
        .map(Optional::of);
  }

  private Either<List<ErrorItem>, MdExceptionManagementSetup> mdExceptionManagementSetup(
      BitemporalDate stateDate,
      String marketDataGroupId,
      LocalDate dashboardDate,
      boolean relevantOnly) {
    return marketDataGroup(marketDataGroupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(
            mdg -> {
              if (relevantOnly) {
                return mdSetup(
                    mdg,
                    curveConfigurationResolvers(stateDate, mdg.getId()),
                    previousMarketDataDate(stateDate, dashboardDate, mdg.getId()),
                    applicableSettingsForLegalEntityIds(stateDate, mdg.getId()));
              } else {
                return mdSetup(
                    mdg,
                    curveConfigurationResolvers(stateDate, mdg.getId()),
                    previousMarketDataDate(stateDate, dashboardDate, mdg.getId()));
              }
            });
  }

  private Map<MdValuationSetting, List<String>> applicableSettingsForLegalEntityIds(
      BitemporalDate stateDate, String marketDataGroupId) {
    return companyRepository.companyList(EntityTeamFilter.emptyFilter()).stream()
        .flatMap(
            v ->
                companyValuationSettingsRepository
                    .getCompanyEntitySettingsResolver(v.getId(), stateDate)
                    .allApplicableLegalEntitiesSettings()
                    .filter(settings -> marketDataGroupId.equals(settings.getMarketDataGroupId())))
        .collect(
            Collectors.groupingBy(
                MdValuationSetting::new,
                Collectors.mapping(ValuationSettingsView::getEntityId, Collectors.toList())));
  }

  private Either<List<ErrorItem>, TrsMdExceptionManagementSetup>
      trsMDExceptionManagementSetupEither(
          BitemporalDate stateDate, String marketDataGroupId, LocalDate dashboardDate) {
    return trsMarketDataGroup(marketDataGroupId)
        .leftMap(ErrorItem.ListOfErrors::from)
        .map(
            mdg ->
                trsMdSetup(
                    mdg,
                    dataProvidersSettings(stateDate, mdg.getId()),
                    previousMarketDataDate(stateDate, dashboardDate, mdg.getId())));
  }

  private Either<ErrorItem, EntityNameView> marketDataGroup(String marketDataGroupId) {
    return marketDataGroupRepository
        .dataGroupName(marketDataGroupId)
        .map(Either::<ErrorItem, EntityNameView>right)
        .orElse(Either.left(Error.OBJECT_NOT_FOUND.entity("Market data group not found")));
  }

  private Either<ErrorItem, EntityNameView> trsMarketDataGroup(String marketDataGroupId) {
    return trsMarketDataGroupRepositoryProvider
        .flatMap(repository -> repository.dataGroupName(marketDataGroupId))
        .map(Either::<ErrorItem, EntityNameView>right)
        .orElse(Either.left(Error.OBJECT_NOT_FOUND.entity("TRS Market data group not found")));
  }

  private LocalDate previousMarketDataDate(
      BitemporalDate stateDate, LocalDate dashboardDate, String marketDataGroupId) {
    return ofNullable(dashboardDate)
        .map(date -> BitemporalDate.newOf(date, stateDate.getRecordDate()))
        .flatMap(
            bitemporalDate ->
                marketDataValueRepository
                    .getLatestMarketDataDate(marketDataGroupId, bitemporalDate)
                    .toOptional())
        .orElse(null);
  }

  private List<CurveConfigurationInstrumentResolver> curveConfigurationResolvers(
      BitemporalDate stateDate, String marketDataGroupId) {
    return curveConfigurationValuationsResolver
        .curveConfigResolversByMarketData(stateDate)
        .getOrDefault(marketDataGroupId, List.of());
  }

  private List<LegalEntityTrsDataProviderResolver> dataProvidersSettings(
      BitemporalDate stateDate, String marketDataGroupId) {
    return settingsResolverProvider
        .map(resolver -> resolver.legalEntityDataProviderSettings(marketDataGroupId, stateDate))
        .orElse(List.of());
  }

  /**
   * Creates a {@link MdExceptionManagementSetup} instance for scenarios where "relevant data only"
   * is <b>false</b>, and thus no specific legal entity valuation settings are applied. The {@code
   * relevantOnly} flag in the resulting {@code MdExceptionManagementSetup} will be set to {@code
   * false}.
   */
  private MdExceptionManagementSetup mdSetup(
      @NonNull EntityNameView marketDataGroup,
      @NonNull List<CurveConfigurationInstrumentResolver> curveConfigurationResolvers,
      @Nullable LocalDate previousDate) {
    var mdg = EntityReference.newOf(marketDataGroup.getId(), marketDataGroup.getName());
    return MdExceptionManagementSetup.newOf(mdg, curveConfigurationResolvers, previousDate, false);
  }

  /**
   * Creates a {@link MdExceptionManagementSetup} instance for scenarios where "relevant data only"
   * is <b>true</b>. This version includes specific legal entity valuation settings. The {@code
   * relevantOnly} flag in the resulting {@code MdExceptionManagementSetup} will be set to {@code
   * true}.
   */
  private MdExceptionManagementSetup mdSetup(
      @NonNull EntityNameView marketDataGroup,
      @NonNull List<CurveConfigurationInstrumentResolver> curveConfigurationResolvers,
      @Nullable LocalDate previousDate,
      @NonNull Map<MdValuationSetting, List<String>> legalEntityIdsByMdValuationSetting) {
    var mdg = EntityReference.newOf(marketDataGroup.getId(), marketDataGroup.getName());
    return MdExceptionManagementSetup.newOf(
        mdg, curveConfigurationResolvers, previousDate, true, legalEntityIdsByMdValuationSetting);
  }

  private TrsMdExceptionManagementSetup trsMdSetup(
      @NonNull EntityNameView marketDataGroup,
      @NonNull List<LegalEntityTrsDataProviderResolver> dataProvidersSettings,
      @Nullable LocalDate previousDate) {
    var mdg = EntityReference.newOf(marketDataGroup.getId(), marketDataGroup.getName());
    return TrsMdExceptionManagementSetup.newOf(mdg, dataProvidersSettings, previousDate);
  }

  private Either<List<ErrorItem>, MdExceptionManagementSetup> validSetup(
      BitemporalDate stateDate, LocalDate dashboardDate, MdExceptionManagementSetup setup) {
    return errorsOrRight(
        mdSetupValidator.validate(stateDate, dashboardDate, setup, CORE_ASSET_GROUPS), setup);
  }

  private Either<List<ErrorItem>, TrsMdExceptionManagementSetup> validSetup(
      BitemporalDate stateDate, LocalDate dashboardDate, TrsMdExceptionManagementSetup setup) {
    return errorsOrRight(
        mdSetupValidator.validateTrs(stateDate, dashboardDate, setup, TRS_ASSET_GROUPS), setup);
  }

  private Either<List<ErrorItem>, MdExceptionManagementSetup> validBatchSetup(
      BitemporalDate stateDate, DateRange dateRange, MdExceptionManagementSetup setup) {
    return errorsOrRight(
        mdSetupValidator.validateBatch(
            stateDate, dateRange, setup.getMarketDataGroup(), CORE_ASSET_GROUPS),
        setup);
  }

  private Either<List<ErrorItem>, TrsMdExceptionManagementSetup> validBatchSetup(
      BitemporalDate stateDate, DateRange dateRange, TrsMdExceptionManagementSetup setup) {
    return errorsOrRight(
        mdSetupValidator.validateBatch(
            stateDate, dateRange, setup.getMarketDataGroup(), TRS_ASSET_GROUPS),
        setup);
  }
}
