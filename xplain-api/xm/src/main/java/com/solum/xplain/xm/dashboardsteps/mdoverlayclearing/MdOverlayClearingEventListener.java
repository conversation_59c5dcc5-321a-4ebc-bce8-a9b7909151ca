package com.solum.xplain.xm.dashboardsteps.mdoverlayclearing;

import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.dashboards.repository.DashboardRepository;
import com.solum.xplain.xm.dashboardsteps.DashboardEvent;
import com.solum.xplain.xm.dashboardsteps.dashboardfinalize.FinalizeDashboardEvent;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MdOverlayClearingEventListener {

  private final DashboardRepository dashboardRepository;
  private final ApplicationEventPublisher publisher;

  private final MdOverlayClearingStepsInitializer clearingStepsInitializer;
  private final MdOverlayClearingStepsFinalizer clearingStepsFinalizer;
  private final MdOverlayClearingCleanup stepCleanup;

  public MdOverlayClearingEventListener(
      DashboardRepository dashboardRepository,
      ApplicationEventPublisher publisher,
      MdOverlayClearingStepsInitializer clearingStepsInitializer,
      MdOverlayClearingStepsFinalizer clearingStepsFinalizer,
      MdOverlayClearingCleanup stepCleanup) {
    this.dashboardRepository = dashboardRepository;
    this.publisher = publisher;
    this.clearingStepsInitializer = clearingStepsInitializer;
    this.clearingStepsFinalizer = clearingStepsFinalizer;
    this.stepCleanup = stepCleanup;
  }

  @EventListener
  public void onEvent(MdOverlayClearingEvent event) {
    log.debug("Received MdOverlayClearingEvent: {}", event);
    if (DashboardEvent.Type.REQUESTED == event.getType()) {
      dashboard(event.getDashboardId()).ifPresent(this::initializeMdOverlayClearing);
    } else if (DashboardEvent.Type.UPDATED == event.getType()) {
      dashboard(event.getDashboardId()).ifPresent(d -> finalizeMdOverlayClearing(d, event));
    } else if (DashboardEvent.Type.COMPLETED == event.getType()) {
      dashboard(event.getDashboardId()).ifPresent(this::onMdOverlayCompleted);
    }
  }

  private void onMdOverlayCompleted(Dashboard dashboard) {
    publisher.publishEvent(new FinalizeDashboardEvent(dashboard.getId()));
  }

  private void initializeMdOverlayClearing(Dashboard dashboard) {
    clearingStepsInitializer
        .execute(dashboard)
        .toOptional()
        .ifPresent(steps -> processMdOverlayClearingSteps(dashboard, steps));
    if (dashboard(dashboard.getId()).isEmpty()) {
      stepCleanup.execute(dashboard.getId());
    }
  }

  private void processMdOverlayClearingSteps(Dashboard dashboard, List<DashboardEntryMd> steps) {
    steps.stream()
        .map(s -> StepStatus.COMPLETED == s.getStatus())
        .reduce(Boolean::logicalAnd)
        .filter(BooleanUtils::isTrue)
        .ifPresent(r -> onClearingCompleted(dashboard));
  }

  private void finalizeMdOverlayClearing(Dashboard dashboard, MdOverlayClearingEvent event) {
    clearingStepsFinalizer
        .execute(dashboard, event)
        .toOptional()
        .filter(steps -> steps.stream().allMatch(s -> s.getStatus() == StepStatus.COMPLETED))
        .ifPresent(steps -> onClearingCompleted(dashboard));
  }

  private void onClearingCompleted(Dashboard dashboard) {
    publisher.publishEvent(
        MdOverlayClearingEvent.builder()
            .dashboardId(dashboard.getId())
            .type(DashboardEvent.Type.COMPLETED)
            .build());
  }

  private Optional<Dashboard> dashboard(String dashboardId) {
    return dashboardRepository.dashboard(dashboardId).toOptional();
  }
}
