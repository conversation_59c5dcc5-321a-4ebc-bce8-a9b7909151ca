package com.solum.xplain.xm;

import com.solum.xplain.shared.spring.mongo.XplainMongoRepositoryFactoryBean;
import com.solum.xplain.xm.excmngmt.process.InstrumentResultPreliminaryQueryRepository;
import com.solum.xplain.xm.excmngmt.process.NonRequiredProviderDataRepository;
import com.solum.xplain.xm.excmngmt.processipv.IpvTradeResultOverlayQueryRepository;
import com.solum.xplain.xm.workflow.repository.XmStepInstanceQueryRepository;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

@Configuration
@EnableMongoRepositories(
    basePackageClasses = {
      XmStepInstanceQueryRepository.class,
      InstrumentResultPreliminaryQueryRepository.class,
      IpvTradeResultOverlayQueryRepository.class,
      NonRequiredProviderDataRepository.class,
    },
    repositoryFactoryBeanClass = XplainMongoRepositoryFactoryBean.class)
public class XmMongoConfig {}
