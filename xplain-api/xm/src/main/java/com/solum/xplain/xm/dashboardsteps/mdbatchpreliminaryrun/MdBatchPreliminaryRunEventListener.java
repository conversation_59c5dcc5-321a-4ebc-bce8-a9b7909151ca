package com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryrun;

import com.solum.xplain.xm.dashboards.entity.Dashboard;
import com.solum.xplain.xm.dashboards.repository.DashboardRepository;
import com.solum.xplain.xm.dashboardsteps.DashboardEvent;
import com.solum.xplain.xm.dashboardsteps.mdbatchpreliminaryclearing.MdBatchPreliminaryClearingEvent;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MdBatchPreliminaryRunEventListener {

  private final DashboardRepository dashboardRepository;
  private final ApplicationEventPublisher publisher;
  private final MdBatchPreliminaryRunExecutor runExecutor;
  private final MdBatchPreliminaryRunCleanup runEraser;

  public MdBatchPreliminaryRunEventListener(
      DashboardRepository dashboardRepository,
      ApplicationEventPublisher publisher,
      MdBatchPreliminaryRunExecutor runExecutor,
      MdBatchPreliminaryRunCleanup runEraser) {
    this.dashboardRepository = dashboardRepository;
    this.publisher = publisher;
    this.runExecutor = runExecutor;
    this.runEraser = runEraser;
  }

  @Async
  @EventListener
  public void onEvent(MdBatchPreliminaryRunEvent event) {
    log.debug("Received MdBatchPreliminaryRunEvent: {}", event);
    if (DashboardEvent.Type.REQUESTED == event.getType()) {
      dashboard(event.getDashboardId()).ifPresent(this::executeRun);
    } else if (DashboardEvent.Type.COMPLETED == event.getType()) {
      dashboard(event.getDashboardId())
          .ifPresentOrElse(this::finalizeRun, () -> runEraser.execute(event.getDashboardId()));
    }
  }

  private void executeRun(Dashboard dashboard) {
    runExecutor
        .execute(dashboard)
        .toOptional()
        .ifPresent(exceptionManagementResultIds -> onRunCompleted(dashboard));
  }

  private void finalizeRun(Dashboard dashboard) {
    publisher.publishEvent(
        MdBatchPreliminaryClearingEvent.builder()
            .dashboardId(dashboard.getId())
            .type(DashboardEvent.Type.REQUESTED)
            .build());
  }

  private void onRunCompleted(Dashboard dashboard) {
    publisher.publishEvent(
        MdBatchPreliminaryRunEvent.builder()
            .dashboardId(dashboard.getId())
            .type(DashboardEvent.Type.COMPLETED)
            .build());
  }

  private Optional<Dashboard> dashboard(String dashboardId) {
    return dashboardRepository.dashboard(dashboardId).toOptional();
  }
}
