package com.solum.xplain.xm.dashboards.validator;

import static com.solum.xplain.core.error.Error.OPERATION_NOT_ALLOWED;

import com.solum.xplain.core.common.CollectionUtils;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.IpvValuationsProvidersView;
import com.solum.xplain.core.error.ErrorItem;
import com.solum.xplain.core.ipv.data.IpvDataRepository;
import com.solum.xplain.core.providers.DataProvider;
import com.solum.xplain.xm.dashboards.entity.IpvDataGroupProducts;
import com.solum.xplain.xm.dashboards.entity.PortfolioDashboardSettings;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class IpvDataValidator {

  private static final String MISSING_IPV_DATA_GROUP = "Missing IPV Valuation Data Group for %s";
  private static final String MISSING_IPV_DATA = "Missing IPV data for %s group";
  private static final String PORTFOLIO_IDENTIFIER = "%s / %s /%s";

  private final IpvDataRepository ipvDataRepository;

  public IpvDataValidator(IpvDataRepository ipvDataRepository) {
    this.ipvDataRepository = ipvDataRepository;
  }

  public Optional<ErrorItem> validate(
      LocalDate dashboardDate,
      BitemporalDate stateDate,
      List<PortfolioDashboardSettings> ipvValuationSettings) {
    List<String> errors =
        CollectionUtils.join(
            missingIpvDataGroupErrors(ipvValuationSettings),
            missingIpvDataErrors(ipvValuationSettings, dashboardDate, stateDate));
    return errors.isEmpty()
        ? Optional.empty()
        : Optional.of(OPERATION_NOT_ALLOWED.entity(String.join(",", errors)));
  }

  private List<String> missingIpvDataGroupErrors(List<PortfolioDashboardSettings> settings) {
    return settings.stream()
        .filter(
            s ->
                s.getIpvDataGroupProducts().stream()
                    .anyMatch(c -> Objects.isNull(c.getIpvDataGroup())))
        .map(s -> String.format(MISSING_IPV_DATA_GROUP, portfolioIdentifier(s)))
        .toList();
  }

  private List<String> missingIpvDataErrors(
      List<PortfolioDashboardSettings> settings, LocalDate date, BitemporalDate stateDate) {
    return settings.stream()
        .map(PortfolioDashboardSettings::getIpvDataGroupProducts)
        .flatMap(Collection::stream)
        .filter(
            v ->
                v.getProducts().values().stream()
                    .flatMap(IpvValuationsProvidersView::toProviderStream)
                    .anyMatch(Predicate.not(DataProvider.XPLAIN_PROVIDER_CODE::equals)))
        .map(IpvDataGroupProducts::getIpvDataGroup)
        .filter(Objects::nonNull)
        .distinct()
        .filter(g -> isDataMissing(g, BitemporalDate.newOf(date, stateDate.getRecordDate())))
        .map(g -> String.format(MISSING_IPV_DATA, g.getName()))
        .toList();
  }

  private boolean isDataMissing(@NonNull EntityReference ipvDataGroup, BitemporalDate stateDate) {
    return ipvDataRepository.getValuesCount(ipvDataGroup.getEntityId(), stateDate) < 1;
  }

  private String portfolioIdentifier(PortfolioDashboardSettings s) {
    return String.format(
        PORTFOLIO_IDENTIFIER,
        s.getCompany().getName(),
        s.getEntity().getName(),
        s.getPortfolio().getName());
  }
}
