package com.solum.xplain.xm.dashboards.forms;

import com.solum.xplain.core.common.daterange.DateRangeForm;
import com.solum.xplain.core.market.validation.ValidMarketDataGroupId;
import com.solum.xplain.trs.market.validation.ValidTrsMarketDataGroupId;
import com.solum.xplain.xm.dashboards.enums.DashboardType;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.group.GroupSequenceProvider;

@Data
@Builder
@ToString
@EqualsAndHashCode
@GroupSequenceProvider(DashboardFormGroupProvider.class)
@ValidDashboard
@UniqueDashboard
public class DashboardForm {

  @NotNull private final DashboardType type;

  @NotNull private final LocalDate stateDate;

  @NotNull @Valid private final DateRangeForm dateRange;

  @Null(groups = {ValuationData.class})
  @ValidMarketDataGroupId(groups = {MarketData.class, MarketDataBatch.class})
  private final String marketDataGroupId;

  @Null(groups = {ValuationData.class})
  @ValidTrsMarketDataGroupId(groups = {MarketData.class, MarketDataBatch.class})
  private final String trsMarketDataGroupId;

  @Null(groups = {MarketData.class, MarketDataBatch.class})
  @NotNull(groups = {ValuationData.class})
  private final List<PricingSlotPortfolioForm> portfolios;

  @Null(groups = {ValuationData.class})
  private final Boolean relevantOnly;
}
