package com.solum.xplain.xm.excmngmt.processipv.enums;

import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.DELTA_QUATERNARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.DELTA_SECONDARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.DELTA_TERTIARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.DO_NOT_PRICE;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.HOLD;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.KEEP;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.OVERRIDE_USER;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.PREVIOUS_DAY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.SWITCH_TO_QUATERNARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.SWITCH_TO_SECONDARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.SWITCH_TO_TERTIARY;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.USE_ACCOUNTING_COST;
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.USE_DEAL_COST;
import static com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType.OVERLAY;

import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import com.solum.xplain.xm.tasks.enums.WithTaskExceptionManagementType;
import java.util.List;

public enum IpvCalculationTestStatus implements WithTaskExceptionManagementType {
  IN_OVERLAY(
      List.of(
          KEEP,
          PREVIOUS_DAY,
          SWITCH_TO_SECONDARY,
          SWITCH_TO_TERTIARY,
          SWITCH_TO_QUATERNARY,
          USE_ACCOUNTING_COST,
          USE_DEAL_COST,
          OVERRIDE_USER,
          DELTA_SECONDARY,
          DELTA_TERTIARY,
          DELTA_QUATERNARY,
          DO_NOT_PRICE,
          HOLD),
      OVERLAY),
  APPROVED(List.of(), OVERLAY);

  private final List<TradeResultResolutionType> allowableResolutionTypes;
  private final TaskExceptionManagementType taskExceptionManagementType;

  IpvCalculationTestStatus(
      List<TradeResultResolutionType> allowableResolutionTypes,
      TaskExceptionManagementType taskExceptionManagementType) {
    this.allowableResolutionTypes = allowableResolutionTypes;
    this.taskExceptionManagementType = taskExceptionManagementType;
  }

  public List<TradeResultResolutionType> getAllowableResolutionTypes() {
    return allowableResolutionTypes;
  }

  @Override
  public TaskExceptionManagementType getTaskExceptionManagementType() {
    return taskExceptionManagementType;
  }
}
