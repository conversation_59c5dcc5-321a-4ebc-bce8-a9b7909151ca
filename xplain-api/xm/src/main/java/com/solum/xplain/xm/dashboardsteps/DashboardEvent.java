package com.solum.xplain.xm.dashboardsteps;

import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor(access = AccessLevel.PROTECTED)
public abstract class DashboardEvent {

  @NonNull private final String dashboardId;

  @NonNull private final Type type;

  public abstract DashboardStep getStep();

  public enum Type {
    REQUESTED,
    UPDATED,
    COMPLETED
  }
}
