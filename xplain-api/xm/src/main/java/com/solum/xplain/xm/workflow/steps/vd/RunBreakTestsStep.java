package com.solum.xplain.xm.workflow.steps.vd;

import com.opengamma.strata.basics.currency.Currency;
import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.excmngmt.processipv.CachingBreakTestHistoryService;
import com.solum.xplain.xm.excmngmt.processipv.CachingPortfolioIpvConverterService;
import com.solum.xplain.xm.excmngmt.processipv.CachingProviderDataService;
import com.solum.xplain.xm.excmngmt.processipv.converter.PortfolioIpvValueConverter;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak;
import com.solum.xplain.xm.excmngmt.processipv.value.BundledProviderData;
import com.solum.xplain.xm.excmngmt.processipv.value.HistoricalTradeValuationData;
import com.solum.xplain.xm.excmngmt.processipv.value.IpvBreakTestCalculations;
import com.solum.xplain.xm.excmngmt.processipv.value.NotionalData;
import com.solum.xplain.xm.excmngmt.processipv.value.TradeBreakCalculator;
import com.solum.xplain.xm.excmngmt.processipv.value.TradeBreakScalingData;
import com.solum.xplain.xm.excmngmt.processipv.value.TradeDataBreakCalculatorSupplier;
import com.solum.xplain.xm.excmngmt.value.EntryBreakHistory;
import com.solum.xplain.xm.workflow.state.VdPhaseContext;
import com.solum.xplain.xm.workflow.state.VdPhaseState;
import com.solum.xplain.xm.workflow.state.VdPhaseStateMapper;
import java.time.Clock;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RunBreakTestsStep implements ServiceStepExecutor<VdPhaseState, VdPhaseContext> {
  private final TradeDataBreakCalculatorSupplier tradeDataBreakCalculatorSupplier;
  private final VdPhaseStateMapper vdPhaseStateMapper;
  private final CachingBreakTestHistoryService cachingBreakTestHistoryService;
  private final CachingProviderDataService cachingProviderDataService;
  private final CachingPortfolioIpvConverterService cachingPortfolioIpvConverterService;
  private Clock clock = Clock.systemDefaultZone();

  /** For unit testing. */
  void configureClock(Clock clock) {
    this.clock = clock;
  }

  @Override
  public void runStep(StepStateOps<VdPhaseState, VdPhaseContext> ops) {
    IpvBreakTestCalculations breakTestCalculations = ops.getContext().breakTestCalculations();
    TradeBreakCalculator tradeBreakCalculator = getTradeBreakCalculator(ops);
    List<TradeResultBreak> tradeResultBreaks =
        breakTestCalculations.processCalc(tradeBreakCalculator);
    boolean hasBreak = tradeResultBreaks.stream().anyMatch(TradeResultBreak::hasBreak);

    ops.setOutcome(
        new MutablePropertyValues()
            .add(VdPhaseState.Fields.breakTestResults, tradeResultBreaks)
            .add(VdPhaseState.Fields.hasBreak, hasBreak));
  }

  private TradeBreakCalculator getTradeBreakCalculator(
      StepStateOps<VdPhaseState, VdPhaseContext> ops) {
    Trade trade = ops.getContext().trade();
    EntryBreakHistory breakHistory =
        cachingBreakTestHistoryService.previousDashboardBreaks(
            trade, ops.getContext().phase(), ops.getContext().stateDate());
    PortfolioIpvValueConverter ipvValueConverter =
        cachingPortfolioIpvConverterService.getConverter(
            trade.getExternalCompanyId(),
            trade.getExternalEntityId(),
            trade.getPortfolioExternalId(),
            ops.getContext().stateDate());
    TradeBreakScalingData tradeBreakScalingData =
        cachingProviderDataService.scalingData(
            ops.getContext().vdg(), trade, ops.getContext().stateDate());
    NotionalData notionalData =
        cachingProviderDataService.notionalData(
            ops.getContext().vdg(), trade, ops.getContext().stateDate());

    tradeBreakScalingData =
        ipvValueConverter.convertScalingData(
            tradeBreakScalingData, Currency.parse(ops.getContext().trade().getCurrency()));

    // NB Primary PV is replaced with Base Value for break tests
    // TODO: make this explicit and allow switch to primary
    BundledProviderData bundledProviderData =
        vdPhaseStateMapper.toBundledProviderData(ops.getInitialState());
    TradeBreakCalculator tradeBreakCalculator =
        tradeDataBreakCalculatorSupplier.supply(
            trade,
            breakHistory == null ? EntryBreakHistory.empty(trade.getKey()) : breakHistory,
            // TODO: HistoricalData only for stale test (SXSD-8028)
            new HistoricalTradeValuationData(Collections.emptyList(), Collections.emptyMap()),
            bundledProviderData,
            tradeBreakScalingData,
            notionalData);
    return tradeBreakCalculator;
  }
}
