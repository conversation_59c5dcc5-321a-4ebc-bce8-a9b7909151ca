package com.solum.xplain.xm.excmngmt.processipv;

import static com.google.common.cache.CacheLoader.from;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.solum.xplain.core.common.versions.BitemporalDate;
import com.solum.xplain.core.company.value.IpvDataGroupVo;
import com.solum.xplain.shared.utils.event.CacheInvalidationListener;
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataWithGreeks;
import com.solum.xplain.xm.excmngmt.processipv.data.Trade;
import com.solum.xplain.xm.excmngmt.processipv.value.IpvExceptionManagementData;
import com.solum.xplain.xm.excmngmt.processipv.value.NotionalData;
import com.solum.xplain.xm.excmngmt.processipv.value.TradeBreakScalingData;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.ExecutionException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service to obtain provider valuation data for individual trades, by querying and caching it in
 * bulk.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CachingProviderDataService implements CacheInvalidationListener {
  public static final Duration FRESHNESS_LIMIT = Duration.of(1, ChronoUnit.MINUTES);
  private final IpvExceptionManagementDataService ipvExceptionManagementDataService;

  private final LoadingCache<BitemporalDate, StateDateCache> providerDataCache =
      CacheBuilder.newBuilder().expireAfterAccess(FRESHNESS_LIMIT).build(from(StateDateCache::new));

  /** Internal cache for a single bitemporal date. */
  @RequiredArgsConstructor
  private class StateDateCache {
    private final BitemporalDate stateDate;

    private record PreviousDayKey(IpvDataGroupVo vdg, String portfolioId) {}

    private final LoadingCache<IpvDataGroupVo, IpvExceptionManagementData> dayCache =
        CacheBuilder.newBuilder()
            .expireAfterWrite(FRESHNESS_LIMIT)
            .build(from(this::valuationsForVdg));
    private final LoadingCache<PreviousDayKey, IpvExceptionManagementData> previousDayCache =
        CacheBuilder.newBuilder()
            .expireAfterWrite(FRESHNESS_LIMIT)
            .build(from(pdk -> previousDataForVdgAndPortfolio(pdk.vdg(), pdk.portfolioId())));

    public List<ProviderDataWithGreeks> providerData(IpvDataGroupVo vdg, Trade trade)
        throws ExecutionException {
      PreviousDayKey pdk = new PreviousDayKey(vdg, trade.getPortfolioId());
      IpvExceptionManagementData currentDay = dayCache.get(vdg);
      IpvExceptionManagementData previousDay = previousDayCache.get(pdk);
      return currentDay.combinedDataForTrade(previousDay, trade.getKey());
    }

    public TradeBreakScalingData scalingData(IpvDataGroupVo vdg, Trade trade)
        throws ExecutionException {
      IpvExceptionManagementData currentDay = dayCache.get(vdg);
      BigDecimal navForTrade = currentDay.navData().get(trade.getKey());
      return new TradeBreakScalingData(navForTrade, BigDecimal.valueOf(trade.getNotional()));
    }

    public BigDecimal previousDayNotional(IpvDataGroupVo vdg, Trade trade)
        throws ExecutionException {
      PreviousDayKey pdk = new PreviousDayKey(vdg, trade.getPortfolioId());
      IpvExceptionManagementData previousDay = previousDayCache.get(pdk);
      return previousDay.notionalData().get(trade.getKey());
    }

    private IpvExceptionManagementData valuationsForVdg(IpvDataGroupVo vdg) {
      log.debug(
          "Loading provider valuation data for {} ({})", vdg.name(), stateDate.getActualDate());
      return ipvExceptionManagementDataService.providerData(
          vdg.entityId(), stateDate.getActualDate());
    }

    private IpvExceptionManagementData previousDataForVdgAndPortfolio(
        IpvDataGroupVo vdg, String portfolioId) {
      log.debug(
          "Loading historic overlay data for {}/{} ({})",
          vdg.name(),
          portfolioId,
          stateDate.getActualDate());
      return ipvExceptionManagementDataService.previousDayExceptionManagementData(
          portfolioId, vdg.entityId(), stateDate.getActualDate());
    }
  }

  /**
   * Check caches for current and previous valuation data for the VDG and fetch if missing, then get
   * the data for the specific trade from those.
   *
   * @param vdg the valuation data group reference
   * @param trade the trade to retrieve valuation data for
   * @param stateDate relevant state date
   * @return list of providers with their valuation data
   */
  public List<ProviderDataWithGreeks> providerData(
      IpvDataGroupVo vdg, Trade trade, BitemporalDate stateDate) {
    try {
      return providerDataCache.get(stateDate).providerData(vdg, trade);
    } catch (ExecutionException e) {
      throw new IllegalStateException(e);
    }
  }

  /**
   * Check caches for state date scaling data for the VDG and fetch if missing, then get the data
   * for the specific trade from those.
   *
   * @param vdg the valuation data group reference
   * @param trade the trade to retrieve valuation data for
   * @param stateDate relevant state date
   * @return trade break scaling data for the trade on the state date
   */
  public TradeBreakScalingData scalingData(
      IpvDataGroupVo vdg, Trade trade, BitemporalDate stateDate) {
    try {
      return providerDataCache.get(stateDate).scalingData(vdg, trade);
    } catch (ExecutionException e) {
      throw new IllegalStateException(e);
    }
  }

  public NotionalData notionalData(IpvDataGroupVo vdg, Trade trade, BitemporalDate stateDate) {
    try {
      var currDayNotional = BigDecimal.valueOf(trade.getNotional());
      var prevDayNotional = providerDataCache.get(stateDate).previousDayNotional(vdg, trade);
      return new NotionalData(currDayNotional, prevDayNotional);
    } catch (ExecutionException e) {
      throw new IllegalStateException(e);
    }
  }

  @Override
  public void invalidateAll() {
    this.providerDataCache.invalidateAll();
  }
}
