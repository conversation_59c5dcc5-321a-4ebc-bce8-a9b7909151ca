package com.solum.xplain.xm.dashboards.entity;

import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline;
import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.core.company.value.SlaDeadlinePortfolioView;
import java.util.List;
import lombok.Data;
import lombok.experimental.FieldNameConstants;
import org.springframework.lang.Nullable;

@Data
@FieldNameConstants
public class PortfolioDashboardSettings {

  private EntityReference company;
  private EntityReference entity;
  private EntityReference portfolio;
  private SlaDeadline slaDeadline;
  @Nullable private EntityReference marketDataGroup;

  private List<IpvDataGroupProducts> ipvDataGroupProducts;

  public boolean hasXplainProvider() {
    return ipvDataGroupProducts.stream().anyMatch(IpvDataGroupProducts::isHasXplainProvider);
  }

  public SlaDeadlinePortfolioView toSlaDeadlinePortfolioView() {
    return SlaDeadlinePortfolioView.newOf(
        portfolio.getEntityId(),
        portfolio.getName(),
        company.getEntityId(),
        company.getName(),
        entity.getEntityId(),
        entity.getName(),
        slaDeadline);
  }
}
