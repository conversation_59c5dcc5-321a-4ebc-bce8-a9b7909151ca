package com.solum.xplain.xm.dashboards.entity;

import com.solum.xplain.core.common.EntityReference;
import com.solum.xplain.xm.dashboards.enums.DashboardStep;
import com.solum.xplain.xm.dashboards.enums.StepStatus;
import com.solum.xplain.xm.dashboardsteps.mdoverlayclearing.DashboardTaskGroup;
import com.solum.xplain.xm.tasks.enums.TaskExceptionManagementType;
import com.solum.xplain.xm.tasks.view.UniqueTaskCountKey;
import java.util.Objects;
import java.util.Optional;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.lang.Nullable;

@Data
@Document
public class DashboardEntryMd extends DashboardEntry {

  /*
   * Present only for MD_OVERLAY_CLEARING.
   */
  private EntityReference curveConfiguration;
  private EntityReference company;
  private EntityReference legalEntity;

  public static DashboardEntryMd newOf(String dashboardId, DashboardStep step) {
    var entry = new DashboardEntryMd();
    entry.setDashboardId(dashboardId);
    entry.setStep(step);
    entry.setStatus(StepStatus.NOT_STARTED);
    return entry;
  }

  public static DashboardEntryMd newOfMarketDataUpload(String dashboardId) {
    return newOf(dashboardId, DashboardStep.MARKET_DATA_UPLOAD);
  }

  public static DashboardEntryMd newOfPreliminaryRun(String dashboardId) {
    return newOf(dashboardId, DashboardStep.MD_PRELIMINARY_RUN);
  }

  public static DashboardEntryMd newOfPreliminaryClearing(
      String dashboardId, String excManagementId, Long breakCount) {
    var entry = newOf(dashboardId, DashboardStep.MD_PRELIMINARY_CLEARING);
    entry.setBreaksCount(breakCount);
    entry.setResultId(excManagementId);
    return entry;
  }

  public static DashboardEntryMd newOfOverlayClearing(
      String dashboardId,
      String excManagementId,
      DashboardTaskGroup dashboardTaskGroup,
      Long breakCount) {
    var entry = newOf(dashboardId, DashboardStep.MD_OVERLAY_CLEARING);
    entry.setBreaksCount(breakCount);
    entry.setCurveConfiguration(dashboardTaskGroup.curveConfigurationReference());
    entry.setCompany(dashboardTaskGroup.companyReference());
    entry.setLegalEntity(dashboardTaskGroup.legalEntityReference());
    entry.setResultId(excManagementId);
    return entry;
  }

  public boolean matches(String companyId, String entityId, String ccId) {
    var matchesCompany = matchesReference(companyId, company);
    var matchesEntity = matchesReference(entityId, legalEntity);
    var matchesCurveConfig = matchesReference(ccId, curveConfiguration);

    return matchesCompany && matchesEntity && matchesCurveConfig;
  }

  private boolean matchesReference(String id, EntityReference reference) {
    var referenceId = reference == null ? null : reference.getEntityId();
    return StringUtils.equals(id, referenceId);
  }

  @Nullable
  public String curveConfigurationReferenceId() {
    return referenceId(curveConfiguration);
  }

  @Nullable
  public String companyReferenceId() {
    return referenceId(company);
  }

  @Nullable
  public String entityReferenceId() {
    return referenceId(legalEntity);
  }

  @Nullable
  private String referenceId(EntityReference entityReference) {
    return entityReference == null ? null : entityReference.getEntityId();
  }

  @Override
  public Optional<UniqueTaskCountKey> taskCountKey() {
    if (getStep() == DashboardStep.MD_OVERLAY_CLEARING) {
      return Optional.of(UniqueTaskCountKey.ofMdOverlay(curveConfiguration, company, legalEntity));
    } else if (getStep() == DashboardStep.MD_PRELIMINARY_CLEARING) {
      return Optional.of(UniqueTaskCountKey.taskCountKey(TaskExceptionManagementType.PRELIMINARY));
    }
    return Optional.empty();
  }

  @Override
  public boolean isSameEntry(DashboardEntry entry) {
    if (entry instanceof DashboardEntryMd mdEntry) {
      if (mdEntry.getStep() == DashboardStep.MD_OVERLAY_CLEARING) {
        return super.isSameEntry(mdEntry)
            && Objects.equals(this.curveConfiguration, mdEntry.curveConfiguration)
            && Objects.equals(this.company, mdEntry.company)
            && Objects.equals(this.legalEntity, mdEntry.legalEntity);
      }
      return super.isSameEntry(mdEntry);
    }
    return false;
  }
}
