package com.solum.xplain.xm.workflow.steps.vd;

import com.solum.xplain.workflow.service.StepStateOps;
import com.solum.xplain.workflow.value.ServiceStepExecutor;
import com.solum.xplain.xm.workflow.state.AttributedValue;
import com.solum.xplain.xm.workflow.state.VdPhaseContext;
import com.solum.xplain.xm.workflow.state.VdPhaseState;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class VerifyResolutionStep implements ServiceStepExecutor<VdPhaseState, VdPhaseContext> {
  @Override
  public void runStep(StepStateOps<VdPhaseState, VdPhaseContext> ops) {
    AttributedValue pendingValue = ops.getInitialState().getValuePendingApproval();
    ops.setOutcome(
        new MutablePropertyValues()
            .add(VdPhaseState.Fields.valuePendingApproval, null)
            .add(VdPhaseState.Fields.baseValue, pendingValue.value())
            .add(VdPhaseState.Fields.providerType, pendingValue.providerType())
            .add(VdPhaseState.Fields.providerName, pendingValue.providerName()));
    // Don't need to set VERIFIED as that is done in SaveResultStep anyway
  }
}
