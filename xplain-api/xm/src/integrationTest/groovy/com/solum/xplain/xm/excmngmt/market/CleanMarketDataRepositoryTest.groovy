package com.solum.xplain.xm.excmngmt.market

import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.ASK
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.BID
import static com.solum.xplain.core.mdvalue.value.ValueBidAskType.MID
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.DASHBOARD_DATE
import static com.solum.xplain.xm.dashboards.entity.DashboardBuilder.PREVIOUS_DASHBOARD_DATE
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.VERIFIED
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_RESOLUTION
import static java.math.BigDecimal.ONE
import static java.math.BigDecimal.TEN

import com.solum.xplain.core.curvegroup.instrument.CoreAssetGroup
import com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType
import com.solum.xplain.core.curvemarket.InstrumentCurveDate
import com.solum.xplain.core.curvemarket.InstrumentOverrideCurveDateDetails
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.trs.value.TrsAssetClassGroup
import com.solum.xplain.xm.dashboards.entity.Dashboard
import com.solum.xplain.xm.dashboards.entity.DashboardBuilder
import com.solum.xplain.xm.dashboards.entity.DashboardEntryMd
import com.solum.xplain.xm.excmngmt.process.data.Instrument
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultOverlay
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultPreliminary
import com.solum.xplain.xm.excmngmt.process.data.InstrumentResultResolution
import com.solum.xplain.xm.excmngmt.process.data.ProviderData
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CleanMarketDataRepositoryTest extends IntegrationSpecification {

  @Resource
  CleanMarketDataRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), InstrumentResultPreliminary)
    operations.remove(new Query(), InstrumentResultOverlay)
    operations.remove(new Query(), Dashboard)
    operations.remove(new Query(), DashboardEntryMd)
  }

  def "should return preliminary clean market data for z score"() {
    setup:
    def today = LocalDate.now()
    def yesterday = today.minusDays(1)
    def tomorrow = today.plusDays(1)

    def preliminaries = [
      new InstrumentResultPreliminary(marketDataGroupId: "MDID", valuationDate: yesterday, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: "MDID", valuationDate: today, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: "MDID", valuationDate: tomorrow, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("BBG", TEN, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: "MDID", valuationDate: tomorrow, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("BBG", ONE, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: "MDID2", valuationDate: tomorrow, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("MMM", ONE, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: "MDID", valuationDate: LocalDate.ofEpochDay(0), status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("MMM", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: "MDID", valuationDate: today, status: WAITING_RESOLUTION, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("MMM", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: "MDID2", valuationDate: today, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: "MDID", valuationDate: today, status: WAITING_RESOLUTION, instrument: new Instrument(key: "KEY2"), providerData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: "MDID", valuationDate: today, status: VERIFIED, instrument: new Instrument(key: "KEY2"), providerData: ProviderData.of("BBG", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: "MDID", valuationDate: yesterday, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("BBG", null, null, MID)),
    ]
    operations.insertAll(preliminaries)

    when:
    def data = repository.cleanMarketForZScores("MDID", ["KEY1"] as Set, LocalDate.now().minusDays(1), LocalDate.now().plusDays(1))

    then:
    data.size() == 4
    data.stream().allMatch { d -> d.instrumentKey == "KEY1" }
    data.date == [yesterday, today, tomorrow, tomorrow]
    equalProviderData(data[0].providerData, preliminaries[0].providerData)
    equalProviderData(data[1].providerData, preliminaries[1].providerData)
    equalProviderData(data[2].providerData, preliminaries[2].providerData)
    equalProviderData(data[3].providerData, preliminaries[3].providerData)

    where:
    groupId << [DashboardBuilder.MARKET_DATA_GROUP.entityId, DashboardBuilder.MARKET_DATA_GROUP_ANOTHER.entityId]
  }

  def "should return preliminary clean market data"() {
    setup:
    def dashboard = DashboardBuilder.MD_AND_TRS_MARKET_DATA_DASHBOARD
    operations.insert(dashboard)

    def preliminaries = [
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("BBG", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("MMM", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("BBG", null, null, MID), resolution: new InstrumentResultResolution(value: 1.0d)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: WAITING_RESOLUTION, instrument: new Instrument(key: "KEY2"), providerData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), providerData: ProviderData.of("BBG", null, null, MID)),
    ]
    operations.insertAll(preliminaries)

    when:
    def data = repository.cleanPreliminaryData(groupId, dashboard.dateRange.singleDate())

    then:
    data.size() == 5
    data.stream().allMatch { d -> d.instrumentKey == "KEY1" }
    equalProviderData(data[0].providerData, preliminaries[0].providerData)
    equalProviderData(data[1].providerData, preliminaries[1].providerData)
    equalProviderData(data[2].providerData, preliminaries[2].providerData)
    equalProviderData(data[3].providerData, preliminaries[3].providerData)
    equalProviderData(data[4].providerData, preliminaries[4].providerData)
    data[4].resolvedData().value == 1.0

    where:
    groupId << [DashboardBuilder.MARKET_DATA_GROUP.entityId, DashboardBuilder.MARKET_DATA_GROUP_ANOTHER.entityId]
  }

  def "should return preliminary clean market data using curve date details"() {
    setup:
    def dashboard = DashboardBuilder.MD_AND_TRS_MARKET_DATA_DASHBOARD
    operations.insert(dashboard)

    def preliminaries = [
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: PREVIOUS_DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", instrumentType: CoreInstrumentType.CDS), providerData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", instrumentType: CoreInstrumentType.CDS), providerData: ProviderData.of("BBG", TEN, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY2", instrumentType: CoreInstrumentType.FX_VOL), providerData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: PREVIOUS_DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY2", instrumentType: CoreInstrumentType.FX_VOL), providerData: ProviderData.of("BBG", TEN, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY3", instrumentType: CoreInstrumentType.FX_VOL_SKEW), providerData: ProviderData.of("BBG", BigDecimal.valueOf(5), null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: PREVIOUS_DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY3", instrumentType: CoreInstrumentType.FX_VOL_SKEW), providerData: ProviderData.of("BBG", BigDecimal.valueOf(50), null, BID)),
    ]
    operations.insertAll(preliminaries)

    def extractor = new InstrumentOverrideCurveDateDetails(
      DASHBOARD_DATE,
      PREVIOUS_DASHBOARD_DATE,
      new InstrumentCurveDate(
      DASHBOARD_DATE,
      [CoreInstrumentType.FX_VOL, CoreInstrumentType.FX_VOL_SKEW]
      )
      )

    when:
    def data = repository.cleanPreliminaryData(groupId, extractor)

    then:
    data.size() == 3
    equalProviderData(data[0].providerData, preliminaries[0].providerData) // ==> non-override date
    equalProviderData(data[1].providerData, preliminaries[2].providerData) // ==> override date
    equalProviderData(data[2].providerData, preliminaries[4].providerData) // ==> override date

    where:
    groupId << [DashboardBuilder.MARKET_DATA_GROUP.entityId, DashboardBuilder.MARKET_DATA_GROUP_ANOTHER.entityId]
  }

  def "should return overlay clean market data for single curve configuration"() {
    setup:
    def dashboard = DashboardBuilder.MD_AND_TRS_MARKET_DATA_DASHBOARD
    operations.insert(dashboard)

    def overlay = [
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("BBG", TEN, null, ASK)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("MMM", ONE, null, BID)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("MMM", null, null, ASK), resolution: new InstrumentResultResolution(value: 1.0)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId2", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("AAA", TEN, null, BID)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: WAITING_RESOLUTION, instrument: new Instrument(key: "KEY2"), primaryProviderData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("BBG", null, null, MID)),
    ]
    operations.insertAll(overlay)

    when:
    def data = repository.cleanOverlayData(groupId, dashboard.dateRange.singleDate(), "ccId")

    then:
    data.size() == 5
    data.stream().allMatch { d -> d.instrumentKey == "KEY1" }
    equalProviderData(data[0].providerData, overlay[0].primaryProviderData)
    equalProviderData(data[1].providerData, overlay[1].primaryProviderData)
    equalProviderData(data[2].providerData, overlay[2].primaryProviderData)
    equalProviderData(data[3].providerData, overlay[3].primaryProviderData)
    equalProviderData(data[4].providerData, overlay[4].primaryProviderData)
    data[4].resolvedData().value == 1.0

    where:
    groupId << [DashboardBuilder.MARKET_DATA_GROUP.entityId, DashboardBuilder.MARKET_DATA_GROUP_ANOTHER.entityId]
  }

  def "should return overlay clean market data for all configs"() {
    setup:
    def dashboard = DashboardBuilder.MD_AND_TRS_MARKET_DATA_DASHBOARD
    operations.insert(dashboard)

    def overlay = [
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("BBG", TEN, null, ASK)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("MMM", ONE, null, BID)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId2", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("AAA", TEN, null, BID)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId2", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("AAA", null, null, BID), resolution: new InstrumentResultResolution(value: 1.0)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: WAITING_RESOLUTION, instrument: new Instrument(key: "KEY2"), primaryProviderData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultOverlay(curveConfigurationId: "ccId", marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1"), primaryProviderData: ProviderData.of("BBG", null, null, MID)),
    ]
    operations.insertAll(overlay)

    when:
    def data = repository.cleanOverlayData(groupId, dashboard.dateRange.singleDate())

    then:
    data.size() == 6
    data.stream().allMatch { d -> d.instrumentKey == "KEY1" }
    equalProviderData(data[0].providerData, overlay[0].primaryProviderData)
    equalProviderData(data[1].providerData, overlay[1].primaryProviderData)
    equalProviderData(data[2].providerData, overlay[2].primaryProviderData)
    equalProviderData(data[3].providerData, overlay[3].primaryProviderData)
    equalProviderData(data[4].providerData, overlay[4].primaryProviderData)
    equalProviderData(data[5].providerData, overlay[5].primaryProviderData)
    data[5].resolvedData().value == 1.0

    where:
    groupId << [DashboardBuilder.MARKET_DATA_GROUP.entityId, DashboardBuilder.MARKET_DATA_GROUP_ANOTHER.entityId]
  }


  def "should return preliminary TRS data for all days"() {
    setup:
    def nextDashboarDay = DASHBOARD_DATE.plusDays(1)

    def preliminaries = [
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("BBG", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("MMM", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("AAA", TEN, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("AAA", null, null, BID), resolution: new InstrumentResultResolution(value: 1.0)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("BBG", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("MMM", ONE, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("AAA", TEN, null, BID)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: nextDashboarDay, status: WAITING_RESOLUTION, instrument: new Instrument(key: "KEY2", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY3", assetClassGroup: CoreAssetGroup.CREDIT), providerData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultPreliminary(marketDataGroupId: groupId, valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), providerData: ProviderData.of("BBG", null, null, MID)),
    ]
    operations.insertAll(preliminaries)

    when:
    def data = repository.cleanPreliminaryTrsData(groupId)

    then:
    data.size() == 11
    data.stream().allMatch { d -> d.instrumentKey == "KEY1" }
    equalProviderData(data[0].providerData, preliminaries[0].providerData)
    equalProviderData(data[1].providerData, preliminaries[1].providerData)
    equalProviderData(data[2].providerData, preliminaries[2].providerData)
    equalProviderData(data[3].providerData, preliminaries[3].providerData)
    equalProviderData(data[4].providerData, preliminaries[4].providerData)
    equalProviderData(data[5].providerData, preliminaries[5].providerData)
    data[5].resolvedData().value == 1.0
    data.stream().filter { v -> v.date == DASHBOARD_DATE }.count() == 6
    data.stream().filter { v -> v.date == nextDashboarDay }.count() == 5

    where:
    groupId << [DashboardBuilder.MARKET_DATA_GROUP.entityId, DashboardBuilder.MARKET_DATA_GROUP_ANOTHER.entityId]
  }

  def "should return overlay TRS data for all days"() {
    setup:
    def nextDashboarDay = DASHBOARD_DATE.plusDays(1)

    def preliminaries = [
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("BBG", TEN, null, ASK)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("MMM", ONE, null, BID)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("AAA", TEN, null, BID)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("BBG", ONE, null, BID)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("BBG", null, null, BID), resolution: new InstrumentResultResolution(value: 1.0d)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("BBG", TEN, null, ASK)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("MMM", ONE, null, BID)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: nextDashboarDay, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("AAA", TEN, null, BID)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: nextDashboarDay, status: WAITING_RESOLUTION, instrument: new Instrument(key: "KEY2", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY3", assetClassGroup: CoreAssetGroup.CREDIT), primaryProviderData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID2", valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY4", assetClassGroup: CoreAssetGroup.CREDIT), primaryProviderData: ProviderData.of("MMM", TEN, null, ASK)),
      new InstrumentResultOverlay(marketDataGroupId: groupId, companyId: "CID", legalEntityId: "LID", valuationDate: DASHBOARD_DATE, status: VERIFIED, instrument: new Instrument(key: "KEY1", assetClassGroup: TrsAssetClassGroup.TRS), primaryProviderData: ProviderData.of("BBG", null, null, MID)),
    ]
    operations.insertAll(preliminaries)

    when:
    def data = repository.cleanOverlayTrsData(groupId, "CID")

    then:
    data.size() == 11
    data.stream().allMatch { d -> d.instrumentKey == "KEY1" }
    equalProviderData(data[0].providerData, preliminaries[0].primaryProviderData)
    equalProviderData(data[1].providerData, preliminaries[1].primaryProviderData)
    equalProviderData(data[2].providerData, preliminaries[2].primaryProviderData)
    equalProviderData(data[3].providerData, preliminaries[3].primaryProviderData)
    equalProviderData(data[4].providerData, preliminaries[4].primaryProviderData)
    equalProviderData(data[5].providerData, preliminaries[5].primaryProviderData)
    data[5].resolvedData().value == 1.0
    data.stream().filter { v -> v.date == DASHBOARD_DATE }.count() == 5
    data.stream().filter { v -> v.date == nextDashboarDay }.count() == 6
    data.stream().allMatch { v -> v.legalEntityId == "LID" }

    where:
    groupId << [DashboardBuilder.MARKET_DATA_GROUP.entityId, DashboardBuilder.MARKET_DATA_GROUP_ANOTHER.entityId]
  }

  def equalProviderData(ProviderData data1, ProviderData data2) {
    data1.provider == data2.provider
    data1.value == data2.value
    data1.bidAskType == data2.bidAskType
  }
}
