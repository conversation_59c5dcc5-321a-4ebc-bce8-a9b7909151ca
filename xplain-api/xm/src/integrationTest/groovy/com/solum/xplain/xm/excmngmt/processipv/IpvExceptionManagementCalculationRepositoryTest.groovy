package com.solum.xplain.xm.excmngmt.processipv

import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider.ofBreakWithoutLevel
import static com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider.ofMultiLevelBreak
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.REJECTED
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.VERIFIED
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_APPROVAL
import static com.solum.xplain.xm.excmngmt.enums.EntryResultStatus.WAITING_RESOLUTION
import static com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase.OVERLAY_1
import static com.solum.xplain.xm.excmngmt.enums.IpvExceptionManagementPhase.OVERLAY_2
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.KEEP
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.OVERRIDE_USER
import static com.solum.xplain.xm.excmngmt.processipv.enums.TradeResultResolutionType.SWITCH_TO_SECONDARY
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.MANUAL
import static com.solum.xplain.xm.excmngmt.rulesipv.value.IpvProvidersType.P2
import static com.solum.xplain.xm.workflow.VdXmWorkflowProvider.VD_XM_PHASE_PROCESS_ID
import static com.solum.xplain.xm.workflow.VdXmWorkflowProvider.VD_XM_PROCESS_ID
import static com.solum.xplain.xm.workflow.VdXmWorkflowProvider.VD_XM_TRADE_PROCESS_ID
import static java.math.BigDecimal.ONE
import static java.math.BigDecimal.TEN
import static java.math.BigDecimal.ZERO
import static java.time.LocalDateTime.now
import static java.time.temporal.ChronoUnit.SECONDS
import static org.springframework.data.mongodb.core.query.Criteria.where
import static org.springframework.data.mongodb.core.query.Query.query

import com.solum.xplain.core.classifiers.pricingslots.PricingSlot
import com.solum.xplain.core.classifiers.sladeadlines.SlaDeadline
import com.solum.xplain.core.classifiers.xm.CoreThresholdLevel
import com.solum.xplain.core.common.AuditContext
import com.solum.xplain.core.common.EntityReference
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.company.value.IpvDataGroupVo
import com.solum.xplain.core.company.value.ProvidersVo
import com.solum.xplain.core.error.ErrorItem
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.portfolio.CoreProductType
import com.solum.xplain.core.portfolio.CoreProductTypeGroup
import com.solum.xplain.core.portfolio.trade.TradeDetailsBuilder
import com.solum.xplain.core.product.ProductType
import com.solum.xplain.core.users.AuditUser
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.extensions.enums.CreditSector
import com.solum.xplain.workflow.entity.ProcessExecution
import com.solum.xplain.workflow.entity.StepInstance
import com.solum.xplain.workflow.value.WorkflowStatus
import com.solum.xplain.xm.dashboards.events.DashboardDeletedEvent
import com.solum.xplain.xm.dashboards.views.DashboardPortfolio
import com.solum.xplain.xm.excmngmt.data.EntryResultBreakByProvider
import com.solum.xplain.xm.excmngmt.enums.VerificationStatus
import com.solum.xplain.xm.excmngmt.evidence.ExceptionManagementEvidence
import com.solum.xplain.xm.excmngmt.form.ApplyOverrideForm
import com.solum.xplain.xm.excmngmt.form.ApplyOverrideForm.IndividualOverrideForm
import com.solum.xplain.xm.excmngmt.form.OverrideForm
import com.solum.xplain.xm.excmngmt.form.ResultDisplayFilterForm
import com.solum.xplain.xm.excmngmt.processipv.data.IpvExceptionManagementResult
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultOverlay
import com.solum.xplain.xm.excmngmt.processipv.data.IpvTradeResultResolution
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataValue
import com.solum.xplain.xm.excmngmt.processipv.data.ProviderDataWithGreeks
import com.solum.xplain.xm.excmngmt.processipv.data.Trade
import com.solum.xplain.xm.excmngmt.processipv.data.TradeResultBreak
import com.solum.xplain.xm.excmngmt.processipv.form.TradeFilterForm
import com.solum.xplain.xm.excmngmt.processipv.value.IpvBreakTestCalculations
import com.solum.xplain.xm.excmngmt.processipv.value.IpvPortfolioItemResultFilter
import com.solum.xplain.xm.excmngmt.processipv.view.IpvTradeOverlayResultView
import com.solum.xplain.xm.excmngmt.rulesipv.IpvBreakTest
import com.solum.xplain.xm.excmngmt.rulesipv.value.ProductTypeFilterForm
import com.solum.xplain.xm.excmngmt.value.BreakTestHistory
import com.solum.xplain.xm.workflow.state.AttributedValue
import com.solum.xplain.xm.workflow.state.PricingSlotTradeWithProviders
import com.solum.xplain.xm.workflow.state.VdDashboardState
import com.solum.xplain.xm.workflow.state.VdEntryContext
import com.solum.xplain.xm.workflow.state.VdPhaseContext
import com.solum.xplain.xm.workflow.state.VdPhaseState
import jakarta.annotation.Resource
import java.time.LocalDate
import org.bson.types.ObjectId
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class IpvExceptionManagementCalculationRepositoryTest extends IntegrationSpecification {

  @Resource
  IpvExceptionManagementCalculationRepository repository
  @Resource
  MongoOperations operations

  def user = UserBuilder.user("userId")

  static def TASK_1_ID = "ipvTask1"
  static def TASK_2_ID = "ipvTask2"

  private AuditContext audit() {
    return new AuditContext(AuditUser.of(user), now().truncatedTo(SECONDS))
  }

  def setup() {
    def auth = new TestingAuthenticationToken(user, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    SecurityContextHolder.getContext().authentication = null
    operations.remove(new Query(), IpvExceptionManagementResult)
    operations.remove(new Query(), IpvTradeResultOverlay)
    operations.remove(new Query(), ProcessExecution)
    operations.remove(new Query(), StepInstance)
  }

  def "should return workflow overlay breaks count"() {
    setup:
    def pi = [new TradeResultBreak(providerValue: new EntryResultBreakByProvider(triggered: true, value: ONE))]
    def piNullValue = [
      new TradeResultBreak(threshold: ONE, providerValue: new EntryResultBreakByProvider(triggered: true))
    ]
    // This is what happens in com.solum.xplain.xm.excmngmt.rules.value.Operator.resolver when the value is null
    def piSkipped = [new TradeResultBreak(providerValue: ofBreakWithoutLevel(false, null))]

    def p1 = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      rootBusinessKey: "dashboardId",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, IpvBreakTestCalculations.ofBreakTests([IpvBreakTest.newOf()])),
      currentState: new VdPhaseState(
      entryStatus: WAITING_RESOLUTION,
      breakTestResults: pi,
      hasBreak: true
      )
      )
    def p2 = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      rootBusinessKey: "dashboardId",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, IpvBreakTestCalculations.ofBreakTests([IpvBreakTest.newOf()])),
      currentState: new VdPhaseState(
      entryStatus: WAITING_RESOLUTION,
      breakTestResults: pi,
      hasBreak: true
      )
      )
    def p3 = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      rootBusinessKey: "dashboardId",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, IpvBreakTestCalculations.ofBreakTests([IpvBreakTest.newOf()])),
      currentState: new VdPhaseState(
      entryStatus: WAITING_APPROVAL,
      breakTestResults: pi,
      hasBreak: true
      )
      )
    def p4 = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      rootBusinessKey: "dashboardId",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, IpvBreakTestCalculations.ofBreakTests([IpvBreakTest.newOf()])),
      currentState: new VdPhaseState(
      entryStatus: VERIFIED,
      breakTestResults: piNullValue,
      hasBreak: true
      )
      )
    def p5 = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      rootBusinessKey: "dashboardId",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, IpvBreakTestCalculations.ofBreakTests([IpvBreakTest.newOf()])),
      currentState: new VdPhaseState(
      entryStatus: VERIFIED,
      breakTestResults: piSkipped,
      )
      )
    def p6 = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      rootBusinessKey: "dashboardId",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, IpvBreakTestCalculations.ofBreakTests([IpvBreakTest.newOf()])),
      currentState: new VdPhaseState(
      entryStatus: WAITING_RESOLUTION,
      breakTestResults: pi,
      hasBreak: true
      )
      )

    operations.insertAll([p1, p2, p3, p4, p5, p6])
    def s1 = new StepInstance(
      executionId: p1.id,
      status: WorkflowStatus.ACTIVE,
      reportable: true,
      stepId: 'decideResolution',
      initialState: new VdPhaseState()
      )
    def s2 = new StepInstance(
      executionId: p2.id,
      status: WorkflowStatus.FINALIZING,
      reportable: true,
      stepId: 'decideResolution',
      initialState: new VdPhaseState()
      )
    def s3 = new StepInstance(
      executionId: p3.id,
      status: WorkflowStatus.DONE,
      reportable: true,
      stepId: 'decideResolution',
      initialState: new VdPhaseState()
      )
    def s4 = new StepInstance(
      executionId: p4.id,
      status: WorkflowStatus.DONE,
      reportable: true,
      stepId: 'decideResolution',
      initialState: new VdPhaseState()
      )
    def s6 = new StepInstance(
      executionId: p6.id,
      status: WorkflowStatus.HELD,
      reportable: true,
      stepId: 'decideResolution',
      initialState: new VdPhaseState()
      )
    operations.insertAll([s1, s2, s3, s4, s6])

    when:
    def result = repository.overlayBreaksCount(["id&stepId=decideResolution"])

    then:
    result.totalCount == 6
    result.appliedTestsCount == 5
    result.totalTestsCount == 6
    result.breaksCount == 5
    result.resolvedCount == 3
    result.verifiedCount == 1
    result.finalizingCount == 1
    result.heldCount == 1
    result.progressKey == "dashboardId"
  }

  def "should fetch overlay view from workflow by task id"() {
    def entityId = ObjectId.get().toHexString()

    def p1 = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      parentBusinessKey: "id",
      status: WorkflowStatus.DONE,
      context: new VdPhaseContext(
      BitemporalDate.newOfNow(),
      new IpvDataGroupVo("vdg", "name"),
      null,
      PricingSlot.LDN_1500,
      SlaDeadline.LDN_1200,
      new Trade(
      entityId: entityId,
      productType: CoreProductType.IRS,
      productGroup: CoreProductTypeGroup.RATES,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      externalCompanyId: "ecId",
      externalEntityId: "EXT",
      portfolioExternalId: "pExtId",
      portfolioId: "portfolioId",
      currency: "EUR",
      currencyPair: "EUR/USD"
      ),
      "GBP",
      new ProvidersVo("pri", "sec", "ter", "qua"),
      OVERLAY_1,
      null
      ),
      currentState: new VdPhaseState(
      baseValue: TEN,
      providerType: MANUAL,
      resolution: OVERRIDE_USER,
      resolutionComment: "resolutionComment",
      manualResolutionValue: TEN,
      approvalComment: "approvalComment",
      entryStatus: VERIFIED,
      breakTestResults: [
        new TradeResultBreak(breakTestName: "test1", threshold: TEN, providerValue: ofMultiLevelBreak(1, ZERO, TEN)),
        new TradeResultBreak(breakTestName: "test2", threshold: TEN, providerValue: ofBreakWithoutLevel(false, null)),
        new TradeResultBreak(breakTestName: "test3", threshold: TEN, providerValue: ofMultiLevelBreak(2, ONE, TEN)),
      ],
      )
      )
    operations.insertAll([p1])

    when:
    def r = repository.overlayItems(["id"], displayFilterForm(),
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    r.content.size() == 1
    r.content[0].trade.tradeInfoTradeType == CoreProductType.IRS
    r.content[0].trade.tradeInfoExternalTradeId == "externalTradeId"
    r.content[0].trade.entityId == entityId
    r.content[0].trade.creditSector == CreditSector.CONSUMER_GOODS
    r.content[0].trade.currency == "EUR"
    r.content[0].trade.tradeInfoTradeTypeGroup == CoreProductTypeGroup.RATES
    r.content[0].trade.currencyPair == "EUR/USD"
    r.content[0].trade.key == "key"
    r.content[0].trade.externalCompanyId == "ecId"
    r.content[0].trade.externalEntityId == "EXT"
    r.content[0].trade.portfolioExternalId == "pExtId"
    r.content[0].trade.portfolioId == "portfolioId"
    r.content[0].trade.tradeInfoExternalTradeId == "externalTradeId"
    r.content[0].breakTests[0].breakTestName == "test1"
    r.content[0].breakTests[0].triggered
    r.content[0].breakTests[0].threshold == ZERO
    r.content[0].breakTests[0].thresholdLevel == 1
    r.content[0].breakTests[0].value == TEN
    r.content[0].breakTests[1].breakTestName == "test2"
    !r.content[0].breakTests[1].triggered
    r.content[0].breakTests[1].threshold == TEN
    r.content[0].breakTests[1].thresholdLevel == null
    r.content[0].breakTests[1].value == null
    r.content[0].breakTests[2].breakTestName == "test3"
    r.content[0].breakTests[2].triggered
    r.content[0].breakTests[2].threshold == ONE
    r.content[0].breakTests[2].thresholdLevel == 2
    r.content[0].breakTests[2].value == TEN
    r.content[0].resolution.resolution == OVERRIDE_USER
    r.content[0].resolution.resolutionComment == "resolutionComment"
    r.content[0].resolution.value == TEN
    r.content[0].resolution.providerName == null
    r.content[0].resolution.providerType == MANUAL
    r.content[0].resolution.approvalComment == "approvalComment"
    // Do not test for previousStatuses as we do not populate them for performance reasons
    r.content[0].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_2
    r.content[0].slaDeadline == SlaDeadline.LDN_1200
    r.content[0].pricingSlot == PricingSlot.LDN_1500
    r.content[0].calculationCurrency == "GBP"
  }


  def "should fetch overlay view from workflow with days breaking field"() {
    def p1 = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      parentBusinessKey: "id",
      status: WorkflowStatus.DONE,
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, null),
      currentState: new VdPhaseState(
      breakTestResults: [
        new TradeResultBreak(breakTestName: "test1", threshold: TEN, providerValue: ofMultiLevelBreak(1, ZERO, TEN), daysBreaking: 1),
        new TradeResultBreak(breakTestName: "test2", threshold: TEN, providerValue: ofBreakWithoutLevel(false, null), daysBreaking: 5),
        new TradeResultBreak(breakTestName: "test3", threshold: TEN, providerValue: ofMultiLevelBreak(2, ONE, TEN), daysBreaking: 2),
      ]
      //        previousStatuses: [EntryResultStatusHistory.newOf(audit().user(), now(), VERIFIED, "resolution", "approval")]
      ),
      )

    def p2 = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      parentBusinessKey: "id",
      status: WorkflowStatus.DONE,
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, null),
      currentState: new VdPhaseState(
      breakTestResults: [
        new TradeResultBreak(breakTestName: "test1", threshold: TEN, providerValue: ofMultiLevelBreak(1, ZERO, TEN)),
        new TradeResultBreak(breakTestName: "test2", threshold: TEN, providerValue: ofBreakWithoutLevel(false, null)),
        new TradeResultBreak(breakTestName: "test3", threshold: TEN, providerValue: ofMultiLevelBreak(2, ONE, TEN)),
      ]
      //        previousStatuses: [EntryResultStatusHistory.newOf(audit().user(), now(), VERIFIED, "resolution", "approval")]
      ),
      )

    operations.insertAll([p1, p2])

    when:
    def r = repository.overlayItems(["id"], displayFilterForm(),
    emptyTableFilter(),
    ScrollRequest.of(0, 10)
    )

    then:
    r.content.size() == 2
    r.content[0].maxDaysBreaking == 5
    r.content[0].breakTests[0].daysBreaking == 1
    r.content[0].breakTests[1].daysBreaking == 5
    r.content[0].breakTests[2].daysBreaking == 2

    r.content[1].maxDaysBreaking == 0
    r.content[1].breakTests[0].daysBreaking == 0
    r.content[1].breakTests[1].daysBreaking == 0
    r.content[1].breakTests[2].daysBreaking == 0
  }

  def "should fetch break test values distributions by taskId"() {
    def trade = new Trade(
      productType: CoreProductType.IRS,
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      externalCompanyId: "ecId",
      externalEntityId: "EXT",
      portfolioId: "portfolioId",
      portfolioExternalId: "pExtId",
      currency: "EUR",
      currencyPair: "EUR/USD",
      underlying: "EUR 3M"
      )

    def p1 = new IpvTradeResultOverlay(
      taskId: "id",
      trade: trade,
      hasBreaks: true,
      maxTriggeredThresholdLevel: 1,
      breakTests: [
        new TradeResultBreak(breakTestName: 'Null', providerValue: new EntryResultBreakByProvider(triggered: true, value: 1)),
        new TradeResultBreak(breakTestName: 'Value', providerValue: new EntryResultBreakByProvider(triggered: true, value: 500, triggeredThreshold: 100)),
        new TradeResultBreak(breakTestName: 'D2D', providerValue: new EntryResultBreakByProvider(triggered: true, value: 20, triggeredThreshold: 10),),
        new TradeResultBreak(breakTestName: 'P2P', providerValue: new EntryResultBreakByProvider(triggered: false)),
      ]
      )
    def p2 = new IpvTradeResultOverlay(
      taskId: "id",
      trade: trade,
      hasBreaks: true,
      maxTriggeredThresholdLevel: 1,
      breakTests: [
        new TradeResultBreak(breakTestName: 'Value', providerValue: new EntryResultBreakByProvider(triggered: true, value: 22, triggeredThreshold: 10)),
        new TradeResultBreak(breakTestName: 'D2D', providerValue: new EntryResultBreakByProvider(triggered: true, value: 13)),
        new TradeResultBreak(breakTestName: 'P2P', providerValue: new EntryResultBreakByProvider(triggered: false, triggeredThreshold: 99)),
      ]
      )
    def p3 = new IpvTradeResultOverlay(
      taskId: "id",
      trade: trade,
      breakTests: [
        new TradeResultBreak(breakTestName: 'Value', providerValue: new EntryResultBreakByProvider(triggered: false, triggeredThreshold: 1)),
        new TradeResultBreak(breakTestName: 'D2D', providerValue: new EntryResultBreakByProvider(triggered: true, triggeredThreshold: 11)),
        new TradeResultBreak(breakTestName: 'P2P', providerValue: new EntryResultBreakByProvider(triggered: true, value: 15, triggeredThreshold: 8)),
      ]
      )

    operations.insertAll([p1, p2, p3])

    when:
    def r = repository.resultValuesDistribution(["id"], tradeFilterForm())

    then:
    r.size() == 3
    r[0].breakTestName == "D2D"
    r[0].thresholds == [10]
    r[0].values == [20]
    r[1].breakTestName == "P2P"
    r[1].thresholds == [8]
    r[1].values == [15]
    r[2].breakTestName == "Value"
    r[2].thresholds.sort(false) == [10, 100]
    r[2].values.sort(false) == [22, 500]
  }

  def "should return views from workflow filtered"() {
    setup:
    def breaksWithoutAndWithTriggered = [
      new TradeResultBreak(breakTestName: "Test1", sequence: 1, providerValue: new EntryResultBreakByProvider(triggered: true), daysBreaking: 1),
      new TradeResultBreak(breakTestName: "Test2", sequence: 2, providerValue: new EntryResultBreakByProvider(triggered: false))
    ]

    def breaksWithAllTriggered = [
      new TradeResultBreak(breakTestName: "Test1", sequence: 1, hidden: true, providerValue: new EntryResultBreakByProvider(triggered: true, triggeredThresholdLevel: 2), daysBreaking: 1),
      new TradeResultBreak(breakTestName: "Test2", sequence: 2, providerValue: new EntryResultBreakByProvider(triggered: true), daysBreaking: 2),
      new TradeResultBreak(breakTestName: "Test3", sequence: 3, providerValue: new EntryResultBreakByProvider(triggered: true, triggeredThresholdLevel: 3), daysBreaking: 3)
    ]

    def resultWithoutBreaks = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      businessKey: "0:resultWithoutBreaks",
      rootBusinessKey: "id",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, null),
      currentState: new VdPhaseState(
      breakTestResults: [],
      hasBreak: false
      ),
      status: WorkflowStatus.DONE
      )
    def resolvedResultWithBreaks = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      businessKey: "1:resolvedResultWithBreaks",
      rootBusinessKey: "id",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, null),
      currentState: new VdPhaseState(
      breakTestResults: breaksWithoutAndWithTriggered,
      hasBreak: true,
      resolution: OVERRIDE_USER,
      resolutionComment: "resolutionComment",
      resolutionEvidence: new ExceptionManagementEvidence(
      fileId: "ID",
      filename: "name"
      ),
      valuePendingApproval: new AttributedValue(TEN, P2, "BBG"),
      entryStatus: WAITING_APPROVAL,
      ),
      status: WorkflowStatus.ACTIVE
      )
    def rejectedResultWithBreaks = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      businessKey: "3:rejectedResultWithBreaks",
      rootBusinessKey: "id",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, null),
      currentState: new VdPhaseState(
      breakTestResults: breaksWithoutAndWithTriggered,
      hasBreak: true,
      resolution: OVERRIDE_USER,
      resolutionComment: "resolutionComment",
      approval: VerificationStatus.REJECTED,
      approvalComment: "Rejected",
      approvalEvidence: new ExceptionManagementEvidence(
      fileId: "ID2",
      filename: "name2"
      ),
      entryStatus: REJECTED,
      ),
      status: WorkflowStatus.ACTIVE,
      )
    def verifiedResultWithAllBreaks = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      businessKey: "2:verifiedResultWithAllBreaks",
      rootBusinessKey: "id",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, null),
      currentState: new VdPhaseState(
      breakTestResults: breaksWithAllTriggered,
      hasBreak: true,
      resolution: KEEP,
      resolutionComment: "resolutionComment",
      approvalComment: "Approved",
      approval: VerificationStatus.VERIFIED,
      entryStatus: VERIFIED,
      ),
      status: WorkflowStatus.DONE,
      )
    def pendingResultWithBreaks = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      businessKey: "4:pendingResultWithBreaks",
      rootBusinessKey: "id",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, null),
      currentState: new VdPhaseState(
      breakTestResults: breaksWithoutAndWithTriggered,
      hasBreak: true,
      resolution: null,
      entryStatus: WAITING_RESOLUTION,
      ),
      status: WorkflowStatus.ACTIVE
      )
    def heldResultWithBreaks = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      businessKey: "5:heldResultWithBreaks",
      rootBusinessKey: "id",
      context: new VdPhaseContext(null, null, null, null, null, null, null, null, OVERLAY_1, null),
      currentState: new VdPhaseState(
      breakTestResults: breaksWithoutAndWithTriggered,
      hasBreak: true,
      resolution: null,
      entryStatus: WAITING_RESOLUTION,
      ),
      status: WorkflowStatus.HELD,
      holdReason: "HOLD_REASON"
      )

    operations.insertAll([
      resultWithoutBreaks,
      resolvedResultWithBreaks,
      verifiedResultWithAllBreaks,
      rejectedResultWithBreaks,
      pendingResultWithBreaks,
      heldResultWithBreaks
    ]
    )

    def result
    when: 'only results with breaks'
    result = repository.overlayItems(["id"],
    displayFilterForm(true),
    emptyTableFilter(),
    ScrollRequest.of(0, 10, Sort.by(IpvTradeOverlayResultView.Fields.id))
    )

    then:
    result.content.size() == 4

    result.content[0].id == resolvedResultWithBreaks.businessKey
    result.content[0].breakTests.size() == 2
    result.content[0].breakTests*.sequence == [1, 2]
    result.content[0].resolution.resolutionEvidence.fileId == "ID"
    result.content[0].resolution.resolutionEvidence.filename == "name"

    result.content[1].id == verifiedResultWithAllBreaks.businessKey
    result.content[1].breakTests.size() == 3
    result.content[1].breakTests*.hidden == [true, null, null]
    result.content[1].maxTriggeredThresholdLevel == CoreThresholdLevel.LEVEL_3

    result.content[2].id == rejectedResultWithBreaks.businessKey
    result.content[2].breakTests.size() == 2
    result.content[2].resolution.approvalEvidence.fileId == "ID2"
    result.content[2].resolution.approvalEvidence.filename == "name2"

    result.content[3].id == pendingResultWithBreaks.businessKey
    result.content[3].breakTests.size() == 2

    when: 'include held results'
    result = repository.overlayItems(["id"],
    displayFilterForm(true, true),
    emptyTableFilter(),
    ScrollRequest.of(0, 10, Sort.by(IpvTradeOverlayResultView.Fields.id))
    )

    then:
    result.content.size() == 5

    result.content[4].id == heldResultWithBreaks.businessKey
    result.content[4].breakTests.size() == 2

    when: 'all results'
    result = repository.overlayItems(["id"],
    displayFilterForm(false, true),
    emptyTableFilter(),
    ScrollRequest.of(0, 10, Sort.by(IpvTradeOverlayResultView.Fields.id))
    )

    then:
    result.content.size() == 6

    result.content[0].id == resultWithoutBreaks.businessKey
    result.content[1].id == resolvedResultWithBreaks.businessKey
    result.content[2].id == verifiedResultWithAllBreaks.businessKey
    result.content[3].id == rejectedResultWithBreaks.businessKey
    result.content[4].id == pendingResultWithBreaks.businessKey
    result.content[5].id == heldResultWithBreaks.businessKey
  }

  def "should return 0 max days breaking from workflow"() {
    setup:
    def resultWithoutBreaks = new ProcessExecution(
      processId: VD_XM_PHASE_PROCESS_ID,
      businessKey: "resultWithoutBreaks",
      parentBusinessKey: "id",
      currentState: new VdPhaseState(
      breakTestResults: []
      )
      )

    operations.insertAll([resultWithoutBreaks])

    def result
    when:
    result = repository.overlayItems(["id"],
    displayFilterForm(false),
    emptyTableFilter(),
    ScrollRequest.of(0, 10, Sort.by(IpvTradeOverlayResultView.Fields.id))
    )

    then:
    result.content.size() == 1

    result.content[0].id == "resultWithoutBreaks"
    result.content[0].breakTests.size() == 0
    result.content[0].maxDaysBreaking == 0
  }

  def "should fetch portfolio item results"() {
    def p1 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: VERIFIED,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      calculationCurrency: "EUR",
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),
      breakTests: [new TradeResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: WAITING_APPROVAL,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      calculationCurrency: "USD",
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId2",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),)

    operations.insertAll([p1, p2])

    when:
    def r = repository.portfolioResultItems("id",
      "portfolioId", OVERLAY_1,
      null,
      new IpvPortfolioItemResultFilter(null, null),
      emptyTableFilter(),
      ScrollRequest.of(0, 10))

    then:
    r.content.size() == 2
    r.content[0].id == p1.id
    r.content[0].tradeInfoTradeType == CoreProductType.IRS
    r.content[0].tradeInfoExternalTradeId == "externalTradeId"
    r.content[0].tradeId == "tradeId"
    r.content[0].resolvedValue == TEN
    r.content[0].resolution == SWITCH_TO_SECONDARY
    r.content[0].resolutionProviderName == "BBG"
    r.content[0].resolutionProviderType == P2
    r.content[0].calculationCurrency == "EUR"
    r.content[0].verifiedAt.truncatedTo(SECONDS) == p1.modifiedAt.truncatedTo(SECONDS)

    r.content[1].id == p2.id
    r.content[1].tradeInfoTradeType == CoreProductType.IRS
    r.content[1].tradeInfoExternalTradeId == "externalTradeId"
    r.content[1].tradeId == "tradeId2"
    r.content[1].resolvedValue == null
    r.content[1].calculationCurrency == null
    r.content[1].resolution == null
    r.content[1].resolutionProviderName == null
    r.content[1].resolutionProviderType == null
    r.content[1].verifiedAt == null
  }

  def "should fetch all portfolio item results"() {
    def p1 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: VERIFIED,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      calculationCurrency: "EUR",
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),
      breakTests: [new TradeResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: WAITING_APPROVAL,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      calculationCurrency: "USD",
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId2",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),)

    operations.insertAll([p1, p2])

    when:
    def r = repository.allPortfolioResultItems("id",
      OVERLAY_1,
      new IpvPortfolioItemResultFilter(null, null),
      emptyTableFilter(),
      ScrollRequest.of(0, 10))

    then:
    r.content.size() == 2
    r.content[0].id == p1.id
    r.content[0].tradeInfoTradeType == CoreProductType.IRS
    r.content[0].tradeInfoExternalTradeId == "externalTradeId"
    r.content[0].tradeId == "tradeId"
    r.content[0].resolvedValue == TEN
    r.content[0].resolution == SWITCH_TO_SECONDARY
    r.content[0].resolutionProviderName == "BBG"
    r.content[0].resolutionProviderType == P2
    r.content[0].calculationCurrency == "EUR"
    r.content[0].verifiedAt.truncatedTo(SECONDS) == p1.modifiedAt.truncatedTo(SECONDS)

    r.content[1].id == p2.id
    r.content[1].tradeInfoTradeType == CoreProductType.IRS
    r.content[1].tradeInfoExternalTradeId == "externalTradeId"
    r.content[1].tradeId == "tradeId2"
    r.content[1].resolvedValue == null
    r.content[1].calculationCurrency == null
    r.content[1].resolution == null
    r.content[1].resolutionProviderName == null
    r.content[1].resolutionProviderType == null
    r.content[1].verifiedAt == null
  }

  def "should fetch only verified portfolio items"() {
    def p1 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: VERIFIED,
      phase: OVERLAY_2,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),
      breakTests: [new TradeResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p1_1 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: VERIFIED,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),
      breakTests: [new TradeResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: WAITING_APPROVAL,
      phase: OVERLAY_2,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId2",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),)

    operations.insertAll([p1, p1_1, p2])

    when:
    def r = repository.portfolioResultItems("id",
      "portfolioId", OVERLAY_2,
      null,
      new IpvPortfolioItemResultFilter(true, null),
      emptyTableFilter(),
      ScrollRequest.of(0, 10))

    then:
    r.content.size() == 1
    r.content[0].tradeInfoTradeType == CoreProductType.IRS
    r.content[0].tradeInfoExternalTradeId == "externalTradeId"
    r.content[0].tradeId == "tradeId"
    r.content[0].resolvedValue == TEN
    r.content[0].resolution == SWITCH_TO_SECONDARY
    r.content[0].resolutionProviderName == "BBG"
    r.content[0].resolutionProviderType == P2
    r.content[0].verifiedAt.truncatedTo(SECONDS) == p1.modifiedAt.truncatedTo(SECONDS)
  }

  def "should fetch all verified portfolio items"() {
    def p1 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: VERIFIED,
      phase: OVERLAY_2,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),
      breakTests: [new TradeResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p1_1 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: VERIFIED,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),
      breakTests: [new TradeResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: WAITING_APPROVAL,
      phase: OVERLAY_2,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId2",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),)

    operations.insertAll([p1, p1_1, p2])

    when:
    def r = repository.allPortfolioResultItems("id",
      OVERLAY_2,
      new IpvPortfolioItemResultFilter(true, null),
      emptyTableFilter(),
      ScrollRequest.of(0, 10))

    then:
    r.content.size() == 1
    r.content[0].tradeInfoTradeType == CoreProductType.IRS
    r.content[0].tradeInfoExternalTradeId == "externalTradeId"
    r.content[0].tradeId == "tradeId"
    r.content[0].resolvedValue == TEN
    r.content[0].resolution == SWITCH_TO_SECONDARY
    r.content[0].resolutionProviderName == "BBG"
    r.content[0].resolutionProviderType == P2
    r.content[0].verifiedAt.truncatedTo(SECONDS) == p1.modifiedAt.truncatedTo(SECONDS)
  }

  def "should fetch only verified after timestamp portfolio items"() {
    def p1 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: VERIFIED,
      phase: OVERLAY_2,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),
      breakTests: [new TradeResultBreak(providerValue: ofBreakWithoutLevel(true, null))])

    operations.insertAll([p1])

    when:
    def r = repository.portfolioResultItems("id",
      "portfolioId", OVERLAY_2,
      null,
      new IpvPortfolioItemResultFilter(true, timeStamp),
      emptyTableFilter(),
      ScrollRequest.of(0, 10))

    then:
    r.content.size() == resultsCount

    where:
    timeStamp             | resultsCount
    now().plusSeconds(2)  | 0
    now().minusSeconds(2) | 1
  }

  def "should fetch all verified portfolio items after timestamp"() {
    def p1 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: VERIFIED,
      phase: OVERLAY_2,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),
      breakTests: [new TradeResultBreak(providerValue: ofBreakWithoutLevel(true, null))])

    operations.insertAll([p1])

    when:
    def r = repository.allPortfolioResultItems("id",
      OVERLAY_2,
      new IpvPortfolioItemResultFilter(true, timeStamp),
      emptyTableFilter(),
      ScrollRequest.of(0, 10))

    then:
    r.content.size() == resultsCount

    where:
    timeStamp             | resultsCount
    now().plusSeconds(10)  | 0
    now().minusSeconds(10) | 1
  }


  def "should stream portfolio item results"() {
    def p1 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: VERIFIED,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      calculationCurrency: "EUR",
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),
      breakTests: [new TradeResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: WAITING_APPROVAL,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      calculationCurrency: "USD",
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId2",
      portfolioId: "portfolioId",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),)

    operations.insertAll([p1, p2])

    when:
    def r = repository.portfolioResultItemsStream("id",
      "portfolioId", OVERLAY_1, emptyTableFilter()).toList()

    then:
    r.size() == 2
    r[0].id == p1.id
    r[0].tradeInfoTradeType == CoreProductType.IRS
    r[0].tradeInfoExternalTradeId == "externalTradeId"
    r[0].tradeId == "tradeId"
    r[0].resolvedValue == TEN
    r[0].resolution == SWITCH_TO_SECONDARY
    r[0].resolutionProviderName == "BBG"
    r[0].resolutionProviderType == P2
    r[0].calculationCurrency == "EUR"
    r[0].verifiedAt.truncatedTo(SECONDS) == p1.modifiedAt.truncatedTo(SECONDS)

    r[1].id == p2.id
    r[1].tradeInfoTradeType == CoreProductType.IRS
    r[1].tradeInfoExternalTradeId == "externalTradeId"
    r[1].tradeId == "tradeId2"
    r[1].resolvedValue == null
    r[1].calculationCurrency == null
    r[1].resolution == null
    r[1].resolutionProviderName == null
    r[1].resolutionProviderType == null
    r[1].verifiedAt == null
  }

  def "should stream all portfolio item results"() {
    def p1 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: VERIFIED,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      calculationCurrency: "EUR",
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId",
      portfolioId: "portfolioId1",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId1",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key1",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),
      breakTests: [new TradeResultBreak(providerValue: ofBreakWithoutLevel(true, null))])
    def p2 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: WAITING_APPROVAL,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      calculationCurrency: "USD",
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId2",
      portfolioId: "portfolioId2",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId2",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key2",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),)
    def p3 = new IpvTradeResultOverlay(
      dashboardId: "id",
      status: WAITING_APPROVAL,
      phase: OVERLAY_1,
      resolution: new IpvTradeResultResolution(resolution: SWITCH_TO_SECONDARY,
      resolutionComment: null,
      value: null,
      providerName: "BBG",
      providerType: P2,
      approvalComment: null,),
      resolvedValue: TEN,
      calculationCurrency: "USD",
      secondaryProviderData: new ProviderDataWithGreeks(provider: "BBG"),
      trade: new Trade(entityId: "tradeId3",
      portfolioId: "portfolioId3",
      productType: CoreProductType.IRS,
      externalTradeId: "externalTradeId3",
      creditSector: CreditSector.CONSUMER_GOODS,
      key: "key3",
      tradeDetails: TradeDetailsBuilder.irsDetails(LocalDate.now()),
      currency: "EUR",
      currencyPair: "EUR/USD"),)

    operations.insertAll([p1, p2, p3])

    when:
    def r = repository.allPortfoliosResultItemsStream("id",
      [
        new DashboardPortfolio(
        EntityReference.newOf("companyId", "company"),
        EntityReference.newOf("entityId", "entity"),
        EntityReference.newOf("portfolioId1", "portfolio 1"), [], false),
        new DashboardPortfolio(
        EntityReference.newOf("companyId", "company"),
        EntityReference.newOf("entityId", "entity"),
        EntityReference.newOf("portfolioId2", "portfolio 2"), [], false)
      ],
      OVERLAY_1).toList()

    then:
    r.size() == 2
    r[0].id == p1.id
    r[0].tradeInfoTradeType == CoreProductType.IRS
    r[0].tradeInfoExternalTradeId == "externalTradeId1"
    r[0].tradeId == "tradeId"
    r[0].resolvedValue == TEN
    r[0].resolution == SWITCH_TO_SECONDARY
    r[0].resolutionProviderName == "BBG"
    r[0].resolutionProviderType == P2
    r[0].calculationCurrency == "EUR"
    r[0].verifiedAt.truncatedTo(SECONDS) == p1.modifiedAt.truncatedTo(SECONDS)

    r[1].id == p2.id
    r[1].tradeInfoTradeType == CoreProductType.IRS
    r[1].tradeInfoExternalTradeId == "externalTradeId2"
    r[1].tradeId == "tradeId2"
    r[1].resolvedValue == null
    r[1].calculationCurrency == null
    r[1].resolution == null
    r[1].resolutionProviderName == null
    r[1].resolutionProviderType == null
    r[1].verifiedAt == null
  }

  def "should return portfolio result counts"() {
    setup:
    def p0 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_1, status: VERIFIED, trade: new Trade(portfolioId: "portfolioId"), ipvDataGroupId: "id1")
    def p1 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: WAITING_RESOLUTION, trade: new Trade(portfolioId: "portfolioId"), ipvDataGroupId: "id1")
    def p2 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: WAITING_RESOLUTION, trade: new Trade(portfolioId: "portfolioId"), ipvDataGroupId: "id1")
    def p3 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: WAITING_APPROVAL, trade: new Trade(portfolioId: "portfolioId1"), ipvDataGroupId: "id1")
    def p4 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: VERIFIED, trade: new Trade(portfolioId: "portfolioId"), ipvDataGroupId: "id1")
    def p5 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: VERIFIED, trade: new Trade(portfolioId: "portfolioId1"), ipvDataGroupId: "id1")
    def p6 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: VERIFIED, trade: new Trade(portfolioId: "portfolioId1"), ipvDataGroupId: "id2")

    operations.insertAll([p0, p1, p2, p3, p4, p5, p6])

    when:
    def results = repository.portfolioResultStatusCounts("dashboardId", OVERLAY_2)

    then:
    results.size() == 2
    def sorted = new ArrayList<>(results)
    sorted.sort { it.portfolioId }

    sorted[0].portfolioId == "portfolioId"
    sorted[0].totalCount == 3
    sorted[0].verifiedCount == 1

    sorted[1].portfolioId == "portfolioId1"
    sorted[1].totalCount == 3
    sorted[1].verifiedCount == 2
  }

  def "should return workflow portfolio result counts"() {
    setup:
    def p0 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_1, status: VERIFIED, trade: new Trade(portfolioId: "portfolioId"), ipvDataGroupId: "id1")
    def p4 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: VERIFIED, trade: new Trade(portfolioId: "portfolioId"), ipvDataGroupId: "id1")
    def p5 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: VERIFIED, trade: new Trade(portfolioId: "portfolioId1"), ipvDataGroupId: "id1")
    def p6 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: VERIFIED, trade: new Trade(portfolioId: "portfolioId1"), ipvDataGroupId: "id2")
    operations.insertAll([p0, p4, p5, p6])

    def vdDashboard = new ProcessExecution(processId: VD_XM_PROCESS_ID, rootBusinessKey: "urn:dashboard:dashboardId", status: WorkflowStatus.ACTIVE, currentState: new VdDashboardState(
    trades: [
      new PricingSlotTradeWithProviders(null, null, "portfolioId", null, null, null),
      new PricingSlotTradeWithProviders(null, null, "portfolioId", null, null, null),
      new PricingSlotTradeWithProviders(null, null, "portfolioId1", null, null, null),
      new PricingSlotTradeWithProviders(null, null, "portfolioId", null, null, null),
      new PricingSlotTradeWithProviders(null, null, "portfolioId1", null, null, null),
      new PricingSlotTradeWithProviders(null, null, "portfolioId1", null, null, null),
    ])
    )
    def vdEntry1 = new ProcessExecution(processId: VD_XM_TRADE_PROCESS_ID, rootBusinessKey: "urn:dashboard:dashboardId", status: WorkflowStatus.HELD, context: new VdEntryContext(null, null, null, null, null, new Trade(portfolioId: "portfolioId"), null, null))
    def vdEntry3 = new ProcessExecution(processId: VD_XM_TRADE_PROCESS_ID, rootBusinessKey: "urn:dashboard:dashboardId", status: WorkflowStatus.ACTIVE, context: new VdEntryContext(null, null, null, null, null, new Trade(portfolioId: "portfolioId1"), null, null))
    def vdEntry4 = new ProcessExecution(processId: VD_XM_TRADE_PROCESS_ID, rootBusinessKey: "urn:dashboard:dashboardId", status: WorkflowStatus.DONE, context: new VdEntryContext(null, null, null, null, null, new Trade(portfolioId: "portfolioId"), null, null))
    def vdEntry5 = new ProcessExecution(processId: VD_XM_TRADE_PROCESS_ID, rootBusinessKey: "urn:dashboard:dashboardId", status: WorkflowStatus.DONE, context: new VdEntryContext(null, null, null, null, null, new Trade(portfolioId: "portfolioId1"), null, null))
    def vdEntry6 = new ProcessExecution(processId: VD_XM_TRADE_PROCESS_ID, rootBusinessKey: "urn:dashboard:dashboardId", status: WorkflowStatus.DONE, context: new VdEntryContext(null, null, null, null, null, new Trade(portfolioId: "portfolioId1"), null, null))
    operations.insertAll([vdDashboard, vdEntry1, vdEntry3, vdEntry4, vdEntry5, vdEntry6])

    when:
    def results = repository.portfolioResultStatusCounts("dashboardId", OVERLAY_2)

    then: "we have results for both portfolios"
    results.size() == 2
    def counts = results.collectEntries { [it.portfolioId, it] }
    counts.keySet().containsAll(["portfolioId", "portfolioId1"])

    and: "the total count is the count per portfolio from the dashboard process trades"
    counts["portfolioId"].totalCount == 3
    counts["portfolioId1"].totalCount == 3

    and: "the verified count is the number of DONE entry processes, per portfolio"
    counts["portfolioId"].verifiedCount == 1
    counts["portfolioId1"].verifiedCount == 2
  }

  def "should load portfolio break history"() {
    setup:
    operations.insertAll([
      new IpvTradeResultOverlay(phase: OVERLAY_1, dashboardId: "dashboardId", hasBreaks: true, breakTests: [
        new TradeResultBreak(breakTestId: "T1", daysBreaking: 1, providerValue: new EntryResultBreakByProvider(triggered: true))
      ], trade: new Trade(key: "K1", portfolioId: "portfolioId")),
      new IpvTradeResultOverlay(phase: OVERLAY_2, dashboardId: "dashboardId", hasBreaks: true, breakTests: [
        new TradeResultBreak(breakTestId: "T1", daysBreaking: 1, providerValue: new EntryResultBreakByProvider(triggered: true))
      ], trade: new Trade(key: "K1", portfolioId: "portfolioId")),
      new IpvTradeResultOverlay(phase: OVERLAY_1, dashboardId: "dashboardId", hasBreaks: true, breakTests: [
        new TradeResultBreak(breakTestId: "T2", daysBreaking: 2, providerValue: new EntryResultBreakByProvider(triggered: true))
      ], trade: new Trade(key: "K2", portfolioId: "portfolioId")),
      new IpvTradeResultOverlay(phase: OVERLAY_1, dashboardId: "dashboardId", hasBreaks: true, breakTests: [
        new TradeResultBreak(breakTestId: "T1", daysBreaking: 3, providerValue: new EntryResultBreakByProvider(triggered: true)),
        new TradeResultBreak(breakTestId: "T2", daysBreaking: 4)
      ], trade: new Trade(key: "K3", portfolioId: "portfolioId")),
      new IpvTradeResultOverlay(phase: OVERLAY_1, dashboardId: "dashboardId", hasBreaks: true, breakTests: [
        new TradeResultBreak(breakTestId: "T2", daysBreaking: 4, providerValue: new EntryResultBreakByProvider(triggered: true)),
        new TradeResultBreak(breakTestId: "T1", daysBreaking: 5, providerValue: new EntryResultBreakByProvider(triggered: true))
      ], trade: new Trade(key: "K4", portfolioId: "portfolioId")),
      new IpvTradeResultOverlay(phase: OVERLAY_1, dashboardId: "dashboardId2", hasBreaks: true, breakTests: [
        new TradeResultBreak(breakTestId: "IGNORED", daysBreaking: 3, providerValue: new EntryResultBreakByProvider(triggered: true))
      ], trade: new Trade(portfolioId: "portfolioId")),
      new IpvTradeResultOverlay(phase: OVERLAY_1, dashboardId: "dashboardId", hasBreaks: true, breakTests: [
        new TradeResultBreak(breakTestId: "IGNORED1", daysBreaking: 2, providerValue: new EntryResultBreakByProvider(triggered: true))
      ], trade: new Trade(portfolioId: "portfolioId1")),
      new IpvTradeResultOverlay(phase: OVERLAY_1, dashboardId: "dashboardId", hasBreaks: false, breakTests: [new TradeResultBreak(breakTestId: "IGNORED2", daysBreaking: 5)], trade: new Trade(portfolioId: "portfolioId"))
    ])

    when:
    def results = repository.portfolioDashboardBreaksHistory("dashboardId", OVERLAY_1, "portfolioId")

    then:
    results.size() == 4
    def sorted = new ArrayList<>(results)
    sorted.sort { it.uniqueKey }

    sorted[0].uniqueKey == "K1"
    sorted[0].breaksHistory == [new BreakTestHistory("T1", 1)]

    sorted[1].uniqueKey == "K2"
    sorted[1].breaksHistory == [new BreakTestHistory("T2", 2)]

    sorted[2].uniqueKey == "K3"
    sorted[2].breaksHistory == [new BreakTestHistory("T1", 3)]

    sorted[3].uniqueKey == "K4"
    sorted[3].breaksHistory == [new BreakTestHistory("T2", 4), new BreakTestHistory("T1", 5)]
  }

  def "should override resolutions"() {
    setup:
    def evidence = new ExceptionManagementEvidence(fileId: "fileId", filename: "filename")
    def overrideUser = UserBuilder.userWithNameAndTeams("overrideUserId", "Override User", [])

    def result1 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: VERIFIED, hasBreaks: true, resolution: new IpvTradeResultResolution(resolution: OVERRIDE_USER, resolutionComment: "comment", value: TEN, providerName: "BBG", providerType: P2, approvalComment: "approvalComment"))
    def result2 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: VERIFIED, hasBreaks: false)

    operations.insertAll([result1, result2])

    def form = new ApplyOverrideForm([
      new IndividualOverrideForm(result1.id, new OverrideForm(ONE, "comment1")),
      new IndividualOverrideForm(result2.id, new OverrideForm(BigDecimal.valueOf(2.0d), "comment2")),
    ])

    when:
    def results = repository.overrideResults("dashboardId", OVERLAY_2, form, evidence, overrideUser)

    then:
    results.isRight()
    results.getOrNull().size() == 2

    def entries = operations.findAll(IpvTradeResultOverlay).sort { it.id }
    entries[0].status == VERIFIED
    entries[0].resolution.providerType == MANUAL
    entries[0].resolution.value == ONE
    entries[0].resolution.resolutionComment == "comment1"
    entries[0].resolution.resolutionEvidence == evidence
    entries[0].resolution.approvalComment == "Automatically validated by the system."
    entries[0].resolution.approvalEvidence == new ExceptionManagementEvidence()

    entries[0].previousStatuses[0].modifiedBy == AuditUser.of(user)
    entries[0].previousStatuses[0].resolutionComment == "comment"
    entries[0].previousStatuses[0].approvalComment == "approvalComment"
    entries[0].previousStatuses[0].status == VERIFIED

    entries[0].previousStatuses[1].modifiedBy == AuditUser.of(overrideUser)
    entries[0].previousStatuses[1].resolutionComment == "comment1"
    entries[0].previousStatuses[1].approvalComment == null
    entries[0].previousStatuses[1].status == WAITING_APPROVAL

    entries[1].status == VERIFIED
    entries[1].resolution.providerType == MANUAL
    entries[1].resolution.value == new BigDecimal(2.0)
    entries[1].resolution.resolutionComment == "comment2"
    entries[1].resolution.resolutionEvidence == evidence
    entries[1].resolution.approvalComment == "Automatically validated by the system."
    entries[1].resolution.approvalEvidence == new ExceptionManagementEvidence()
  }

  def "should fail override resolutions when not all entries verified"() {
    setup:
    def evidence = new ExceptionManagementEvidence(fileId: "fileId", filename: "filename")
    def overrideUser = UserBuilder.userWithNameAndTeams("overrideUserId", "Override User", [])

    def result1 = new IpvTradeResultOverlay(dashboardId: "dashboardId", status: VERIFIED, hasBreaks: true, resolution: new IpvTradeResultResolution(resolution: OVERRIDE_USER, resolutionComment: "comment", value: TEN, providerName: "BBG", providerType: P2, approvalComment: "approvalComment"))
    def result2 = new IpvTradeResultOverlay(dashboardId: "dashboardId", status: VERIFIED, hasBreaks: false)
    def result3 = new IpvTradeResultOverlay(dashboardId: "dashboardId", status: WAITING_RESOLUTION)

    operations.insertAll([result1, result2, result3])

    def form = new ApplyOverrideForm([
      new IndividualOverrideForm(result1.id, new OverrideForm(ONE, "comment1")),
      new IndividualOverrideForm(result2.id, new OverrideForm(BigDecimal.valueOf(2.0d), "comment2")),
      new IndividualOverrideForm(result3.id, new OverrideForm(BigDecimal.valueOf(3.0d), "comment3"))
    ])

    when:
    def results = repository.overrideResults("dashboardId", OVERLAY_2, form, evidence, overrideUser)

    then:
    results.isLeft()
    def error = (ErrorItem) results.left().get()
    error.description == "Only verified entries can be overridden. Not all passed entries can be overridden"

    def entries = operations.findAll(IpvTradeResultOverlay).sort { it.id }
    entries[0].status == VERIFIED
    entries[0].resolution.providerType == P2
    entries[0].resolution.value == TEN
    entries[0].resolution.resolutionComment == "comment"
    entries[0].resolution.resolutionEvidence == null
    entries[0].resolution.approvalComment == "approvalComment"
    entries[0].resolution.approvalEvidence == null
    entries[0].previousStatuses == null

    entries[1].status == VERIFIED
    entries[1].resolution == null
    entries[1].previousStatuses == null

    entries[2].status == WAITING_RESOLUTION
    entries[2].resolution == null
  }

  def "should return previous dashboard results for phase (#phase)"() {
    setup:
    def trades = [
      new IpvTradeResultOverlay(
      phase: OVERLAY_1,
      status: VERIFIED,
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade1", portfolioId: "P1", notional: 5e6d),
      resolvedValue: 10,
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100)),
      secondaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 200)),
      tertiaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 300)),
      quaternaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 400))
      ),
      new IpvTradeResultOverlay(
      phase: OVERLAY_2,
      status: VERIFIED,
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade1", portfolioId: "P1", notional: 5e6d),
      resolvedValue: 20,
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100)),
      secondaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 200)),
      tertiaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 300)),
      quaternaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 400))
      ),
      new IpvTradeResultOverlay(
      phase: OVERLAY_1,
      status: VERIFIED,
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade1", portfolioId: "P2"),
      ),
      new IpvTradeResultOverlay(
      phase: OVERLAY_1,
      status: WAITING_APPROVAL,
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade2", portfolioId: "P1"),
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100))),
      new IpvTradeResultOverlay(
      phase: OVERLAY_1,
      status: VERIFIED,
      ipvDataGroupId: "IPV2",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade1", portfolioId: "P1"),
      ),
      new IpvTradeResultOverlay(
      phase: OVERLAY_1,
      status: VERIFIED,
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now().minusDays(1),
      trade: new Trade(key: "trade1", portfolioId: "P1"),
      ),
    ]
    operations.insertAll(trades)

    when:
    def results = repository.getResolvedTradeData(
      Optional.ofNullable(phase), "P1", "IPV1", LocalDate.now())

    then:
    results.size() == 1
    results[0].key == "trade1"
    results[0].valuationDate == LocalDate.now()
    results[0].resolvedValue == expectedValue
    results[0].primaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100.0))
    results[0].secondaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 200.0))
    results[0].tertiaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 300.0))
    results[0].quaternaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 400.0))
    results[0].notional == 5e6d

    where:
    phase       || expectedValue
    OVERLAY_1   || 10
    OVERLAY_2   || 20
    null        || 20
  }

  def "should return previous dashboard results from last phase available if phase not specified"() {
    setup:
    def trades = [
      new IpvTradeResultOverlay(
      phase: OVERLAY_1,
      status: VERIFIED,
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade1", portfolioId: "P1"),
      resolvedValue: 10,
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100)),
      secondaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 200)),
      tertiaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 300)),
      quaternaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 400))
      ),
      new IpvTradeResultOverlay(
      phase: OVERLAY_2,
      status: VERIFIED,
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade1", portfolioId: "P1"),
      resolvedValue: 20,
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100)),
      secondaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 200)),
      tertiaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 300)),
      quaternaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 400))
      ),
      new IpvTradeResultOverlay(
      phase: OVERLAY_1,
      status: VERIFIED,
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade1", portfolioId: "P2"),
      ),
      new IpvTradeResultOverlay(
      phase: OVERLAY_1,
      status: WAITING_APPROVAL,
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade2", portfolioId: "P1"),
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100))),
      new IpvTradeResultOverlay(
      phase: OVERLAY_1,
      status: VERIFIED,
      ipvDataGroupId: "IPV2",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade1", portfolioId: "P1"),
      ),
      new IpvTradeResultOverlay(
      phase: OVERLAY_1,
      status: VERIFIED,
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now().minusDays(1),
      trade: new Trade(key: "trade1", portfolioId: "P1"),
      ),
    ]
    operations.insertAll(trades)

    when:
    def results = repository.getResolvedTradeData(Optional.empty(), "P1", "IPV1", LocalDate.now())
    then:
    results.size() == 1
    results[0].key == "trade1"
    results[0].valuationDate == LocalDate.now()
    results[0].resolvedValue == 20
    results[0].primaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100.0))
    results[0].secondaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 200.0))
    results[0].tertiaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 300.0))
    results[0].quaternaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 400.0))
  }

  def "should return all dashboard verified instruments"() {
    setup:
    def trades = [
      new IpvTradeResultOverlay(
      status: VERIFIED,
      phase: OVERLAY_1,
      dashboardId: "dashboardId",
      ipvDataGroupId: "IPV1",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade1", portfolioId: "P1"),
      resolvedValue: 10,
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100)),
      secondaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 200)),
      tertiaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 300)),
      quaternaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 400))
      ),
      //Skip because of invalid phase
      new IpvTradeResultOverlay(
      status: VERIFIED,
      phase: OVERLAY_2,
      ipvDataGroupId: "IPV1",
      dashboardId: "dashboardId",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade2", portfolioId: "P2"),
      ),
      //Second trade returned
      new IpvTradeResultOverlay(
      status: VERIFIED,
      phase: OVERLAY_1,
      ipvDataGroupId: "IPV1",
      dashboardId: "dashboardId",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade3", portfolioId: "P1"),
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100))),
      // Skipped because of different IPV data group
      new IpvTradeResultOverlay(
      status: VERIFIED,
      ipvDataGroupId: "IPV2",
      dashboardId: "dashboardId",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade4", portfolioId: "P1"),
      ),
      // Skipped because of different dashboard
      new IpvTradeResultOverlay(
      status: VERIFIED,
      ipvDataGroupId: "IPV1",
      dashboardId: "dashboardId2",
      valuationDate: LocalDate.now().minusDays(1),
      trade: new Trade(key: "trade5", portfolioId: "P1"),
      ),
      // Skipped because of wrong phase
      new IpvTradeResultOverlay(
      status: VERIFIED,
      phase: OVERLAY_2,
      ipvDataGroupId: "IPV1",
      dashboardId: "dashboardId",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade3", portfolioId: "P1"),
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100))),
    ]
    operations.insertAll(trades)

    when:
    def results = repository.getFirstPhaseResults("dashboardId", "IPV1")
    then:
    results.size() == 2
    results[0].key == "trade1"
    results[0].valuationDate == LocalDate.now()
    results[0].resolvedValue == 10
    results[0].primaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100.0))
    results[0].secondaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 200.0))
    results[0].tertiaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 300.0))
    results[0].quaternaryProviderData == new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 400.0))

    results[1].key() == "trade3"
  }

  def "should return all phase 1 verified trades"() {
    setup:
    def trades = [
      new IpvTradeResultOverlay(
      status: VERIFIED,
      phase: OVERLAY_1,
      dashboardId: "dashboardId",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade1", portfolioId: "P1", productType: CoreProductType.IRS),
      resolvedValue: 10,
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100)),
      secondaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 200)),
      tertiaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 300)),
      quaternaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 400))
      ),
      //Skip - invalid product type
      new IpvTradeResultOverlay(
      status: VERIFIED,
      phase: OVERLAY_1,
      dashboardId: "dashboardId",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade2", portfolioId: "P1", productType: CoreProductType.XCCY),
      resolvedValue: 10,
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100)),
      secondaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 200)),
      tertiaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 300)),
      quaternaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 400))
      ),
      //Skip because of invalid phase
      new IpvTradeResultOverlay(
      status: VERIFIED,
      phase: OVERLAY_2,
      dashboardId: "dashboardId",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade3", portfolioId: "P1", productType: CoreProductType.IRS),
      ),
      //Different portfolio result
      new IpvTradeResultOverlay(
      status: VERIFIED,
      phase: OVERLAY_1,
      dashboardId: "dashboardId",
      valuationDate: LocalDate.now(),
      trade: new Trade(key: "trade4", portfolioId: "P2", productType: CoreProductType.IRS),
      primaryProviderData: new ProviderDataWithGreeks(pv: new ProviderDataValue(value: 100))),
      // Skipped because of different dashboard
      new IpvTradeResultOverlay(
      status: VERIFIED,
      phase: OVERLAY_1,
      dashboardId: "dashboardId2",
      valuationDate: LocalDate.now().minusDays(1),
      trade: new Trade(key: "trade5", portfolioId: "P2", productType: CoreProductType.IRS),
      )
    ]
    operations.insertAll(trades)

    when:
    def results = repository.getFirstPhaseTrades("dashboardId", "P1", [CoreProductType.IRS]).toList()

    then:
    results.size() == 1
    results[0] == new Trade(key: "trade1", portfolioId: "P1", productType: CoreProductType.IRS)

    when:
    results = repository.getFirstPhaseTrades("dashboardId", "P2", [CoreProductType.IRS]).toList()

    then:
    results.size() == 1
    results[0] == new Trade(key: "trade4", portfolioId: "P2", productType: CoreProductType.IRS)
  }

  def "should clean up data when dashboard is deleted"() {
    given:
    def p1 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_1, status: VERIFIED, trade: new Trade(portfolioId: "portfolioId"))
    def p2 = new IpvTradeResultOverlay(dashboardId: "dashboardId", phase: OVERLAY_2, status: VERIFIED, trade: new Trade(portfolioId: "portfolioId"))
    operations.insertAll([p1, p2])

    when:
    repository.removeDashboardReference(DashboardDeletedEvent.newOf("dashboardId"))
    def remaining = operations.exists(query(where(IpvTradeResultOverlay.Fields.dashboardId).is("dashboardId")), IpvTradeResultOverlay)

    then:
    !remaining
  }

  def static tradeFilterForm(
    List<String> portfolioIds = [],
    List<String> underlyingIndices = [],
    List<ProductType> productTypes = [],
    List<String> breakingTests = [],
    List<Integer> maxTriggeredThresholdLevels = []) {
    new TradeFilterForm(
      portfolioIds,
      underlyingIndices,
      new ProductTypeFilterForm(productTypes: productTypes),
      breakingTests,
      maxTriggeredThresholdLevels
      )
  }

  static def displayFilterForm(Boolean onlyCurvesWithBreaks = false, Boolean includeHeld = false) {
    new ResultDisplayFilterForm(onlyCurvesWithBreaks, includeHeld)
  }

  static def provider(BigDecimal pv = 100, BigDecimal delta = 100, BigDecimal vega = 100, BigDecimal parRate = 100, BigDecimal impliedVol = 100) {
    new ProviderDataWithGreeks(
      pv: new ProviderDataValue(value: pv),
      delta: new ProviderDataValue(value: delta),
      vega: new ProviderDataValue(value: vega),
      parRate: new ProviderDataValue(value: parRate),
      impliedVol: new ProviderDataValue(value: impliedVol),
      )
  }
}
