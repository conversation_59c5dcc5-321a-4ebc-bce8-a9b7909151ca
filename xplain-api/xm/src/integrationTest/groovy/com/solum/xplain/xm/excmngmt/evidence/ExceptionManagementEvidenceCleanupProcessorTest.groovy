package com.solum.xplain.xm.excmngmt.evidence

import static org.springframework.data.mongodb.core.query.Criteria.where

import com.solum.xplain.core.helper.IntegrationSpecification
import jakarta.annotation.Resource
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.gridfs.GridFsOperations
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class ExceptionManagementEvidenceCleanupProcessorTest extends IntegrationSpecification {
  @Resource
  MongoOperations operations
  @Resource
  GridFsOperations gridFsOperations

  @Resource
  ExceptionManagementEvidenceCleanupProcessor cleaner

  def cleanup() {
    operations.remove(new Query(), "fs.files")
    operations.remove(new Query(), "fs.chunks")
  }

  def "should remove evidence without references"() {
    setup:
    def metadata = new EvidenceFileMetadata()
    metadata.setDashboardIds([])

    def bytes = new byte[2]
    def fileId = gridFsOperations.store(new ByteArrayInputStream(bytes), "testFile.csv", metadata).toHexString()

    when:
    cleaner.cleanUp()

    then:
    def loaded = gridFsOperations.findOne(Query.query(where("_id").is(fileId)))
    loaded == null
  }

  def "should not remove evidence with references"() {
    setup:
    def metadata = new EvidenceFileMetadata()
    metadata.setDashboardIds(["id1", "id2"])

    def bytes = new byte[2]
    def fileId = gridFsOperations.store(new ByteArrayInputStream(bytes), "testFile.csv", metadata).toHexString()

    when:
    cleaner.cleanUp()

    then:
    def loaded = gridFsOperations.findOne(Query.query(where("_id").is(fileId)))
    loaded != null
  }
}
