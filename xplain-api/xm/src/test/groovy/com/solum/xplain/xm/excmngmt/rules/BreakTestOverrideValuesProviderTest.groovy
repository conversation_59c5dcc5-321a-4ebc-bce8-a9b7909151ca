package com.solum.xplain.xm.excmngmt.rules

import static com.opengamma.strata.basics.currency.Currency.EUR
import static com.opengamma.strata.basics.currency.Currency.USD
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.CAPFLOOR_VOLS
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.CDS
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_RATES
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_SWAP
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_VOLS
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.FX_VOL_SKEW
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.INFLATION_RATE
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.IR_RATE
import static com.solum.xplain.core.curvegroup.instrument.CoreAssetClass.SWAPTION_VOLS
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.BOND_YIELD
import static com.solum.xplain.core.curvegroup.instrument.CoreInstrumentType.FIXED_IBOR_SWAP
import static org.hamcrest.Matchers.anything
import static org.hamcrest.Matchers.containsInAnyOrder
import static org.hamcrest.Matchers.empty
import static org.hamcrest.Matchers.hasSize
import static spock.util.matcher.HamcrestSupport.that

import com.solum.xplain.core.instrument.InstrumentTypeResolver
import com.solum.xplain.core.instrument.InstrumentTypeResolverHelper
import com.solum.xplain.xm.excmngmt.rules.filter.AssetFilter
import spock.lang.Specification
import spock.lang.Unroll

class BreakTestOverrideValuesProviderTest extends Specification {
  static InstrumentTypeResolver typeResolver = InstrumentTypeResolverHelper.coreResolver()

  @Unroll
  def "should return correct curves for #assetClass #rateCcys #irInstruments"() {
    setup:
    AssetFilter assetFilter = new AssetFilter(
      assetClasses: [assetClass],
      rateCcys: rateCcys,
      irInstruments: irInstruments
      )

    when:
    def provider = valuesProvider(assetFilter)
    def curveNames = provider.getCurveNames()

    then:
    that curveNames, hasSize(curvesCount)
    that curveNames, curvesMatcher

    where:
    assetClass     | rateCcys             | irInstruments     | curvesCount | curvesMatcher
    IR_RATE        | []                   | [FIXED_IBOR_SWAP] | 52          | anything()
    IR_RATE        | [EUR.code]           | []                | 22          | anything()
    IR_RATE        | [EUR.code]           | [FIXED_IBOR_SWAP] | 3           | containsInAnyOrder("EUR 1M", "EUR 3M", "EUR 6M")
    IR_RATE        | [EUR.code, USD.code] | [FIXED_IBOR_SWAP] | 4           | containsInAnyOrder("EUR 1M", "EUR 3M", "EUR 6M", "USD 3M")
    IR_RATE        | [USD.code]           | [BOND_YIELD]      | 1           | containsInAnyOrder("UST")

    INFLATION_RATE | []                   | []                | 0           | empty()

    SWAPTION_VOLS  | []                   | []                | 0           | empty()
    CAPFLOOR_VOLS  | []                   | []                | 0           | empty()

    CDS            | []                   | []                | 0           | empty()

    FX_SWAP        | []                   | []                | 0           | empty()
    FX_RATES       | []                   | []                | 0           | empty()
    FX_VOLS        | []                   | []                | 0           | empty()
    FX_VOL_SKEW    | []                   | []                | 0           | empty()
  }

  @Unroll
  def "should return correct surfaces for #assetClass #rateCcys #irInstruments"() {
    setup:
    AssetFilter assetFilter = new AssetFilter(
      assetClasses: [assetClass],
      rateCcys: rateCcys,
      irInstruments: irInstruments
      )

    when:
    def provider = valuesProvider(assetFilter)
    def surfaceNames = provider.getSurfaceNames()

    then:
    that surfaceNames, hasSize(surfacesCount)
    that surfaceNames, surfacesMatcher

    where:
    assetClass     | rateCcys             | irInstruments     | surfacesCount | surfacesMatcher
    IR_RATE        | []                   | [FIXED_IBOR_SWAP] | 0             | empty()
    IR_RATE        | [EUR.code]           | []                | 0             | empty()
    IR_RATE        | [EUR.code]           | [FIXED_IBOR_SWAP] | 0             | empty()
    IR_RATE        | [EUR.code, USD.code] | [FIXED_IBOR_SWAP] | 0             | empty()

    INFLATION_RATE | []                   | []                | 0             | empty()
    INFLATION_RATE | [EUR.code]           | []                | 0             | empty()
    INFLATION_RATE | [EUR.code, USD.code] | []                | 0             | empty()

    SWAPTION_VOLS  | []                   | []                | 54            | anything()
    SWAPTION_VOLS  | [EUR.code]           | []                | 5             | containsInAnyOrder("EUR 3M Vols", "EUR ESTR Vols", "EUR 1M Vols", "EUR EONIA Vols", "EUR 6M Vols")
    SWAPTION_VOLS  | [EUR.code, USD.code] | []                | 8             | containsInAnyOrder("EUR EONIA Vols", "EUR 3M Vols", "EUR 1M Vols", "USD SOFR Vols", "USD FEDFUNDS Vols", "EUR ESTR Vols", "USD 3M Vols", "EUR 6M Vols")

    CAPFLOOR_VOLS  | []                   | []                | 54            | anything()
    CAPFLOOR_VOLS  | [EUR.code]           | []                | 5             | containsInAnyOrder("EUR 3M Vols", "EUR ESTR Vols", "EUR 1M Vols", "EUR EONIA Vols", "EUR 6M Vols")
    CAPFLOOR_VOLS  | [EUR.code, USD.code] | []                | 8             | containsInAnyOrder("EUR EONIA Vols", "EUR 3M Vols", "EUR 1M Vols", "USD SOFR Vols", "USD FEDFUNDS Vols", "EUR ESTR Vols", "USD 3M Vols", "EUR 6M Vols")

    CDS            | []                   | []                | 0             | empty()

    FX_SWAP        | []                   | []                | 0             | empty()
    FX_RATES       | []                   | []                | 0             | empty()
    FX_VOLS        | []                   | []                | 0             | empty()
    FX_VOL_SKEW    | []                   | []                | 0             | empty()
  }

  def "should return correct values for combined asset classes"() {
    setup:
    AssetFilter assetFilter = new AssetFilter(
      assetClasses:
      [
        IR_RATE,
        INFLATION_RATE,
        SWAPTION_VOLS,
        CAPFLOOR_VOLS,
        CDS,
        FX_SWAP,
        FX_RATES,
        FX_VOLS,
        FX_VOL_SKEW
      ],
      rateCcys: [EUR.code],
      irInstruments: [FIXED_IBOR_SWAP]
      )

    when:
    def provider = valuesProvider(assetFilter)
    def curveNames = provider.getCurveNames()
    def surfaceNames = provider.getSurfaceNames()

    then:
    that curveNames, hasSize(3)
    that curveNames, containsInAnyOrder("EUR 1M", "EUR 3M", "EUR 6M")

    that surfaceNames, hasSize(5)
    that surfaceNames, containsInAnyOrder("EUR 3M Vols", "EUR ESTR Vols", "EUR 1M Vols", "EUR EONIA Vols", "EUR 6M Vols")
  }

  def valuesProvider(AssetFilter assetFilter) {
    new BreakTestOverrideValuesProvider(assetFilter, typeResolver.values())
  }
}
