package com.solum.xplain.secmaster.controller

import static com.solum.xplain.core.common.EntityId.entityId
import static com.solum.xplain.core.common.value.ArchiveEntityFormBuilder.archiveEntityForm
import static com.solum.xplain.shared.utils.filter.TableFilter.emptyTableFilter
import static io.atlassian.fugue.Either.right
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put

import com.fasterxml.jackson.databind.ObjectMapper
import com.solum.xplain.core.common.GroupRequest
import com.solum.xplain.core.common.ScrollRequest
import com.solum.xplain.core.common.ScrollableEntry
import com.solum.xplain.core.common.csv.FileResponseEntity
import com.solum.xplain.core.common.csv.ImportOptions
import com.solum.xplain.core.common.value.ArchiveEntityForm
import com.solum.xplain.core.common.value.DateList
import com.solum.xplain.core.portfolio.form.PortfolioItemSearchForm
import com.solum.xplain.secmaster.helpers.MockMvcConfiguration
import java.time.LocalDate
import org.spockframework.spring.SpringBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
@MockMvcConfiguration
@WebMvcTest(controllers = [SecMasterTradeController])
class SecMasterTradeControllerTest extends Specification {

  @SpringBean
  private SecMasterTradeControllerService service = Mock()

  @Autowired
  private ObjectMapper objectMapper

  @Autowired
  private MockMvc mockMvc

  def STATE_DATE = LocalDate.of(2020, 1, 1)

  def "should get trades"() {
    setup:
    1 * service.all(
      { it.getActualDate() == STATE_DATE },
      true,
      emptyTableFilter(),
      _ as ScrollRequest,
      GroupRequest.emptyGroupRequest()) >> ScrollableEntry.empty()

    when:
    def results = mockMvc.perform(get('/security-master')
      .param("stateDate", "2020-01-01")
      .param("withArchived", "true")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should get trades count"() {
    setup:
    1 * service.count({ it.getActualDate() == STATE_DATE }) >> 1L

    when:
    def results = mockMvc.perform(get('/security-master/count')
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
    results.getResponse().getContentAsString() == "1"
  }

  def "should archive trade"() {
    setup:
    service.archive(
      "tradeId",
      LocalDate.parse("2020-01-01"),
      _ as ArchiveEntityForm) >> right(entityId("1"))

    def results = mockMvc.perform(put("/security-master/tradeId/2020-01-01/archive")
      .content(objectMapper.writeValueAsString(form))
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf())).andReturn()
    expect:
    with(results.getResponse()) {
      getStatus() == code
      getContentAsString().indexOf(responseBody) >= 0
    }
    where:
    form                        | code | responseBody
    archiveEntityForm()         | 200  | "id"
    new ArchiveEntityForm(null) | 412  | "NotNull.archiveEntityForm.versionForm"
  }

  def "should delete trade"() {
    setup:
    service.delete("tradeId", STATE_DATE) >> right(entityId("1"))

    when:
    def results = mockMvc.perform(put("/security-master/tradeId/2020-01-01/delete")
      .contentType(MediaType.APPLICATION_JSON)
      .with(csrf())).andReturn()

    then:
    with(results.getResponse()) {
      getStatus() == 200
      getContentAsString().indexOf("id") >= 0
    }
  }

  def "should get trades future versions"() {
    setup:
    1 * service.futureVersions(new PortfolioItemSearchForm("id", STATE_DATE)) >> new DateList([])

    when:
    def results = mockMvc.perform(get('/security-master/future-versions/search')
      .param("stateDate", "2020-01-01")
      .param("externalTradeId", "id")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should get trades in csv format"() {
    setup:
    1 * service.export({ it.getActualDate() == STATE_DATE }, _, _) >> FileResponseEntity.csvFile(new ByteArrayResource("".getBytes()), "name")

    def result = mockMvc.perform(get("/security-master/csv")
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    expect:
    result.getResponse().getStatus() == 200
  }

  def "should upload values csv"() {
    setup:
    def file = new MockMultipartFile("file", "file", MediaType.TEXT_PLAIN_VALUE, "test".bytes)
    1 * service.uploadTrades(_, _ as ImportOptions) >> right([entityId("id")])

    when:
    def results = mockMvc.perform(multipart("/security-master/upload")
      .file(file)
      .param("duplicateAction", "APPEND")
      .param("stateDate", "2020-01-01")
      .with(csrf()))
      .andReturn()

    then:
    results != null
    with(results.getResponse()) {
      getStatus() == 200
    }
  }

  def "should get trade versions"() {
    setup:
    1 * service.itemVersions("id") >> []

    when:
    def results = mockMvc.perform(get('/security-master/id/versions')
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
  }

  def "should get trade allocations count"() {
    setup:
    1 * service.allocationsCount("id", { it.getActualDate() == STATE_DATE }) >> right(1L)

    when:
    def results = mockMvc.perform(get('/security-master/id/allocations/count')
      .param("stateDate", "2020-01-01")
      .contentType(MediaType.APPLICATION_JSON))
      .andReturn()

    then:
    results != null
    results.getResponse().getStatus() == 200
    results.getResponse().getContentAsString() == "1"
  }
}
