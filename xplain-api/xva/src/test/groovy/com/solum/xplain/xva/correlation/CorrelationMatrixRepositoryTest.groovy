package com.solum.xplain.xva.correlation

import static CorrelationMatrixBuilder.correlation
import static com.solum.xplain.core.common.filter.VersionedEntityFilter.active
import static com.solum.xplain.core.users.UserBuilder.user
import static com.solum.xplain.xva.correlation.value.CorrelationType.FX
import static java.time.LocalDate.now
import static java.time.LocalDate.ofEpochDay

import com.solum.xplain.core.authentication.value.XplainPrincipal
import com.solum.xplain.core.common.value.ArchiveEntityForm
import com.solum.xplain.core.common.value.FutureVersionsAction
import com.solum.xplain.core.common.value.NewVersionFormV2
import com.solum.xplain.core.common.versions.BitemporalDate
import com.solum.xplain.core.common.versions.State
import com.solum.xplain.core.helper.IntegrationSpecification
import com.solum.xplain.core.teams.Team
import com.solum.xplain.xva.correlation.value.CorrelationMatrixCreateForm
import com.solum.xplain.xva.correlation.value.CorrelationMatrixRemoveValueForm
import com.solum.xplain.xva.correlation.value.CorrelationMatrixSearchForm
import com.solum.xplain.xva.correlation.value.CorrelationMatrixUpdateForm
import com.solum.xplain.xva.correlation.value.CorrelationMatrixUpdateValueForm
import com.solum.xplain.xva.correlation.value.CorrelationMatrixValueForm
import jakarta.annotation.Resource
import java.time.LocalDate
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.data.mongodb.core.MongoOperations
import org.springframework.data.mongodb.core.query.Query
import org.springframework.security.authentication.TestingAuthenticationToken
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.test.context.ActiveProfiles

@SpringBootTest
@ActiveProfiles("test")
class CorrelationMatrixRepositoryTest extends IntegrationSpecification {
  def static VAL_DT = LocalDate.of(2022, 4, 1)
  def static VERSION_FORM = new NewVersionFormV2("new comment", LocalDate.parse("2020-01-01"), LocalDate.parse("2020-01-01"), FutureVersionsAction.KEEP)

  @Resource
  MongoOperations operations

  @Resource
  CorrelationMatrixRepository repository

  XplainPrincipal creator

  def setup() {
    creator = user("creatorId")
    def auth = new TestingAuthenticationToken(creator, null)
    auth.authenticated = true
    SecurityContextHolder.getContext().authentication = auth
  }

  def cleanup() {
    operations.remove(new Query(), CorrelationMatrix)
    operations.remove(new Query(), Team)
  }


  def "should create correlation matrix"() {
    setup:
    def form = new CorrelationMatrixCreateForm(name: "Matrix name", domesticCcy: "EUR", versionForm: VERSION_FORM)

    when:
    def result = repository.insert(form)

    then:
    result.isRight()
    def loaded = operations.findOne(new Query(), CorrelationMatrix)
    loaded.name == "Matrix name"
    loaded.domesticCcy == "EUR"
    loaded.entityId == result.getOrNull().id
    loaded.validFrom == LocalDate.parse("2020-01-01")
    loaded.recordDate != null
    loaded.modifiedAt != null
    loaded.modifiedAt != null
  }

  def "should return correlation matrix list"() {
    setup:
    def matrix = correlation()
    operations.insert(matrix)

    def matrix2 = new CorrelationMatrixBuilder()
      .values([CorrelationMatrixValue.of("EUR", FX, "USD", FX, 0.1)])
      .build()
    operations.insert(matrix2)

    when:
    def result = repository.correlationMatrixList(BitemporalDate.newOfNow(), active(), null)

    then:
    result.size() == 2
    def matrixView = result[0]
    matrixView.numberOfEntries == 1
    matrixView.entityId == matrix.entityId
    matrixView.name == "Matrix"
    matrixView.domesticCcy == "EUR"
    matrixView.updatedBy == "Full Name"
    matrixView.updatedAt != null
    matrixView.validFrom == ofEpochDay(0)
    matrixView.comment == "comment"
    matrixView.recordDate != null
    matrixView.state == State.ACTIVE
  }

  def "should return correlation matrix list for domesticCcy EUR"() {
    setup:
    def matrix1 = new CorrelationMatrixBuilder()
      .domesticCcy("EUR")
      .values([CorrelationMatrixValue.of("EUR", FX, "USD", FX, 0.1)])
      .build()
    operations.insert(matrix1)

    def matrix2 = new CorrelationMatrixBuilder()
      .name("USD Matrix")
      .domesticCcy("USD")
      .values([CorrelationMatrixValue.of("EUR", FX, "USD", FX, 0.1)])
      .build()
    operations.insert(matrix2)

    when:
    def result = repository.correlationMatrixList(BitemporalDate.newOfNow(), active(), "EUR")

    then:
    result.size() == 1
    def matrixView = result[0]
    matrixView.numberOfEntries == 1
    matrixView.name == "Matrix"
    matrixView.domesticCcy == "EUR"
    matrixView.updatedBy == "Full Name"
    matrixView.updatedAt != null
    matrixView.state == State.ACTIVE
  }

  def "should update correlation matrix"() {
    setup:
    def matrix = correlation()
    operations.insert(matrix)

    def form = new CorrelationMatrixUpdateForm(domesticCcy: "USD", versionForm: VERSION_FORM)

    when:
    def result = repository.update(matrix.entityId, matrix.validFrom, form)

    then:
    result.isRight()

    def allItems = operations.query(CorrelationMatrix).all()
    allItems.size() == 2
    def loaded = allItems.max({ it.getRecordDate() })
    loaded.entityId == result.getOrNull().id
    loaded.name == "Matrix"
    loaded.comment == "new comment"
    loaded.domesticCcy == "USD"
    loaded.values.size() == 1
    loaded.values[0].value == matrix.values[0].value
    loaded.values[0].id.key1 == new CorrelationMatrixKey("EUR", FX)
    loaded.values[0].id.key2 == new CorrelationMatrixKey("USD", FX)
  }

  def "should update values"() {
    setup:
    def values = [
      CorrelationMatrixValue.of("GBP", FX, "USD", FX, 1),
      CorrelationMatrixValue.of("EUR", FX, "GBP", FX, 1)
    ]
    def matrix = correlation()
    operations.insert(matrix)

    when:
    def result = repository.updateValues(matrix.entityId, matrix.validFrom, VERSION_FORM, values)

    then:
    result.isRight()
    def allItems = operations.query(CorrelationMatrix).all()
    allItems.size() == 2
    def loaded = allItems.max({ it.getRecordDate() })
    loaded
    loaded.entityId == result.getOrNull().id
    loaded.name == "Matrix"
    loaded.comment == "new comment"
    loaded.validFrom == LocalDate.parse("2020-01-01")
    loaded.domesticCcy == "EUR"
    loaded.values.size() == 2
    loaded.values[0].id.key1 == CorrelationMatrixKey.of("GBP", FX)
    loaded.values[0].id.key2 == CorrelationMatrixKey.of("USD", FX)
    loaded.values[1].id.key1 == CorrelationMatrixKey.of("EUR", FX)
    loaded.values[1].id.key2 == CorrelationMatrixKey.of("GBP", FX)
  }

  def "should add new value"() {
    setup:
    def values = [CorrelationMatrixValue.of("EUR", FX, "USD", FX, 1)]
    def matrix = new CorrelationMatrixBuilder().values(values).build()
    operations.insert(matrix)

    when:
    def valueForm = new CorrelationMatrixValueForm("EUR", FX.name(), "AED", FX.name(), 2)
    def updateForm = new CorrelationMatrixUpdateValueForm(valueForm, VERSION_FORM)
    def result = repository.updateValue(matrix.entityId, matrix.validFrom, updateForm)

    then:
    result.isRight()
    def allItems = operations.query(CorrelationMatrix).all()
    allItems.size() == 2
    def loaded = allItems.max({ it.getRecordDate() })
    loaded
    loaded.entityId == result.getOrNull().id
    loaded.name == "Matrix"
    loaded.comment == "new comment"
    loaded.validFrom == LocalDate.parse("2020-01-01")
    loaded.domesticCcy == "EUR"
    loaded.values.size() == 2
    loaded.values[0].id.key1 == CorrelationMatrixKey.of("EUR", FX)
    loaded.values[0].id.key2 == CorrelationMatrixKey.of("USD", FX)
    loaded.values[1].id.key1 == CorrelationMatrixKey.of("EUR", FX)
    loaded.values[1].id.key2 == CorrelationMatrixKey.of("AED", FX)
  }

  def "should remove value"() {
    setup:
    def values = [
      CorrelationMatrixValue.of("EUR", FX, "USD", FX, 1),
      CorrelationMatrixValue.of("EUR", FX, "GBP", FX, 1),
      CorrelationMatrixValue.of("USD", FX, "AUD", FX, 1)
    ]
    def matrix = new CorrelationMatrixBuilder().values(values).build()
    operations.insert(matrix)

    when:
    def removeForm = new CorrelationMatrixRemoveValueForm("EUR", FX.name(), VERSION_FORM)
    def result = repository.removeValue(matrix.entityId, matrix.validFrom, removeForm)

    then:
    result.isRight()
    def allItems = operations.query(CorrelationMatrix).all()
    allItems.size() == 2
    def loaded = allItems.max({ it.getRecordDate() })
    loaded
    loaded.entityId == result.getOrNull().id
    loaded.name == "Matrix"
    loaded.comment == "new comment"
    loaded.validFrom == LocalDate.parse("2020-01-01")
    loaded.domesticCcy == "EUR"
    loaded.values.size() == 1
    loaded.values[0].id.key1 == CorrelationMatrixKey.of("USD", FX)
    loaded.values[0].id.key2 == CorrelationMatrixKey.of("AUD", FX)
  }

  def "should return correlation matrix"() {
    setup:
    def matrix = correlation()
    operations.insert(matrix)
    def matrix2 = new CorrelationMatrixBuilder()
      .entityId(matrix.entityId)
      .name("new version")
      .build()
    operations.insert(matrix2)

    when:
    def result = repository.correlation(matrix.entityId, BitemporalDate.newOfNow())

    then:
    result.isRight()
    def view = result.getOrNull()
    view.name == "new version"
    view.domesticCcy == "EUR"
    view.entityId == matrix.entityId
    view.validFrom == ofEpochDay(0)
    view.comment == "comment"
    view.recordDate != null
    view.state == State.ACTIVE
  }

  def "should delete correlation matrix"() {
    setup:
    def matrix = correlation()
    operations.insert(matrix)

    when:
    def result = repository.delete(matrix.entityId, matrix.validFrom)

    then:
    result.isRight()
    result.getOrNull().id == matrix.entityId
    def loaded = operations.query(CorrelationMatrix).all()
    loaded.size() == 2
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == matrix.validFrom
    loaded[1].state == State.DELETED
    loaded[1].validFrom == matrix.validFrom
  }

  def "should archive correlation matrix"() {
    setup:
    def matrix = correlation()
    operations.insert(matrix)

    when:
    def archiveForm = new ArchiveEntityForm(NewVersionFormV2.newDefault())
    def result = repository.archive(matrix.entityId, matrix.validFrom, archiveForm)

    then:
    result.isRight()
    result.getOrNull().id == matrix.entityId
    def loaded = operations.query(CorrelationMatrix).all()
    loaded.size() == 2
    loaded[0].state == State.ACTIVE
    loaded[0].validFrom == matrix.validFrom
    loaded[1].state == State.ARCHIVED
    loaded[1].validFrom == matrix.validFrom
  }

  def "should get matrix versions"() {
    setup:
    def matrix = correlation()
    operations.insert(matrix)
    def matrix2 = correlation()
    matrix2.setEntityId(matrix.getEntityId())
    matrix2.setComment("second version")
    matrix2.setValidFrom(VAL_DT)
    operations.insert(matrix2)
    def matrix3 = correlation()
    matrix3.setEntityId(matrix.getEntityId())
    matrix2.setComment("third version")
    matrix3.setValidFrom(now())
    operations.insert(matrix3)

    when:
    def result = repository.getVersionViews(matrix.entityId)

    then:
    result.size() == 3
    result[0].validFrom == now()
    result[0].state == State.ACTIVE
    result[1].validFrom == VAL_DT
    result[1].state == State.ACTIVE
    result[2].validFrom == ofEpochDay(0)
    result[2].state == State.ACTIVE
  }

  def "should get correlation matrix future versions dates list"() {
    setup:
    def cm1 = correlation()
    def cm2 = new CorrelationMatrixBuilder().validFrom(ofEpochDay(1)).build()
    def cm3 = new CorrelationMatrixBuilder().validFrom(ofEpochDay(2)).build()
    def cm4 = new CorrelationMatrixBuilder().validFrom(ofEpochDay(3)).state(State.DELETED).build()
    operations.insertAll([cm1, cm2, cm3, cm4])

    when:
    def result = repository.getFutureVersions(new CorrelationMatrixSearchForm("Matrix", ofEpochDay(0)))

    then:
    result.dates == [ofEpochDay(1), ofEpochDay(2)]
  }
}
