package com.solum.xplain.trs.portfolio.csv;

import static com.solum.xplain.trs.portfolio.csv.TrsTradeCsvLoader.parseUniqueKey;
import static java.lang.String.format;

import com.solum.xplain.core.portfolio.csv.RowFilter;
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioNamesUniqueKey;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TrsTradeRowFilterUtils {
  private static final String PORTFOLIO_FILTER_MESSAGE = "Company %s Entity %s Portfolio %s";

  public static RowFilter portfolioRowsFilter(NonMtmPortfolioNamesUniqueKey namesUniqueKey) {
    var filterLabel =
        format(
            PORTFOLIO_FILTER_MESSAGE,
            namesUniqueKey.getCompanyExternalId(),
            namesUniqueKey.getEntityExternalId(),
            namesUniqueKey.getNonMtmPortfolioExternalId());
    return new RowFilter(
        row -> parseUniqueKey(row).map(namesUniqueKey::equals).getOr(() -> false), filterLabel);
  }
}
