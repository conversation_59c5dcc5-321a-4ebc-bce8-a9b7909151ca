package com.solum.xplain.trs.valuation.calculation;

import static com.google.common.base.Strings.isNullOrEmpty;
import static com.opengamma.strata.basics.date.BusinessDayConventions.FOLLOWING;

import com.opengamma.strata.basics.ReferenceData;
import com.opengamma.strata.basics.currency.Currency;
import com.opengamma.strata.basics.date.BusinessDayAdjustment;
import com.opengamma.strata.basics.date.BusinessDayConvention;
import com.opengamma.strata.basics.date.BusinessDayConventions;
import com.opengamma.strata.basics.date.DayCount;
import com.opengamma.strata.basics.date.DaysAdjustment;
import com.opengamma.strata.basics.date.HolidayCalendarId;
import com.opengamma.strata.basics.index.IborIndex;
import com.opengamma.strata.basics.index.OvernightIndex;
import com.opengamma.strata.basics.schedule.Frequency;
import com.opengamma.strata.basics.schedule.PeriodicSchedule;
import com.opengamma.strata.basics.schedule.RollConvention;
import com.opengamma.strata.basics.schedule.StubConvention;
import com.opengamma.strata.basics.value.ValueSchedule;
import com.opengamma.strata.product.swap.CompoundingMethod;
import com.opengamma.strata.product.swap.FixedRateCalculation;
import com.opengamma.strata.product.swap.IborRateCalculation;
import com.opengamma.strata.product.swap.NotionalSchedule;
import com.opengamma.strata.product.swap.OvernightAccrualMethod;
import com.opengamma.strata.product.swap.OvernightRateCalculation;
import com.opengamma.strata.product.swap.PaymentSchedule;
import com.opengamma.strata.product.swap.RateCalculation;
import com.opengamma.strata.product.swap.RateCalculationSwapLeg;
import com.solum.xplain.extensions.calendar.ValuationDateReferenceData;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeDetails;
import com.solum.xplain.trs.portfolio.trade.value.TrsTradeLegDetails;
import com.solum.xplain.trs.valuation.calculation.period.CalculatedAccruedPeriods;
import com.solum.xplain.trs.valuation.calculation.period.FundingAccruedPeriod;
import com.solum.xplain.trs.valuation.calculation.period.PerformanceAccruedPeriod;
import com.solum.xplain.trs.value.TrsTradeCalculationType;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class LegAccrualCalculationFactory {
  private final ReferenceData referenceData;

  public LegAccrualCalculation<FundingAccruedPeriod> fundingLegCalculation(
      String tradeId,
      TrsTradeDetails trsTradeDetails,
      CalculatedAccruedPeriods<PerformanceAccruedPeriod> periods) {
    var fundingLeg = trsTradeDetails.fundingLeg();
    var requirements = calculationRequirements(fundingLeg);

    var calculation =
        swapLegCalculation(
            tradeId,
            fundingLeg,
            periods,
            requirements,
            trsTradeDetails.getOtherLegLastPaymentWithheld());

    if (fundingLeg.getType() == TrsTradeCalculationType.OVERNIGHT) {
      return overnightSwapLegAccrualCalculation(
          requirements.getCalendar(), fundingLeg, calculation);
    }

    return calculation;
  }

  public LegAccrualCalculation<PerformanceAccruedPeriod> performanceLegCalculation(
      TrsTradeDetails trsTradeDetails, LocalDate valuationDate) {
    var performanceLeg = trsTradeDetails.performanceLeg();
    var requirements = calculationRequirements(performanceLeg);

    return performanceLeg(trsTradeDetails, performanceLeg, requirements, valuationDate);
  }

  private LegAccrualCalculationRequirements calculationRequirements(TrsTradeLegDetails legDetails) {
    var calendar = calendar(legDetails.getCalendars());
    var accrualSchedule = accrualSchedule(legDetails);
    var bda = businessDayAdjustment(legDetails.getBusinessDayConvention(), calendar);
    var adjustment =
        DaysAdjustment.builder()
            .days(legDetails.getAccrualOffsetDays())
            .calendar(bda.getCalendar())
            .adjustment(bda)
            .build();
    var paymentSchedule = paymentSchedule(legDetails);
    return new LegAccrualCalculationRequirements(
        calendar, accrualSchedule, paymentSchedule, adjustment);
  }

  private SwapLegAccrualCalculation swapLegCalculation(
      String tradeId,
      TrsTradeLegDetails legDetails,
      CalculatedAccruedPeriods<PerformanceAccruedPeriod> periods,
      LegAccrualCalculationRequirements requirements,
      boolean paymentWithheld) {
    var leg =
        RateCalculationSwapLeg.builder()
            .payReceive(legDetails.getPayReceive())
            .accrualSchedule(requirements.getAccrualSchedule())
            .paymentSchedule(requirements.getPaymentSchedule())
            .notionalSchedule(notionalSchedule(legDetails))
            .accrualScheduleOffset(requirements.getAccrualOffset())
            .calculation(toRateCalculation(legDetails))
            .build();

    return SwapLegAccrualCalculation.newOf(tradeId, leg, periods, paymentWithheld, referenceData);
  }

  private NotionalSchedule notionalSchedule(TrsTradeLegDetails legDetails) {
    return NotionalSchedule.builder()
        .currency(Currency.of(legDetails.getCurrency()))
        .amount(ValueSchedule.of(legDetails.getNotional()))
        .build();
  }

  private RateCalculation toRateCalculation(TrsTradeLegDetails legDetails) {
    return switch (legDetails.getType()) {
      case IBOR -> iborRateCalculation(legDetails);
      case FIXED -> fixedRateCalculation(legDetails);
      case OVERNIGHT -> overnightRateCalculation(legDetails);
      default -> throw new IllegalStateException("Unexpected value: " + legDetails.getType());
    };
  }

  private PerformanceLegCalculation performanceLeg(
      TrsTradeDetails details,
      TrsTradeLegDetails legDetails,
      LegAccrualCalculationRequirements requirements,
      LocalDate valuationDate) {

    var valRefData = ValuationDateReferenceData.wrap(this.referenceData, valuationDate);
    var schedule =
        requirements
            .getAccrualSchedule()
            .createSchedule(referenceData)
            .toAdjusted(requirements.getAccrualOffset().resolve(referenceData));

    var calendar = calendar(legDetails.getFixingCalendars());

    var daysAdjustment =
        DaysAdjustment.builder()
            .days(legDetails.getFixingDateOffsetDays())
            .calendar(calendar)
            .adjustment(floatFixingOffsetAdjustment(calendar))
            .build()
            .normalized();
    var paymentOffset = requirements.paymentSchedule.getPaymentDateOffset().resolve(referenceData);

    return PerformanceLegCalculation.builder()
        .payReceive(legDetails.getPayReceive())
        .trsIndexType(legDetails.getTrsIndexType())
        .trsIndex(legDetails.getIndex())
        .paymentDateOffset(paymentOffset)
        .accrualSchedule(schedule)
        .notional(BigDecimal.valueOf(legDetails.getNotional()))
        .dividendPayout(
            legDetails.getDividendPayout() == null
                ? null
                : BigDecimal.valueOf(legDetails.getDividendPayout()))
        .managementFee(details.getManagementFees())
        .managementDayCount(
            details.getManagementFeesDayCount() == null
                ? null
                : DayCount.of(details.getManagementFeesDayCount()))
        .fixingOffset(daysAdjustment.resolve(valRefData))
        .initialIndex(
            legDetails.getInitialCoupon() == null
                ? null
                : BigDecimal.valueOf(legDetails.getInitialCoupon()))
        .paymentWithheld(details.getPerformanceLegLastPaymentWithheld())
        .build();
  }

  private OvernightSwapLegAccrualCalculation overnightSwapLegAccrualCalculation(
      HolidayCalendarId calendar,
      TrsTradeLegDetails legDetails,
      SwapLegAccrualCalculation accrualCalculation) {
    var timeseriesAdjustment =
        Optional.ofNullable(legDetails.getFixingDateOffsetDays())
            .map(
                days ->
                    DaysAdjustment.builder()
                        .days(days)
                        .calendar(OvernightIndex.of(legDetails.getIndex()).getFixingCalendar())
                        .adjustment(floatFixingOffsetAdjustment(calendar))
                        .build()
                        .normalized())
            .map(r -> r.resolve(referenceData))
            .orElse(null);

    return OvernightSwapLegAccrualCalculation.newOf(accrualCalculation, timeseriesAdjustment);
  }

  private BusinessDayAdjustment floatFixingOffsetAdjustment(HolidayCalendarId calendar) {
    return businessDayAdjustment(BusinessDayConventions.PRECEDING.getName(), calendar);
  }

  private RateCalculation fixedRateCalculation(TrsTradeLegDetails leg) {
    return FixedRateCalculation.builder()
        .dayCount(DayCount.of(leg.getDayCount()))
        .rate(ValueSchedule.of(leg.getInitialValue()))
        .build();
  }

  private RateCalculation iborRateCalculation(TrsTradeLegDetails leg) {
    var iborIndex = IborIndex.of(leg.getIndex());
    var daysAdjustment =
        Optional.ofNullable(leg.getFixingDateOffsetDays())
            .map(
                days ->
                    DaysAdjustment.builder()
                        .days(days)
                        .calendar(iborIndex.getFixingDateOffset().getCalendar())
                        .adjustment(iborIndex.getFixingDateOffset().getAdjustment())
                        .build()
                        .normalized())
            .orElse(iborIndex.getFixingDateOffset());

    return IborRateCalculation.builder()
        .dayCount(DayCount.of(leg.getDayCount()))
        .index(iborIndex)
        .fixingDateOffset(daysAdjustment)
        .spread(valueSchedule(leg.getInitialValue()))
        .firstRate(leg.getInitialCoupon())
        .build();
  }

  private RateCalculation overnightRateCalculation(TrsTradeLegDetails leg) {
    var overnightAccrual =
        leg.getOvernightAccrualMethod() == null
            ? OvernightAccrualMethod.COMPOUNDED
            : OvernightAccrualMethod.of(leg.getOvernightAccrualMethod());

    return OvernightRateCalculation.builder()
        .dayCount(DayCount.of(leg.getDayCount()))
        .index(OvernightIndex.of(leg.getIndex()))
        .spread(valueSchedule(leg.getInitialValue()))
        .accrualMethod(overnightAccrual)
        .rateCutOffDays(Integer.parseInt(leg.getOvernightRateCutOffDays()))
        .build();
  }

  private ValueSchedule valueSchedule(Double value) {
    if (value == null) {
      return null;
    }
    return ValueSchedule.of(value);
  }

  private PeriodicSchedule accrualSchedule(TrsTradeLegDetails legDetails) {
    var calendar = calendar(legDetails.getCalendars());
    var bda = businessDayAdjustment(legDetails.getBusinessDayConvention(), calendar);

    return PeriodicSchedule.builder()
        .startDate(legDetails.getStartDate())
        .endDate(legDetails.getEndDate())
        .frequency(Frequency.parse(legDetails.getAccrualFrequency()))
        .businessDayAdjustment(bda)
        .rollConvention(rollConvention(legDetails))
        .stubConvention(stubConvention(legDetails))
        .firstRegularStartDate(legDetails.getRegularStartDate())
        .lastRegularEndDate(legDetails.getRegularEndDate())
        .build();
  }

  private PaymentSchedule paymentSchedule(TrsTradeLegDetails legDetails) {
    var calendar = calendar(legDetails.getCalendars());
    var bda = businessDayAdjustment(legDetails.getBusinessDayConvention(), calendar);
    var offsetAdjustment = BusinessDayAdjustment.of(FOLLOWING, calendar);
    var compoundingMethod =
        isNullOrEmpty(legDetails.getPaymentCompounding())
            ? CompoundingMethod.NONE
            : CompoundingMethod.of(legDetails.getPaymentCompounding());
    return PaymentSchedule.builder()
        .paymentFrequency(Frequency.parse(legDetails.getPaymentFrequency()))
        .businessDayAdjustment(bda)
        .paymentDateOffset(
            DaysAdjustment.builder()
                .days(legDetails.getPaymentOffsetDays())
                .calendar(calendar)
                .adjustment(offsetAdjustment)
                .build())
        .compoundingMethod(compoundingMethod)
        .build();
  }

  private HolidayCalendarId calendar(List<String> calendars) {
    return calendars.stream()
        .map(HolidayCalendarId::of)
        .reduce(HolidayCalendarId::combinedWith)
        .orElseThrow(() -> new IllegalArgumentException("At least one calendar is required"));
  }

  private BusinessDayAdjustment businessDayAdjustment(
      String businessDayConvention, HolidayCalendarId calendar) {
    var convention = BusinessDayConvention.of(businessDayConvention);
    return BusinessDayAdjustment.of(convention, calendar);
  }

  private RollConvention rollConvention(TrsTradeLegDetails legDetails) {
    return legDetails.getRollConvention() == null
        ? null
        : RollConvention.of(legDetails.getRollConvention());
  }

  private StubConvention stubConvention(TrsTradeLegDetails legDetails) {
    return legDetails.getStubConvention() == null
        ? null
        : StubConvention.of(legDetails.getStubConvention());
  }

  @Data
  private static class LegAccrualCalculationRequirements {
    private final HolidayCalendarId calendar;
    private final PeriodicSchedule accrualSchedule;
    private final PaymentSchedule paymentSchedule;
    private final DaysAdjustment accrualOffset;
  }
}
