package com.solum.xplain.trs.portfolio.trade.validation;

import com.opengamma.strata.basics.schedule.Frequency;
import com.solum.xplain.trs.portfolio.trade.form.TrsLegForm;
import io.atlassian.fugue.Checked;
import io.atlassian.fugue.extensions.step.Steps;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class RequiredEqualAccrualPaymentFrequenciesValidator
    implements ConstraintValidator<RequiredEqualAccrualPaymentFrequencies, TrsLegForm> {

  public static boolean isValid(String accrualFreq, String paymentFreq) {
    return Steps.begin(Checked.now(() -> Frequency.parse(accrualFreq)))
        .then(() -> Checked.now(() -> Frequency.parse(paymentFreq)))
        .yield(Frequency::equals)
        .getOrElse(() -> true);
  }

  @Override
  public boolean isValid(TrsLegForm value, ConstraintValidatorContext context) {
    context.disableDefaultConstraintViolation();
    context
        .buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
        .addPropertyNode("accrualFrequency")
        .addConstraintViolation();
    return isValid(value.getAccrualFrequency(), value.getPaymentFrequency());
  }
}
