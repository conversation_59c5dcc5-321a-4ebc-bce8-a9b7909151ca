package com.solum.xplain.trs.portfolio.search

import com.solum.xplain.core.authentication.AuthenticationContext
import com.solum.xplain.core.portfolio.PortfolioTeamFilter
import com.solum.xplain.core.search.SearchRequest
import com.solum.xplain.core.search.SearchResponse
import com.solum.xplain.core.users.UserBuilder
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilter
import com.solum.xplain.trs.portfolio.NonMtmPortfolioTeamFilterProvider
import com.solum.xplain.trs.portfolio.repository.NonMtmPortfolioRepository
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioCondensedView
import com.solum.xplain.trs.portfolio.value.NonMtmPortfolioFilter
import spock.lang.Specification

class NonMtmPortfolioItemSearchServiceTest extends Specification {
  static USER = UserBuilder.user()

  NonMtmPortfolioItemSearchRepository repository = Mock()
  NonMtmPortfolioRepository portfolioRepository = Mock()
  NonMtmPortfolioTeamFilterProvider filterProvider = Mock()
  AuthenticationContext authenticationContext = Mock()

  NonMtmPortfolioItemSearchService service = new NonMtmPortfolioItemSearchService(repository, portfolioRepository, filterProvider, authenticationContext)

  def "should correctly search trade"() {
    setup:
    def request = Mock(SearchRequest)
    def filter = Mock(NonMtmPortfolioTeamFilter)

    1 * authenticationContext.currentUser() >> USER
    1 * filterProvider.provideFilter(USER) >> filter
    1 * portfolioRepository.portfolioCondensedViews(new NonMtmPortfolioFilter(false), filter) >> [new NonMtmPortfolioCondensedView(id: "ID")]

    1 * repository.nonMtmPortfolioItems(["ID"], request) >> new SearchResponse<>([])

    when:
    def result = service.search(request)

    then:
    result == new SearchResponse<>([])
  }
}
